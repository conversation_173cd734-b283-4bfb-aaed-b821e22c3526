#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聊天API路由

提供与QYuan进行智能对话的API接口
"""

import logging
import uuid
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse

from ..models import ChatRequest, ChatResponse, APIResponse
from ..app import get_qyuan_core, verify_api_key
from ...core.qyuan_core import QYuanCore

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/send",
    response_model=ChatResponse,
    summary="发送聊天消息",
    description="向QYuan发送消息并获取智能回复"
)
async def send_message(
    request: ChatRequest,
    qyuan: QYuanCore = Depends(get_qyuan_core),
    _: bool = Depends(verify_api_key)
):
    """发送聊天消息"""
    try:
        # 生成会话ID（如果没有提供）
        session_id = request.session_id or str(uuid.uuid4())
        user_id = request.user_id or "api_user"
        
        logger.info(f"处理聊天消息: {request.message[:50]}... (用户: {user_id})")
        
        # 获取通信引擎
        communication_engine = qyuan.engines.get("communication")
        if not communication_engine:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="通信引擎不可用"
            )
        
        # 处理消息
        response_text = await communication_engine.process_user_message(
            message=request.message,
            user_id=user_id
        )
        
        # 获取意图分析结果（如果可用）
        intent = None
        confidence = None
        
        if hasattr(communication_engine, 'llm_manager') and communication_engine.llm_manager:
            try:
                intent_result = await communication_engine.llm_manager.analyze_intent(request.message)
                intent = intent_result.intent
                confidence = intent_result.confidence
            except Exception as e:
                logger.warning(f"意图分析失败: {e}")
        
        # 构建响应
        response = ChatResponse(
            response=response_text,
            intent=intent,
            confidence=confidence,
            session_id=session_id,
            context=request.context
        )
        
        logger.info(f"聊天响应生成成功: {len(response_text)} 字符")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理聊天消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理消息时发生错误: {str(e)}"
        )


@router.post(
    "/stream",
    summary="流式聊天",
    description="以流式方式获取QYuan的回复（实时输出）"
)
async def stream_message(
    request: ChatRequest,
    qyuan: QYuanCore = Depends(get_qyuan_core),
    _: bool = Depends(verify_api_key)
):
    """流式聊天消息"""
    try:
        # 生成会话ID（如果没有提供）
        session_id = request.session_id or str(uuid.uuid4())
        user_id = request.user_id or "api_user"
        
        logger.info(f"处理流式聊天消息: {request.message[:50]}... (用户: {user_id})")
        
        # 获取通信引擎
        communication_engine = qyuan.engines.get("communication")
        if not communication_engine:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="通信引擎不可用"
            )
        
        async def generate_response():
            """生成流式响应"""
            try:
                # 处理消息
                response_text = await communication_engine.process_user_message(
                    message=request.message,
                    user_id=user_id
                )
                
                # 模拟流式输出（将完整响应分块发送）
                import json
                
                # 发送开始标记
                yield f"data: {json.dumps({'type': 'start', 'session_id': session_id})}\n\n"
                
                # 分块发送响应
                chunk_size = 10  # 每次发送10个字符
                for i in range(0, len(response_text), chunk_size):
                    chunk = response_text[i:i + chunk_size]
                    yield f"data: {json.dumps({'type': 'chunk', 'content': chunk})}\n\n"
                    
                    # 添加小延迟模拟实时输出
                    import asyncio
                    await asyncio.sleep(0.1)
                
                # 发送结束标记
                yield f"data: {json.dumps({'type': 'end', 'session_id': session_id})}\n\n"
                
            except Exception as e:
                logger.error(f"流式响应生成失败: {e}")
                yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
        
        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理流式聊天消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理流式消息时发生错误: {str(e)}"
        )


@router.get(
    "/history/{session_id}",
    response_model=List[dict],
    summary="获取聊天历史",
    description="获取指定会话的聊天历史记录"
)
async def get_chat_history(
    session_id: str,
    limit: int = 20,
    qyuan: QYuanCore = Depends(get_qyuan_core),
    _: bool = Depends(verify_api_key)
):
    """获取聊天历史"""
    try:
        logger.info(f"获取聊天历史: {session_id} (限制: {limit})")
        
        # 获取通信引擎
        communication_engine = qyuan.engines.get("communication")
        if not communication_engine:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="通信引擎不可用"
            )
        
        # 如果通信引擎有记忆管理器，从记忆中获取历史
        if hasattr(communication_engine, 'memory_manager') and communication_engine.memory_manager:
            try:
                memories = communication_engine.memory_manager.get_recent_memories(
                    session_id=session_id,
                    hours=24,  # 获取最近24小时的记忆
                    memory_types=["raw"],
                    limit=limit
                )
                
                # 转换为聊天历史格式
                history = []
                for memory in memories:
                    if memory.get('event_type') == 'conversation':
                        try:
                            import json
                            content = json.loads(memory['content'])
                            history.append({
                                "timestamp": memory['created_at'],
                                "user_message": content.get('user_message', ''),
                                "assistant_response": content.get('assistant_response', ''),
                                "intent": content.get('user_intent'),
                                "confidence": content.get('intent_confidence', 0.0)
                            })
                        except (json.JSONDecodeError, KeyError) as e:
                            logger.warning(f"解析记忆内容失败: {e}")
                            continue
                
                # 按时间排序（最新的在前）
                history.sort(key=lambda x: x['timestamp'], reverse=True)
                
                logger.info(f"获取聊天历史成功: {len(history)} 条记录")
                return history
                
            except Exception as e:
                logger.error(f"从记忆获取聊天历史失败: {e}")
        
        # 如果记忆管理器不可用，返回空历史
        logger.warning("记忆管理器不可用，返回空聊天历史")
        return []
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取聊天历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取聊天历史时发生错误: {str(e)}"
        )


@router.delete(
    "/history/{session_id}",
    response_model=APIResponse,
    summary="清空聊天历史",
    description="清空指定会话的聊天历史记录"
)
async def clear_chat_history(
    session_id: str,
    qyuan: QYuanCore = Depends(get_qyuan_core),
    _: bool = Depends(verify_api_key)
):
    """清空聊天历史"""
    try:
        logger.info(f"清空聊天历史: {session_id}")
        
        # 获取通信引擎
        communication_engine = qyuan.engines.get("communication")
        if not communication_engine:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="通信引擎不可用"
            )
        
        # 清空LLM对话历史
        if hasattr(communication_engine, 'llm_manager') and communication_engine.llm_manager:
            communication_engine.llm_manager.clear_conversation_history()
        
        # 清空活跃对话状态
        if hasattr(communication_engine, 'active_conversations'):
            communication_engine.active_conversations.pop(session_id, None)
        
        logger.info(f"聊天历史清空成功: {session_id}")
        
        return APIResponse(
            success=True,
            message=f"会话 {session_id} 的聊天历史已清空"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清空聊天历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清空聊天历史时发生错误: {str(e)}"
        )

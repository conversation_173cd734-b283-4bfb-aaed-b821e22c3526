#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI元素识别包

提供UI元素识别功能，基于Windows UI Automation API。
支持多种后端实现：pywinauto, win32gui。
其中Win32GUI负责窗口级操作，PyWinAuto负责窗口内元素识别。
"""

# 导出主要的类和接口
from .ui_automation_base import UIAutomationBase
from .ui_automation_factory import UIAutomationFactory

# 为了方便使用，直接导出工厂类的create方法
from .ui_automation_factory import UIAutomationFactory as _Factory
create = _Factory.create

# 明确指定导出的符号
__all__ = ['UIAutomationBase', 'UIAutomationFactory', 'create']

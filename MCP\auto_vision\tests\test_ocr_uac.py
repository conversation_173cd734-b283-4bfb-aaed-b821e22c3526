#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试OCR识别Windows账户控制弹窗
"""

import os
import sys
import time
import platform
from PIL import Image, ImageGrab

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 导入OCR文本识别模块
from src.ocr_recognition import OCRRecognition


def capture_uac_dialog():
    """
    尝试捕获UAC对话框
    
    Returns:
        PIL.Image: 捕获的屏幕图像
    """
    print("请在5秒内触发一个UAC对话框...")
    for i in range(5, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    # 捕获全屏
    screenshot = ImageGrab.grab()
    
    # 保存截图
    screenshot_path = os.path.join(parent_dir, "screenshots", "uac_dialog.png")
    os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
    screenshot.save(screenshot_path)
    print(f"截图已保存至: {screenshot_path}")
    
    return screenshot, screenshot_path


def test_ocr_uac_dialog():
    """测试OCR识别UAC对话框"""
    print("\n测试OCR识别UAC对话框...")
    
    # 检查是否为Windows平台
    if platform.system() != "Windows":
        print("此测试仅适用于Windows平台")
        return False
    
    # 创建OCR对象
    ocr = OCRRecognition(engine="auto", fallback=True)
    
    # 获取引擎信息
    engine_info = ocr.get_engine_info()
    print("\n引擎信息:")
    print(f"主引擎: {engine_info['primary_engine']['name']}")
    if engine_info['fallback_engine']:
        print(f"备用引擎: {engine_info['fallback_engine']['name']}")
    
    # 捕获UAC对话框
    try:
        screenshot, screenshot_path = capture_uac_dialog()
    except Exception as e:
        print(f"捕获UAC对话框失败: {e}")
        return False
    
    # 识别文本
    print("\n识别UAC对话框文本...")
    start_time = time.time()
    results = ocr.recognize_text(screenshot, return_format="structured")
    end_time = time.time()
    
    # 打印识别结果
    print(f"识别耗时: {end_time - start_time:.2f}秒")
    print(f"识别到{len(results)}个文本块")
    
    # 打印所有结果
    for i, item in enumerate(results):
        print(f"{i+1}. 文本: {item['text']}")
        print(f"   位置: {item['box']}")
        print(f"   置信度: {item['confidence']}")
        print("-" * 50)
    
    # 检查是否识别到UAC相关文本
    uac_keywords = ["用户账户控制", "User Account Control", "UAC", "管理员", "Administrator", 
                   "权限", "permission", "是否允许", "允许", "Allow", "更改", "Change"]
    
    found_uac = False
    for item in results:
        for keyword in uac_keywords:
            if keyword.lower() in item['text'].lower():
                print(f"\n找到UAC相关文本: {item['text']}")
                found_uac = True
                break
        if found_uac:
            break
    
    if not found_uac:
        print("\n未找到UAC相关文本")
    
    return found_uac


def test_ocr_with_existing_uac_image():
    """使用已有的UAC对话框图像测试OCR"""
    print("\n使用已有的UAC对话框图像测试OCR...")
    
    # 检查是否为Windows平台
    if platform.system() != "Windows":
        print("此测试仅适用于Windows平台")
        return False
    
    # 创建OCR对象
    ocr = OCRRecognition(engine="auto", fallback=True)
    
    # 获取引擎信息
    engine_info = ocr.get_engine_info()
    print("\n引擎信息:")
    print(f"主引擎: {engine_info['primary_engine']['name']}")
    if engine_info['fallback_engine']:
        print(f"备用引擎: {engine_info['fallback_engine']['name']}")
    
    # 检查是否存在UAC对话框图像
    screenshot_path = os.path.join(parent_dir, "screenshots", "uac_dialog.png")
    if not os.path.exists(screenshot_path):
        print(f"UAC对话框图像不存在: {screenshot_path}")
        return False
    
    # 加载图像
    screenshot = Image.open(screenshot_path)
    
    # 识别文本
    print(f"\n识别UAC对话框文本: {screenshot_path}")
    start_time = time.time()
    results = ocr.recognize_text(screenshot, return_format="structured")
    end_time = time.time()
    
    # 打印识别结果
    print(f"识别耗时: {end_time - start_time:.2f}秒")
    print(f"识别到{len(results)}个文本块")
    
    # 打印所有结果
    for i, item in enumerate(results):
        print(f"{i+1}. 文本: {item['text']}")
        print(f"   位置: {item['box']}")
        print(f"   置信度: {item['confidence']}")
        print("-" * 50)
    
    # 检查是否识别到UAC相关文本
    uac_keywords = ["用户账户控制", "User Account Control", "UAC", "管理员", "Administrator", 
                   "权限", "permission", "是否允许", "允许", "Allow", "更改", "Change"]
    
    found_uac = False
    for item in results:
        for keyword in uac_keywords:
            if keyword.lower() in item['text'].lower():
                print(f"\n找到UAC相关文本: {item['text']}")
                found_uac = True
                break
        if found_uac:
            break
    
    if not found_uac:
        print("\n未找到UAC相关文本")
    
    return found_uac


if __name__ == "__main__":
    # 测试OCR识别UAC对话框
    print("请选择测试模式:")
    print("1. 实时捕获UAC对话框并识别")
    print("2. 使用已有的UAC对话框图像测试")
    
    choice = input("请输入选项 (1/2): ")
    
    if choice == "1":
        result = test_ocr_uac_dialog()
    elif choice == "2":
        result = test_ocr_with_existing_uac_image()
    else:
        print("无效的选项")
        sys.exit(1)
    
    # 输出结果
    print("\n测试结果:")
    print(f"OCR识别UAC对话框: {'成功' if result else '失败'}")

"""
辅助函数模块，提供各种工具函数。
"""

import json
import logging
from typing import Any, Dict, Optional, Union


# 配置日志
def setup_logging(level=logging.INFO):
    """
    设置日志配置。
    
    Args:
        level: 日志级别
    """
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def validate_coordinates(x: Optional[int], y: Optional[int], screen_width: int, screen_height: int) -> Dict[str, Any]:
    """
    验证坐标是否在屏幕范围内。
    
    Args:
        x: X坐标
        y: Y坐标
        screen_width: 屏幕宽度
        screen_height: 屏幕高度
    
    Returns:
        dict: 包含验证结果的字典
    """
    if x is None or y is None:
        return {"valid": True, "message": "坐标未提供，将使用当前位置"}
    
    if not isinstance(x, (int, float)) or not isinstance(y, (int, float)):
        return {"valid": False, "message": "坐标必须是数字"}
    
    if x < 0 or x >= screen_width or y < 0 or y >= screen_height:
        return {
            "valid": False,
            "message": f"坐标 ({x}, {y}) 超出屏幕范围 (0-{screen_width-1}, 0-{screen_height-1})"
        }
    
    return {"valid": True, "message": "坐标有效"}


def format_response(success: bool, data: Dict[str, Any] = None, error: str = None) -> str:
    """
    格式化响应为JSON字符串。
    
    Args:
        success: 操作是否成功
        data: 响应数据
        error: 错误信息
    
    Returns:
        str: JSON格式的响应字符串
    """
    response = {
        "success": success
    }
    
    if data:
        response["data"] = data
    
    if error:
        response["error"] = error
    
    return json.dumps(response, ensure_ascii=False)


def parse_button(button: str) -> str:
    """
    解析并验证鼠标按钮。
    
    Args:
        button: 鼠标按钮名称
    
    Returns:
        str: 有效的鼠标按钮名称
    """
    valid_buttons = ["left", "right", "middle"]
    button = button.lower()
    
    if button not in valid_buttons:
        return "left"  # 默认使用左键
    
    return button


def parse_direction(direction: str) -> str:
    """
    解析并验证滚动方向。
    
    Args:
        direction: 滚动方向
    
    Returns:
        str: 有效的滚动方向
    """
    valid_directions = ["up", "down"]
    direction = direction.lower()
    
    if direction not in valid_directions:
        return "up"  # 默认向上滚动
    
    return direction

# -*- coding: utf-8 -*-
"""
循环性能优化器
优化感知-行动循环的响应时间，实现并行化处理和效率评估机制
严格按照代码规范的单一职责原则
"""

import asyncio
import time
import statistics
from typing import Dict, Any, Optional, List, Callable, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

from ..utils.logger import get_logger


class OptimizationStrategy(Enum):
    """优化策略枚举"""
    PARALLEL_PROCESSING = "parallel_processing"
    CACHING = "caching"
    PIPELINE = "pipeline"
    ADAPTIVE_TIMING = "adaptive_timing"
    RESOURCE_POOLING = "resource_pooling"


@dataclass
class PerformanceMetrics:
    """性能指标"""
    cycle_times: List[float] = field(default_factory=list)
    perception_times: List[float] = field(default_factory=list)
    decision_times: List[float] = field(default_factory=list)
    execution_times: List[float] = field(default_factory=list)
    verification_times: List[float] = field(default_factory=list)
    
    total_cycles: int = 0
    successful_cycles: int = 0
    failed_cycles: int = 0
    
    cache_hits: int = 0
    cache_misses: int = 0
    
    parallel_tasks: int = 0
    sequential_tasks: int = 0
    
    last_update: datetime = field(default_factory=datetime.now)
    
    @property
    def average_cycle_time(self) -> float:
        """平均循环时间"""
        return statistics.mean(self.cycle_times) if self.cycle_times else 0.0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_cycles == 0:
            return 0.0
        return self.successful_cycles / self.total_cycles
    
    @property
    def cache_hit_rate(self) -> float:
        """缓存命中率"""
        total_cache_requests = self.cache_hits + self.cache_misses
        if total_cache_requests == 0:
            return 0.0
        return self.cache_hits / total_cache_requests
    
    @property
    def parallelization_rate(self) -> float:
        """并行化率"""
        total_tasks = self.parallel_tasks + self.sequential_tasks
        if total_tasks == 0:
            return 0.0
        return self.parallel_tasks / total_tasks


@dataclass
class OptimizationConfig:
    """优化配置"""
    enable_parallel_processing: bool = True
    enable_caching: bool = True
    enable_pipeline: bool = True
    enable_adaptive_timing: bool = True
    
    max_parallel_tasks: int = 4
    cache_size: int = 100
    cache_ttl: float = 300.0  # 缓存生存时间（秒）
    
    target_cycle_time: float = 5.0  # 目标循环时间（秒）
    performance_window_size: int = 50  # 性能统计窗口大小
    
    optimization_interval: float = 30.0  # 优化调整间隔（秒）


class LoopOptimizer:
    """循环性能优化器"""
    
    def __init__(self, config: OptimizationConfig = None):
        """初始化循环优化器"""
        self.config = config or OptimizationConfig()
        self.logger = get_logger(self.__class__.__name__)
        
        # 性能指标
        self.metrics = PerformanceMetrics()
        
        # 缓存系统
        self.cache: Dict[str, Tuple[Any, datetime]] = {}
        
        # 并行任务池
        self.task_pool: List[asyncio.Task] = []
        
        # 优化策略
        self.active_strategies: List[OptimizationStrategy] = []
        
        # 性能回调
        self.performance_callbacks: List[Callable] = []
        
        # 自适应参数
        self.adaptive_params = {
            "perception_timeout": 2.0,
            "decision_timeout": 3.0,
            "execution_timeout": 2.0,
            "verification_timeout": 1.0,
        }
        
        # 初始化优化策略
        self._initialize_strategies()
    
    def _initialize_strategies(self):
        """初始化优化策略"""
        if self.config.enable_parallel_processing:
            self.active_strategies.append(OptimizationStrategy.PARALLEL_PROCESSING)
        
        if self.config.enable_caching:
            self.active_strategies.append(OptimizationStrategy.CACHING)
        
        if self.config.enable_pipeline:
            self.active_strategies.append(OptimizationStrategy.PIPELINE)
        
        if self.config.enable_adaptive_timing:
            self.active_strategies.append(OptimizationStrategy.ADAPTIVE_TIMING)
        
        self.logger.info(f"启用优化策略: {[s.value for s in self.active_strategies]}")
    
    async def optimize_perception_phase(self, perception_func: Callable, *args, **kwargs) -> Any:
        """优化感知阶段"""
        start_time = time.time()
        
        try:
            # 检查缓存
            if OptimizationStrategy.CACHING in self.active_strategies:
                cache_key = self._generate_cache_key("perception", args, kwargs)
                cached_result = self._get_from_cache(cache_key)
                
                if cached_result is not None:
                    self.metrics.cache_hits += 1
                    self.metrics.perception_times.append(time.time() - start_time)
                    return cached_result
                else:
                    self.metrics.cache_misses += 1
            
            # 执行感知任务
            if OptimizationStrategy.PARALLEL_PROCESSING in self.active_strategies:
                # 并行处理多个感知任务
                result = await self._execute_with_timeout(
                    perception_func(*args, **kwargs),
                    self.adaptive_params["perception_timeout"]
                )
                self.metrics.parallel_tasks += 1
            else:
                result = await perception_func(*args, **kwargs)
                self.metrics.sequential_tasks += 1
            
            # 缓存结果
            if OptimizationStrategy.CACHING in self.active_strategies and result is not None:
                cache_key = self._generate_cache_key("perception", args, kwargs)
                self._put_to_cache(cache_key, result)
            
            execution_time = time.time() - start_time
            self.metrics.perception_times.append(execution_time)
            
            return result
            
        except Exception as e:
            self.logger.error(f"感知阶段优化异常: {e}")
            execution_time = time.time() - start_time
            self.metrics.perception_times.append(execution_time)
            raise
    
    async def optimize_decision_phase(self, decision_func: Callable, *args, **kwargs) -> Any:
        """优化决策阶段"""
        start_time = time.time()
        
        try:
            # 检查缓存
            if OptimizationStrategy.CACHING in self.active_strategies:
                cache_key = self._generate_cache_key("decision", args, kwargs)
                cached_result = self._get_from_cache(cache_key)
                
                if cached_result is not None:
                    self.metrics.cache_hits += 1
                    self.metrics.decision_times.append(time.time() - start_time)
                    return cached_result
                else:
                    self.metrics.cache_misses += 1
            
            # 执行决策任务
            result = await self._execute_with_timeout(
                decision_func(*args, **kwargs),
                self.adaptive_params["decision_timeout"]
            )
            
            # 缓存结果
            if OptimizationStrategy.CACHING in self.active_strategies and result is not None:
                cache_key = self._generate_cache_key("decision", args, kwargs)
                self._put_to_cache(cache_key, result)
            
            execution_time = time.time() - start_time
            self.metrics.decision_times.append(execution_time)
            
            return result
            
        except Exception as e:
            self.logger.error(f"决策阶段优化异常: {e}")
            execution_time = time.time() - start_time
            self.metrics.decision_times.append(execution_time)
            raise
    
    async def optimize_execution_phase(self, execution_func: Callable, *args, **kwargs) -> Any:
        """优化执行阶段"""
        start_time = time.time()
        
        try:
            # 执行任务（执行阶段通常不缓存）
            result = await self._execute_with_timeout(
                execution_func(*args, **kwargs),
                self.adaptive_params["execution_timeout"]
            )
            
            execution_time = time.time() - start_time
            self.metrics.execution_times.append(execution_time)
            
            return result
            
        except Exception as e:
            self.logger.error(f"执行阶段优化异常: {e}")
            execution_time = time.time() - start_time
            self.metrics.execution_times.append(execution_time)
            raise
    
    async def optimize_verification_phase(self, verification_func: Callable, *args, **kwargs) -> Any:
        """优化验证阶段"""
        start_time = time.time()
        
        try:
            # 执行验证任务
            result = await self._execute_with_timeout(
                verification_func(*args, **kwargs),
                self.adaptive_params["verification_timeout"]
            )
            
            execution_time = time.time() - start_time
            self.metrics.verification_times.append(execution_time)
            
            return result
            
        except Exception as e:
            self.logger.error(f"验证阶段优化异常: {e}")
            execution_time = time.time() - start_time
            self.metrics.verification_times.append(execution_time)
            raise
    
    async def optimize_full_cycle(self, cycle_func: Callable, *args, **kwargs) -> Any:
        """优化完整循环"""
        start_time = time.time()
        
        try:
            # 执行完整循环
            result = await cycle_func(*args, **kwargs)
            
            # 记录循环时间
            cycle_time = time.time() - start_time
            self.metrics.cycle_times.append(cycle_time)
            self.metrics.total_cycles += 1
            
            if result:
                self.metrics.successful_cycles += 1
            else:
                self.metrics.failed_cycles += 1
            
            # 触发性能分析
            await self._analyze_performance()
            
            return result
            
        except Exception as e:
            self.logger.error(f"循环优化异常: {e}")
            cycle_time = time.time() - start_time
            self.metrics.cycle_times.append(cycle_time)
            self.metrics.total_cycles += 1
            self.metrics.failed_cycles += 1
            raise
    
    async def _execute_with_timeout(self, coro, timeout: float) -> Any:
        """带超时的执行"""
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            self.logger.warning(f"任务执行超时: {timeout}秒")
            raise
    
    def _generate_cache_key(self, phase: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        # 简化的缓存键生成逻辑
        key_parts = [phase]
        
        # 添加参数哈希
        if args:
            key_parts.append(str(hash(str(args))))
        
        if kwargs:
            key_parts.append(str(hash(str(sorted(kwargs.items())))))
        
        return "_".join(key_parts)
    
    def _get_from_cache(self, key: str) -> Optional[Any]:
        """从缓存获取"""
        if key in self.cache:
            value, timestamp = self.cache[key]
            
            # 检查是否过期
            if datetime.now() - timestamp < timedelta(seconds=self.config.cache_ttl):
                return value
            else:
                # 删除过期缓存
                del self.cache[key]
        
        return None
    
    def _put_to_cache(self, key: str, value: Any):
        """放入缓存"""
        # 检查缓存大小
        if len(self.cache) >= self.config.cache_size:
            # 删除最旧的缓存项
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][1])
            del self.cache[oldest_key]
        
        self.cache[key] = (value, datetime.now())
    
    async def _analyze_performance(self):
        """分析性能并调整参数"""
        if OptimizationStrategy.ADAPTIVE_TIMING not in self.active_strategies:
            return
        
        # 只在有足够数据时进行分析
        if len(self.metrics.cycle_times) < 10:
            return
        
        # 分析最近的性能数据
        recent_cycle_times = self.metrics.cycle_times[-self.config.performance_window_size:]
        avg_cycle_time = statistics.mean(recent_cycle_times)
        
        # 如果平均循环时间超过目标，调整超时参数
        if avg_cycle_time > self.config.target_cycle_time:
            # 减少超时时间以提高响应速度
            for param in self.adaptive_params:
                self.adaptive_params[param] *= 0.95
                self.adaptive_params[param] = max(0.5, self.adaptive_params[param])  # 最小值限制
            
            self.logger.debug(f"调整超时参数以提高性能: {self.adaptive_params}")
        
        elif avg_cycle_time < self.config.target_cycle_time * 0.7:
            # 增加超时时间以提高成功率
            for param in self.adaptive_params:
                self.adaptive_params[param] *= 1.05
                self.adaptive_params[param] = min(10.0, self.adaptive_params[param])  # 最大值限制
            
            self.logger.debug(f"调整超时参数以提高成功率: {self.adaptive_params}")
        
        # 通知性能回调
        await self._notify_performance_callbacks()
    
    async def _notify_performance_callbacks(self):
        """通知性能回调"""
        for callback in self.performance_callbacks:
            try:
                await callback(self.metrics)
            except Exception as e:
                self.logger.error(f"性能回调异常: {e}")
    
    def register_performance_callback(self, callback: Callable):
        """注册性能回调"""
        self.performance_callbacks.append(callback)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        return {
            "metrics": {
                "average_cycle_time": self.metrics.average_cycle_time,
                "success_rate": self.metrics.success_rate,
                "cache_hit_rate": self.metrics.cache_hit_rate,
                "parallelization_rate": self.metrics.parallelization_rate,
                "total_cycles": self.metrics.total_cycles,
            },
            "adaptive_params": self.adaptive_params.copy(),
            "active_strategies": [s.value for s in self.active_strategies],
            "cache_size": len(self.cache),
            "last_update": self.metrics.last_update.isoformat()
        }
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        self.logger.info("缓存已清空")
    
    def reset_metrics(self):
        """重置性能指标"""
        self.metrics = PerformanceMetrics()
        self.logger.info("性能指标已重置")

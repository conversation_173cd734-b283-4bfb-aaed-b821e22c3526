#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速输入法测试脚本

比较原有方法和改进方法的输入法检测结果。
"""

from keyboard_controller import KeyboardController
from improved_ime_handler import ImprovedIMEHandler

def main():
    print("=" * 60)
    print("输入法检测对比测试")
    print("=" * 60)
    
    # 初始化两个处理器
    controller = KeyboardController()
    ime_handler = ImprovedIMEHandler()
    
    # 原有方法检测
    print("1. 原有方法检测结果:")
    try:
        original_result = controller.is_ime_enabled()
        print(f"   结果: {'中文输入法' if original_result else '英文输入法'}")
    except Exception as e:
        print(f"   错误: {str(e)}")
    
    # 改进方法检测
    print("\n2. 改进方法检测结果:")
    try:
        is_chinese, status = ime_handler.is_chinese_ime_active()
        print(f"   结果: {'中文输入法' if is_chinese else '英文输入法'}")
        print(f"   详细: {status}")
    except Exception as e:
        print(f"   错误: {str(e)}")
    
    # 详细信息
    print("\n3. 详细输入法信息:")
    try:
        info = ime_handler.get_detailed_ime_info()
        print(f"   当前布局: {info['current_layout_hex']}")
        print(f"   是否中文: {info['is_chinese']}")
        print(f"   状态信息: {info['status_message']}")
        print(f"   已安装布局: {info['installed_layouts']}")
    except Exception as e:
        print(f"   错误: {str(e)}")
    
    # 结论
    print("\n4. 对比结论:")
    try:
        original_result = controller.is_ime_enabled()
        is_chinese, _ = ime_handler.is_chinese_ime_active()
        
        if original_result == is_chinese:
            print("   ✅ 两种方法检测结果一致")
        else:
            print("   ⚠️  两种方法检测结果不一致")
            print(f"   原有方法: {'中文' if original_result else '英文'}")
            print(f"   改进方法: {'中文' if is_chinese else '英文'}")
            print("   建议使用改进方法的结果")
    except Exception as e:
        print(f"   错误: {str(e)}")

if __name__ == "__main__":
    main()

// -*- coding: utf-8 -*-

/**
 * QYuan前端类型定义
 * 严格按照代码规范，使用TypeScript类型注解
 */

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'command' | 'response' | 'status' | 'error' | 'heartbeat';
  data: Record<string, any>;
  timestamp: string;
  sessionId: string;
  messageId?: string;
}

// 聊天消息类型
export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  sessionId: string;
  metadata?: Record<string, any>;
}

// 系统状态类型
export interface SystemStatus {
  isOnline: boolean;
  engines: EngineStatus[];
  performance: PerformanceMetrics;
  lastUpdate: string;
}

// 引擎状态类型
export interface EngineStatus {
  name: string;
  status: 'running' | 'stopped' | 'error' | 'initializing';
  health: 'healthy' | 'warning' | 'critical';
  lastActivity: string;
  metrics?: Record<string, number>;
}

// 性能指标类型
export interface PerformanceMetrics {
  cpuUsage: number;
  memoryUsage: number;
  responseTime: number;
  requestCount: number;
  errorRate: number;
}

// 用户会话类型
export interface UserSession {
  sessionId: string;
  userId?: string;
  startTime: string;
  lastActivity: string;
  messageCount: number;
  isActive: boolean;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
  executionTime?: number;
}

// 任务类型
export interface Task {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  createdAt: string;
  updatedAt: string;
  result?: any;
  error?: string;
}

// 操作记录类型
export interface ActionRecord {
  id: string;
  type: string;
  description: string;
  status: 'success' | 'failed' | 'pending';
  timestamp: string;
  duration?: number;
  details?: Record<string, any>;
}

// 配置类型
export interface AppConfig {
  apiBaseUrl: string;
  wsUrl: string;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  autoConnect: boolean;
  heartbeatInterval: number;
}

// 组件Props基础类型
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

// 导出所有类型
export type {
  WebSocketMessage,
  ChatMessage,
  SystemStatus,
  EngineStatus,
  PerformanceMetrics,
  UserSession,
  ApiResponse,
  Task,
  ActionRecord,
  AppConfig,
  BaseComponentProps,
  AppError,
};

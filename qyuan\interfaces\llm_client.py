# -*- coding: utf-8 -*-
"""
LLM客户端模块 - 提供统一的LLM接口
"""

import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, List, Any, Optional, AsyncGenerator
from dataclasses import dataclass
from datetime import datetime

from ..core.exceptions import LLMError, LLMAPIError, LLMNetworkError, LLMTimeoutError

@dataclass
class LLMMessage:
    """LLM消息数据类"""
    role: str  # system, user, assistant
    content: str
    timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "role": self.role,
            "content": self.content
        }

@dataclass
class LLMResponse:
    """LLM响应数据类"""
    content: str
    model: str
    usage: Dict[str, int]
    response_time: float
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class LLMClient:
    """LLM客户端基类"""
    
    def __init__(self, api_base: str, api_key: str, model: str, timeout: int = 30, max_retries: int = 3):
        self.api_base = api_base.rstrip('/')
        self.api_key = api_key
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.logger = logging.getLogger("QYuan.LLMClient")
        
        # 统计信息
        self.total_requests = 0
        self.total_tokens = 0
        self.total_cost = 0.0
        self.error_count = 0
        
        # 连接池
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def initialize(self):
        """初始化客户端"""
        if self.session is None:
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=5,
                ttl_dns_cache=300,
                use_dns_cache=True
            )
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json",
                    "User-Agent": "QYuan/1.0"
                }
            )
            
            self.logger.info("LLM客户端初始化完成")
    
    async def close(self):
        """关闭客户端"""
        if self.session:
            await self.session.close()
            self.session = None
            self.logger.info("LLM客户端已关闭")
    
    async def chat_completion(
        self,
        messages: List[LLMMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> LLMResponse:
        """聊天补全接口"""
        if not self.session:
            await self.initialize()
        
        # 构建请求数据
        request_data = {
            "model": self.model,
            "messages": [msg.to_dict() for msg in messages],
            "temperature": temperature,
            "stream": stream
        }
        
        if max_tokens:
            request_data["max_tokens"] = max_tokens
        
        # 添加其他参数
        request_data.update(kwargs)
        
        start_time = time.time()
        
        for attempt in range(self.max_retries + 1):
            try:
                self.logger.debug(f"发送LLM请求 (尝试 {attempt + 1}/{self.max_retries + 1})")
                
                async with self.session.post(
                    f"{self.api_base}/v1/chat/completions",
                    json=request_data
                ) as response:
                    
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        # 更新统计信息
                        self.total_requests += 1
                        if "usage" in result:
                            self.total_tokens += result["usage"].get("total_tokens", 0)
                        
                        # 构建响应对象
                        llm_response = LLMResponse(
                            content=result["choices"][0]["message"]["content"],
                            model=result.get("model", self.model),
                            usage=result.get("usage", {}),
                            response_time=response_time,
                            timestamp=datetime.now(),
                            metadata={"attempt": attempt + 1, "request_data": request_data}
                        )
                        
                        self.logger.debug(f"LLM请求成功，响应时间: {response_time:.2f}s")
                        return llm_response
                    
                    else:
                        error_text = await response.text()
                        error_msg = f"LLM API错误: {response.status} - {error_text}"
                        
                        if response.status == 429:  # 速率限制
                            if attempt < self.max_retries:
                                wait_time = min(2 ** attempt, 10)  # 最大等待10秒
                                self.logger.warning(f"速率限制，等待 {wait_time}s 后重试")
                                await asyncio.sleep(wait_time)
                                continue
                        
                        self.error_count += 1
                        raise LLMAPIError(error_msg, error_code=str(response.status))
            
            except asyncio.TimeoutError:
                if attempt < self.max_retries:
                    self.logger.warning(f"请求超时，重试中...")
                    await asyncio.sleep(1)
                    continue
                
                self.error_count += 1
                raise LLMTimeoutError(f"LLM请求超时 (>{self.timeout}s)")
            
            except aiohttp.ClientError as e:
                if attempt < self.max_retries:
                    self.logger.warning(f"网络错误，重试中: {e}")
                    await asyncio.sleep(1)
                    continue
                
                self.error_count += 1
                raise LLMNetworkError(f"LLM网络错误: {e}")
            
            except Exception as e:
                self.error_count += 1
                raise LLMError(f"LLM请求异常: {e}")
        
        # 如果所有重试都失败了
        self.error_count += 1
        raise LLMError(f"LLM请求失败，已重试 {self.max_retries} 次")
    
    async def stream_chat_completion(
        self,
        messages: List[LLMMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """流式聊天补全接口"""
        if not self.session:
            await self.initialize()
        
        request_data = {
            "model": self.model,
            "messages": [msg.to_dict() for msg in messages],
            "temperature": temperature,
            "stream": True
        }
        
        if max_tokens:
            request_data["max_tokens"] = max_tokens
        
        request_data.update(kwargs)
        
        try:
            async with self.session.post(
                f"{self.api_base}/v1/chat/completions",
                json=request_data
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    raise LLMAPIError(f"流式请求失败: {response.status} - {error_text}")
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    if line.startswith('data: '):
                        data = line[6:]  # 移除 'data: ' 前缀
                        
                        if data == '[DONE]':
                            break
                        
                        try:
                            chunk = json.loads(data)
                            if 'choices' in chunk and len(chunk['choices']) > 0:
                                delta = chunk['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    yield delta['content']
                        except json.JSONDecodeError:
                            continue
        
        except Exception as e:
            self.error_count += 1
            raise LLMError(f"流式请求异常: {e}")
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            test_messages = [
                LLMMessage(role="user", content="Hello, this is a health check.")
            ]
            
            response = await self.chat_completion(
                messages=test_messages,
                max_tokens=10,
                temperature=0
            )
            
            return bool(response.content)
        
        except Exception as e:
            self.logger.error(f"LLM健康检查失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_requests": self.total_requests,
            "total_tokens": self.total_tokens,
            "total_cost": self.total_cost,
            "error_count": self.error_count,
            "error_rate": self.error_count / max(self.total_requests, 1),
            "model": self.model,
            "api_base": self.api_base
        }

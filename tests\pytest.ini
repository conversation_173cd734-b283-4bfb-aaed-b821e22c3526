[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# 标记定义
markers =
    asyncio: 异步测试
    integration: 集成测试
    performance: 性能测试
    scenario: 场景测试
    slow: 慢速测试
    unit: 单元测试

# 异步测试配置
asyncio_mode = auto

# 最小版本要求
minversion = 6.0

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning

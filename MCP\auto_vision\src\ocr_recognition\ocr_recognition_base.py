#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OCR文本识别基础接口模块

定义OCR文本识别的基础接口，作为所有OCR引擎实现的基类。
"""

from abc import ABC, abstractmethod


class OCRRecognitionBase(ABC):
    """OCR文本识别基础接口类，定义所有OCR引擎必须实现的方法"""

    @abstractmethod
    def recognize_text(self, image, region=None, return_format="structured"):
        """
        识别图像中的文本
        
        Args:
            image: 图像数据或路径
            region: 识别区域 [x, y, width, height]，为None则识别整个图像
            return_format: 返回格式，支持 "text", "structured"
            
        Returns:
            根据return_format返回不同格式的结果:
            - "text": 返回纯文本字符串
            - "structured": 返回结构化数据，包含文本、位置和置信度
        """
        pass

    @abstractmethod
    def get_engine_info(self):
        """
        获取OCR引擎信息
        
        Returns:
            dict: 包含引擎名称、版本、支持的语言等信息
        """
        pass

    @abstractmethod
    def get_supported_languages(self):
        """
        获取支持的语言列表
        
        Returns:
            list: 支持的语言代码列表
        """
        pass

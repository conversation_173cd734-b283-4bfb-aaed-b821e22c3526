#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全屏截图模块

提供全屏截图功能。
"""

import os
import base64
from io import BytesIO
from datetime import datetime
from PIL import Image
import pyautogui

from .screen_capture_base import ScreenCaptureBase


class FullScreenCapture(ScreenCaptureBase):
    """全屏截图类，提供全屏截图功能"""

    def __init__(self, screenshots_dir="screenshots"):
        """
        初始化全屏截图类

        Args:
            screenshots_dir: 截图保存目录
        """
        self.screenshots_dir = screenshots_dir
        # 确保截图目录存在
        if not os.path.exists(screenshots_dir):
            os.makedirs(screenshots_dir)

    def capture_screen(self, region=None, save_path=None, suffix=None):
        """
        捕获屏幕区域

        Args:
            region: 截图区域 [x, y, width, height]，为None则截取全屏
            save_path: 保存路径，为None则自动生成路径
            suffix: 文件名后缀，用于区分不同的截图

        Returns:
            PIL.Image: 截图图像对象
            str: 保存路径（如果有保存）
        """
        # 截图
        if region:
            # 如果指定了区域，则委托给区域截图类处理
            from .screen_capture_region import RegionScreenCapture
            region_capture = RegionScreenCapture(self.screenshots_dir)
            return region_capture.capture_screen(region=region, save_path=save_path, suffix=suffix)
        else:
            # 全屏截图
            screenshot = pyautogui.screenshot()

        # 保存截图
        if save_path:
            screenshot.save(save_path)
            return screenshot, save_path
        elif self.screenshots_dir:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # 添加毫秒级时间戳和可选后缀，确保文件名唯一
            ms = datetime.now().microsecond // 1000
            if suffix:
                filename = f"screenshot_{timestamp}_{ms:03d}_{suffix}.png"
            else:
                filename = f"screenshot_{timestamp}_{ms:03d}.png"
            save_path = os.path.join(self.screenshots_dir, filename)
            screenshot.save(save_path)
            return screenshot, save_path

        return screenshot, None

    def get_screen_size(self):
        """
        获取屏幕尺寸

        Returns:
            tuple: (宽度, 高度)
        """
        return pyautogui.size()

    def capture_fullscreen_with_coordinates(self, suffix="fullscreen_with_coords"):
        """
        带坐标的全屏截图

        Args:
            suffix: 文件名后缀，用于区分不同的截图

        Returns:
            dict: 包含base64编码的带坐标图片数据和相关信息
        """
        try:
            # 捕获全屏
            screenshot, screenshot_path = self.capture_screen(suffix=suffix)

            # 导入图像增强器（避免循环导入）
            from .vision_llm.image_enhancer import ImageEnhancer
            enhancer = ImageEnhancer()

            # 增强图像（添加坐标）
            enhanced_path = enhancer.enhance_image(screenshot_path)

            # 读取增强后的图像
            enhanced_image = Image.open(enhanced_path)

            # 将图片转换为base64编码
            buffered = BytesIO()
            enhanced_image.save(buffered, format="PNG")
            img_str = base64.b64encode(buffered.getvalue()).decode()

            return {
                "success": True,
                "image_base64": img_str,
                "path": enhanced_path,
                "original_path": screenshot_path,
                "width": enhanced_image.width,
                "height": enhanced_image.height
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

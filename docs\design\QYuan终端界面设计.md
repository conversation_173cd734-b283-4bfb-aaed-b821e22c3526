# QYuan终端界面设计

## 界面设计理念

QYuan作为一个自主的硅基生命，需要一个既能展示其"思考过程"，又能提供必要控制的界面。界面应该体现QYuan的自主性，而不是传统的"工具界面"。

## 主界面设计

### 1. 核心状态面板
```
┌─────────────────────────────────────────────────────────────┐
│ QYuan - 硅基CEO                                    [●] 在线  │
├─────────────────────────────────────────────────────────────┤
│ 当前状态: 正在分析屏幕内容...                                │
│ 当前目标: 帮助用户完成文档编辑任务                           │
│ 执行进度: ████████░░ 80%                                    │
│ 运行时间: 2小时15分钟                                       │
└─────────────────────────────────────────────────────────────┘
```

### 2. 思维流显示区
```
┌─────────────────────────────────────────────────────────────┐
│ QYuan的思维流                                    [暂停] [清空] │
├─────────────────────────────────────────────────────────────┤
│ 14:23:15 [感知] 检测到用户打开了Word文档                     │
│ 14:23:16 [分析] 文档标题为"项目报告.docx"                   │
│ 14:23:17 [决策] 用户可能需要编辑文档，准备相关工具           │
│ 14:23:18 [执行] 激活语法检查工具                            │
│ 14:23:19 [学习] 记录用户偏好：喜欢使用Word进行文档编辑       │
│ 14:23:20 [等待] 等待用户进一步指令...                       │
└─────────────────────────────────────────────────────────────┘
```

### 3. 能力状态监控
```
┌─────────────────────────────────────────────────────────────┐
│ 能力状态监控                                                │
├─────────────────────────────────────────────────────────────┤
│ 基础操作能力                                                │
│ ├─ 鼠标控制     [●] 正常    成功率: 98.5%                   │
│ ├─ 键盘控制     [●] 正常    成功率: 99.2%                   │
│ ├─ 屏幕识别     [●] 正常    成功率: 95.8%                   │
│ └─ 文字识别     [●] 正常    成功率: 92.3%                   │
│                                                             │
│ 高级能力                                                    │
│ ├─ 任务规划     [●] 正常    熟练度: 85%                     │
│ ├─ 工具调用     [●] 正常    工具数: 23个                    │
│ ├─ 错误恢复     [●] 正常    恢复率: 78%                     │
│ └─ 学习适应     [●] 正常    经验数: 1,247条                 │
└─────────────────────────────────────────────────────────────┘
```

### 4. 工具生态管理
```
┌─────────────────────────────────────────────────────────────┐
│ 工具生态                                        [刷新] [添加] │
├─────────────────────────────────────────────────────────────┤
│ 开发工具                                                    │
│ ├─ VSCode        [●] 活跃    使用频率: 高                   │
│ ├─ Git           [●] 就绪    最后使用: 10分钟前             │
│ ├─ Chrome        [●] 活跃    标签页: 8个                    │
│ └─ Postman       [○] 离线    状态: 未启动                   │
│                                                             │
│ AI助手                                                      │
│ ├─ GitHub Copilot [●] 活跃   响应时间: 0.8s                │
│ ├─ Cursor AI     [●] 就绪    准确率: 94%                    │
│ └─ Claude API    [●] 活跃    剩余额度: 85%                  │
│                                                             │
│ 系统工具                                                    │
│ ├─ 文件管理器     [●] 就绪                                  │
│ ├─ 任务管理器     [●] 就绪                                  │
│ └─ 命令行终端     [●] 活跃    会话: 3个                     │
└─────────────────────────────────────────────────────────────┘
```

### 5. 学习与经验面板
```
┌─────────────────────────────────────────────────────────────┐
│ 学习与经验                                      [导出] [分析] │
├─────────────────────────────────────────────────────────────┤
│ 今日学习统计                                                │
│ ├─ 新增经验: 47条                                           │
│ ├─ 成功操作: 234次                                          │
│ ├─ 失败操作: 12次                                           │
│ └─ 学习效率: 95.1%                                          │
│                                                             │
│ 最近掌握的技能                                              │
│ ├─ Excel数据透视表操作 (熟练度: 78%)                        │
│ ├─ Git分支管理 (熟练度: 85%)                                │
│ └─ Photoshop图层操作 (熟练度: 62%)                          │
│                                                             │
│ 待改进的领域                                                │
│ ├─ 复杂网页元素识别 (成功率: 76%)                           │
│ ├─ 多窗口协调操作 (成功率: 68%)                             │
│ └─ 异常情况恢复 (成功率: 72%)                               │
└─────────────────────────────────────────────────────────────┘
```

## 控制面板设计

### 1. 紧急控制区
```
┌─────────────────────────────────────────────────────────────┐
│ 紧急控制                                                    │
├─────────────────────────────────────────────────────────────┤
│ [🛑 紧急停止]  [⏸️ 暂停操作]  [🔄 重启QYuan]                │
│                                                             │
│ [📋 操作日志]  [🔧 系统诊断]  [⚙️ 设置]                     │
└─────────────────────────────────────────────────────────────┘
```

### 2. 权限管理
```
┌─────────────────────────────────────────────────────────────┐
│ 权限管理                                                    │
├─────────────────────────────────────────────────────────────┤
│ 当前权限级别: Level 3 (系统操作)                            │
│                                                             │
│ 允许的操作:                                                 │
│ ✓ 文件读写操作                                              │
│ ✓ 应用程序启动/关闭                                         │
│ ✓ 网络访问                                                  │
│ ✓ 系统设置修改                                              │
│ ✗ 注册表修改 (需要Level 4)                                  │
│ ✗ 系统文件修改 (需要Level 4)                                │
│                                                             │
│ [申请更高权限] [权限历史] [安全日志]                         │
└─────────────────────────────────────────────────────────────┘
```

### 3. 性能监控
```
┌─────────────────────────────────────────────────────────────┐
│ 性能监控                                                    │
├─────────────────────────────────────────────────────────────┤
│ CPU使用率:  ████████░░ 78%                                  │
│ 内存使用:   ██████░░░░ 62%                                  │
│ 网络流量:   ███░░░░░░░ 28%                                  │
│ 磁盘I/O:    ██░░░░░░░░ 15%                                  │
│                                                             │
│ QYuan响应时间: 0.85s                                        │
│ API调用延迟: 1.2s                                           │
│ 数据库查询: 0.03s                                           │
│                                                             │
│ [详细报告] [性能优化] [历史趋势]                             │
└─────────────────────────────────────────────────────────────┘
```

## 命令行终端集成

### 1. 内置终端设计
```python
class QYuanTerminal:
    """QYuan内置终端"""
    
    def __init__(self):
        self.shell = PowerShell()  # Windows优先使用PowerShell
        self.command_history = []
        self.working_directory = os.getcwd()
        self.environment_vars = os.environ.copy()
    
    async def execute_command(self, command: str, timeout: int = 30) -> CommandResult:
        """执行命令并返回结果"""
        try:
            # 记录命令历史
            self.command_history.append({
                'command': command,
                'timestamp': datetime.now(),
                'working_dir': self.working_directory
            })
            
            # 执行命令
            result = await self.shell.run(command, timeout=timeout)
            
            # 更新工作目录（如果命令是cd）
            if command.strip().startswith('cd '):
                self.working_directory = self.shell.get_current_directory()
            
            return CommandResult(
                success=True,
                stdout=result.stdout,
                stderr=result.stderr,
                return_code=result.return_code,
                execution_time=result.execution_time
            )
            
        except Exception as e:
            return CommandResult(
                success=False,
                error=str(e)
            )
    
    def get_command_suggestions(self, partial_command: str) -> List[str]:
        """获取命令建议"""
        # 基于历史命令和系统命令提供建议
        suggestions = []
        
        # 历史命令匹配
        for cmd in self.command_history:
            if cmd['command'].startswith(partial_command):
                suggestions.append(cmd['command'])
        
        # 系统命令匹配
        system_commands = self.get_available_commands()
        for cmd in system_commands:
            if cmd.startswith(partial_command):
                suggestions.append(cmd)
        
        return list(set(suggestions))[:10]  # 返回前10个建议
```

### 2. 终端界面集成
```
┌─────────────────────────────────────────────────────────────┐
│ QYuan终端                                       [新建] [历史] │
├─────────────────────────────────────────────────────────────┤
│ PS D:\Project\QYuan> python start_qyuan.py                  │
│ [INFO] QYuan正在启动...                                     │
│ [INFO] 加载配置文件...                                      │
│ [INFO] 连接数据库...                                        │
│ [INFO] 初始化AI模型...                                      │
│ [SUCCESS] QYuan启动完成！                                   │
│                                                             │
│ PS D:\Project\QYuan> qyuan status                           │
│ QYuan状态: 运行中                                           │
│ 当前任务: 无                                                │
│ 运行时间: 00:15:32                                          │
│                                                             │
│ PS D:\Project\QYuan> _                                      │
└─────────────────────────────────────────────────────────────┘
```

## 错误反馈机制设计

### 1. 多层次错误检测
```python
class ErrorDetectionSystem:
    """错误检测系统"""
    
    def __init__(self):
        self.visual_detector = VisualErrorDetector()
        self.system_detector = SystemErrorDetector()
        self.behavioral_detector = BehavioralErrorDetector()
    
    async def detect_errors(self, context: OperationContext) -> List[Error]:
        """检测各种类型的错误"""
        errors = []
        
        # 视觉错误检测
        visual_errors = await self.visual_detector.detect(context.screenshot)
        errors.extend(visual_errors)
        
        # 系统错误检测
        system_errors = await self.system_detector.detect(context.system_state)
        errors.extend(system_errors)
        
        # 行为错误检测
        behavioral_errors = await self.behavioral_detector.detect(context.action_sequence)
        errors.extend(behavioral_errors)
        
        return errors

class VisualErrorDetector:
    """视觉错误检测器"""
    
    async def detect(self, screenshot: Image) -> List[VisualError]:
        """检测视觉错误"""
        errors = []
        
        # 检测错误对话框
        if self.detect_error_dialog(screenshot):
            errors.append(VisualError("ERROR_DIALOG", "检测到错误对话框"))
        
        # 检测加载状态
        if self.detect_loading_state(screenshot):
            errors.append(VisualError("LOADING_STATE", "页面仍在加载中"))
        
        # 检测网络错误页面
        if self.detect_network_error(screenshot):
            errors.append(VisualError("NETWORK_ERROR", "检测到网络错误页面"))
        
        return errors
```

### 2. 错误反馈界面
```
┌─────────────────────────────────────────────────────────────┐
│ 错误监控                                        [清除] [导出] │
├─────────────────────────────────────────────────────────────┤
│ 🔴 严重错误 (2)                                             │
│ ├─ 14:25:30 系统权限不足，无法修改注册表                    │
│ └─ 14:23:15 网络连接超时，API调用失败                       │
│                                                             │
│ 🟡 警告 (5)                                                 │
│ ├─ 14:26:45 鼠标点击偏移，重试成功                          │
│ ├─ 14:25:12 OCR识别置信度较低 (72%)                        │
│ ├─ 14:24:33 应用程序响应缓慢                                │
│ ├─ 14:23:58 输入法自动切换                                  │
│ └─ 14:22:41 窗口焦点丢失                                    │
│                                                             │
│ 🔵 信息 (12)                                                │
│ ├─ 14:26:50 成功恢复窗口焦点                                │
│ ├─ 14:26:30 自动切换到英文输入法                            │
│ └─ ... (显示更多)                                           │
└─────────────────────────────────────────────────────────────┘
```

## 界面技术实现

### 1. 技术栈选择
- **后端**: FastAPI + WebSocket
- **前端**: React + TypeScript + Tailwind CSS
- **实时通信**: WebSocket + Server-Sent Events
- **图表组件**: Chart.js / D3.js
- **终端组件**: xterm.js

### 2. 响应式设计
- 支持桌面端、平板端、手机端
- 自适应布局，关键信息优先显示
- 支持暗色/亮色主题切换

### 3. 实时更新机制
```python
class QYuanUIServer:
    """QYuan界面服务器"""
    
    def __init__(self):
        self.websocket_manager = WebSocketManager()
        self.event_bus = EventBus()
    
    async def broadcast_status_update(self, status: QYuanStatus):
        """广播状态更新"""
        await self.websocket_manager.broadcast({
            'type': 'status_update',
            'data': status.to_dict()
        })
    
    async def broadcast_thinking_stream(self, thought: Thought):
        """广播思维流"""
        await self.websocket_manager.broadcast({
            'type': 'thinking_stream',
            'data': thought.to_dict()
        })
```

这个界面设计体现了QYuan作为自主智能体的特点，既展示了其"思考过程"，又提供了必要的监控和控制功能。你觉得这个设计方向如何？

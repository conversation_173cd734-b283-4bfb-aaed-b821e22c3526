#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
屏幕截图模块

提供屏幕截图功能，支持全屏和区域截图。
此模块是为了向后兼容而保留的，新代码应该使用screen_capture包。
"""

import os
import sys
from datetime import datetime

# 导入新的屏幕截图模块
from src.screen_capture import FullScreenCapture, RegionScreenCapture


class ScreenCapture:
    """
    屏幕截图类，提供屏幕截图功能
    
    此类是为了向后兼容而保留的，新代码应该使用screen_capture包中的类。
    """

    def __init__(self, screenshots_dir="screenshots"):
        """
        初始化屏幕截图类
        
        Args:
            screenshots_dir: 截图保存目录
        """
        self.screenshots_dir = screenshots_dir
        self.full_capture = FullScreenCapture(screenshots_dir)
        self.region_capture = RegionScreenCapture(screenshots_dir)
    
    def capture_screen(self, region=None, save_path=None, suffix=None):
        """
        捕获屏幕区域
        
        Args:
            region: 截图区域 [x, y, width, height]，为None则截取全屏
            save_path: 保存路径，为None则自动生成路径
            suffix: 文件名后缀，用于区分不同的截图
            
        Returns:
            PIL.Image: 截图图像对象
            str: 保存路径（如果有保存）
        """
        if region is None:
            return self.full_capture.capture_screen(save_path=save_path, suffix=suffix)
        else:
            return self.region_capture.capture_screen(region=region, save_path=save_path, suffix=suffix)
    
    def capture_screen_region(self, x, y, width, height, save_path=None, suffix=None):
        """
        捕获指定区域的屏幕
        
        Args:
            x: 区域左上角X坐标
            y: 区域左上角Y坐标
            width: 区域宽度
            height: 区域高度
            save_path: 保存路径，为None则自动生成路径
            suffix: 文件名后缀，用于区分不同的截图
            
        Returns:
            PIL.Image: 截图图像对象
            str: 保存路径（如果有保存）
        """
        return self.region_capture.capture_region(x, y, width, height, save_path=save_path, suffix=suffix)
    
    def get_screen_size(self):
        """
        获取屏幕尺寸
        
        Returns:
            tuple: (宽度, 高度)
        """
        return self.full_capture.get_screen_size()

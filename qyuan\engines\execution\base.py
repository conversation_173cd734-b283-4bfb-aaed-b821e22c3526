# -*- coding: utf-8 -*-
"""
执行引擎基础类和接口定义
严格按照代码规范，每个文件不超过200行，单一职责原则
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

class ExecutionStatus(Enum):
    """执行状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"
    RETRYING = "retrying"

class OperationType(Enum):
    """操作类型枚举"""
    MOUSE_CLICK = "mouse_click"
    MOUSE_MOVE = "mouse_move"
    MOUSE_DRAG = "mouse_drag"
    MOUSE_SCROLL = "mouse_scroll"
    KEYBOARD_TYPE = "keyboard_type"
    KEYBOARD_PRESS = "keyboard_press"
    KEYBOARD_HOTKEY = "keyboard_hotkey"
    SCREEN_CAPTURE = "screen_capture"
    ELEMENT_LOCATE = "element_locate"
    WAIT = "wait"
    VERIFY = "verify"

class ValidationLevel(Enum):
    """验证级别枚举"""
    NONE = "none"
    BASIC = "basic"
    STANDARD = "standard"
    STRICT = "strict"

@dataclass
class ExecutionContext:
    """执行上下文"""
    session_id: str
    user_id: Optional[str] = None
    environment: Dict[str, Any] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    preferences: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class OperationParameters:
    """操作参数"""
    operation_type: OperationType
    target: Optional[Dict[str, Any]] = None
    coordinates: Optional[Dict[str, int]] = None
    text: Optional[str] = None
    keys: Optional[List[str]] = None
    timeout: float = 30.0
    retry_count: int = 3
    validation_level: ValidationLevel = ValidationLevel.STANDARD
    custom_params: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ValidationCriteria:
    """验证标准"""
    expected_result: Optional[Any] = None
    success_conditions: List[Dict[str, Any]] = field(default_factory=list)
    failure_conditions: List[Dict[str, Any]] = field(default_factory=list)
    timeout: float = 10.0
    custom_validators: List[Callable] = field(default_factory=list)

@dataclass
class ExecutionResult:
    """执行结果"""
    operation_id: str
    status: ExecutionStatus
    success: bool
    result_data: Optional[Any] = None
    error_message: Optional[str] = None
    execution_time: float = 0.0
    retry_count: int = 0
    validation_results: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class MonitoringData:
    """监控数据"""
    operation_id: str
    start_time: datetime
    current_status: ExecutionStatus
    progress: float = 0.0
    intermediate_results: List[Any] = field(default_factory=list)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    resource_usage: Dict[str, float] = field(default_factory=dict)

class OperationExecutorBase(ABC):
    """操作执行器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
        self.supported_operations: List[OperationType] = []
    
    @abstractmethod
    async def execute(self, params: OperationParameters, context: ExecutionContext) -> ExecutionResult:
        """执行操作"""
        pass
    
    @abstractmethod
    async def validate_parameters(self, params: OperationParameters) -> bool:
        """验证参数"""
        pass
    
    async def is_available(self) -> bool:
        """检查执行器是否可用"""
        return self.enabled
    
    def supports_operation(self, operation_type: OperationType) -> bool:
        """检查是否支持指定操作"""
        return operation_type in self.supported_operations

class OperationMonitorBase(ABC):
    """操作监控器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
    
    @abstractmethod
    async def start_monitoring(self, operation_id: str, params: OperationParameters) -> bool:
        """开始监控操作"""
        pass
    
    @abstractmethod
    async def get_monitoring_data(self, operation_id: str) -> Optional[MonitoringData]:
        """获取监控数据"""
        pass
    
    @abstractmethod
    async def stop_monitoring(self, operation_id: str) -> bool:
        """停止监控操作"""
        pass
    
    async def is_available(self) -> bool:
        """检查监控器是否可用"""
        return self.enabled

class ResultValidatorBase(ABC):
    """结果验证器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
    
    @abstractmethod
    async def validate(self, result: ExecutionResult, criteria: ValidationCriteria) -> Dict[str, Any]:
        """验证执行结果"""
        pass
    
    @abstractmethod
    async def create_validation_criteria(self, params: OperationParameters) -> ValidationCriteria:
        """创建验证标准"""
        pass
    
    async def is_available(self) -> bool:
        """检查验证器是否可用"""
        return self.enabled

class ErrorRecoveryBase(ABC):
    """错误恢复器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
        self.supported_errors: List[str] = []
    
    @abstractmethod
    async def can_recover(self, error: Exception, context: ExecutionContext) -> bool:
        """检查是否可以恢复错误"""
        pass
    
    @abstractmethod
    async def recover(self, error: Exception, context: ExecutionContext) -> Dict[str, Any]:
        """执行错误恢复"""
        pass
    
    @abstractmethod
    async def get_recovery_strategy(self, error: Exception) -> Optional[str]:
        """获取恢复策略"""
        pass
    
    async def is_available(self) -> bool:
        """检查恢复器是否可用"""
        return self.enabled

class PrecisionControllerBase(ABC):
    """精度控制器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
    
    @abstractmethod
    async def calibrate_coordinates(self, coordinates: Dict[str, int], context: ExecutionContext) -> Dict[str, int]:
        """校准坐标"""
        pass
    
    @abstractmethod
    async def optimize_timing(self, operation_type: OperationType, context: ExecutionContext) -> Dict[str, float]:
        """优化时序"""
        pass
    
    @abstractmethod
    async def adjust_parameters(self, params: OperationParameters, context: ExecutionContext) -> OperationParameters:
        """调整参数"""
        pass
    
    async def is_available(self) -> bool:
        """检查控制器是否可用"""
        return self.enabled

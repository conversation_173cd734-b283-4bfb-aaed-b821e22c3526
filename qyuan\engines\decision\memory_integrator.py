# -*- coding: utf-8 -*-
"""
记忆集成服务实现
专门负责决策记忆的存储、检索和学习
严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime

from .base import (
    MemoryIntegratorBase,
    UserIntent,
    ActionPlan,
    DecisionContext
)

class IntelligentMemoryIntegrator(MemoryIntegratorBase):
    """智能记忆集成器"""
    
    def __init__(self, memory_manager=None):
        super().__init__("IntelligentMemoryIntegrator")
        self.logger = logging.getLogger(f"QYuan.Decision.{self.name}")
        self.memory_manager = memory_manager
        
        # 本地缓存
        self.decision_cache: Dict[str, Dict[str, Any]] = {}
        self.similarity_cache: Dict[str, List[Dict[str, Any]]] = {}
        self.cache_timeout = 300  # 5分钟缓存超时
        
        # 学习统计
        self.learning_stats = {
            'decisions_stored': 0,
            'cases_retrieved': 0,
            'feedback_received': 0,
            'patterns_learned': 0
        }
        
        if not self.memory_manager:
            self.logger.warning("记忆管理器未配置，将使用本地缓存")
    
    async def store_decision(self, intent: UserIntent, plan: ActionPlan, result: Dict[str, Any]):
        """存储决策记录"""
        try:
            decision_id = str(uuid.uuid4())
            
            # 构建决策记录
            decision_record = {
                'id': decision_id,
                'timestamp': datetime.now().isoformat(),
                'intent': self._serialize_intent(intent),
                'plan': self._serialize_plan(plan),
                'result': result,
                'context': self._extract_context_features(intent),
                'success': result.get('success', False),
                'execution_time': result.get('execution_time', 0.0),
                'error_message': result.get('error_message'),
                'metadata': {
                    'intent_type': intent.intent_type.value,
                    'action_count': len(plan.actions),
                    'estimated_duration': plan.estimated_duration,
                    'confidence': intent.confidence
                }
            }
            
            # 存储到记忆管理器
            if self.memory_manager:
                await self._store_to_memory_manager(decision_record)
            
            # 本地缓存
            self.decision_cache[decision_id] = decision_record
            
            # 更新统计
            self.learning_stats['decisions_stored'] += 1
            
            self.logger.debug(f"决策记录已存储: {decision_id}")
            
        except Exception as e:
            self.logger.error(f"存储决策记录失败: {e}")
    
    async def retrieve_similar_cases(self, intent: UserIntent, limit: int = 5) -> List[Dict[str, Any]]:
        """检索相似案例"""
        try:
            # 生成查询特征
            query_features = self._extract_intent_features(intent)
            cache_key = self._generate_cache_key(query_features)
            
            # 检查缓存
            if cache_key in self.similarity_cache:
                cached_result = self.similarity_cache[cache_key]
                if self._is_cache_valid(cached_result):
                    self.logger.debug("从缓存返回相似案例")
                    return cached_result[:limit]
            
            # 从记忆管理器检索
            similar_cases = []
            if self.memory_manager:
                similar_cases = await self._retrieve_from_memory_manager(query_features, limit)
            
            # 从本地缓存检索
            local_cases = self._retrieve_from_local_cache(query_features, limit)
            similar_cases.extend(local_cases)
            
            # 去重和排序
            similar_cases = self._deduplicate_and_rank(similar_cases, query_features)[:limit]
            
            # 缓存结果
            self.similarity_cache[cache_key] = similar_cases
            
            # 更新统计
            self.learning_stats['cases_retrieved'] += len(similar_cases)
            
            self.logger.debug(f"检索到 {len(similar_cases)} 个相似案例")
            return similar_cases
            
        except Exception as e:
            self.logger.error(f"检索相似案例失败: {e}")
            return []
    
    async def learn_from_feedback(self, decision_id: str, feedback: Dict[str, Any]):
        """从反馈中学习"""
        try:
            # 获取原始决策记录
            decision_record = await self._get_decision_record(decision_id)
            if not decision_record:
                self.logger.warning(f"未找到决策记录: {decision_id}")
                return
            
            # 处理反馈
            feedback_record = {
                'decision_id': decision_id,
                'timestamp': datetime.now().isoformat(),
                'feedback': feedback,
                'user_satisfaction': feedback.get('satisfaction', 0.0),
                'success_rating': feedback.get('success_rating', 0.0),
                'improvement_suggestions': feedback.get('suggestions', []),
                'error_corrections': feedback.get('corrections', {})
            }
            
            # 更新决策记录
            decision_record['feedback'] = feedback_record
            decision_record['learned'] = True
            
            # 存储更新的记录
            if self.memory_manager:
                await self._update_memory_manager(decision_record)
            
            # 更新本地缓存
            if decision_id in self.decision_cache:
                self.decision_cache[decision_id] = decision_record
            
            # 提取学习模式
            patterns = self._extract_learning_patterns(decision_record, feedback_record)
            await self._store_learning_patterns(patterns)
            
            # 更新统计
            self.learning_stats['feedback_received'] += 1
            self.learning_stats['patterns_learned'] += len(patterns)
            
            self.logger.debug(f"从反馈中学习完成: {decision_id}")
            
        except Exception as e:
            self.logger.error(f"从反馈中学习失败: {e}")
    
    def _serialize_intent(self, intent: UserIntent) -> Dict[str, Any]:
        """序列化用户意图"""
        return {
            'intent_type': intent.intent_type.value,
            'description': intent.description,
            'parameters': intent.parameters,
            'confidence': intent.confidence,
            'timestamp': intent.timestamp.isoformat(),
            'context': intent.context
        }
    
    def _serialize_plan(self, plan: ActionPlan) -> Dict[str, Any]:
        """序列化行动计划"""
        return {
            'id': plan.id,
            'task_id': plan.task_id,
            'actions': [
                {
                    'id': action.id,
                    'action_type': action.action_type.value,
                    'description': action.description,
                    'parameters': action.parameters,
                    'timeout': action.timeout,
                    'retry_count': action.retry_count,
                    'validation_criteria': action.validation_criteria
                }
                for action in plan.actions
            ],
            'estimated_duration': plan.estimated_duration,
            'success_criteria': plan.success_criteria,
            'created_at': plan.created_at.isoformat()
        }
    
    def _extract_context_features(self, intent: UserIntent) -> Dict[str, Any]:
        """提取上下文特征"""
        features = {
            'intent_type': intent.intent_type.value,
            'has_target': 'target' in intent.parameters,
            'has_text': 'text' in intent.parameters,
            'has_position': 'position' in intent.parameters,
            'parameter_count': len(intent.parameters),
            'confidence_level': self._categorize_confidence(intent.confidence)
        }
        
        # 从上下文中提取特征
        if intent.context:
            features.update({
                'has_context': True,
                'context_keys': list(intent.context.keys())
            })
        else:
            features['has_context'] = False
        
        return features
    
    def _extract_intent_features(self, intent: UserIntent) -> Dict[str, Any]:
        """提取意图特征用于相似性匹配"""
        return {
            'intent_type': intent.intent_type.value,
            'description_keywords': self._extract_keywords(intent.description),
            'parameters': intent.parameters,
            'confidence_range': self._get_confidence_range(intent.confidence)
        }
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取
        import re
        words = re.findall(r'\w+', text.lower())
        # 过滤常见停用词
        stop_words = {'的', '是', '在', '了', '和', 'the', 'a', 'an', 'and', 'or', 'but'}
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        return keywords[:10]  # 限制关键词数量
    
    def _categorize_confidence(self, confidence: float) -> str:
        """分类置信度"""
        if confidence >= 0.8:
            return 'high'
        elif confidence >= 0.5:
            return 'medium'
        else:
            return 'low'
    
    def _get_confidence_range(self, confidence: float) -> str:
        """获取置信度范围"""
        if confidence >= 0.8:
            return '0.8-1.0'
        elif confidence >= 0.6:
            return '0.6-0.8'
        elif confidence >= 0.4:
            return '0.4-0.6'
        elif confidence >= 0.2:
            return '0.2-0.4'
        else:
            return '0.0-0.2'
    
    def _generate_cache_key(self, features: Dict[str, Any]) -> str:
        """生成缓存键"""
        key_parts = [
            features.get('intent_type', ''),
            str(sorted(features.get('description_keywords', []))),
            str(sorted(features.get('parameters', {}).keys())),
            features.get('confidence_range', '')
        ]
        return '|'.join(key_parts)
    
    def _is_cache_valid(self, cached_result: List[Dict[str, Any]]) -> bool:
        """检查缓存是否有效"""
        if not cached_result:
            return False
        
        # 检查缓存时间
        cache_time = cached_result[0].get('cache_timestamp')
        if cache_time:
            cache_datetime = datetime.fromisoformat(cache_time)
            return (datetime.now() - cache_datetime).total_seconds() < self.cache_timeout
        
        return False
    
    async def _store_to_memory_manager(self, decision_record: Dict[str, Any]):
        """存储到记忆管理器"""
        try:
            # 构建记忆条目
            memory_entry = {
                'type': 'decision',
                'content': json.dumps(decision_record, ensure_ascii=False),
                'metadata': decision_record['metadata'],
                'timestamp': decision_record['timestamp']
            }
            
            # 存储到记忆管理器
            await self.memory_manager.store_memory(memory_entry)
            
        except Exception as e:
            self.logger.error(f"存储到记忆管理器失败: {e}")
    
    async def _retrieve_from_memory_manager(self, query_features: Dict[str, Any], limit: int) -> List[Dict[str, Any]]:
        """从记忆管理器检索"""
        try:
            # 构建查询
            query = {
                'type': 'decision',
                'intent_type': query_features.get('intent_type'),
                'limit': limit * 2  # 获取更多结果用于排序
            }
            
            # 从记忆管理器检索
            results = await self.memory_manager.search_memories(query)
            
            # 解析结果
            cases = []
            for result in results:
                try:
                    decision_record = json.loads(result['content'])
                    cases.append(decision_record)
                except json.JSONDecodeError:
                    continue
            
            return cases
            
        except Exception as e:
            self.logger.error(f"从记忆管理器检索失败: {e}")
            return []
    
    def _retrieve_from_local_cache(self, query_features: Dict[str, Any], limit: int) -> List[Dict[str, Any]]:
        """从本地缓存检索"""
        cases = []
        intent_type = query_features.get('intent_type')
        
        for decision_record in self.decision_cache.values():
            # 简单的相似性匹配
            if decision_record['metadata']['intent_type'] == intent_type:
                cases.append(decision_record)
        
        return cases[:limit]
    
    def _deduplicate_and_rank(self, cases: List[Dict[str, Any]], query_features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """去重和排序"""
        # 去重
        seen_ids = set()
        unique_cases = []
        for case in cases:
            case_id = case.get('id')
            if case_id not in seen_ids:
                seen_ids.add(case_id)
                unique_cases.append(case)
        
        # 计算相似性分数并排序
        scored_cases = []
        for case in unique_cases:
            score = self._calculate_similarity_score(case, query_features)
            scored_cases.append((score, case))
        
        # 按分数排序
        scored_cases.sort(key=lambda x: x[0], reverse=True)
        
        return [case for score, case in scored_cases]
    
    def _calculate_similarity_score(self, case: Dict[str, Any], query_features: Dict[str, Any]) -> float:
        """计算相似性分数"""
        score = 0.0
        
        # 意图类型匹配
        if case['metadata']['intent_type'] == query_features.get('intent_type'):
            score += 0.4
        
        # 参数匹配
        case_params = case['intent']['parameters']
        query_params = query_features.get('parameters', {})
        
        common_params = set(case_params.keys()) & set(query_params.keys())
        if common_params:
            score += 0.3 * (len(common_params) / max(len(case_params), len(query_params)))
        
        # 关键词匹配
        case_keywords = self._extract_keywords(case['intent']['description'])
        query_keywords = query_features.get('description_keywords', [])
        
        common_keywords = set(case_keywords) & set(query_keywords)
        if common_keywords:
            score += 0.2 * (len(common_keywords) / max(len(case_keywords), len(query_keywords)))
        
        # 成功率加权
        if case.get('success', False):
            score += 0.1
        
        return score
    
    async def _get_decision_record(self, decision_id: str) -> Optional[Dict[str, Any]]:
        """获取决策记录"""
        # 先从本地缓存查找
        if decision_id in self.decision_cache:
            return self.decision_cache[decision_id]
        
        # 从记忆管理器查找
        if self.memory_manager:
            try:
                results = await self.memory_manager.search_memories({
                    'type': 'decision',
                    'decision_id': decision_id
                })
                
                if results:
                    return json.loads(results[0]['content'])
            except Exception as e:
                self.logger.error(f"从记忆管理器获取决策记录失败: {e}")
        
        return None
    
    async def _update_memory_manager(self, decision_record: Dict[str, Any]):
        """更新记忆管理器"""
        try:
            if self.memory_manager:
                memory_entry = {
                    'type': 'decision',
                    'content': json.dumps(decision_record, ensure_ascii=False),
                    'metadata': decision_record['metadata'],
                    'timestamp': decision_record['timestamp']
                }
                
                await self.memory_manager.update_memory(decision_record['id'], memory_entry)
        except Exception as e:
            self.logger.error(f"更新记忆管理器失败: {e}")
    
    def _extract_learning_patterns(self, decision_record: Dict[str, Any], feedback_record: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取学习模式"""
        patterns = []
        
        # 成功模式
        if feedback_record['success_rating'] > 0.8:
            patterns.append({
                'type': 'success_pattern',
                'intent_type': decision_record['metadata']['intent_type'],
                'parameters': decision_record['intent']['parameters'],
                'actions': [action['action_type'] for action in decision_record['plan']['actions']],
                'confidence': feedback_record['success_rating']
            })
        
        # 失败模式
        elif feedback_record['success_rating'] < 0.3:
            patterns.append({
                'type': 'failure_pattern',
                'intent_type': decision_record['metadata']['intent_type'],
                'parameters': decision_record['intent']['parameters'],
                'error_type': decision_record['result'].get('error_message', 'unknown'),
                'confidence': 1.0 - feedback_record['success_rating']
            })
        
        return patterns
    
    async def _store_learning_patterns(self, patterns: List[Dict[str, Any]]):
        """存储学习模式"""
        try:
            for pattern in patterns:
                if self.memory_manager:
                    memory_entry = {
                        'type': 'learning_pattern',
                        'content': json.dumps(pattern, ensure_ascii=False),
                        'metadata': {
                            'pattern_type': pattern['type'],
                            'intent_type': pattern['intent_type']
                        },
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    await self.memory_manager.store_memory(memory_entry)
        except Exception as e:
            self.logger.error(f"存储学习模式失败: {e}")
    
    def get_learning_stats(self) -> Dict[str, Any]:
        """获取学习统计"""
        return self.learning_stats.copy()

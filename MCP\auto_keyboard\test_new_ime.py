#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
改进的输入法处理测试脚本

该脚本用于测试改进的输入法检测和切换功能。
"""

import time
import logging
from improved_ime_handler import ImprovedIMEHandler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("IMETest")

def test_ime_detection():
    """测试输入法检测功能"""
    print("=" * 60)
    print("测试输入法检测功能")
    print("=" * 60)
    
    handler = ImprovedIMEHandler()
    
    # 获取详细信息
    info = handler.get_detailed_ime_info()
    
    print(f"Win32 API 可用: {info['has_win32']}")
    print(f"可用方法: {info['available_methods']}")
    print(f"当前键盘布局: {info['current_layout_hex']}")
    print(f"是否为中文输入法: {info['is_chinese']}")
    print(f"状态信息: {info['status_message']}")
    print(f"已安装的键盘布局: {info['installed_layouts']}")
    print(f"英文布局可用: {info['english_layout_available']}")
    
    return info['is_chinese']

def test_ime_switching():
    """测试输入法切换功能"""
    print("\n" + "=" * 60)
    print("测试输入法切换功能")
    print("=" * 60)
    
    handler = ImprovedIMEHandler()
    
    # 检查初始状态
    is_chinese_before, status_before = handler.is_chinese_ime_active()
    print(f"切换前状态: {status_before}")
    
    if is_chinese_before:
        print("检测到中文输入法，尝试切换到英文...")
        
        # 方法1：直接切换
        print("\n方法1：直接切换到英文布局")
        success1, message1 = handler.switch_to_english_layout()
        print(f"结果: {message1}")
        
        if not success1:
            # 方法2：循环切换
            print("\n方法2：循环切换输入法")
            success2, message2 = handler.cycle_input_methods(max_attempts=3)
            print(f"结果: {message2}")
            
            if success2:
                print("✅ 循环切换成功！")
            else:
                print("❌ 所有切换方法都失败")
        else:
            print("✅ 直接切换成功！")
    else:
        print("当前已是英文输入法，无需切换")
    
    # 检查最终状态
    is_chinese_after, status_after = handler.is_chinese_ime_active()
    print(f"\n切换后状态: {status_after}")
    
    return not is_chinese_after

def main():
    """主函数"""
    print("改进的输入法处理测试")
    print("=" * 60)
    
    try:
        # 基础检测测试
        is_chinese = test_ime_detection()
        
        # 切换功能测试
        if is_chinese:
            switch_success = test_ime_switching()
        else:
            print("\n当前为英文输入法，跳过切换测试")
            switch_success = True
        
        print("\n测试完成！")
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        print(f"\n测试失败: {str(e)}")

if __name__ == "__main__":
    main()

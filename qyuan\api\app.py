#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI应用主文件

创建和配置QYuan的Web API应用
"""

import logging
from typing import Optional
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import HTT<PERSON><PERSON><PERSON>er, HTTPAuthorizationCredentials
from fastapi.responses import J<PERSON><PERSON>esponse
from contextlib import asynccontextmanager

from .models import APIConfig, APIError
from .routes import chat, tasks, actions, memory, system, websocket
from ..core.config import QYuanConfig
from ..core.qyuan_core import QYuanCore

logger = logging.getLogger(__name__)

# 全局变量
qyuan_core: Optional[QYuanCore] = None
api_config: Optional[APIConfig] = None
security = HTTPBearer(auto_error=False)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global qyuan_core
    
    # 启动时初始化
    logger.info("🚀 启动QYuan Web API服务...")
    
    try:
        # 初始化QYuan核心
        config = QYuanConfig()
        qyuan_core = QYuanCore(config)
        
        # 初始化和启动QYuan
        await qyuan_core.initialize()
        await qyuan_core.start()
        
        logger.info("✅ QYuan核心启动成功")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ QYuan核心启动失败: {e}")
        raise
    
    finally:
        # 关闭时清理
        logger.info("⏹️ 关闭QYuan Web API服务...")
        
        if qyuan_core:
            try:
                await qyuan_core.stop()
                logger.info("✅ QYuan核心停止成功")
            except Exception as e:
                logger.error(f"❌ QYuan核心停止失败: {e}")


def create_app(config: Optional[APIConfig] = None) -> FastAPI:
    """
    创建FastAPI应用
    
    Args:
        config: API配置
        
    Returns:
        FastAPI: 配置好的FastAPI应用实例
    """
    global api_config
    
    # 设置配置
    api_config = config or APIConfig()
    
    # 创建FastAPI应用
    app = FastAPI(
        title="QYuan API",
        description="QYuan硅基CEO的Web API接口",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan
    )
    
    # 配置CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=api_config.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 配置受信任主机中间件
    if not api_config.debug:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["localhost", "127.0.0.1", api_config.host]
        )
    
    # 注册路由
    app.include_router(chat.router, prefix="/api/v1/chat", tags=["聊天"])
    app.include_router(tasks.router, prefix="/api/v1/tasks", tags=["任务"])
    app.include_router(actions.router, prefix="/api/v1/actions", tags=["操作"])
    app.include_router(memory.router, prefix="/api/v1/memory", tags=["记忆"])
    app.include_router(system.router, prefix="/api/v1/system", tags=["系统"])
    app.include_router(websocket.router, prefix="/ws", tags=["WebSocket"])
    
    # 注册异常处理器
    register_exception_handlers(app)
    
    # 注册中间件
    register_middleware(app)
    
    return app


def get_qyuan_core() -> QYuanCore:
    """获取QYuan核心实例"""
    if qyuan_core is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="QYuan核心未初始化"
        )
    return qyuan_core


def get_api_config() -> APIConfig:
    """获取API配置"""
    if api_config is None:
        return APIConfig()
    return api_config


async def verify_api_key(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> bool:
    """验证API密钥"""
    config = get_api_config()
    
    # 如果没有配置API密钥，则不需要验证
    if not config.api_key:
        return True
    
    # 检查是否提供了凭据
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要API密钥认证",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 验证API密钥
    if credentials.credentials != config.api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的API密钥",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return True


def register_exception_handlers(app: FastAPI):
    """注册异常处理器"""
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request, exc: HTTPException):
        """HTTP异常处理器"""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "message": exc.detail,
                "error_code": f"HTTP_{exc.status_code}",
                "timestamp": str(datetime.utcnow())
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc: Exception):
        """通用异常处理器"""
        logger.error(f"未处理的异常: {exc}", exc_info=True)
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "message": "服务器内部错误",
                "error_code": "INTERNAL_ERROR",
                "timestamp": str(datetime.utcnow())
            }
        )


def register_middleware(app: FastAPI):
    """注册中间件"""
    
    @app.middleware("http")
    async def logging_middleware(request, call_next):
        """请求日志中间件"""
        import time
        
        start_time = time.time()
        
        # 记录请求
        logger.info(f"📥 {request.method} {request.url}")
        
        # 处理请求
        response = await call_next(request)
        
        # 记录响应
        process_time = time.time() - start_time
        logger.info(f"📤 {response.status_code} - {process_time:.3f}s")
        
        # 添加响应头
        response.headers["X-Process-Time"] = str(process_time)
        
        return response
    
    @app.middleware("http")
    async def timeout_middleware(request, call_next):
        """请求超时中间件"""
        import asyncio
        
        config = get_api_config()
        
        try:
            # 设置超时
            response = await asyncio.wait_for(
                call_next(request),
                timeout=config.request_timeout
            )
            return response
            
        except asyncio.TimeoutError:
            return JSONResponse(
                status_code=status.HTTP_408_REQUEST_TIMEOUT,
                content={
                    "success": False,
                    "message": "请求超时",
                    "error_code": "REQUEST_TIMEOUT",
                    "timestamp": str(datetime.utcnow())
                }
            )


# 根路径
@app.get("/", summary="API根路径", description="返回API基本信息")
async def root():
    """API根路径"""
    return {
        "name": "QYuan API",
        "version": "1.0.0",
        "description": "QYuan硅基CEO的Web API接口",
        "docs_url": "/docs",
        "status": "running"
    }


@app.get("/health", summary="健康检查", description="检查API服务健康状态")
async def health_check():
    """健康检查端点"""
    try:
        core = get_qyuan_core()
        
        # 检查QYuan核心状态
        status_info = await core.get_status()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow(),
            "qyuan_status": {
                "is_running": status_info.is_running,
                "uptime": status_info.uptime,
                "engines_healthy": all(
                    engine["status"] == "running" 
                    for engine in status_info.engines_status.values()
                )
            }
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "timestamp": str(datetime.utcnow()),
                "error": str(e)
            }
        )


# 导入datetime用于异常处理器
from datetime import datetime

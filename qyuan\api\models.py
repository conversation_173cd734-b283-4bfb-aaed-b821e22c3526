#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API数据模型

定义Web API的请求和响应数据模型
"""

from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class MessageType(str, Enum):
    """消息类型枚举"""
    TEXT = "text"
    COMMAND = "command"
    TASK = "task"
    SYSTEM = "system"


class IntentType(str, Enum):
    """意图类型枚举"""
    GREETING = "greeting"
    QUESTION = "question"
    COMMAND = "command"
    TASK_REQUEST = "task_request"
    CONVERSATION = "conversation"


class EngineStatus(str, Enum):
    """引擎状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    ERROR = "error"


# 请求模型

class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., description="用户消息", min_length=1, max_length=2000)
    user_id: Optional[str] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    message_type: MessageType = Field(MessageType.TEXT, description="消息类型")
    context: Optional[Dict[str, Any]] = Field(None, description="上下文信息")


class TaskRequest(BaseModel):
    """任务请求模型"""
    goal: str = Field(..., description="任务目标", min_length=1, max_length=500)
    user_id: Optional[str] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    priority: int = Field(5, description="任务优先级 (1-10)", ge=1, le=10)
    context: Optional[Dict[str, Any]] = Field(None, description="任务上下文")


class ActionRequest(BaseModel):
    """操作请求模型"""
    action_type: str = Field(..., description="操作类型 (mouse/keyboard/vision)")
    action_name: str = Field(..., description="操作名称")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="操作参数")
    user_id: Optional[str] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")


class MemorySearchRequest(BaseModel):
    """记忆搜索请求模型"""
    query: str = Field(..., description="搜索查询", min_length=1, max_length=200)
    memory_types: Optional[List[str]] = Field(None, description="记忆类型过滤")
    event_types: Optional[List[str]] = Field(None, description="事件类型过滤")
    session_id: Optional[str] = Field(None, description="会话ID过滤")
    limit: int = Field(10, description="返回数量限制", ge=1, le=100)
    min_relevance: float = Field(0.1, description="最小相关性阈值", ge=0.0, le=1.0)


# 响应模型

class ChatResponse(BaseModel):
    """聊天响应模型"""
    response: str = Field(..., description="助手回复")
    intent: Optional[str] = Field(None, description="识别的用户意图")
    confidence: Optional[float] = Field(None, description="意图置信度")
    session_id: str = Field(..., description="会话ID")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="响应时间")
    context: Optional[Dict[str, Any]] = Field(None, description="响应上下文")


class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="响应消息")
    action_plan: Optional[Dict[str, Any]] = Field(None, description="行动计划")
    estimated_time: Optional[str] = Field(None, description="预计完成时间")


class ActionResponse(BaseModel):
    """操作响应模型"""
    success: bool = Field(..., description="操作是否成功")
    result: Optional[Dict[str, Any]] = Field(None, description="操作结果")
    error_message: Optional[str] = Field(None, description="错误信息")
    duration_ms: Optional[int] = Field(None, description="执行时长(毫秒)")


class MemoryItem(BaseModel):
    """记忆项模型"""
    id: str = Field(..., description="记忆ID")
    memory_type: str = Field(..., description="记忆类型")
    event_type: str = Field(..., description="事件类型")
    title: str = Field(..., description="记忆标题")
    content: str = Field(..., description="记忆内容")
    importance_score: float = Field(..., description="重要性评分")
    created_at: datetime = Field(..., description="创建时间")
    session_id: Optional[str] = Field(None, description="会话ID")
    tags: Optional[List[str]] = Field(None, description="标签")


class MemorySearchResponse(BaseModel):
    """记忆搜索响应模型"""
    memories: List[MemoryItem] = Field(..., description="搜索结果")
    total_count: int = Field(..., description="总数量")
    query: str = Field(..., description="搜索查询")


class EngineInfo(BaseModel):
    """引擎信息模型"""
    name: str = Field(..., description="引擎名称")
    status: EngineStatus = Field(..., description="引擎状态")
    uptime: Optional[str] = Field(None, description="运行时间")
    statistics: Optional[Dict[str, Any]] = Field(None, description="统计信息")


class SystemStatus(BaseModel):
    """系统状态模型"""
    is_running: bool = Field(..., description="系统是否运行中")
    uptime: Optional[str] = Field(None, description="系统运行时间")
    version: str = Field(..., description="系统版本")
    engines: List[EngineInfo] = Field(..., description="引擎状态列表")
    current_goal: Optional[str] = Field(None, description="当前目标")
    current_task: Optional[str] = Field(None, description="当前任务")
    memory_stats: Optional[Dict[str, Any]] = Field(None, description="记忆统计")
    mcp_status: Optional[Dict[str, Any]] = Field(None, description="MCP服务状态")


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="健康状态")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="检查时间")
    components: Dict[str, bool] = Field(..., description="组件健康状态")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")


class APIResponse(BaseModel):
    """通用API响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="响应时间")


# WebSocket消息模型

class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    type: str = Field(..., description="消息类型")
    data: Dict[str, Any] = Field(..., description="消息数据")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="消息时间")
    session_id: Optional[str] = Field(None, description="会话ID")


class WebSocketResponse(BaseModel):
    """WebSocket响应模型"""
    type: str = Field(..., description="响应类型")
    data: Any = Field(..., description="响应数据")
    success: bool = Field(True, description="是否成功")
    error: Optional[str] = Field(None, description="错误信息")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="响应时间")


# 配置模型

class APIConfig(BaseModel):
    """API配置模型"""
    host: str = Field("0.0.0.0", description="服务器主机")
    port: int = Field(8000, description="服务器端口")
    debug: bool = Field(False, description="调试模式")
    api_key: Optional[str] = Field(None, description="API密钥")
    cors_origins: List[str] = Field(["*"], description="CORS允许的源")
    max_connections: int = Field(100, description="最大连接数")
    request_timeout: int = Field(30, description="请求超时时间(秒)")


# 错误模型

class APIError(BaseModel):
    """API错误模型"""
    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="错误时间")

# -*- coding: utf-8 -*-
"""
感知引擎主类实现
整合所有感知服务，提供统一的感知接口
严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Union

from .. import BaseEngine
from .base import (
    ScreenAnalysis,
    Screenshot,
    UIElement,
    PerceptionResult,
    PerceptionType,
    Rectangle
)
from .screen_capture import ScreenCaptureService
from .element_detection import UIElementDetectionService
from .text_recognition import TextRecognitionService
from .visual_understanding import VisualUnderstandingService
from .system_state import SystemStateService

class PerceptionEngine(BaseEngine):
    """感知引擎主类"""
    
    def __init__(self, qyuan_core=None):
        super().__init__("Perception", qyuan_core)
        
        # 初始化各个感知服务
        self.screen_capture = ScreenCaptureService()
        self.element_detection = UIElementDetectionService()
        self.text_recognition = TextRecognitionService()
        self.system_state = SystemStateService()
        
        # 视觉理解服务需要LLM客户端
        llm_client = None
        if qyuan_core and hasattr(qyuan_core, 'llm_manager'):
            llm_client = qyuan_core.llm_manager.get_client()
        
        self.visual_understanding = VisualUnderstandingService(llm_client)
        
        # 感知历史记录
        self.perception_history: List[PerceptionResult] = []
        self.max_history_size = 100
        
        # 性能统计
        self.performance_stats = {
            "total_perceptions": 0,
            "successful_perceptions": 0,
            "average_processing_time": 0.0,
            "last_perception_time": None
        }
    
    async def _initialize(self):
        """初始化感知引擎"""
        self.logger.info("初始化感知引擎...")
        
        # 检查各个服务的可用性
        services_status = await self._check_services_availability()
        
        self.logger.info("感知引擎服务状态:")
        for service_name, status in services_status.items():
            status_text = "可用" if status else "不可用"
            self.logger.info(f"  {service_name}: {status_text}")
        
        # 如果没有任何服务可用，记录警告
        if not any(services_status.values()):
            self.logger.warning("所有感知服务都不可用，感知功能将受限")
        
        self.logger.info("感知引擎初始化完成")
    
    async def _start(self):
        """启动感知引擎"""
        self.logger.info("感知引擎启动完成")
    
    async def _stop(self):
        """停止感知引擎"""
        self.logger.info("感知引擎停止完成")
    
    async def _health_check(self) -> bool:
        """健康检查"""
        try:
            # 检查至少有一个服务可用
            services_status = await self._check_services_availability()
            return any(services_status.values())
        except Exception as e:
            self.logger.error(f"感知引擎健康检查失败: {e}")
            return False
    
    async def analyze_current_screen(self) -> ScreenAnalysis:
        """分析当前屏幕状态"""
        start_time = time.time()
        
        try:
            self.logger.debug("开始分析当前屏幕...")
            
            # 1. 截取屏幕
            screenshot = await self.screen_capture.capture_full_screen()
            
            # 2. 并发执行各种分析
            tasks = [
                self._safe_detect_elements(screenshot),
                self._safe_extract_text(screenshot),
                self._safe_get_visual_description(screenshot)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            ui_elements = results[0] if not isinstance(results[0], Exception) else []
            text_content = results[1] if not isinstance(results[1], Exception) else []
            visual_description = results[2] if not isinstance(results[2], Exception) else None
            
            # 3. 获取上下文信息
            context_info = await self._safe_get_context_info(screenshot)
            
            analysis_time = time.time() - start_time
            
            # 4. 构建分析结果
            screen_analysis = ScreenAnalysis(
                screenshot=screenshot,
                ui_elements=ui_elements,
                text_content=text_content,
                visual_description=visual_description,
                context_info=context_info,
                analysis_time=analysis_time
            )
            
            self.logger.debug(f"屏幕分析完成，耗时: {analysis_time:.2f}秒")
            
            # 更新统计信息
            self._update_performance_stats(True, analysis_time)
            
            return screen_analysis
            
        except Exception as e:
            analysis_time = time.time() - start_time
            self.logger.error(f"屏幕分析失败: {e}")
            
            # 更新统计信息
            self._update_performance_stats(False, analysis_time)
            
            # 返回基础分析结果
            return ScreenAnalysis(
                screenshot=Screenshot(b"", 0, 0),
                ui_elements=[],
                text_content=[],
                visual_description=f"分析失败: {str(e)}",
                context_info={"error": str(e)},
                analysis_time=analysis_time
            )
    
    async def find_element_by_text(self, text: str) -> Optional[UIElement]:
        """根据文本查找UI元素"""
        try:
            screenshot = await self.screen_capture.capture_full_screen()
            return await self.element_detection.find_element_by_text(screenshot, text)
        except Exception as e:
            self.logger.error(f"根据文本查找元素失败: {e}")
            return None
    
    async def get_text_at_position(self, position: Rectangle) -> Optional[str]:
        """获取指定位置的文本"""
        try:
            screenshot = await self.screen_capture.capture_full_screen()
            return await self.text_recognition.get_text_at_position(screenshot, position)
        except Exception as e:
            self.logger.error(f"获取位置文本失败: {e}")
            return None
    
    async def get_system_state(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            result = await self.system_state.process(None)
            return result.data if result.success else {}
        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            return {"error": str(e)}
    
    async def capture_screen_region(self, region: Rectangle) -> Optional[Screenshot]:
        """捕获屏幕指定区域"""
        try:
            return await self.screen_capture.capture_region(region)
        except Exception as e:
            self.logger.error(f"捕获屏幕区域失败: {e}")
            return None
    
    async def _safe_detect_elements(self, screenshot: Screenshot) -> List[UIElement]:
        """安全的UI元素检测"""
        try:
            result = await self.element_detection.process(screenshot)
            return result.data if result.success else []
        except Exception as e:
            self.logger.warning(f"UI元素检测失败: {e}")
            return []
    
    async def _safe_extract_text(self, screenshot: Screenshot) -> List[str]:
        """安全的文本提取"""
        try:
            result = await self.text_recognition.process(screenshot)
            return result.data if result.success else []
        except Exception as e:
            self.logger.warning(f"文本提取失败: {e}")
            return []
    
    async def _safe_get_visual_description(self, screenshot: Screenshot) -> Optional[str]:
        """安全的视觉描述"""
        try:
            result = await self.visual_understanding.process(screenshot)
            return result.data if result.success else None
        except Exception as e:
            self.logger.warning(f"视觉描述失败: {e}")
            return None
    
    async def _safe_get_context_info(self, screenshot: Screenshot) -> Dict[str, Any]:
        """安全的上下文信息获取"""
        try:
            return await self.visual_understanding.analyze_context(screenshot)
        except Exception as e:
            self.logger.warning(f"上下文分析失败: {e}")
            return {}
    
    async def _check_services_availability(self) -> Dict[str, bool]:
        """检查各个服务的可用性"""
        services = {
            "screen_capture": self.screen_capture,
            "element_detection": self.element_detection,
            "text_recognition": self.text_recognition,
            "visual_understanding": self.visual_understanding,
            "system_state": self.system_state
        }
        
        availability = {}
        
        for service_name, service in services.items():
            try:
                availability[service_name] = await service.is_available()
            except Exception as e:
                self.logger.warning(f"检查服务 {service_name} 可用性失败: {e}")
                availability[service_name] = False
        
        return availability
    
    def _update_performance_stats(self, success: bool, processing_time: float):
        """更新性能统计"""
        self.performance_stats["total_perceptions"] += 1
        
        if success:
            self.performance_stats["successful_perceptions"] += 1
        
        # 更新平均处理时间
        total = self.performance_stats["total_perceptions"]
        current_avg = self.performance_stats["average_processing_time"]
        self.performance_stats["average_processing_time"] = (
            (current_avg * (total - 1) + processing_time) / total
        )
        
        self.performance_stats["last_perception_time"] = time.time()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self.performance_stats.copy()
        
        # 计算成功率
        total = stats["total_perceptions"]
        successful = stats["successful_perceptions"]
        stats["success_rate"] = (successful / total) if total > 0 else 0.0
        
        return stats
    
    def get_perception_history(self, limit: int = 10) -> List[PerceptionResult]:
        """获取感知历史记录"""
        return self.perception_history[-limit:] if self.perception_history else []

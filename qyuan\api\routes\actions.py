#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
操作API路由

提供QYuan操作执行的API接口
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status

from ..models import ActionRequest, ActionResponse, APIResponse
from ..app import get_qyuan_core, verify_api_key
from ...core.qyuan_core import QYuanCore

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/execute",
    response_model=ActionResponse,
    summary="执行操作",
    description="执行指定的操作（鼠标、键盘、视觉等）"
)
async def execute_action(
    request: ActionRequest,
    qyuan: QYuanCore = Depends(get_qyuan_core),
    _: bool = Depends(verify_api_key)
):
    """执行操作"""
    try:
        logger.info(f"执行操作: {request.action_type}.{request.action_name}")
        
        # 获取执行引擎
        execution_engine = qyuan.engines.get("execution")
        if not execution_engine:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="执行引擎不可用"
            )
        
        # 根据操作类型执行相应操作
        if request.action_type == "mouse":
            result = await execution_engine.execute_mouse_action(
                request.action_name, **request.parameters
            )
        elif request.action_type == "keyboard":
            result = await execution_engine.execute_keyboard_action(
                request.action_name, **request.parameters
            )
        elif request.action_type == "vision":
            result = await execution_engine.execute_vision_action(
                request.action_name, **request.parameters
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的操作类型: {request.action_type}"
            )
        
        return ActionResponse(
            success=result.get("success", False),
            result=result.get("result"),
            error_message=result.get("error"),
            duration_ms=result.get("duration_ms")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"执行操作失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"执行操作时发生错误: {str(e)}"
        )

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
改进的输入法检测和切换功能测试脚本

该脚本用于测试改进后的键盘控制器输入法检测和切换功能。
"""

import time
import logging
import pyperclip
from keyboard_controller import KeyboardController

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ImprovedIMETest")

def test_ime_detection():
    """测试输入法检测功能"""
    logger.info("开始测试输入法检测功能")
    
    controller = KeyboardController()
    
    # 检测当前输入法状态
    is_chinese = controller.is_ime_enabled()
    print(f"当前输入法状态: {'中文' if is_chinese else '英文'}")
    
    logger.info("输入法检测功能测试完成")
    return is_chinese

def test_improved_ime_switch():
    """测试改进的输入法切换功能"""
    logger.info("开始测试改进的输入法切换功能")
    
    controller = KeyboardController()
    
    # 检测当前输入法状态
    is_chinese_before = controller.is_ime_enabled()
    print(f"切换前输入法状态: {'中文' if is_chinese_before else '英文'}")
    
    # 切换到英文输入法，最多尝试3次
    print("尝试切换到英文输入法（最多尝试3次）...")
    result = controller.switch_to_english_input(max_attempts=3)
    print(f"切换结果: {'成功' if result else '失败'}")
    
    # 再次检测输入法状态
    is_chinese_after = controller.is_ime_enabled()
    print(f"切换后输入法状态: {'中文' if is_chinese_after else '英文'}")
    
    logger.info("改进的输入法切换功能测试完成")

def test_improved_typing_with_ime_check():
    """测试改进的在确保英文输入法状态下输入文本"""
    logger.info("开始测试改进的在确保英文输入法状态下输入文本")
    
    controller = KeyboardController()
    
    # 等待用户准备
    print("请在5秒内将光标放在文本编辑器中...")
    time.sleep(5)
    
    # 测试在确保英文输入法状态下输入文本
    test_text = "This text should be typed in English input mode."
    print(f"测试: 在确保英文输入法状态下输入文本 '{test_text}'")
    result = controller.ensure_english_input_before_typing(test_text)
    print(f"输入结果: {'成功' if result else '失败'}")
    time.sleep(1)
    
    # 测试使用剪贴板输入中文文本
    controller.type_key('enter')
    chinese_text = "这段中文文本将通过剪贴板粘贴，避免输入法问题。"
    print(f"测试: 使用剪贴板输入中文文本 '{chinese_text}'")
    
    # 保存原始剪贴板内容
    original_clipboard = pyperclip.paste()
    
    try:
        # 设置中文文本到剪贴板
        controller.set_clipboard_text(chinese_text)
        time.sleep(0.5)
        
        # 执行粘贴操作
        controller.hotkey('ctrl', 'v')
        time.sleep(1)
        
        print("剪贴板粘贴完成")
    finally:
        # 恢复原始剪贴板内容
        pyperclip.copy(original_clipboard)
    
    logger.info("改进的在确保英文输入法状态下输入文本测试完成")

def main():
    """主函数，运行所有测试"""
    logger.info("开始改进的输入法检测和切换功能测试")
    
    print("改进的输入法检测和切换功能测试程序")
    print("==============================")
    print("该程序将测试改进后的键盘控制器输入法检测和切换功能。")
    print()
    
    # 询问用户是否准备好
    input("准备好开始测试了吗？按回车键继续...")
    
    # 运行测试
    try:
        is_chinese = test_ime_detection()
        print("\n按回车键继续下一个测试...")
        input()
        
        test_improved_ime_switch()
        print("\n按回车键继续下一个测试...")
        input()
        
        test_improved_typing_with_ime_check()
        
        print("\n所有测试已完成！")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")
        print(f"\n测试过程中出错: {str(e)}")
    
    logger.info("改进的输入法检测和切换功能测试结束")

if __name__ == "__main__":
    main()

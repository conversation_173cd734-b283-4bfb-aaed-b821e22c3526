# QYuan命令行终端能力设计

## 核心理念

命令行终端是QYuan的"原生语言"，就像人类的母语一样。QYuan应该能够像资深系统管理员一样熟练使用命令行，这是其作为硅基生命的基础能力之一。

## 终端能力架构

### 1. 多Shell支持
```python
class TerminalManager:
    """终端管理器"""
    
    def __init__(self):
        self.shells = {
            'powershell': PowerShellInterface(),
            'cmd': CommandPromptInterface(),
            'bash': BashInterface(),  # WSL支持
            'python': PythonREPLInterface(),
            'node': NodeREPLInterface()
        }
        self.active_sessions = {}
        self.command_history = CommandHistory()
    
    async def create_session(self, shell_type: str, session_id: str = None) -> TerminalSession:
        """创建终端会话"""
        if session_id is None:
            session_id = self.generate_session_id()
        
        shell = self.shells[shell_type]
        session = TerminalSession(
            session_id=session_id,
            shell=shell,
            working_directory=os.getcwd(),
            environment=os.environ.copy()
        )
        
        self.active_sessions[session_id] = session
        return session
    
    async def execute_command(self, session_id: str, command: str, timeout: int = 30) -> CommandResult:
        """执行命令"""
        session = self.active_sessions[session_id]
        
        # 预处理命令
        processed_command = await self.preprocess_command(command, session)
        
        # 执行命令
        result = await session.execute(processed_command, timeout)
        
        # 后处理结果
        processed_result = await self.postprocess_result(result, session)
        
        # 记录历史
        await self.command_history.record(session_id, command, processed_result)
        
        return processed_result
```

### 2. PowerShell深度集成
```python
class PowerShellInterface:
    """PowerShell接口"""
    
    def __init__(self):
        self.process = None
        self.stdin = None
        self.stdout = None
        self.stderr = None
    
    async def initialize(self):
        """初始化PowerShell进程"""
        self.process = await asyncio.create_subprocess_exec(
            'powershell.exe', '-NoExit', '-Command', '-',
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            text=True
        )
        
        self.stdin = self.process.stdin
        self.stdout = self.process.stdout
        self.stderr = self.process.stderr
        
        # 设置PowerShell环境
        await self.setup_environment()
    
    async def setup_environment(self):
        """设置PowerShell环境"""
        setup_commands = [
            # 设置执行策略
            "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force",
            # 导入常用模块
            "Import-Module Microsoft.PowerShell.Management",
            "Import-Module Microsoft.PowerShell.Utility",
            # 设置编码
            "[Console]::OutputEncoding = [System.Text.Encoding]::UTF8",
            # 设置提示符
            "function prompt { return 'QYuan-PS> ' }"
        ]
        
        for cmd in setup_commands:
            await self.execute_raw(cmd)
    
    async def execute_raw(self, command: str) -> CommandResult:
        """执行原始PowerShell命令"""
        try:
            # 发送命令
            self.stdin.write(f"{command}\n")
            await self.stdin.drain()
            
            # 读取输出
            output_lines = []
            error_lines = []
            
            # 使用超时机制读取输出
            start_time = time.time()
            while time.time() - start_time < 30:  # 30秒超时
                try:
                    line = await asyncio.wait_for(self.stdout.readline(), timeout=1.0)
                    if line:
                        output_lines.append(line.strip())
                    else:
                        break
                except asyncio.TimeoutError:
                    # 检查是否有错误输出
                    try:
                        error_line = await asyncio.wait_for(self.stderr.readline(), timeout=0.1)
                        if error_line:
                            error_lines.append(error_line.strip())
                    except asyncio.TimeoutError:
                        pass
                    
                    # 检查命令是否完成
                    if self.is_command_complete(output_lines):
                        break
            
            return CommandResult(
                command=command,
                stdout='\n'.join(output_lines),
                stderr='\n'.join(error_lines),
                return_code=0 if not error_lines else 1,
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            return CommandResult(
                command=command,
                stdout="",
                stderr=str(e),
                return_code=1,
                execution_time=0
            )
```

### 3. 智能命令补全
```python
class CommandCompletion:
    """智能命令补全"""
    
    def __init__(self):
        self.command_db = CommandDatabase()
        self.context_analyzer = ContextAnalyzer()
        self.usage_tracker = UsageTracker()
    
    async def get_completions(self, partial_command: str, context: TerminalContext) -> List[Completion]:
        """获取命令补全建议"""
        completions = []
        
        # 基于历史使用的补全
        history_completions = await self.get_history_completions(partial_command, context)
        completions.extend(history_completions)
        
        # 基于系统命令的补全
        system_completions = await self.get_system_completions(partial_command, context)
        completions.extend(system_completions)
        
        # 基于上下文的智能补全
        context_completions = await self.get_context_completions(partial_command, context)
        completions.extend(context_completions)
        
        # 排序和去重
        completions = self.rank_completions(completions, context)
        
        return completions[:10]  # 返回前10个建议
    
    async def get_context_completions(self, partial_command: str, context: TerminalContext) -> List[Completion]:
        """基于上下文的智能补全"""
        completions = []
        
        # 分析当前工作目录
        if context.working_directory:
            # 文件和目录补全
            if any(cmd in partial_command for cmd in ['cd', 'ls', 'dir', 'cat', 'type']):
                file_completions = await self.get_file_completions(partial_command, context.working_directory)
                completions.extend(file_completions)
        
        # 分析当前项目类型
        project_type = await self.detect_project_type(context.working_directory)
        if project_type == 'python':
            python_completions = await self.get_python_completions(partial_command)
            completions.extend(python_completions)
        elif project_type == 'node':
            node_completions = await self.get_node_completions(partial_command)
            completions.extend(node_completions)
        elif project_type == 'git':
            git_completions = await self.get_git_completions(partial_command)
            completions.extend(git_completions)
        
        return completions
    
    async def get_python_completions(self, partial_command: str) -> List[Completion]:
        """Python项目相关补全"""
        completions = []
        
        python_commands = [
            "python -m venv venv",
            "python -m pip install",
            "python -m pip freeze > requirements.txt",
            "python -m pytest",
            "python -m black .",
            "python -m flake8",
            "python manage.py runserver",  # Django
            "python app.py",  # Flask
            "uvicorn main:app --reload",  # FastAPI
        ]
        
        for cmd in python_commands:
            if cmd.startswith(partial_command):
                completions.append(Completion(
                    text=cmd,
                    description="Python项目命令",
                    score=0.8
                ))
        
        return completions
```

### 4. 命令执行策略
```python
class CommandExecutionStrategy:
    """命令执行策略"""
    
    def __init__(self):
        self.safety_checker = CommandSafetyChecker()
        self.permission_manager = PermissionManager()
        self.execution_monitor = ExecutionMonitor()
    
    async def execute_command_safely(self, command: str, context: TerminalContext) -> CommandResult:
        """安全执行命令"""
        
        # 安全检查
        safety_result = await self.safety_checker.check(command)
        if not safety_result.is_safe:
            return CommandResult(
                command=command,
                stdout="",
                stderr=f"命令被安全检查拒绝: {safety_result.reason}",
                return_code=1
            )
        
        # 权限检查
        permission_result = await self.permission_manager.check_permission(command, context)
        if not permission_result.has_permission:
            return CommandResult(
                command=command,
                stdout="",
                stderr=f"权限不足: {permission_result.reason}",
                return_code=1
            )
        
        # 执行前准备
        execution_context = await self.prepare_execution(command, context)
        
        # 监控执行
        monitor_task = asyncio.create_task(
            self.execution_monitor.monitor_execution(execution_context)
        )
        
        try:
            # 执行命令
            result = await self.execute_with_monitoring(command, execution_context)
            
            # 后处理
            processed_result = await self.postprocess_execution(result, execution_context)
            
            return processed_result
            
        finally:
            # 停止监控
            monitor_task.cancel()
    
    async def execute_with_monitoring(self, command: str, context: ExecutionContext) -> CommandResult:
        """带监控的命令执行"""
        
        start_time = time.time()
        
        try:
            # 根据命令类型选择执行方式
            if self.is_long_running_command(command):
                result = await self.execute_long_running(command, context)
            elif self.is_interactive_command(command):
                result = await self.execute_interactive(command, context)
            else:
                result = await self.execute_standard(command, context)
            
            execution_time = time.time() - start_time
            result.execution_time = execution_time
            
            return result
            
        except asyncio.TimeoutError:
            return CommandResult(
                command=command,
                stdout="",
                stderr="命令执行超时",
                return_code=124,  # 超时错误码
                execution_time=time.time() - start_time
            )
        except Exception as e:
            return CommandResult(
                command=command,
                stdout="",
                stderr=f"执行异常: {str(e)}",
                return_code=1,
                execution_time=time.time() - start_time
            )
```

### 5. 命令学习与优化
```python
class CommandLearningSystem:
    """命令学习系统"""
    
    def __init__(self):
        self.usage_analyzer = UsageAnalyzer()
        self.pattern_detector = PatternDetector()
        self.optimization_engine = OptimizationEngine()
    
    async def learn_from_usage(self, command_session: CommandSession):
        """从使用中学习"""
        
        # 分析命令模式
        patterns = await self.pattern_detector.detect_patterns(command_session.commands)
        
        # 识别重复任务
        repetitive_tasks = await self.identify_repetitive_tasks(command_session.commands)
        
        # 学习用户偏好
        preferences = await self.learn_user_preferences(command_session)
        
        # 优化建议
        optimizations = await self.optimization_engine.generate_optimizations(
            patterns, repetitive_tasks, preferences
        )
        
        # 保存学习结果
        await self.save_learning_results(command_session.session_id, {
            'patterns': patterns,
            'repetitive_tasks': repetitive_tasks,
            'preferences': preferences,
            'optimizations': optimizations
        })
    
    async def identify_repetitive_tasks(self, commands: List[Command]) -> List[RepetitiveTask]:
        """识别重复任务"""
        repetitive_tasks = []
        
        # 查找命令序列模式
        command_sequences = self.extract_command_sequences(commands)
        
        for sequence in command_sequences:
            if sequence.frequency > 3:  # 出现3次以上认为是重复任务
                task = RepetitiveTask(
                    name=self.generate_task_name(sequence),
                    commands=sequence.commands,
                    frequency=sequence.frequency,
                    time_saved_potential=self.calculate_time_saved(sequence)
                )
                repetitive_tasks.append(task)
        
        return repetitive_tasks
    
    async def suggest_automation(self, repetitive_task: RepetitiveTask) -> AutomationSuggestion:
        """建议自动化方案"""
        
        # 生成脚本
        script_content = await self.generate_automation_script(repetitive_task)
        
        # 计算收益
        benefits = await self.calculate_automation_benefits(repetitive_task)
        
        return AutomationSuggestion(
            task=repetitive_task,
            script_content=script_content,
            script_type=self.determine_script_type(repetitive_task),
            benefits=benefits,
            implementation_effort=self.estimate_implementation_effort(repetitive_task)
        )
```

### 6. 终端环境管理
```python
class TerminalEnvironmentManager:
    """终端环境管理器"""
    
    def __init__(self):
        self.environments = {}
        self.active_environment = None
    
    async def create_environment(self, name: str, config: EnvironmentConfig) -> Environment:
        """创建环境"""
        environment = Environment(
            name=name,
            shell_type=config.shell_type,
            working_directory=config.working_directory,
            environment_variables=config.environment_variables,
            path_extensions=config.path_extensions,
            aliases=config.aliases
        )
        
        await environment.initialize()
        self.environments[name] = environment
        
        return environment
    
    async def switch_environment(self, name: str):
        """切换环境"""
        if name not in self.environments:
            raise EnvironmentNotFoundError(f"环境 '{name}' 不存在")
        
        self.active_environment = self.environments[name]
        await self.active_environment.activate()
    
    async def create_project_environment(self, project_path: str) -> Environment:
        """为项目创建专用环境"""
        project_type = await self.detect_project_type(project_path)
        
        if project_type == 'python':
            return await self.create_python_environment(project_path)
        elif project_type == 'node':
            return await self.create_node_environment(project_path)
        elif project_type == 'dotnet':
            return await self.create_dotnet_environment(project_path)
        else:
            return await self.create_generic_environment(project_path)
    
    async def create_python_environment(self, project_path: str) -> Environment:
        """创建Python项目环境"""
        venv_path = os.path.join(project_path, 'venv')
        
        # 检查是否已有虚拟环境
        if not os.path.exists(venv_path):
            # 创建虚拟环境
            await self.execute_command(f"python -m venv {venv_path}")
        
        # 配置环境
        config = EnvironmentConfig(
            shell_type='powershell',
            working_directory=project_path,
            environment_variables={
                'VIRTUAL_ENV': venv_path,
                'PYTHONPATH': project_path
            },
            path_extensions=[
                os.path.join(venv_path, 'Scripts'),  # Windows
                os.path.join(venv_path, 'bin')       # Linux/Mac
            ],
            aliases={
                'pip': os.path.join(venv_path, 'Scripts', 'pip.exe'),
                'python': os.path.join(venv_path, 'Scripts', 'python.exe')
            }
        )
        
        return await self.create_environment(f"python-{os.path.basename(project_path)}", config)
```

## 内置命令扩展

### 1. QYuan专用命令
```python
class QYuanCommands:
    """QYuan专用命令"""
    
    def __init__(self, qyuan_core):
        self.qyuan = qyuan_core
        self.commands = {
            'qyuan': self.qyuan_command,
            'qstatus': self.status_command,
            'qlearn': self.learn_command,
            'qtools': self.tools_command,
            'qmemory': self.memory_command,
            'qgoal': self.goal_command
        }
    
    async def qyuan_command(self, args: List[str]) -> CommandResult:
        """QYuan主命令"""
        if not args:
            return self.show_help()
        
        subcommand = args[0]
        
        if subcommand == 'status':
            return await self.status_command(args[1:])
        elif subcommand == 'start':
            return await self.start_command(args[1:])
        elif subcommand == 'stop':
            return await self.stop_command(args[1:])
        elif subcommand == 'restart':
            return await self.restart_command(args[1:])
        elif subcommand == 'config':
            return await self.config_command(args[1:])
        else:
            return CommandResult(
                stdout="",
                stderr=f"未知子命令: {subcommand}",
                return_code=1
            )
    
    async def status_command(self, args: List[str]) -> CommandResult:
        """状态命令"""
        status = await self.qyuan.get_status()
        
        output = f"""
QYuan状态报告
=============
运行状态: {status.running_state}
当前任务: {status.current_task or '无'}
运行时间: {status.uptime}
处理的命令数: {status.commands_processed}
成功率: {status.success_rate:.1%}
当前目标: {status.current_goal or '无'}

能力状态:
- 鼠标控制: {status.mouse_control_status}
- 键盘控制: {status.keyboard_control_status}
- 屏幕识别: {status.vision_status}
- 学习系统: {status.learning_status}

最近错误: {status.recent_errors or '无'}
        """.strip()
        
        return CommandResult(stdout=output, return_code=0)
```

这个命令行终端能力设计让QYuan具备了强大的系统操作能力，能够像专业系统管理员一样使用命令行工具。你觉得还需要补充哪些方面？

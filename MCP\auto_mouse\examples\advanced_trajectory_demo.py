"""
高级轨迹混合算法演示。
"""

import sys
import os
import time
import random

# 添加src目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入鼠标控制器和记录器
from src.mouse_controller import MouseController
from src.mouse_recorder import MouseRecorder

def record_full_screen_trajectory():
    """记录全屏鼠标轨迹。"""
    print("=== 全屏鼠标轨迹记录 ===")
    print("将记录30秒的鼠标移动轨迹")
    print("请在这段时间内进行一次近乎全屏的鼠标移动")
    print("尝试覆盖屏幕的不同区域，包括边缘和中心")
    print("按Enter键开始记录...")
    input()
    
    recorder = MouseRecorder()
    recorder.start_recording(duration=30)
    
    # 等待记录完成
    while recorder.recording:
        print("正在记录...", end="\r")
        time.sleep(1)
    
    print("\n记录完成！轨迹已保存")
    return True

def demonstrate_advanced_trajectory():
    """演示高级轨迹混合算法。"""
    print("\n=== 高级轨迹混合算法演示 ===")
    
    # 创建鼠标控制器
    mouse = MouseController()
    
    # 检查是否有可用轨迹
    if not mouse.trajectories:
        print("没有可用的轨迹记录，请先运行记录功能")
        return False
    
    print(f"找到 {len(mouse.trajectories)} 个轨迹记录")
    print(f"总轨迹点数: {sum(len(t) for t in mouse.trajectories)}")
    print("将演示高级轨迹混合算法")
    print("按Enter键开始演示...")
    input()
    
    # 获取屏幕尺寸
    screen_size = mouse.get_screen_size()
    width, height = screen_size["width"], screen_size["height"]
    
    # 定义测试点
    test_points = [
        # 屏幕四角
        (50, 50),                    # 左上
        (width - 50, 50),            # 右上
        (50, height - 50),           # 左下
        (width - 50, height - 50),   # 右下
        
        # 屏幕中心
        (width // 2, height // 2),
        
        # 屏幕边缘中点
        (width // 2, 50),            # 上边缘
        (width - 50, height // 2),   # 右边缘
        (width // 2, height - 50),   # 下边缘
        (50, height // 2),           # 左边缘
    ]
    
    # 随机打乱测试点顺序
    random.shuffle(test_points)
    
    # 先移动到第一个点
    first_point = test_points[0]
    print(f"\n移动到起始点 ({first_point[0]}, {first_point[1]})...")
    mouse.move_mouse(first_point[0], first_point[1], duration=0.5)
    time.sleep(1)
    
    # 依次移动到每个测试点
    for i in range(1, len(test_points)):
        prev_point = test_points[i-1]
        curr_point = test_points[i]
        
        # 计算距离
        distance = ((curr_point[0] - prev_point[0]) ** 2 + (curr_point[1] - prev_point[1]) ** 2) ** 0.5
        
        print(f"\n测试 {i}: 从 ({prev_point[0]}, {prev_point[1]}) 到 ({curr_point[0]}, {curr_point[1]})")
        print(f"距离: {distance:.2f} 像素")
        
        # 使用高级轨迹混合算法移动
        result = mouse.move_mouse(curr_point[0], curr_point[1])
        print(result["message"])
        
        # 等待一段时间
        time.sleep(1)
    
    print("\n演示完成！")
    return True

def main():
    """主函数。"""
    print("高级轨迹混合算法演示")
    print("-" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 记录全屏鼠标轨迹")
        print("2. 演示高级轨迹混合算法")
        print("3. 退出")
        
        choice = input("请输入选项 (1-3): ")
        
        if choice == "1":
            record_full_screen_trajectory()
        elif choice == "2":
            demonstrate_advanced_trajectory()
        elif choice == "3":
            print("退出程序")
            break
        else:
            print("无效选项，请重新输入")

if __name__ == "__main__":
    main()

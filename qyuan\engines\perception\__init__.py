# -*- coding: utf-8 -*-
"""
感知引擎模块
提供完整的屏幕感知、UI元素检测、文本识别、视觉理解和系统状态感知功能
"""

from .base import (
    PerceptionType,
    ElementType,
    Rectangle,
    UIElement,
    Screenshot,
    PerceptionResult,
    ScreenAnalysis,
    PerceptionServiceBase,
    ScreenCaptureServiceBase,
    UIElementDetectionServiceBase,
    TextRecognitionServiceBase,
    VisualUnderstandingServiceBase,
    SystemStateServiceBase
)

from .screen_capture import ScreenCaptureService
from .element_detection import UIElementDetectionService
from .text_recognition import TextRecognitionService
from .visual_understanding import VisualUnderstandingService
from .system_state import SystemStateService
from .perception_engine import PerceptionEngine

__all__ = [
    # 基础类和数据类型
    'PerceptionType',
    'ElementType',
    'Rectangle',
    'UIElement',
    'Screenshot',
    'PerceptionResult',
    'ScreenAnalysis',

    # 基础服务类
    'PerceptionServiceBase',
    'ScreenCaptureServiceBase',
    'UIElementDetectionServiceBase',
    'TextRecognitionServiceBase',
    'VisualUnderstandingServiceBase',
    'SystemStateServiceBase',

    # 具体服务实现
    'ScreenCaptureService',
    'UIElementDetectionService',
    'TextRecognitionService',
    'VisualUnderstandingService',
    'SystemStateService',

    # 主引擎
    'PerceptionEngine'
]

# -*- coding: utf-8 -*-
"""
屏幕截图服务实现
专门负责屏幕截图功能，严格按照代码规范的单一职责原则
"""

import asyncio
import time
from typing import Optional, Dict, Any
from datetime import datetime
import logging

try:
    import pyautogui
    import PIL.Image
    import io
    PYAUTOGUI_AVAILABLE = True
except ImportError:
    PYAUTOGUI_AVAILABLE = False

try:
    import win32gui
    import win32ui
    import win32con
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False

from .base import (
    ScreenCaptureServiceBase, 
    Screenshot, 
    Rectangle, 
    PerceptionResult, 
    PerceptionType
)

class ScreenCaptureService(ScreenCaptureServiceBase):
    """屏幕截图服务实现"""
    
    def __init__(self):
        super().__init__("ScreenCapture")
        self.logger = logging.getLogger(f"QYuan.Perception.{self.name}")
        self.screenshot_cache: Dict[str, Screenshot] = {}
        self.cache_timeout = 1.0  # 缓存超时时间（秒）
        
        # 检查依赖可用性
        self.pyautogui_available = PYAUTOGUI_AVAILABLE
        self.win32_available = WIN32_AVAILABLE
        
        if not self.pyautogui_available:
            self.logger.warning("PyAutoGUI不可用，部分截图功能将受限")
        
        if not self.win32_available:
            self.logger.warning("Win32API不可用，窗口截图功能将受限")
    
    async def process(self, input_data: Any) -> PerceptionResult:
        """处理截图请求"""
        start_time = time.time()
        
        try:
            if isinstance(input_data, dict):
                capture_type = input_data.get('type', 'full_screen')
                
                if capture_type == 'full_screen':
                    screenshot = await self.capture_full_screen()
                elif capture_type == 'window':
                    window_handle = input_data.get('window_handle')
                    screenshot = await self.capture_window(window_handle)
                elif capture_type == 'region':
                    region_data = input_data.get('region')
                    region = Rectangle(**region_data) if region_data else None
                    screenshot = await self.capture_region(region)
                else:
                    raise ValueError(f"不支持的截图类型: {capture_type}")
            else:
                # 默认全屏截图
                screenshot = await self.capture_full_screen()
            
            processing_time = time.time() - start_time
            
            result = PerceptionResult(
                perception_type=PerceptionType.SCREEN_CAPTURE,
                success=True,
                data=screenshot,
                confidence=1.0,
                processing_time=processing_time
            )
            
            self.last_result = result
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"截图处理失败: {e}")
            
            result = PerceptionResult(
                perception_type=PerceptionType.SCREEN_CAPTURE,
                success=False,
                data=None,
                confidence=0.0,
                error_message=str(e),
                processing_time=processing_time
            )
            
            self.last_result = result
            return result
    
    async def capture_full_screen(self) -> Screenshot:
        """捕获全屏截图"""
        cache_key = "full_screen"
        
        # 检查缓存
        cached_screenshot = self._get_cached_screenshot(cache_key)
        if cached_screenshot:
            return cached_screenshot
        
        if not self.pyautogui_available:
            raise RuntimeError("PyAutoGUI不可用，无法进行屏幕截图")
        
        try:
            # 使用PyAutoGUI截图
            pil_image = pyautogui.screenshot()
            
            # 转换为字节数据
            image_bytes = self._pil_to_bytes(pil_image)
            
            screenshot = Screenshot(
                image_data=image_bytes,
                width=pil_image.width,
                height=pil_image.height,
                format="PNG"
            )
            
            # 缓存截图
            self._cache_screenshot(cache_key, screenshot)
            
            self.logger.debug(f"全屏截图完成: {screenshot.width}x{screenshot.height}")
            return screenshot
            
        except Exception as e:
            self.logger.error(f"全屏截图失败: {e}")
            raise
    
    async def capture_window(self, window_handle: Optional[int]) -> Screenshot:
        """捕获指定窗口截图"""
        if not window_handle:
            # 如果没有指定窗口句柄，获取活动窗口
            window_handle = self._get_active_window_handle()
        
        if not self.win32_available:
            self.logger.warning("Win32API不可用，使用全屏截图代替")
            return await self.capture_full_screen()
        
        cache_key = f"window_{window_handle}"
        
        # 检查缓存
        cached_screenshot = self._get_cached_screenshot(cache_key)
        if cached_screenshot:
            return cached_screenshot
        
        try:
            # 获取窗口矩形
            window_rect = win32gui.GetWindowRect(window_handle)
            left, top, right, bottom = window_rect
            width = right - left
            height = bottom - top
            
            # 获取窗口设备上下文
            hwndDC = win32gui.GetWindowDC(window_handle)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            
            # 创建位图
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)
            
            # 复制窗口内容
            saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
            
            # 获取位图数据
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            
            # 转换为PIL图像
            pil_image = PIL.Image.frombuffer(
                'RGB',
                (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                bmpstr, 'raw', 'BGRX', 0, 1
            )
            
            # 转换为字节数据
            image_bytes = self._pil_to_bytes(pil_image)
            
            screenshot = Screenshot(
                image_data=image_bytes,
                width=width,
                height=height,
                format="PNG"
            )
            
            # 清理资源
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(window_handle, hwndDC)
            
            # 缓存截图
            self._cache_screenshot(cache_key, screenshot)
            
            self.logger.debug(f"窗口截图完成: {width}x{height}")
            return screenshot
            
        except Exception as e:
            self.logger.error(f"窗口截图失败: {e}")
            raise
    
    async def capture_region(self, region: Optional[Rectangle]) -> Screenshot:
        """捕获指定区域截图"""
        if not region:
            return await self.capture_full_screen()
        
        if not self.pyautogui_available:
            raise RuntimeError("PyAutoGUI不可用，无法进行区域截图")
        
        cache_key = f"region_{region.x}_{region.y}_{region.width}_{region.height}"
        
        # 检查缓存
        cached_screenshot = self._get_cached_screenshot(cache_key)
        if cached_screenshot:
            return cached_screenshot
        
        try:
            # 使用PyAutoGUI区域截图
            pil_image = pyautogui.screenshot(region=(
                region.x, region.y, region.width, region.height
            ))
            
            # 转换为字节数据
            image_bytes = self._pil_to_bytes(pil_image)
            
            screenshot = Screenshot(
                image_data=image_bytes,
                width=region.width,
                height=region.height,
                format="PNG"
            )
            
            # 缓存截图
            self._cache_screenshot(cache_key, screenshot)
            
            self.logger.debug(f"区域截图完成: {region.width}x{region.height}")
            return screenshot
            
        except Exception as e:
            self.logger.error(f"区域截图失败: {e}")
            raise
    
    def _pil_to_bytes(self, pil_image: 'PIL.Image.Image') -> bytes:
        """将PIL图像转换为字节数据"""
        img_byte_arr = io.BytesIO()
        pil_image.save(img_byte_arr, format='PNG')
        return img_byte_arr.getvalue()
    
    def _get_active_window_handle(self) -> Optional[int]:
        """获取活动窗口句柄"""
        if not self.win32_available:
            return None
        
        try:
            return win32gui.GetForegroundWindow()
        except Exception as e:
            self.logger.error(f"获取活动窗口句柄失败: {e}")
            return None
    
    def _get_cached_screenshot(self, cache_key: str) -> Optional[Screenshot]:
        """获取缓存的截图"""
        if cache_key in self.screenshot_cache:
            screenshot = self.screenshot_cache[cache_key]
            # 检查缓存是否过期
            if (datetime.now() - screenshot.timestamp).total_seconds() < self.cache_timeout:
                return screenshot
            else:
                # 删除过期缓存
                del self.screenshot_cache[cache_key]
        return None
    
    def _cache_screenshot(self, cache_key: str, screenshot: Screenshot):
        """缓存截图"""
        self.screenshot_cache[cache_key] = screenshot
        
        # 清理过期缓存
        current_time = datetime.now()
        expired_keys = [
            key for key, cached_screenshot in self.screenshot_cache.items()
            if (current_time - cached_screenshot.timestamp).total_seconds() > self.cache_timeout
        ]
        
        for key in expired_keys:
            del self.screenshot_cache[key]

# 视觉LLM模块

本模块提供与视觉LLM（如Gemini）的连接功能，用于分析屏幕截图。

## 模块目的

视觉LLM模块的主要目的是为MCP服务器提供屏幕分析功能。由于MCP服务器不能直接将图像返回给主LLM，只能返回文本信息，所以我们需要一个辅助LLM来帮助进行图像识别。

主LLM可能会需要向MCP询问现在屏幕上有什么，是什么颜色，有哪些文件，有没有遮挡，某个按钮或文本框的大概坐标是多少等等。这时候，我们需要把主LLM的问题和截图同时发送给辅助LLM，让辅助LLM进行反馈。

## 模块结构

- `image_enhancer.py`: 提供图像增强功能，包括添加经纬线和坐标标注
- `vision_llm.py`: 提供与视觉LLM（如Gemini）的连接功能
- `screen_analyzer.py`: 提供屏幕分析服务，结合屏幕截图、图像增强和视觉LLM功能

## 使用方法

```python
from src.image_enhancer import ImageEnhancer
from src.vision_llm import VisionLLM
from src.screen_analyzer import ScreenAnalyzer

# 方法1：使用屏幕分析服务（推荐）
analyzer = ScreenAnalyzer()

# 捕获并分析全屏
result = analyzer.capture_and_analyze_screen(
    question="这个屏幕上有什么内容？请描述主要元素及其位置。"
)
print(f"分析结果:\n{result['analysis']}")

# 捕获并分析区域
region = (100, 100, 400, 300)  # x, y, width, height
result = analyzer.capture_and_analyze_screen(
    question="这个区域中有什么内容？请描述主要元素及其位置。",
    region=region
)
print(f"分析结果:\n{result['analysis']}")

# 分析最新的屏幕截图
result = analyzer.analyze_latest_screenshot(
    question="屏幕上有什么颜色？"
)
print(f"分析结果:\n{result['analysis']}")

# 方法2：单独使用各个组件
# 创建截图对象
from src.screen_capture import FullScreenCapture
screen_capture = FullScreenCapture()

# 捕获全屏
screenshot, screenshot_path = screen_capture.capture_screen()

# 创建图像增强对象
enhancer = ImageEnhancer()

# 增强图像
enhanced_path = enhancer.enhance_image(screenshot_path)

# 创建视觉LLM对象
vision_llm = VisionLLM()

# 分析屏幕截图
result = vision_llm.analyze_screenshot(
    screenshot_path, 
    enhanced_path, 
    "这个屏幕上有什么内容？请描述主要元素及其位置。"
)
print(f"分析结果:\n{result}")
```

## 功能特点

### 图像增强

图像增强功能由`ImageEnhancer`类提供，可以为图像添加经纬线和坐标标注，帮助视觉LLM更准确地描述屏幕上元素的位置。

增强后的图像在原始图像的四周添加了50像素的白边，并在白边上标注了坐标，避免截图颜色变化导致标注难以识别的问题。

### 视觉LLM

视觉LLM功能由`VisionLLM`类提供，可以将图像发送给Gemini等视觉LLM进行分析。

当前使用的是Gemini 2.5 Flash Preview模型，可以通过API密钥和模型名称进行配置。

### 屏幕分析服务

屏幕分析服务由`ScreenAnalyzer`类提供，结合了屏幕截图、图像增强和视觉LLM功能，提供了一站式的屏幕分析服务。

## 开发历史

### 2023-05-20

- 初始版本
- 实现了图像增强功能，包括添加经纬线和坐标标注
- 实现了与Gemini API的连接
- 实现了屏幕分析服务，结合屏幕截图、图像增强和视觉LLM功能
- 更新了MCP服务器，集成屏幕分析服务

## 依赖

- PIL (Pillow): `pip install pillow`
- Requests: `pip install requests`
- NumPy: `pip install numpy`

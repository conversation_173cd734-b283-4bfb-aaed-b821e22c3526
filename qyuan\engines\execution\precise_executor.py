# -*- coding: utf-8 -*-
"""
精确操作执行器实现
专门负责高精度的操作执行，严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
import uuid
from typing import Dict, List, Optional, Any

try:
    import pyautogui
    import pynput
    from pynput import mouse, keyboard
    AUTOMATION_AVAILABLE = True
except ImportError:
    AUTOMATION_AVAILABLE = False

from .base import (
    OperationExecutorBase,
    OperationParameters,
    ExecutionContext,
    ExecutionResult,
    ExecutionStatus,
    OperationType
)

class PreciseOperationExecutor(OperationExecutorBase):
    """精确操作执行器"""
    
    def __init__(self):
        super().__init__("PreciseOperationExecutor")
        self.logger = logging.getLogger(f"QYuan.Execution.{self.name}")
        
        # 支持的操作类型
        self.supported_operations = [
            OperationType.MOUSE_CLICK,
            OperationType.MOUSE_MOVE,
            OperationType.MOUSE_DRAG,
            OperationType.MOUSE_SCROLL,
            OperationType.KEYBOARD_TYPE,
            OperationType.KEYBOARD_PRESS,
            OperationType.KEYBOARD_HOTKEY,
            OperationType.WAIT
        ]
        
        # 检查依赖可用性
        self.automation_available = AUTOMATION_AVAILABLE
        if not self.automation_available:
            self.logger.warning("自动化库不可用，执行功能将受限")
        
        # 精度配置
        self.precision_config = {
            'mouse_move_duration': 0.5,
            'click_duration': 0.1,
            'type_interval': 0.05,
            'coordinate_tolerance': 2,
            'retry_delay': 1.0
        }
        
        # 执行统计
        self.execution_stats = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'average_execution_time': 0.0
        }
    
    async def execute(self, params: OperationParameters, context: ExecutionContext) -> ExecutionResult:
        """执行操作"""
        operation_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            self.logger.debug(f"开始执行操作: {params.operation_type.value}")
            
            # 验证参数
            if not await self.validate_parameters(params):
                raise ValueError("操作参数验证失败")
            
            # 检查可用性
            if not self.automation_available:
                raise RuntimeError("自动化库不可用")
            
            # 根据操作类型执行相应操作
            result_data = None
            
            if params.operation_type == OperationType.MOUSE_CLICK:
                result_data = await self._execute_mouse_click(params, context)
            elif params.operation_type == OperationType.MOUSE_MOVE:
                result_data = await self._execute_mouse_move(params, context)
            elif params.operation_type == OperationType.MOUSE_DRAG:
                result_data = await self._execute_mouse_drag(params, context)
            elif params.operation_type == OperationType.MOUSE_SCROLL:
                result_data = await self._execute_mouse_scroll(params, context)
            elif params.operation_type == OperationType.KEYBOARD_TYPE:
                result_data = await self._execute_keyboard_type(params, context)
            elif params.operation_type == OperationType.KEYBOARD_PRESS:
                result_data = await self._execute_keyboard_press(params, context)
            elif params.operation_type == OperationType.KEYBOARD_HOTKEY:
                result_data = await self._execute_keyboard_hotkey(params, context)
            elif params.operation_type == OperationType.WAIT:
                result_data = await self._execute_wait(params, context)
            else:
                raise ValueError(f"不支持的操作类型: {params.operation_type}")
            
            execution_time = time.time() - start_time
            
            # 更新统计
            self._update_stats(True, execution_time)
            
            return ExecutionResult(
                operation_id=operation_id,
                status=ExecutionStatus.SUCCESS,
                success=True,
                result_data=result_data,
                execution_time=execution_time,
                metadata={
                    'operation_type': params.operation_type.value,
                    'precision_config': self.precision_config
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"操作执行失败: {e}")
            
            # 更新统计
            self._update_stats(False, execution_time)
            
            return ExecutionResult(
                operation_id=operation_id,
                status=ExecutionStatus.FAILED,
                success=False,
                error_message=str(e),
                execution_time=execution_time,
                metadata={
                    'operation_type': params.operation_type.value,
                    'error_type': type(e).__name__
                }
            )
    
    async def validate_parameters(self, params: OperationParameters) -> bool:
        """验证参数"""
        try:
            # 检查操作类型支持
            if not self.supports_operation(params.operation_type):
                return False
            
            # 根据操作类型验证特定参数
            if params.operation_type in [OperationType.MOUSE_CLICK, OperationType.MOUSE_MOVE]:
                if not params.coordinates:
                    return False
                if 'x' not in params.coordinates or 'y' not in params.coordinates:
                    return False
            
            elif params.operation_type == OperationType.MOUSE_DRAG:
                if not params.coordinates:
                    return False
                if not all(key in params.coordinates for key in ['start_x', 'start_y', 'end_x', 'end_y']):
                    return False
            
            elif params.operation_type in [OperationType.KEYBOARD_TYPE]:
                if not params.text:
                    return False
            
            elif params.operation_type in [OperationType.KEYBOARD_PRESS, OperationType.KEYBOARD_HOTKEY]:
                if not params.keys:
                    return False
            
            elif params.operation_type == OperationType.WAIT:
                if 'duration' not in params.custom_params:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"参数验证失败: {e}")
            return False
    
    async def _execute_mouse_click(self, params: OperationParameters, context: ExecutionContext) -> Dict[str, Any]:
        """执行鼠标点击"""
        x = params.coordinates['x']
        y = params.coordinates['y']
        button = params.custom_params.get('button', 'left')
        clicks = params.custom_params.get('clicks', 1)
        
        # 移动到目标位置
        pyautogui.moveTo(x, y, duration=self.precision_config['mouse_move_duration'])
        
        # 等待稳定
        await asyncio.sleep(0.1)
        
        # 执行点击
        if button == 'left':
            pyautogui.click(x, y, clicks=clicks, duration=self.precision_config['click_duration'])
        elif button == 'right':
            pyautogui.rightClick(x, y)
        elif button == 'middle':
            pyautogui.middleClick(x, y)
        
        return {
            'action': 'mouse_click',
            'coordinates': {'x': x, 'y': y},
            'button': button,
            'clicks': clicks
        }
    
    async def _execute_mouse_move(self, params: OperationParameters, context: ExecutionContext) -> Dict[str, Any]:
        """执行鼠标移动"""
        x = params.coordinates['x']
        y = params.coordinates['y']
        duration = params.custom_params.get('duration', self.precision_config['mouse_move_duration'])
        
        # 获取当前位置
        current_x, current_y = pyautogui.position()
        
        # 移动到目标位置
        pyautogui.moveTo(x, y, duration=duration)
        
        return {
            'action': 'mouse_move',
            'from': {'x': current_x, 'y': current_y},
            'to': {'x': x, 'y': y},
            'duration': duration
        }
    
    async def _execute_mouse_drag(self, params: OperationParameters, context: ExecutionContext) -> Dict[str, Any]:
        """执行鼠标拖拽"""
        start_x = params.coordinates['start_x']
        start_y = params.coordinates['start_y']
        end_x = params.coordinates['end_x']
        end_y = params.coordinates['end_y']
        duration = params.custom_params.get('duration', 1.0)
        button = params.custom_params.get('button', 'left')
        
        # 移动到起始位置
        pyautogui.moveTo(start_x, start_y, duration=0.3)
        await asyncio.sleep(0.1)
        
        # 执行拖拽
        pyautogui.dragTo(end_x, end_y, duration=duration, button=button)
        
        return {
            'action': 'mouse_drag',
            'start': {'x': start_x, 'y': start_y},
            'end': {'x': end_x, 'y': end_y},
            'duration': duration,
            'button': button
        }
    
    async def _execute_mouse_scroll(self, params: OperationParameters, context: ExecutionContext) -> Dict[str, Any]:
        """执行鼠标滚动"""
        x = params.coordinates.get('x', None) if params.coordinates else None
        y = params.coordinates.get('y', None) if params.coordinates else None
        clicks = params.custom_params.get('clicks', 3)
        direction = params.custom_params.get('direction', 'down')
        
        # 如果指定了位置，先移动到该位置
        if x is not None and y is not None:
            pyautogui.moveTo(x, y, duration=0.2)
            await asyncio.sleep(0.1)
        
        # 执行滚动
        scroll_clicks = clicks if direction == 'up' else -clicks
        pyautogui.scroll(scroll_clicks)
        
        return {
            'action': 'mouse_scroll',
            'position': {'x': x, 'y': y} if x is not None else None,
            'clicks': clicks,
            'direction': direction
        }
    
    async def _execute_keyboard_type(self, params: OperationParameters, context: ExecutionContext) -> Dict[str, Any]:
        """执行键盘输入"""
        text = params.text
        interval = params.custom_params.get('interval', self.precision_config['type_interval'])
        
        # 逐字符输入以提高精度
        pyautogui.typewrite(text, interval=interval)
        
        return {
            'action': 'keyboard_type',
            'text': text,
            'length': len(text),
            'interval': interval
        }
    
    async def _execute_keyboard_press(self, params: OperationParameters, context: ExecutionContext) -> Dict[str, Any]:
        """执行按键按下"""
        keys = params.keys
        
        for key in keys:
            pyautogui.press(key)
            await asyncio.sleep(0.05)  # 短暂延迟
        
        return {
            'action': 'keyboard_press',
            'keys': keys
        }
    
    async def _execute_keyboard_hotkey(self, params: OperationParameters, context: ExecutionContext) -> Dict[str, Any]:
        """执行组合键"""
        keys = params.keys
        
        # 执行组合键
        pyautogui.hotkey(*keys)
        
        return {
            'action': 'keyboard_hotkey',
            'keys': keys
        }
    
    async def _execute_wait(self, params: OperationParameters, context: ExecutionContext) -> Dict[str, Any]:
        """执行等待"""
        duration = params.custom_params['duration']
        
        await asyncio.sleep(duration)
        
        return {
            'action': 'wait',
            'duration': duration
        }
    
    def _update_stats(self, success: bool, execution_time: float):
        """更新执行统计"""
        self.execution_stats['total_operations'] += 1
        
        if success:
            self.execution_stats['successful_operations'] += 1
        else:
            self.execution_stats['failed_operations'] += 1
        
        # 更新平均执行时间
        total = self.execution_stats['total_operations']
        current_avg = self.execution_stats['average_execution_time']
        self.execution_stats['average_execution_time'] = (
            (current_avg * (total - 1) + execution_time) / total
        )
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计"""
        stats = self.execution_stats.copy()
        total = stats['total_operations']
        stats['success_rate'] = (stats['successful_operations'] / total) if total > 0 else 0.0
        return stats
    
    def update_precision_config(self, config: Dict[str, Any]):
        """更新精度配置"""
        self.precision_config.update(config)
        self.logger.info(f"精度配置已更新: {config}")
    
    async def test_basic_operations(self) -> Dict[str, Any]:
        """测试基础操作"""
        test_results = {}
        
        try:
            # 测试鼠标位置获取
            current_pos = pyautogui.position()
            test_results['mouse_position'] = {
                'success': True,
                'position': {'x': current_pos.x, 'y': current_pos.y}
            }
        except Exception as e:
            test_results['mouse_position'] = {'success': False, 'error': str(e)}
        
        try:
            # 测试屏幕尺寸获取
            screen_size = pyautogui.size()
            test_results['screen_size'] = {
                'success': True,
                'size': {'width': screen_size.width, 'height': screen_size.height}
            }
        except Exception as e:
            test_results['screen_size'] = {'success': False, 'error': str(e)}
        
        return test_results

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
记忆管理器

实现QYuan的记忆存储、检索和管理功能，支持分层记忆架构
"""

import logging
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from ..database.database_manager import db_manager
from ..database.models import (
    Memory, Conversation, ActionRecord, KnowledgePattern, 
    Context, MemoryIndex, MemoryType, EventType
)

logger = logging.getLogger(__name__)


class MemoryManager:
    """记忆管理器"""
    
    def __init__(self):
        """初始化记忆管理器"""
        self.db_manager = db_manager
        self._initialized = False
    
    async def initialize(self) -> bool:
        """
        初始化记忆管理器
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 初始化数据库
            if not self.db_manager.initialize():
                logger.error("数据库初始化失败")
                return False
            
            # 测试数据库连接
            if not self.db_manager.test_connection():
                logger.error("数据库连接测试失败")
                return False
            
            self._initialized = True
            logger.info("记忆管理器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"记忆管理器初始化失败: {e}")
            return False
    
    def close(self):
        """关闭记忆管理器"""
        try:
            self.db_manager.close()
            self._initialized = False
            logger.info("记忆管理器已关闭")
        except Exception as e:
            logger.error(f"关闭记忆管理器失败: {e}")
    
    # 原始记忆管理
    
    def store_conversation(self, session_id: str, user_message: str, 
                          assistant_response: str, user_intent: str = None,
                          intent_confidence: float = 0.0, context: Dict = None) -> str:
        """
        存储对话记忆
        
        Args:
            session_id: 会话ID
            user_message: 用户消息
            assistant_response: 助手回复
            user_intent: 用户意图
            intent_confidence: 意图置信度
            context: 上下文信息
            
        Returns:
            str: 记忆ID
        """
        try:
            with self.db_manager.get_session() as session:
                # 创建对话记录
                conversation = Conversation(
                    session_id=session_id,
                    user_message=user_message,
                    assistant_response=assistant_response,
                    user_intent=user_intent,
                    intent_confidence=intent_confidence,
                    context=context or {}
                )
                session.add(conversation)
                session.flush()
                
                # 创建对应的记忆记录
                memory = Memory(
                    memory_type=MemoryType.RAW.value,
                    event_type=EventType.CONVERSATION.value,
                    title=f"对话: {user_message[:50]}...",
                    content=json.dumps({
                        "user_message": user_message,
                        "assistant_response": assistant_response,
                        "user_intent": user_intent,
                        "intent_confidence": intent_confidence
                    }, ensure_ascii=False),
                    metadata={
                        "conversation_id": conversation.id,
                        "session_id": session_id,
                        "message_length": len(user_message),
                        "response_length": len(assistant_response)
                    },
                    session_id=session_id,
                    importance_score=self._calculate_conversation_importance(
                        user_message, assistant_response, intent_confidence
                    )
                )
                session.add(memory)
                session.flush()
                
                # 更新对话记录的记忆关联
                conversation.memory_id = memory.id
                
                # 创建索引
                self._create_memory_indexes(session, memory, user_message + " " + assistant_response)
                
                logger.info(f"存储对话记忆成功: {memory.id}")
                return memory.id
                
        except Exception as e:
            logger.error(f"存储对话记忆失败: {e}")
            return None
    
    def store_action_record(self, session_id: str, action_type: str, action_name: str,
                           parameters: Dict, success: bool, result: Dict = None,
                           error_message: str = None, duration_ms: int = None,
                           context: Dict = None, goal_id: str = None) -> str:
        """
        存储操作记录
        
        Args:
            session_id: 会话ID
            action_type: 操作类型
            action_name: 操作名称
            parameters: 操作参数
            success: 是否成功
            result: 操作结果
            error_message: 错误信息
            duration_ms: 执行时长（毫秒）
            context: 上下文信息
            goal_id: 目标ID
            
        Returns:
            str: 记忆ID
        """
        try:
            with self.db_manager.get_session() as session:
                # 创建操作记录
                action_record = ActionRecord(
                    session_id=session_id,
                    action_type=action_type,
                    action_name=action_name,
                    parameters=parameters or {},
                    success=success,
                    result=result or {},
                    error_message=error_message,
                    completed_at=datetime.utcnow(),
                    duration_ms=duration_ms,
                    context=context or {},
                    goal_id=goal_id
                )
                session.add(action_record)
                session.flush()
                
                # 创建对应的记忆记录
                memory = Memory(
                    memory_type=MemoryType.RAW.value,
                    event_type=EventType.ACTION.value,
                    title=f"操作: {action_type}.{action_name}",
                    content=json.dumps({
                        "action_type": action_type,
                        "action_name": action_name,
                        "parameters": parameters,
                        "success": success,
                        "result": result,
                        "error_message": error_message,
                        "duration_ms": duration_ms
                    }, ensure_ascii=False),
                    metadata={
                        "action_record_id": action_record.id,
                        "session_id": session_id,
                        "goal_id": goal_id,
                        "success": success,
                        "duration_ms": duration_ms
                    },
                    session_id=session_id,
                    importance_score=self._calculate_action_importance(
                        action_type, success, duration_ms, error_message
                    )
                )
                session.add(memory)
                session.flush()
                
                # 更新操作记录的记忆关联
                action_record.memory_id = memory.id
                
                # 创建索引
                index_text = f"{action_type} {action_name} {json.dumps(parameters)}"
                self._create_memory_indexes(session, memory, index_text)
                
                logger.info(f"存储操作记忆成功: {memory.id}")
                return memory.id
                
        except Exception as e:
            logger.error(f"存储操作记忆失败: {e}")
            return None
    
    def store_system_event(self, event_type: str, title: str, content: str,
                          metadata: Dict = None, importance_score: float = 0.5) -> str:
        """
        存储系统事件记忆
        
        Args:
            event_type: 事件类型
            title: 事件标题
            content: 事件内容
            metadata: 元数据
            importance_score: 重要性评分
            
        Returns:
            str: 记忆ID
        """
        try:
            with self.db_manager.get_session() as session:
                memory = Memory(
                    memory_type=MemoryType.RAW.value,
                    event_type=EventType.SYSTEM.value,
                    title=title,
                    content=content,
                    metadata=metadata or {},
                    importance_score=importance_score
                )
                session.add(memory)
                session.flush()
                
                # 创建索引
                self._create_memory_indexes(session, memory, f"{title} {content}")
                
                logger.info(f"存储系统事件记忆成功: {memory.id}")
                return memory.id
                
        except Exception as e:
            logger.error(f"存储系统事件记忆失败: {e}")
            return None
    
    # 记忆检索
    
    def search_memories(self, query: str, memory_types: List[str] = None,
                       event_types: List[str] = None, session_id: str = None,
                       limit: int = 10, min_relevance: float = 0.1) -> List[Dict]:
        """
        搜索记忆
        
        Args:
            query: 搜索查询
            memory_types: 记忆类型过滤
            event_types: 事件类型过滤
            session_id: 会话ID过滤
            limit: 返回数量限制
            min_relevance: 最小相关性阈值
            
        Returns:
            List[Dict]: 搜索结果
        """
        try:
            with self.db_manager.get_session() as session:
                # 构建查询
                query_obj = session.query(Memory).filter(Memory.is_active == True)
                
                # 添加过滤条件
                if memory_types:
                    query_obj = query_obj.filter(Memory.memory_type.in_(memory_types))
                
                if event_types:
                    query_obj = query_obj.filter(Memory.event_type.in_(event_types))
                
                if session_id:
                    query_obj = query_obj.filter(Memory.session_id == session_id)
                
                # 关键词搜索
                if query:
                    search_terms = query.lower().split()
                    for term in search_terms:
                        query_obj = query_obj.filter(
                            or_(
                                Memory.title.ilike(f"%{term}%"),
                                Memory.content.ilike(f"%{term}%")
                            )
                        )
                
                # 按重要性和时间排序
                memories = query_obj.order_by(
                    desc(Memory.importance_score),
                    desc(Memory.created_at)
                ).limit(limit).all()
                
                # 转换为字典格式
                results = []
                for memory in memories:
                    result = {
                        "id": memory.id,
                        "memory_type": memory.memory_type,
                        "event_type": memory.event_type,
                        "title": memory.title,
                        "content": memory.content,
                        "metadata": memory.metadata,
                        "importance_score": memory.importance_score,
                        "created_at": memory.created_at.isoformat(),
                        "session_id": memory.session_id,
                        "tags": memory.tags
                    }
                    results.append(result)
                
                logger.info(f"搜索记忆完成，找到 {len(results)} 条结果")
                return results
                
        except Exception as e:
            logger.error(f"搜索记忆失败: {e}")
            return []
    
    def get_recent_memories(self, session_id: str = None, hours: int = 24,
                           memory_types: List[str] = None, limit: int = 20) -> List[Dict]:
        """
        获取最近的记忆
        
        Args:
            session_id: 会话ID过滤
            hours: 时间范围（小时）
            memory_types: 记忆类型过滤
            limit: 返回数量限制
            
        Returns:
            List[Dict]: 最近的记忆
        """
        try:
            with self.db_manager.get_session() as session:
                # 计算时间范围
                since_time = datetime.utcnow() - timedelta(hours=hours)
                
                # 构建查询
                query_obj = session.query(Memory).filter(
                    and_(
                        Memory.is_active == True,
                        Memory.created_at >= since_time
                    )
                )
                
                # 添加过滤条件
                if session_id:
                    query_obj = query_obj.filter(Memory.session_id == session_id)
                
                if memory_types:
                    query_obj = query_obj.filter(Memory.memory_type.in_(memory_types))
                
                # 按时间倒序排列
                memories = query_obj.order_by(desc(Memory.created_at)).limit(limit).all()
                
                # 转换为字典格式
                results = []
                for memory in memories:
                    result = {
                        "id": memory.id,
                        "memory_type": memory.memory_type,
                        "event_type": memory.event_type,
                        "title": memory.title,
                        "content": memory.content,
                        "metadata": memory.metadata,
                        "importance_score": memory.importance_score,
                        "created_at": memory.created_at.isoformat(),
                        "session_id": memory.session_id
                    }
                    results.append(result)
                
                logger.info(f"获取最近记忆完成，找到 {len(results)} 条记录")
                return results
                
        except Exception as e:
            logger.error(f"获取最近记忆失败: {e}")
            return []

    # 上下文管理

    def create_context(self, session_id: str, context_type: str,
                      initial_state: Dict = None, metadata: Dict = None) -> str:
        """
        创建上下文

        Args:
            session_id: 会话ID
            context_type: 上下文类型
            initial_state: 初始状态
            metadata: 元数据

        Returns:
            str: 上下文ID
        """
        try:
            with self.db_manager.get_session() as session:
                context = Context(
                    session_id=session_id,
                    context_type=context_type,
                    current_state=initial_state or {},
                    metadata=metadata or {}
                )
                session.add(context)
                session.flush()

                logger.info(f"创建上下文成功: {context.id}")
                return context.id

        except Exception as e:
            logger.error(f"创建上下文失败: {e}")
            return None

    def update_context(self, context_id: str, new_state: Dict,
                      append_to_history: bool = True) -> bool:
        """
        更新上下文

        Args:
            context_id: 上下文ID
            new_state: 新状态
            append_to_history: 是否添加到历史记录

        Returns:
            bool: 更新是否成功
        """
        try:
            with self.db_manager.get_session() as session:
                context = session.query(Context).filter(Context.id == context_id).first()
                if not context:
                    logger.error(f"上下文不存在: {context_id}")
                    return False

                # 保存历史状态
                if append_to_history and context.current_state:
                    if not context.history:
                        context.history = []
                    context.history.append({
                        "state": context.current_state,
                        "timestamp": datetime.utcnow().isoformat()
                    })

                # 更新当前状态
                context.current_state = new_state
                context.updated_at = datetime.utcnow()

                logger.info(f"更新上下文成功: {context_id}")
                return True

        except Exception as e:
            logger.error(f"更新上下文失败: {e}")
            return False

    def get_context(self, context_id: str) -> Optional[Dict]:
        """
        获取上下文

        Args:
            context_id: 上下文ID

        Returns:
            Optional[Dict]: 上下文信息
        """
        try:
            with self.db_manager.get_session() as session:
                context = session.query(Context).filter(Context.id == context_id).first()
                if not context:
                    return None

                return {
                    "id": context.id,
                    "session_id": context.session_id,
                    "context_type": context.context_type,
                    "current_state": context.current_state,
                    "history": context.history or [],
                    "is_active": context.is_active,
                    "created_at": context.created_at.isoformat(),
                    "updated_at": context.updated_at.isoformat(),
                    "metadata": context.metadata
                }

        except Exception as e:
            logger.error(f"获取上下文失败: {e}")
            return None

    def get_session_contexts(self, session_id: str, active_only: bool = True) -> List[Dict]:
        """
        获取会话的所有上下文

        Args:
            session_id: 会话ID
            active_only: 是否只返回活跃的上下文

        Returns:
            List[Dict]: 上下文列表
        """
        try:
            with self.db_manager.get_session() as session:
                query_obj = session.query(Context).filter(Context.session_id == session_id)

                if active_only:
                    query_obj = query_obj.filter(Context.is_active == True)

                contexts = query_obj.order_by(desc(Context.updated_at)).all()

                results = []
                for ctx in contexts:
                    result = {
                        "id": ctx.id,
                        "context_type": ctx.context_type,
                        "current_state": ctx.current_state,
                        "is_active": ctx.is_active,
                        "created_at": ctx.created_at.isoformat(),
                        "updated_at": ctx.updated_at.isoformat()
                    }
                    results.append(result)

                return results

        except Exception as e:
            logger.error(f"获取会话上下文失败: {e}")
            return []

    # 辅助方法

    def _calculate_conversation_importance(self, user_message: str,
                                         assistant_response: str,
                                         intent_confidence: float) -> float:
        """计算对话重要性评分"""
        score = 0.5  # 基础分数

        # 根据消息长度调整
        if len(user_message) > 100:
            score += 0.1

        # 根据意图置信度调整
        score += intent_confidence * 0.3

        # 根据特定关键词调整
        important_keywords = ["错误", "问题", "帮助", "重要", "紧急"]
        for keyword in important_keywords:
            if keyword in user_message:
                score += 0.1
                break

        return min(score, 1.0)

    def _calculate_action_importance(self, action_type: str, success: bool,
                                   duration_ms: int, error_message: str) -> float:
        """计算操作重要性评分"""
        score = 0.5  # 基础分数

        # 失败的操作更重要
        if not success:
            score += 0.3

        # 有错误信息的操作更重要
        if error_message:
            score += 0.2

        # 执行时间长的操作更重要
        if duration_ms and duration_ms > 5000:  # 超过5秒
            score += 0.1

        # 某些操作类型更重要
        important_actions = ["vision", "system"]
        if action_type in important_actions:
            score += 0.1

        return min(score, 1.0)

    def _create_memory_indexes(self, session: Session, memory: Memory, text: str):
        """为记忆创建索引"""
        try:
            # 关键词索引
            keywords = self._extract_keywords(text)
            for keyword in keywords:
                index = MemoryIndex(
                    memory_id=memory.id,
                    index_type="keyword",
                    index_key=keyword,
                    index_value=keyword,
                    weight=1.0
                )
                session.add(index)

            # 时间索引
            time_index = MemoryIndex(
                memory_id=memory.id,
                index_type="temporal",
                index_key="created_at",
                index_value=memory.created_at.isoformat(),
                weight=1.0
            )
            session.add(time_index)

        except Exception as e:
            logger.error(f"创建记忆索引失败: {e}")

    def _extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """从文本中提取关键词"""
        # 简单的关键词提取（可以后续优化为更复杂的NLP方法）
        import re

        # 移除标点符号，转换为小写
        text = re.sub(r'[^\w\s]', ' ', text.lower())

        # 分词
        words = text.split()

        # 过滤停用词和短词
        stop_words = {"的", "是", "在", "了", "和", "与", "或", "但", "如果", "因为", "所以"}
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]

        # 去重并限制数量
        keywords = list(set(keywords))[:max_keywords]

        return keywords

    # 统计和管理方法

    def get_memory_stats(self) -> Dict:
        """获取记忆统计信息"""
        try:
            stats = self.db_manager.get_table_stats()

            with self.db_manager.get_session() as session:
                # 按类型统计记忆
                memory_type_stats = {}
                for memory_type in MemoryType:
                    count = session.query(Memory).filter(
                        Memory.memory_type == memory_type.value
                    ).count()
                    memory_type_stats[memory_type.value] = count

                # 按事件类型统计
                event_type_stats = {}
                for event_type in EventType:
                    count = session.query(Memory).filter(
                        Memory.event_type == event_type.value
                    ).count()
                    event_type_stats[event_type.value] = count

                # 最近活动统计
                recent_24h = session.query(Memory).filter(
                    Memory.created_at >= datetime.utcnow() - timedelta(hours=24)
                ).count()

                recent_7d = session.query(Memory).filter(
                    Memory.created_at >= datetime.utcnow() - timedelta(days=7)
                ).count()

            return {
                "table_stats": stats,
                "memory_type_stats": memory_type_stats,
                "event_type_stats": event_type_stats,
                "recent_24h": recent_24h,
                "recent_7d": recent_7d,
                "database_info": self.db_manager.get_database_info()
            }

        except Exception as e:
            logger.error(f"获取记忆统计失败: {e}")
            return {}

    def cleanup_old_memories(self, days: int = 30, keep_important: bool = True) -> int:
        """
        清理旧记忆

        Args:
            days: 保留天数
            keep_important: 是否保留重要记忆

        Returns:
            int: 清理的记忆数量
        """
        try:
            with self.db_manager.get_session() as session:
                cutoff_date = datetime.utcnow() - timedelta(days=days)

                query_obj = session.query(Memory).filter(
                    Memory.created_at < cutoff_date
                )

                if keep_important:
                    query_obj = query_obj.filter(Memory.importance_score < 0.7)

                # 标记为非活跃而不是删除
                count = query_obj.update({"is_active": False})

                logger.info(f"清理旧记忆完成，标记 {count} 条记忆为非活跃")
                return count

        except Exception as e:
            logger.error(f"清理旧记忆失败: {e}")
            return 0

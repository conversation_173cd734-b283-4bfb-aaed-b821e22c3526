#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试UI自动化和屏幕截图集成
"""

import os
import sys
import time

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 导入UI自动化模块
from src.ui_automation.ui_automation_factory import UIAutomationFactory

# 导入屏幕截图模块
from src.screen_capture import RegionScreenCapture


def test_chrome_region_capture():
    """测试Chrome窗口区域截图"""
    print("测试Chrome窗口区域截图...")

    # 创建UI自动化对象
    ui = UIAutomationFactory.create(backend="win32gui")

    # 创建区域截图对象
    region_capture = RegionScreenCapture()

    # 1. 查找Chrome窗口
    print("\n1. 查找Chrome窗口...")
    windows = ui.get_window_by_title("Chrome", partial_match=True)
    if not windows:
        print("未找到Chrome窗口")
        return False

    # 获取窗口信息
    window = windows[0]
    window_info = ui.get_window_info(window)
    if not window_info:
        print("无法获取窗口信息")
        return False

    print(f"找到Chrome窗口: {window_info['title']}")

    # 2. 激活Chrome窗口
    print("\n2. 激活Chrome窗口...")
    if window_info['state']['minimized']:
        print("Chrome窗口已最小化，正在恢复...")
        ui.restore_window(window)
        time.sleep(1)

    ui.activate_window(window)
    time.sleep(1)
    print("Chrome窗口已激活")

    # 重新获取窗口信息（激活后可能位置有变化）
    window_info = ui.get_window_info(window)

    # 3. 获取Chrome窗口坐标
    print("\n3. 获取Chrome窗口坐标...")
    rect = window_info["rectangle"]
    left = rect["left"]
    top = rect["top"]
    right = rect["right"]
    bottom = rect["bottom"]
    width = rect["width"]
    height = rect["height"]

    print(f"Chrome窗口坐标: 左上({left}, {top}), 右下({right}, {bottom}), 尺寸({width} x {height})")

    # 4. 截取Chrome窗口区域
    print("\n4. 截取Chrome窗口区域...")
    try:
        screenshot, save_path = region_capture.capture_region(
            left, top, width, height, suffix="chrome_full"
        )
        print(f"Chrome窗口截图已保存至: {save_path}")

        # 检查文件是否存在
        if os.path.exists(save_path):
            print(f"截图文件存在，大小: {os.path.getsize(save_path) / 1024:.2f} KB")
        else:
            print("截图文件不存在！")
    except Exception as e:
        print(f"截图出错: {e}")

    # 5. 模拟LLM指定区域截图
    print("\n5. 模拟LLM指定区域截图...")

    # 模拟LLM指定的区域坐标（例如，窗口中的一个按钮或文本区域）
    custom_x = left + width // 3
    custom_y = top + height // 3
    custom_width = width // 4
    custom_height = height // 4

    try:
        screenshot, save_path = region_capture.capture_region(
            custom_x, custom_y, custom_width, custom_height, suffix="llm_specified_region"
        )
        print(f"LLM指定区域截图已保存至: {save_path}")
        print(f"区域坐标: 左上角({custom_x}, {custom_y}), 尺寸({custom_width} x {custom_height})")
    except Exception as e:
        print(f"截图出错: {e}")

    print("\n测试完成!")
    return True


if __name__ == "__main__":
    # 确保测试目录存在
    os.makedirs("tests", exist_ok=True)

    # 测试Chrome窗口区域截图
    chrome_result = test_chrome_region_capture()

    # 输出总结果
    print("\n测试结果:")
    print(f"Chrome窗口区域截图: {'成功' if chrome_result else '失败'}")

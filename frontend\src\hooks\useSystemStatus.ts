// -*- coding: utf-8 -*-

import { useState, useEffect, useCallback } from 'react';
import { SystemStatus } from '../types';
import { ApiService } from '../services/api';

/**
 * 系统状态Hook
 * 专门负责系统状态数据管理
 * 严格按照代码规范的单一职责原则
 */
export function useSystemStatus() {
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  const apiService = new ApiService();

  /**
   * 获取系统状态
   */
  const fetchSystemStatus = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiService.getSystemStatus();
      if (response.success && response.data) {
        setStatus(response.data);
        setLastUpdate(new Date());
      } else {
        setError(response.error || '获取系统状态失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '网络错误');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 获取健康检查
   */
  const checkHealth = useCallback(async () => {
    try {
      const response = await apiService.getHealthCheck();
      return response.success;
    } catch (err) {
      console.error('健康检查失败:', err);
      return false;
    }
  }, []);

  /**
   * 获取系统统计信息
   */
  const fetchSystemStats = useCallback(async () => {
    try {
      const response = await apiService.getSystemStats();
      return response.data;
    } catch (err) {
      console.error('获取系统统计失败:', err);
      return null;
    }
  }, []);

  /**
   * 刷新系统状态
   */
  const refresh = useCallback(() => {
    fetchSystemStatus();
  }, [fetchSystemStatus]);

  /**
   * 自动刷新系统状态
   */
  useEffect(() => {
    // 初始加载
    fetchSystemStatus();

    // 设置定时刷新（每30秒）
    const interval = setInterval(() => {
      fetchSystemStatus();
    }, 30000);

    return () => {
      clearInterval(interval);
    };
  }, [fetchSystemStatus]);

  /**
   * 计算系统整体健康状态
   */
  const getOverallHealth = useCallback(() => {
    if (!status) return 'unknown';

    const engineHealthCounts = status.engines.reduce(
      (acc, engine) => {
        acc[engine.health] = (acc[engine.health] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    if (engineHealthCounts.critical > 0) return 'critical';
    if (engineHealthCounts.warning > 0) return 'warning';
    return 'healthy';
  }, [status]);

  /**
   * 获取运行中的引擎数量
   */
  const getRunningEnginesCount = useCallback(() => {
    if (!status) return 0;
    return status.engines.filter(engine => engine.status === 'running').length;
  }, [status]);

  /**
   * 获取引擎总数
   */
  const getTotalEnginesCount = useCallback(() => {
    if (!status) return 0;
    return status.engines.length;
  }, [status]);

  /**
   * 检查是否在线
   */
  const isOnline = useCallback(() => {
    return status?.isOnline || false;
  }, [status]);

  /**
   * 获取性能指标摘要
   */
  const getPerformanceSummary = useCallback(() => {
    if (!status?.performance) return null;

    const { performance } = status;
    return {
      cpuUsage: `${performance.cpuUsage.toFixed(1)}%`,
      memoryUsage: `${performance.memoryUsage.toFixed(1)}%`,
      responseTime: `${performance.responseTime.toFixed(0)}ms`,
      requestCount: performance.requestCount.toLocaleString(),
      errorRate: `${(performance.errorRate * 100).toFixed(2)}%`,
    };
  }, [status]);

  return {
    status,
    loading,
    error,
    lastUpdate,
    fetchSystemStatus,
    checkHealth,
    fetchSystemStats,
    refresh,
    getOverallHealth,
    getRunningEnginesCount,
    getTotalEnginesCount,
    isOnline,
    getPerformanceSummary,
  };
}

# 屏幕分析模块

本模块提供屏幕分析功能，通过辅助LLM分析屏幕截图，为主LLM提供视觉信息。

## 模块目的

屏幕分析模块的主要目的是为与用户交互的主LLM提供视觉信息。由于MCP服务器不能直接将图像返回给主LLM，只能返回文本信息，所以我们使用辅助LLM（如Gemini）来分析截图，并将分析结果返回给主LLM。

模块提供以下核心功能：

1. **全屏分析**：捕获整个屏幕，通过辅助LLM分析，并将分析结果返回给主LLM
2. **区域分析**：捕获指定区域，通过辅助LLM分析，并将分析结果返回给主LLM
3. **坐标增强**：为截图添加经纬线和坐标标注，帮助辅助LLM更准确地描述屏幕上元素的位置

这种设计模拟了人类的视觉体验：先看整个屏幕，然后聚焦于特定区域，并理解所看到的内容。单纯的截图功能没有实际意义，因为主LLM无法直接查看图像，必须通过辅助LLM的分析结果来"看到"屏幕内容。

## 模块结构

- `__init__.py`: 包初始化文件，导出主要的类和接口
- `screen_capture_base.py`: 定义屏幕截图的基础接口类，作为所有实现的基类
- `screen_capture_factory.py`: 提供创建不同类型屏幕截图对象的工厂类
- `screen_capture_full.py`: 实现全屏截图功能
- `screen_capture_region.py`: 实现区域截图功能
- `vision_llm/`: 视觉LLM相关功能
  - `image_enhancer.py`: 提供图像增强功能，包括添加经纬线和坐标标注
  - `vision_llm.py`: 提供与视觉LLM（如Gemini）的连接功能
  - `screen_analyzer.py`: 提供屏幕分析服务，结合屏幕截图、图像增强和视觉LLM功能

## 使用方法

### 屏幕分析功能

```python
from src.screen_capture import ScreenCapture

# 创建屏幕分析对象
screen_capture = ScreenCapture()  # ScreenCapture 现在是 ScreenAnalyzer 的别名

# 捕获并分析全屏
result = screen_capture.capture_and_analyze_screen(
    question="这个屏幕上有什么内容？请描述主要元素及其位置。"
)
print(f"分析结果:\n{result['analysis']}")

# 捕获并分析区域
region = (100, 100, 400, 300)  # x, y, width, height
result = screen_capture.capture_and_analyze_screen(
    question="这个区域中有什么内容？请描述主要元素及其位置。",
    region=region
)
print(f"分析结果:\n{result['analysis']}")

# 分析最新的屏幕截图
result = screen_capture.analyze_latest_screenshot(
    question="屏幕上有什么颜色？"
)
print(f"分析结果:\n{result['analysis']}")
```

### 在MCP服务器中使用

```python
# 在MCP服务器中，可以使用以下工具来分析屏幕

# 分析全屏
result = await analyze_screen(question="这个屏幕上有什么内容？请描述主要元素及其位置。")
print(result["vision_analysis"])

# 分析区域
result = await analyze_screen_region(
    question="这个区域中有什么内容？请描述主要元素及其位置。",
    x=100, y=100, width=400, height=300
)
print(result["vision_analysis"])

# 分析最新的屏幕截图
result = await analyze_latest_screenshot(question="屏幕上有什么颜色？")
print(result["vision_analysis"])

# 组合分析（可以同时获取OCR、UI元素和视觉LLM分析结果）
result = await capture_and_analyze_screen(
    question="这个屏幕上有什么内容？请描述主要元素及其位置。",
    analysis_type="combined"
)
print(result["vision_analysis"])  # 视觉LLM分析结果
print(result["ocr"])  # OCR文本识别结果
print(result["ui_elements"])  # UI元素识别结果
```

## 功能特点

### 全屏分析

全屏分析功能可以捕获整个屏幕的内容，通过辅助LLM分析，并将分析结果返回给主LLM。这通常是主LLM首先请求的视觉信息，用于获取整体视觉上下文。

### 区域分析

区域分析功能可以捕获主LLM指定的屏幕区域，通过辅助LLM分析，并将分析结果返回给主LLM。当主LLM需要更详细地查看特定区域时，可以提供具体的坐标，请求区域分析。

### 图像增强

图像增强功能由`ImageEnhancer`类提供，可以为图像添加经纬线和坐标标注，帮助辅助LLM更准确地描述屏幕上元素的位置。

增强后的图像具有以下特点：

- 在原始图像的四周添加了100像素的白边，并在白边上标注了坐标，提供更充足的空间显示坐标信息
- 使用红色粗线（2像素宽）绘制经纬线，使其更加显眼
- 经纬线间距为200像素，避免过于密集
- 在图像四个角落的白边区域标注了屏幕边界坐标，让LLM知道屏幕的最大范围
- 使用较大字体（36像素）标注坐标，配合扩大的白边，显著提高可读性
- 为坐标标注添加了彩色背景（黄色用于角落坐标，浅蓝色用于Y轴坐标，浅绿色用于X轴坐标），使其更加醒目
- 优化了坐标标注的位置，确保所有标注都在白边区域内，避免与画面重合
- 改进了坐标标注的对齐方式，使其更加整齐美观

### 视觉LLM分析

视觉LLM分析功能由`VisionLLM`类提供，可以将图像发送给Gemini等视觉LLM进行分析，并将分析结果返回给主LLM。

当前使用的是Gemini 2.5 Flash Preview模型，可以通过API密钥和模型名称进行配置。

### 文件命名

截图文件名包含时间戳和毫秒级时间戳，以及可选的后缀，确保文件名唯一，方便审核和区分不同的截图。

文件名格式：`screenshot_YYYYMMDD_HHMMSS_mmm_suffix.png`

## 开发历史

### 2023-05-19

- 初始版本
- 实现了全屏截图和区域截图功能
- 添加了文件名生成逻辑，包含时间戳和毫秒级时间戳，以及可选的后缀
- 重构代码，按照UI自动化工具的目录逻辑，创建了单独的目录结构
- 将功能分离到不同的文件中，提高了代码的可维护性
- 移除了测试代码，创建了单独的测试文件

### 2023-05-20

- 简化了功能，专注于为LLM提供视觉信息
- 明确了模块的目的：提供全屏截图和LLM指定区域的截图
- 更新了文档，明确说明截图功能的真正目的和使用方式
- 简化了测试代码，更符合实际使用场景
- 添加了视觉LLM分析功能，包括图像增强和屏幕分析服务
- 将视觉LLM功能整合到屏幕截图模块中，使其成为截图工具的一部分
- 更新了文档，添加了视觉LLM分析功能的使用方法和说明

### 2023-05-21

- 重新设计模块接口，确保所有截图操作都会通过辅助LLM进行分析
- 移除单独的截图功能，或者将其设为私有方法
- 更新MCP服务器，只提供带分析结果的截图功能
- 更新文档，明确说明截图必须通过辅助LLM进行分析
- 将`ScreenCapture`设为`ScreenAnalyzer`的别名，使其成为主要的对外接口
- 简化了导出的符号，只导出带分析功能的接口
- 改进了图像增强功能：
  - 将经纬线从灰色细线改为红色粗线（2像素宽），使其更加显眼
  - 将网格间距从100像素改为200像素，避免过于密集
  - 在图像四个角落添加了屏幕边界坐标标注，让LLM知道屏幕的最大范围
  - 使用更大的字体（36像素）标注坐标，显著提高可读性
  - 为坐标标注添加了彩色背景（黄色用于角落坐标，浅蓝色用于Y轴坐标，浅绿色用于X轴坐标），使其更加醒目
  - 优化了坐标标注的位置，确保所有标注都在白边区域内，避免与画面重合
  - 改进了坐标标注的对齐方式，使其更加整齐美观
  - 使用系统字体（Arial）替代默认字体，确保字体大小设置生效

## 依赖

- PIL (Pillow): `pip install pillow`
- PyAutoGUI: `pip install pyautogui`
- Requests: `pip install requests`
- NumPy: `pip install numpy`

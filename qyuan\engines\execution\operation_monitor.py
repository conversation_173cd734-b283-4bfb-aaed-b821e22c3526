# -*- coding: utf-8 -*-
"""
操作监控器实现
专门负责监控操作执行过程，严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
import psutil
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from .base import (
    OperationMonitorBase,
    OperationParameters,
    MonitoringData,
    ExecutionStatus,
    OperationType
)

class RealTimeOperationMonitor(OperationMonitorBase):
    """实时操作监控器"""
    
    def __init__(self):
        super().__init__("RealTimeOperationMonitor")
        self.logger = logging.getLogger(f"QYuan.Execution.{self.name}")
        
        # 监控数据存储
        self.monitoring_sessions: Dict[str, MonitoringData] = {}
        self.monitoring_tasks: Dict[str, asyncio.Task] = {}
        
        # 监控配置
        self.monitor_config = {
            'update_interval': 0.1,  # 监控更新间隔（秒）
            'max_monitoring_time': 300,  # 最大监控时间（秒）
            'performance_sampling_rate': 1.0,  # 性能采样率（秒）
            'resource_monitoring': True,  # 是否监控资源使用
            'detailed_logging': False  # 是否详细日志
        }
        
        # 性能基线
        self.performance_baselines = {
            OperationType.MOUSE_CLICK: {'expected_time': 0.5, 'max_time': 2.0},
            OperationType.MOUSE_MOVE: {'expected_time': 0.3, 'max_time': 1.0},
            OperationType.KEYBOARD_TYPE: {'expected_time': 1.0, 'max_time': 5.0},
            OperationType.WAIT: {'expected_time': None, 'max_time': None}
        }
    
    async def start_monitoring(self, operation_id: str, params: OperationParameters) -> bool:
        """开始监控操作"""
        try:
            if operation_id in self.monitoring_sessions:
                self.logger.warning(f"操作 {operation_id} 已在监控中")
                return False
            
            # 创建监控数据
            monitoring_data = MonitoringData(
                operation_id=operation_id,
                start_time=datetime.now(),
                current_status=ExecutionStatus.PENDING,
                progress=0.0,
                performance_metrics={},
                resource_usage={}
            )
            
            self.monitoring_sessions[operation_id] = monitoring_data
            
            # 启动监控任务
            monitor_task = asyncio.create_task(
                self._monitor_operation(operation_id, params)
            )
            self.monitoring_tasks[operation_id] = monitor_task
            
            self.logger.debug(f"开始监控操作: {operation_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动监控失败: {e}")
            return False
    
    async def get_monitoring_data(self, operation_id: str) -> Optional[MonitoringData]:
        """获取监控数据"""
        return self.monitoring_sessions.get(operation_id)
    
    async def stop_monitoring(self, operation_id: str) -> bool:
        """停止监控操作"""
        try:
            # 取消监控任务
            if operation_id in self.monitoring_tasks:
                task = self.monitoring_tasks[operation_id]
                if not task.done():
                    task.cancel()
                del self.monitoring_tasks[operation_id]
            
            # 更新监控数据状态
            if operation_id in self.monitoring_sessions:
                monitoring_data = self.monitoring_sessions[operation_id]
                if monitoring_data.current_status == ExecutionStatus.RUNNING:
                    monitoring_data.current_status = ExecutionStatus.CANCELLED
            
            self.logger.debug(f"停止监控操作: {operation_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"停止监控失败: {e}")
            return False
    
    async def _monitor_operation(self, operation_id: str, params: OperationParameters):
        """监控操作执行过程"""
        try:
            monitoring_data = self.monitoring_sessions[operation_id]
            start_time = time.time()
            
            while True:
                current_time = time.time()
                elapsed_time = current_time - start_time
                
                # 检查超时
                if elapsed_time > self.monitor_config['max_monitoring_time']:
                    monitoring_data.current_status = ExecutionStatus.TIMEOUT
                    break
                
                # 更新监控数据
                await self._update_monitoring_data(monitoring_data, params, elapsed_time)
                
                # 检查操作是否完成
                if monitoring_data.current_status in [
                    ExecutionStatus.SUCCESS, 
                    ExecutionStatus.FAILED, 
                    ExecutionStatus.CANCELLED
                ]:
                    break
                
                # 等待下次更新
                await asyncio.sleep(self.monitor_config['update_interval'])
                
        except asyncio.CancelledError:
            self.logger.debug(f"监控任务被取消: {operation_id}")
        except Exception as e:
            self.logger.error(f"监控过程出错: {e}")
            if operation_id in self.monitoring_sessions:
                self.monitoring_sessions[operation_id].current_status = ExecutionStatus.FAILED
    
    async def _update_monitoring_data(self, monitoring_data: MonitoringData, params: OperationParameters, elapsed_time: float):
        """更新监控数据"""
        try:
            # 更新基本信息
            monitoring_data.progress = self._calculate_progress(params, elapsed_time)
            
            # 更新性能指标
            monitoring_data.performance_metrics.update({
                'elapsed_time': elapsed_time,
                'expected_completion': self._estimate_completion_time(params, elapsed_time),
                'performance_score': self._calculate_performance_score(params, elapsed_time)
            })
            
            # 更新资源使用情况
            if self.monitor_config['resource_monitoring']:
                monitoring_data.resource_usage.update(await self._get_resource_usage())
            
            # 检测异常情况
            anomalies = self._detect_anomalies(monitoring_data, params)
            if anomalies:
                monitoring_data.performance_metrics['anomalies'] = anomalies
            
        except Exception as e:
            self.logger.error(f"更新监控数据失败: {e}")
    
    def _calculate_progress(self, params: OperationParameters, elapsed_time: float) -> float:
        """计算操作进度"""
        # 根据操作类型和预期时间计算进度
        baseline = self.performance_baselines.get(params.operation_type)
        if not baseline or baseline['expected_time'] is None:
            return 0.0  # 无法估算进度的操作
        
        expected_time = baseline['expected_time']
        progress = min(1.0, elapsed_time / expected_time)
        
        return progress
    
    def _estimate_completion_time(self, params: OperationParameters, elapsed_time: float) -> Optional[float]:
        """估算完成时间"""
        baseline = self.performance_baselines.get(params.operation_type)
        if not baseline or baseline['expected_time'] is None:
            return None
        
        expected_time = baseline['expected_time']
        
        # 如果已经超过预期时间，使用最大时间估算
        if elapsed_time > expected_time:
            return baseline.get('max_time', expected_time * 2)
        
        return expected_time
    
    def _calculate_performance_score(self, params: OperationParameters, elapsed_time: float) -> float:
        """计算性能分数"""
        baseline = self.performance_baselines.get(params.operation_type)
        if not baseline or baseline['expected_time'] is None:
            return 1.0
        
        expected_time = baseline['expected_time']
        
        # 性能分数：越快越好，超过预期时间分数下降
        if elapsed_time <= expected_time:
            return 1.0
        else:
            max_time = baseline.get('max_time', expected_time * 2)
            if elapsed_time >= max_time:
                return 0.0
            else:
                return 1.0 - (elapsed_time - expected_time) / (max_time - expected_time)
    
    async def _get_resource_usage(self) -> Dict[str, float]:
        """获取资源使用情况"""
        try:
            # 获取CPU和内存使用率
            cpu_percent = psutil.cpu_percent(interval=None)
            memory_info = psutil.virtual_memory()
            
            # 获取当前进程的资源使用
            current_process = psutil.Process()
            process_cpu = current_process.cpu_percent()
            process_memory = current_process.memory_info().rss / 1024 / 1024  # MB
            
            return {
                'system_cpu_percent': cpu_percent,
                'system_memory_percent': memory_info.percent,
                'process_cpu_percent': process_cpu,
                'process_memory_mb': process_memory
            }
            
        except Exception as e:
            self.logger.warning(f"获取资源使用情况失败: {e}")
            return {}
    
    def _detect_anomalies(self, monitoring_data: MonitoringData, params: OperationParameters) -> List[str]:
        """检测异常情况"""
        anomalies = []
        
        try:
            # 检查执行时间异常
            elapsed_time = monitoring_data.performance_metrics.get('elapsed_time', 0)
            baseline = self.performance_baselines.get(params.operation_type)
            
            if baseline and baseline['max_time']:
                if elapsed_time > baseline['max_time']:
                    anomalies.append(f"执行时间超过最大预期: {elapsed_time:.2f}s > {baseline['max_time']}s")
            
            # 检查资源使用异常
            resource_usage = monitoring_data.resource_usage
            if resource_usage:
                cpu_percent = resource_usage.get('system_cpu_percent', 0)
                memory_percent = resource_usage.get('system_memory_percent', 0)
                
                if cpu_percent > 90:
                    anomalies.append(f"系统CPU使用率过高: {cpu_percent:.1f}%")
                
                if memory_percent > 90:
                    anomalies.append(f"系统内存使用率过高: {memory_percent:.1f}%")
            
            # 检查性能分数异常
            performance_score = monitoring_data.performance_metrics.get('performance_score', 1.0)
            if performance_score < 0.3:
                anomalies.append(f"性能分数过低: {performance_score:.2f}")
            
        except Exception as e:
            self.logger.warning(f"异常检测失败: {e}")
        
        return anomalies
    
    def update_performance_baseline(self, operation_type: OperationType, baseline: Dict[str, float]):
        """更新性能基线"""
        self.performance_baselines[operation_type] = baseline
        self.logger.info(f"更新性能基线 {operation_type.value}: {baseline}")
    
    def get_monitoring_summary(self) -> Dict[str, Any]:
        """获取监控摘要"""
        active_sessions = len(self.monitoring_sessions)
        running_tasks = len([task for task in self.monitoring_tasks.values() if not task.done()])
        
        # 统计各状态的操作数量
        status_counts = {}
        for monitoring_data in self.monitoring_sessions.values():
            status = monitoring_data.current_status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            'active_sessions': active_sessions,
            'running_tasks': running_tasks,
            'status_distribution': status_counts,
            'config': self.monitor_config,
            'baselines': {op_type.value: baseline for op_type, baseline in self.performance_baselines.items()}
        }
    
    async def cleanup_completed_sessions(self, max_age_hours: int = 24):
        """清理已完成的监控会话"""
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(hours=max_age_hours)
            
            sessions_to_remove = []
            
            for operation_id, monitoring_data in self.monitoring_sessions.items():
                # 检查会话是否已完成且超过保留时间
                if (monitoring_data.current_status in [
                    ExecutionStatus.SUCCESS, 
                    ExecutionStatus.FAILED, 
                    ExecutionStatus.CANCELLED,
                    ExecutionStatus.TIMEOUT
                ] and monitoring_data.start_time < cutoff_time):
                    sessions_to_remove.append(operation_id)
            
            # 移除过期会话
            for operation_id in sessions_to_remove:
                del self.monitoring_sessions[operation_id]
                if operation_id in self.monitoring_tasks:
                    del self.monitoring_tasks[operation_id]
            
            if sessions_to_remove:
                self.logger.info(f"清理了 {len(sessions_to_remove)} 个过期监控会话")
            
        except Exception as e:
            self.logger.error(f"清理监控会话失败: {e}")
    
    def update_monitor_config(self, config: Dict[str, Any]):
        """更新监控配置"""
        self.monitor_config.update(config)
        self.logger.info(f"监控配置已更新: {config}")

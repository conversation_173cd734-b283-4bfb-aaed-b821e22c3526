# -*- coding: utf-8 -*-
"""
LLM管理器 - 统一管理LLM服务和连接池
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

from .llm_client import LLMClient
from .qyuan_llm import QYuanLLM
from ..core.config import QYuanConfig
from ..core.exceptions import LLMError, InitializationError

class LLMManager:
    """LLM管理器"""
    
    def __init__(self, config: QYuanConfig):
        self.config = config
        self.logger = logging.getLogger("QYuan.LLMManager")
        
        # LLM客户端实例
        self.llm_client: Optional[LLMClient] = None
        self.qyuan_llm: Optional[QYuanLLM] = None
        
        # 健康检查
        self.last_health_check: Optional[datetime] = None
        self.health_check_interval = timedelta(minutes=5)
        self.is_healthy = False
        
        # 统计信息
        self.start_time: Optional[datetime] = None
        self.total_requests = 0
        self.total_errors = 0
    
    async def initialize(self):
        """初始化LLM管理器"""
        try:
            self.logger.info("初始化LLM管理器...")
            
            # 创建LLM客户端
            self.llm_client = LLMClient(
                api_base=self.config.llm.api_base,
                api_key=self.config.llm.api_key,
                model=self.config.llm.model,
                timeout=self.config.llm.timeout,
                max_retries=self.config.llm.max_retries
            )
            
            # 初始化客户端
            await self.llm_client.initialize()
            
            # 创建QYuan专用LLM接口
            self.qyuan_llm = QYuanLLM(self.llm_client)
            
            # 执行健康检查
            await self.health_check()
            
            if not self.is_healthy:
                raise InitializationError("LLM服务健康检查失败")
            
            self.start_time = datetime.now()
            self.logger.info("✅ LLM管理器初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ LLM管理器初始化失败: {e}")
            raise InitializationError(f"LLM管理器初始化失败: {e}")
    
    async def shutdown(self):
        """关闭LLM管理器"""
        self.logger.info("关闭LLM管理器...")
        
        if self.llm_client:
            await self.llm_client.close()
            self.llm_client = None
        
        self.qyuan_llm = None
        self.is_healthy = False
        
        self.logger.info("✅ LLM管理器已关闭")
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.llm_client:
                self.is_healthy = False
                return False
            
            # 执行健康检查
            self.is_healthy = await self.llm_client.health_check()
            self.last_health_check = datetime.now()
            
            if self.is_healthy:
                self.logger.debug("LLM服务健康检查通过")
            else:
                self.logger.warning("LLM服务健康检查失败")
            
            return self.is_healthy
            
        except Exception as e:
            self.logger.error(f"LLM健康检查异常: {e}")
            self.is_healthy = False
            return False
    
    async def should_check_health(self) -> bool:
        """判断是否需要进行健康检查"""
        if self.last_health_check is None:
            return True
        
        return datetime.now() - self.last_health_check > self.health_check_interval
    
    async def ensure_healthy(self):
        """确保LLM服务健康"""
        if await self.should_check_health():
            await self.health_check()
        
        if not self.is_healthy:
            raise LLMError("LLM服务不可用")
    
    async def chat(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """智能对话"""
        try:
            await self.ensure_healthy()
            
            if not self.qyuan_llm:
                raise LLMError("QYuan LLM接口未初始化")
            
            self.total_requests += 1
            response = await self.qyuan_llm.chat(message, context)
            
            return response
            
        except Exception as e:
            self.total_errors += 1
            self.logger.error(f"LLM对话失败: {e}")
            raise
    
    async def analyze_intent(self, message: str):
        """分析用户意图"""
        try:
            await self.ensure_healthy()
            
            if not self.qyuan_llm:
                raise LLMError("QYuan LLM接口未初始化")
            
            return await self.qyuan_llm.analyze_intent(message)
            
        except Exception as e:
            self.logger.error(f"意图分析失败: {e}")
            raise
    
    async def create_action_plan(self, goal: str, context: Optional[Dict[str, Any]] = None):
        """创建行动计划"""
        try:
            await self.ensure_healthy()
            
            if not self.qyuan_llm:
                raise LLMError("QYuan LLM接口未初始化")
            
            return await self.qyuan_llm.create_action_plan(goal, context)
            
        except Exception as e:
            self.logger.error(f"行动计划创建失败: {e}")
            raise
    
    async def analyze_screen_content(self, screen_description: str) -> Dict[str, Any]:
        """分析屏幕内容"""
        try:
            await self.ensure_healthy()
            
            if not self.qyuan_llm:
                raise LLMError("QYuan LLM接口未初始化")
            
            return await self.qyuan_llm.analyze_screen_content(screen_description)
            
        except Exception as e:
            self.logger.error(f"屏幕内容分析失败: {e}")
            raise
    
    def clear_conversation_history(self):
        """清空对话历史"""
        if self.qyuan_llm:
            self.qyuan_llm.clear_history()
            self.logger.info("对话历史已清空")
    
    def get_conversation_summary(self) -> str:
        """获取对话摘要"""
        if self.qyuan_llm:
            return self.qyuan_llm.get_conversation_summary()
        return "LLM服务未初始化"
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        base_stats = {
            "is_healthy": self.is_healthy,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "total_requests": self.total_requests,
            "total_errors": self.total_errors,
            "error_rate": self.total_errors / max(self.total_requests, 1)
        }
        
        # 添加LLM客户端统计
        if self.llm_client:
            client_stats = self.llm_client.get_statistics()
            base_stats.update(client_stats)
        
        # 计算运行时间
        if self.start_time:
            uptime = datetime.now() - self.start_time
            base_stats["uptime_seconds"] = uptime.total_seconds()
        
        return base_stats
    
    def get_status(self) -> Dict[str, Any]:
        """获取状态信息"""
        return {
            "initialized": self.llm_client is not None,
            "healthy": self.is_healthy,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
            "config": {
                "api_base": self.config.llm.api_base,
                "model": self.config.llm.model,
                "timeout": self.config.llm.timeout,
                "max_retries": self.config.llm.max_retries
            },
            "statistics": self.get_statistics()
        }

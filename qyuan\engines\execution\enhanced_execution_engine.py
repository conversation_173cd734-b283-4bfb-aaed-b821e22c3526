# -*- coding: utf-8 -*-
"""
增强执行引擎实现
在原有MCP集成基础上，添加精确控制、监控、验证和错误恢复功能
严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
import uuid
from typing import Dict, List, Optional, Any, Union

from .. import BaseEngine
from .base import (
    OperationParameters,
    ExecutionContext,
    ExecutionResult,
    ExecutionStatus,
    OperationType,
    ValidationCriteria,
    ValidationLevel
)
from .precise_executor import PreciseOperationExecutor
from .operation_monitor import RealTimeOperationMonitor
from .result_validator import IntelligentResultValidator
from .error_recovery import IntelligentErrorRecovery
from .precision_controller import AdvancedPrecisionController

class EnhancedExecutionEngine(BaseEngine):
    """增强执行引擎主类"""
    
    def __init__(self, qyuan_core=None):
        super().__init__("EnhancedExecution", qyuan_core)
        
        # 初始化各个执行服务
        self.precise_executor = PreciseOperationExecutor()
        self.operation_monitor = RealTimeOperationMonitor()
        self.result_validator = IntelligentResultValidator()
        self.error_recovery = IntelligentErrorRecovery()
        self.precision_controller = AdvancedPrecisionController()
        
        # 执行历史记录
        self.execution_history: List[ExecutionResult] = []
        self.max_history_size = 100
        
        # 性能统计
        self.performance_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0.0,
            "average_validation_time": 0.0,
            "recovery_attempts": 0,
            "last_execution_time": None
        }
        
        # 执行配置
        self.execution_config = {
            'enable_monitoring': True,
            'enable_validation': True,
            'enable_error_recovery': True,
            'enable_precision_control': True,
            'default_validation_level': ValidationLevel.STANDARD,
            'max_retry_attempts': 3,
            'execution_timeout': 60.0
        }
    
    async def _initialize(self):
        """初始化增强执行引擎"""
        self.logger.info("初始化增强执行引擎...")
        
        # 检查各个服务的可用性
        services_status = await self._check_services_availability()
        
        self.logger.info("增强执行引擎服务状态:")
        for service_name, status in services_status.items():
            status_text = "可用" if status else "不可用"
            self.logger.info(f"  {service_name}: {status_text}")
        
        # 系统校准
        if self.execution_config['enable_precision_control']:
            calibration_result = await self.precision_controller.calibrate_system()
            if calibration_result['success']:
                self.logger.info("系统精度校准完成")
            else:
                self.logger.warning("系统精度校准失败")
        
        self.logger.info("增强执行引擎初始化完成")
    
    async def _start(self):
        """启动增强执行引擎"""
        self.logger.info("增强执行引擎启动完成")
    
    async def _stop(self):
        """停止增强执行引擎"""
        # 清理监控会话
        await self.operation_monitor.cleanup_completed_sessions()
        self.logger.info("增强执行引擎停止完成")
    
    async def _health_check(self) -> bool:
        """健康检查"""
        try:
            # 检查核心服务可用性
            services_status = await self._check_services_availability()
            
            # 至少需要执行器可用
            return services_status.get('precise_executor', False)
            
        except Exception as e:
            self.logger.error(f"增强执行引擎健康检查失败: {e}")
            return False
    
    async def execute_operation(self, params: OperationParameters, context: Optional[ExecutionContext] = None) -> ExecutionResult:
        """执行操作（主要接口）"""
        start_time = time.time()
        operation_id = str(uuid.uuid4())
        
        # 创建执行上下文
        if context is None:
            context = ExecutionContext(
                session_id=str(uuid.uuid4()),
                environment={'operation_id': operation_id}
            )
        
        try:
            self.logger.debug(f"开始执行操作: {params.operation_type.value}")
            
            # 1. 精度控制和参数调整
            if self.execution_config['enable_precision_control']:
                params = await self.precision_controller.adjust_parameters(params, context)
            
            # 2. 开始监控
            if self.execution_config['enable_monitoring']:
                await self.operation_monitor.start_monitoring(operation_id, params)
            
            # 3. 执行操作（带重试机制）
            result = await self._execute_with_retry(params, context, operation_id)
            
            # 4. 结果验证
            if self.execution_config['enable_validation'] and result.success:
                validation_result = await self._validate_result(result, params, context)
                result.validation_results = validation_result
                
                # 如果验证失败，更新结果状态
                if not validation_result.get('overall_success', True):
                    result.success = False
                    result.status = ExecutionStatus.FAILED
                    result.error_message = "操作验证失败"
            
            # 5. 停止监控
            if self.execution_config['enable_monitoring']:
                await self.operation_monitor.stop_monitoring(operation_id)
            
            # 6. 更新统计和历史
            execution_time = time.time() - start_time
            result.execution_time = execution_time
            
            self._update_performance_stats(result.success, execution_time)
            self._cache_execution_history(result)
            
            self.logger.debug(f"操作执行完成: {result.success}")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"操作执行异常: {e}")
            
            # 停止监控
            if self.execution_config['enable_monitoring']:
                await self.operation_monitor.stop_monitoring(operation_id)
            
            # 创建失败结果
            result = ExecutionResult(
                operation_id=operation_id,
                status=ExecutionStatus.FAILED,
                success=False,
                error_message=str(e),
                execution_time=execution_time
            )
            
            self._update_performance_stats(False, execution_time)
            self._cache_execution_history(result)
            
            return result
    
    async def _execute_with_retry(self, params: OperationParameters, context: ExecutionContext, operation_id: str) -> ExecutionResult:
        """带重试机制的执行"""
        last_error = None
        
        for attempt in range(params.retry_count + 1):
            try:
                # 更新重试计数
                context.environment['retry_count'] = attempt
                
                # 执行操作
                result = await self.precise_executor.execute(params, context)
                
                if result.success:
                    return result
                
                # 如果失败且启用错误恢复
                if (self.execution_config['enable_error_recovery'] and 
                    attempt < params.retry_count):
                    
                    # 尝试错误恢复
                    error = Exception(result.error_message or "操作执行失败")
                    if await self.error_recovery.can_recover(error, context):
                        self.logger.info(f"尝试错误恢复，第 {attempt + 1} 次重试")
                        
                        recovery_result = await self.error_recovery.recover(error, context)
                        self.performance_stats['recovery_attempts'] += 1
                        
                        if recovery_result['success']:
                            # 恢复成功，继续重试
                            continue
                
                last_error = result.error_message
                
            except Exception as e:
                last_error = str(e)
                
                # 如果启用错误恢复且还有重试机会
                if (self.execution_config['enable_error_recovery'] and 
                    attempt < params.retry_count):
                    
                    if await self.error_recovery.can_recover(e, context):
                        self.logger.info(f"尝试异常恢复，第 {attempt + 1} 次重试")
                        
                        recovery_result = await self.error_recovery.recover(e, context)
                        self.performance_stats['recovery_attempts'] += 1
                        
                        if recovery_result['success']:
                            continue
        
        # 所有重试都失败
        return ExecutionResult(
            operation_id=operation_id,
            status=ExecutionStatus.FAILED,
            success=False,
            error_message=last_error or "操作执行失败",
            retry_count=params.retry_count
        )
    
    async def _validate_result(self, result: ExecutionResult, params: OperationParameters, context: ExecutionContext) -> Dict[str, Any]:
        """验证执行结果"""
        try:
            # 创建验证标准
            criteria = await self.result_validator.create_validation_criteria(params)
            
            # 执行验证
            validation_result = await self.result_validator.validate(result, criteria)
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"结果验证失败: {e}")
            return {
                'overall_success': False,
                'errors': [f"验证过程异常: {str(e)}"]
            }
    
    async def _check_services_availability(self) -> Dict[str, bool]:
        """检查各个服务的可用性"""
        services = {
            "precise_executor": self.precise_executor,
            "operation_monitor": self.operation_monitor,
            "result_validator": self.result_validator,
            "error_recovery": self.error_recovery,
            "precision_controller": self.precision_controller
        }
        
        availability = {}
        
        for service_name, service in services.items():
            try:
                availability[service_name] = await service.is_available()
            except Exception as e:
                self.logger.warning(f"检查服务 {service_name} 可用性失败: {e}")
                availability[service_name] = False
        
        return availability
    
    def _update_performance_stats(self, success: bool, execution_time: float):
        """更新性能统计"""
        self.performance_stats["total_executions"] += 1
        
        if success:
            self.performance_stats["successful_executions"] += 1
        else:
            self.performance_stats["failed_executions"] += 1
        
        # 更新平均执行时间
        total = self.performance_stats["total_executions"]
        current_avg = self.performance_stats["average_execution_time"]
        self.performance_stats["average_execution_time"] = (
            (current_avg * (total - 1) + execution_time) / total
        )
        
        self.performance_stats["last_execution_time"] = time.time()
    
    def _cache_execution_history(self, result: ExecutionResult):
        """缓存执行历史"""
        self.execution_history.append(result)
        
        # 限制历史记录大小
        if len(self.execution_history) > self.max_history_size:
            self.execution_history = self.execution_history[-self.max_history_size:]
    
    # 便捷接口方法
    
    async def click(self, x: int, y: int, button: str = 'left', **kwargs) -> ExecutionResult:
        """点击操作"""
        params = OperationParameters(
            operation_type=OperationType.MOUSE_CLICK,
            coordinates={'x': x, 'y': y},
            custom_params={'button': button, **kwargs}
        )
        return await self.execute_operation(params)
    
    async def type_text(self, text: str, **kwargs) -> ExecutionResult:
        """输入文本"""
        params = OperationParameters(
            operation_type=OperationType.KEYBOARD_TYPE,
            text=text,
            custom_params=kwargs
        )
        return await self.execute_operation(params)
    
    async def scroll(self, direction: str = 'down', clicks: int = 3, **kwargs) -> ExecutionResult:
        """滚动操作"""
        params = OperationParameters(
            operation_type=OperationType.MOUSE_SCROLL,
            custom_params={'direction': direction, 'clicks': clicks, **kwargs}
        )
        return await self.execute_operation(params)
    
    async def wait(self, duration: float) -> ExecutionResult:
        """等待操作"""
        params = OperationParameters(
            operation_type=OperationType.WAIT,
            custom_params={'duration': duration}
        )
        return await self.execute_operation(params)
    
    # 获取信息的方法
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self.performance_stats.copy()
        total = stats["total_executions"]
        stats["success_rate"] = (stats["successful_executions"] / total) if total > 0 else 0.0
        
        # 添加各服务的统计
        stats["executor_stats"] = self.precise_executor.get_execution_stats()
        stats["validator_stats"] = self.result_validator.get_validation_stats()
        stats["recovery_stats"] = self.error_recovery.get_recovery_stats()
        stats["precision_stats"] = self.precision_controller.get_precision_stats()
        
        return stats
    
    def get_execution_history(self, limit: int = 10) -> List[ExecutionResult]:
        """获取执行历史记录"""
        return self.execution_history[-limit:] if self.execution_history else []
    
    def get_monitoring_summary(self) -> Dict[str, Any]:
        """获取监控摘要"""
        return self.operation_monitor.get_monitoring_summary()
    
    def update_execution_config(self, config: Dict[str, Any]):
        """更新执行配置"""
        self.execution_config.update(config)
        self.logger.info(f"执行配置已更新: {config}")

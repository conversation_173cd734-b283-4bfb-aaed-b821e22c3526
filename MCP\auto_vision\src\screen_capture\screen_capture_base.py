#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
屏幕截图基础接口模块

定义屏幕截图的基础接口，作为所有实现的基类。
"""

import base64
from io import BytesIO
from abc import ABC, abstractmethod


class ScreenCaptureBase(ABC):
    """屏幕截图基础接口类，定义所有实现必须提供的方法"""

    @abstractmethod
    def capture_screen(self, region=None, save_path=None, suffix=None):
        """
        捕获屏幕区域

        Args:
            region: 截图区域 [x, y, width, height]，为None则截取全屏
            save_path: 保存路径，为None则自动生成路径
            suffix: 文件名后缀，用于区分不同的截图

        Returns:
            PIL.Image: 截图图像对象
            str: 保存路径（如果有保存）
        """
        pass

    @abstractmethod
    def get_screen_size(self):
        """
        获取屏幕尺寸

        Returns:
            tuple: (宽度, 高度)
        """
        pass

    def capture_screen_to_base64(self, region=None, suffix=None):
        """
        捕获屏幕区域并转换为base64编码

        Args:
            region: 截图区域 [x, y, width, height]，为None则截取全屏
            suffix: 文件名后缀，用于区分不同的截图

        Returns:
            dict: 包含base64编码的图片数据和相关信息
        """
        try:
            # 捕获屏幕
            screenshot, screenshot_path = self.capture_screen(region=region, suffix=suffix)

            # 将图片转换为base64编码
            buffered = BytesIO()
            screenshot.save(buffered, format="PNG")
            img_str = base64.b64encode(buffered.getvalue()).decode()

            # 构建返回结果
            result = {
                "success": True,
                "image_base64": img_str,
                "path": screenshot_path,
                "width": screenshot.width,
                "height": screenshot.height
            }

            # 如果是区域截图，添加区域信息
            if region:
                result["x"] = region[0]
                result["y"] = region[1]
                result["width"] = region[2]
                result["height"] = region[3]

            return result
        except Exception as e:
            return {"success": False, "error": str(e)}

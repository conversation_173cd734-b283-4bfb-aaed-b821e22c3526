// -*- coding: utf-8 -*-

import React, { useState, useRef, useEffect } from 'react';
import { Send, Mic, MicOff, Paperclip } from 'lucide-react';
import { cn } from '../../utils';
import { BaseComponentProps, ChatMessage } from '../../types';
import { Button } from '../common/Button';
import { Input } from '../common/Input';
import { Card } from '../common/Card';
import { ChatMessageList } from './ChatMessageList';
import { ChatInput } from './ChatInput';

/**
 * ChatInterface组件属性接口
 */
interface ChatInterfaceProps extends BaseComponentProps {
  messages: ChatMessage[];
  loading?: boolean;
  onSendMessage: (message: string) => void;
  onClearHistory?: () => void;
}

/**
 * ChatInterface组件
 * 聊天界面主组件，包含消息列表和输入区域
 * 严格按照代码规范的单一职责原则
 */
export function ChatInterface({
  className,
  messages,
  loading = false,
  onSendMessage,
  onClearHistory,
  ...props
}: ChatInterfaceProps) {
  const [inputValue, setInputValue] = useState<string>('');
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  /**
   * 滚动到消息底部
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  /**
   * 监听消息变化，自动滚动到底部
   */
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  /**
   * 获取聊天界面样式类名
   */
  const getChatInterfaceClasses = () => {
    return cn(
      'flex',
      'flex-col',
      'h-full',
      'bg-white',
      'dark:bg-gray-800',
      'rounded-lg',
      'shadow-sm',
      'border',
      'border-gray-200',
      'dark:border-gray-700',
      className
    );
  };

  /**
   * 获取消息区域样式类名
   */
  const getMessagesAreaClasses = () => {
    return cn(
      'flex-1',
      'overflow-hidden',
      'flex',
      'flex-col'
    );
  };

  /**
   * 获取输入区域样式类名
   */
  const getInputAreaClasses = () => {
    return cn(
      'border-t',
      'border-gray-200',
      'dark:border-gray-700',
      'p-4',
      'bg-gray-50',
      'dark:bg-gray-900'
    );
  };

  /**
   * 处理发送消息
   */
  const handleSendMessage = () => {
    const trimmedMessage = inputValue.trim();
    if (trimmedMessage && !loading) {
      onSendMessage(trimmedMessage);
      setInputValue('');
    }
  };

  /**
   * 处理输入框回车事件
   */
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  /**
   * 处理语音录制切换
   */
  const handleToggleRecording = () => {
    setIsRecording(!isRecording);
    // TODO: 实现语音录制功能
  };

  /**
   * 处理文件上传
   */
  const handleFileUpload = () => {
    // TODO: 实现文件上传功能
  };

  /**
   * 处理清除历史
   */
  const handleClearHistory = () => {
    if (onClearHistory) {
      onClearHistory();
    }
  };

  return (
    <div className={getChatInterfaceClasses()} {...props}>
      {/* 聊天头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            QYuan 对话
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            与硅基CEO进行智能对话
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearHistory}
            disabled={messages.length === 0}
          >
            清除历史
          </Button>
        </div>
      </div>

      {/* 消息区域 */}
      <div className={getMessagesAreaClasses()}>
        <ChatMessageList
          messages={messages}
          loading={loading}
        />
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className={getInputAreaClasses()}>
        <ChatInput
          value={inputValue}
          onChange={setInputValue}
          onSend={handleSendMessage}
          onKeyDown={handleKeyDown}
          loading={loading}
          isRecording={isRecording}
          onToggleRecording={handleToggleRecording}
          onFileUpload={handleFileUpload}
        />
      </div>
    </div>
  );
}

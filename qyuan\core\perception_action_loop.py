# -*- coding: utf-8 -*-
"""
感知-行动循环核心类
实现QYuan的核心操作机制：感知→决策→执行→验证→学习的闭环
严格按照代码规范的单一职责原则
"""

import asyncio
import time
import uuid
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

from .exceptions import QYuanException
from .loop_optimizer import LoopOptimizer, OptimizationConfig
from ..engines.perception.base import ScreenAnalysis
from ..engines.decision.base import UserIntent, ActionPlan, DecisionResult
from ..engines.execution.base import ExecutionResult
from ..utils.logger import get_logger


class LoopStatus(Enum):
    """循环状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    ERROR = "error"


class LoopInterruptReason(Enum):
    """循环中断原因"""
    USER_REQUEST = "user_request"
    GOAL_COMPLETED = "goal_completed"
    ERROR_OCCURRED = "error_occurred"
    TIMEOUT = "timeout"
    RESOURCE_EXHAUSTED = "resource_exhausted"


@dataclass
class LoopMetrics:
    """循环性能指标"""
    total_cycles: int = 0
    successful_cycles: int = 0
    failed_cycles: int = 0
    average_cycle_time: float = 0.0
    perception_time: float = 0.0
    decision_time: float = 0.0
    execution_time: float = 0.0
    verification_time: float = 0.0
    last_cycle_time: Optional[datetime] = None
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_cycles == 0:
            return 0.0
        return self.successful_cycles / self.total_cycles


@dataclass
class LoopState:
    """循环状态数据"""
    status: LoopStatus = LoopStatus.IDLE
    current_goal: Optional[Any] = None
    current_cycle: int = 0
    start_time: Optional[datetime] = None
    last_perception: Optional[ScreenAnalysis] = None
    last_decision: Optional[DecisionResult] = None
    last_execution: Optional[ExecutionResult] = None
    interrupt_reason: Optional[LoopInterruptReason] = None
    error_message: Optional[str] = None
    metrics: LoopMetrics = field(default_factory=LoopMetrics)


class PerceptionActionLoop:
    """感知-行动循环核心类"""
    
    def __init__(self, qyuan_core):
        """初始化感知-行动循环"""
        self.qyuan_core = qyuan_core
        self.logger = qyuan_core.logger if hasattr(qyuan_core, 'logger') else None

        # 循环状态
        self.state = LoopState()

        # 循环配置
        self.config = {
            "max_cycle_time": 30.0,  # 单次循环最大时间（秒）
            "max_total_time": 300.0,  # 总循环最大时间（秒）
            "cycle_interval": 0.1,  # 循环间隔（秒）
            "error_retry_count": 3,  # 错误重试次数
            "error_retry_delay": 1.0,  # 错误重试延迟（秒）
        }

        # 性能优化器
        optimization_config = OptimizationConfig(
            target_cycle_time=5.0,
            enable_parallel_processing=True,
            enable_caching=True,
            enable_adaptive_timing=True
        )
        self.optimizer = LoopOptimizer(optimization_config)

        # 中断处理器
        self.interrupt_handlers: Dict[LoopInterruptReason, Callable] = {}

        # 性能监控
        self.performance_callbacks: List[Callable] = []

        # 循环控制
        self._stop_requested = False
        self._pause_requested = False
        self._current_task = None
    
    async def start_loop(self, goal: Any, timeout: Optional[float] = None) -> Dict[str, Any]:
        """启动感知-行动循环"""
        self.logger.info(f"启动感知-行动循环，目标: {goal}")
        
        # 重置状态
        self.state = LoopState(
            status=LoopStatus.RUNNING,
            current_goal=goal,
            start_time=datetime.now()
        )
        
        self._stop_requested = False
        self._pause_requested = False
        
        try:
            # 设置超时
            loop_timeout = timeout or self.config["max_total_time"]
            
            # 启动循环任务
            self._current_task = asyncio.create_task(
                asyncio.wait_for(self._run_loop(), timeout=loop_timeout)
            )
            
            result = await self._current_task
            
            self.logger.info(f"感知-行动循环完成: {result}")
            return result
            
        except asyncio.TimeoutError:
            self.logger.warning("感知-行动循环超时")
            await self._handle_interrupt(LoopInterruptReason.TIMEOUT)
            return {"success": False, "reason": "timeout", "metrics": self.state.metrics}
            
        except Exception as e:
            self.logger.error(f"感知-行动循环异常: {e}")
            await self._handle_interrupt(LoopInterruptReason.ERROR_OCCURRED, str(e))
            return {"success": False, "reason": "error", "error": str(e), "metrics": self.state.metrics}
        
        finally:
            self.state.status = LoopStatus.IDLE
            self._current_task = None
    
    async def stop_loop(self, reason: str = "user_request"):
        """停止感知-行动循环"""
        self.logger.info(f"请求停止感知-行动循环: {reason}")
        
        self._stop_requested = True
        self.state.status = LoopStatus.STOPPING
        
        if self._current_task and not self._current_task.done():
            self._current_task.cancel()
            try:
                await self._current_task
            except asyncio.CancelledError:
                pass
        
        await self._handle_interrupt(LoopInterruptReason.USER_REQUEST, reason)
    
    async def pause_loop(self):
        """暂停感知-行动循环"""
        self.logger.info("暂停感知-行动循环")
        self._pause_requested = True
        self.state.status = LoopStatus.PAUSED
    
    async def resume_loop(self):
        """恢复感知-行动循环"""
        self.logger.info("恢复感知-行动循环")
        self._pause_requested = False
        self.state.status = LoopStatus.RUNNING
    
    async def _run_loop(self) -> Dict[str, Any]:
        """运行主循环"""
        self.logger.debug("开始执行感知-行动循环")
        
        while not self._stop_requested:
            # 检查暂停状态
            if self._pause_requested:
                await asyncio.sleep(0.1)
                continue
            
            # 执行单次循环
            cycle_start = time.time()
            cycle_success = False
            
            try:
                cycle_success = await self._execute_single_cycle()
                
                if cycle_success:
                    self.state.metrics.successful_cycles += 1
                else:
                    self.state.metrics.failed_cycles += 1
                
            except Exception as e:
                self.logger.error(f"循环执行异常: {e}")
                self.state.metrics.failed_cycles += 1
                await self._handle_cycle_error(e)
            
            finally:
                # 更新循环统计
                cycle_time = time.time() - cycle_start
                self.state.current_cycle += 1
                self.state.metrics.total_cycles += 1
                self.state.metrics.last_cycle_time = datetime.now()
                
                # 更新平均循环时间
                if self.state.metrics.total_cycles > 0:
                    total_time = (self.state.metrics.average_cycle_time * 
                                (self.state.metrics.total_cycles - 1) + cycle_time)
                    self.state.metrics.average_cycle_time = total_time / self.state.metrics.total_cycles
                
                # 通知性能回调
                await self._notify_performance_callbacks()
            
            # 检查目标是否完成
            if await self._is_goal_completed():
                self.logger.info("目标已完成，结束循环")
                await self._handle_interrupt(LoopInterruptReason.GOAL_COMPLETED)
                break
            
            # 循环间隔
            await asyncio.sleep(self.config["cycle_interval"])
        
        return {
            "success": True,
            "cycles": self.state.current_cycle,
            "metrics": self.state.metrics,
            "completion_reason": self.state.interrupt_reason.value if self.state.interrupt_reason else "completed"
        }
    
    async def _execute_single_cycle(self) -> bool:
        """执行单次感知-行动循环（优化版本）"""
        if self.logger:
            self.logger.debug(f"执行第 {self.state.current_cycle + 1} 次循环")

        try:
            # 使用优化器执行完整循环
            return await self.optimizer.optimize_full_cycle(self._execute_optimized_cycle)

        except Exception as e:
            if self.logger:
                self.logger.error(f"单次循环执行异常: {e}")
            return False

    async def _execute_optimized_cycle(self) -> bool:
        """执行优化的循环"""
        try:
            # 1. 感知阶段（优化）
            perception_result = await self.optimizer.optimize_perception_phase(
                self._perception_phase_core
            )

            if not perception_result:
                if self.logger:
                    self.logger.warning("感知阶段失败")
                return False

            # 2. 决策阶段（优化）
            decision_result = await self.optimizer.optimize_decision_phase(
                self._decision_phase_core, perception_result
            )

            if not decision_result:
                if self.logger:
                    self.logger.warning("决策阶段失败")
                return False

            # 3. 执行阶段（优化）
            execution_result = await self.optimizer.optimize_execution_phase(
                self._execution_phase_core, decision_result
            )

            if not execution_result:
                if self.logger:
                    self.logger.warning("执行阶段失败")
                return False

            # 4. 验证阶段（优化）
            verification_result = await self.optimizer.optimize_verification_phase(
                self._verification_phase_core, execution_result
            )

            # 5. 学习阶段
            await self._learning_phase(perception_result, decision_result, execution_result, verification_result)

            return verification_result

        except Exception as e:
            if self.logger:
                self.logger.error(f"优化循环执行异常: {e}")
            return False
    
    async def _perception_phase(self) -> Optional[ScreenAnalysis]:
        """感知阶段（兼容性方法）"""
        return await self._perception_phase_core()

    async def _perception_phase_core(self) -> Optional[ScreenAnalysis]:
        """感知阶段核心逻辑"""
        try:
            perception_engine = self.qyuan_core.engines.get("perception")
            if not perception_engine:
                if self.logger:
                    self.logger.error("感知引擎不可用")
                return None

            analysis = await perception_engine.analyze_current_screen()
            self.state.last_perception = analysis

            if self.logger and analysis:
                self.logger.debug(f"感知完成，识别到 {len(analysis.ui_elements)} 个UI元素")
            return analysis

        except Exception as e:
            if self.logger:
                self.logger.error(f"感知阶段异常: {e}")
            return None
    
    async def _decision_phase(self, perception_result: ScreenAnalysis) -> Optional[DecisionResult]:
        """决策阶段（兼容性方法）"""
        return await self._decision_phase_core(perception_result)

    async def _decision_phase_core(self, perception_result: ScreenAnalysis) -> Optional[DecisionResult]:
        """决策阶段核心逻辑"""
        try:
            decision_engine = self.qyuan_core.engines.get("decision")
            if not decision_engine:
                if self.logger:
                    self.logger.error("决策引擎不可用")
                return None

            # 构建决策输入
            goal_description = getattr(self.state.current_goal, 'description', str(self.state.current_goal))

            decision_result = await decision_engine.make_decision(
                user_input=goal_description,
                perception_data=perception_result
            )

            self.state.last_decision = decision_result

            if self.logger:
                self.logger.debug(f"决策完成: {decision_result.intent.intent_type if decision_result else 'None'}")
            return decision_result

        except Exception as e:
            if self.logger:
                self.logger.error(f"决策阶段异常: {e}")
            return None
    
    async def _execution_phase(self, decision_result: DecisionResult) -> Optional[ExecutionResult]:
        """执行阶段（兼容性方法）"""
        return await self._execution_phase_core(decision_result)

    async def _execution_phase_core(self, decision_result: DecisionResult) -> Optional[ExecutionResult]:
        """执行阶段核心逻辑"""
        try:
            execution_engine = self.qyuan_core.engines.get("execution")
            if not execution_engine:
                if self.logger:
                    self.logger.error("执行引擎不可用")
                return None

            # 如果有行动计划，执行第一个行动
            if decision_result.action_plan and decision_result.action_plan.actions:
                action = decision_result.action_plan.actions[0]
                execution_result = await execution_engine.execute_action(action)
                self.state.last_execution = execution_result

                if self.logger:
                    self.logger.debug(f"执行完成: {execution_result.success if execution_result else False}")
                return execution_result
            else:
                if self.logger:
                    self.logger.warning("没有可执行的行动计划")
                return None

        except Exception as e:
            if self.logger:
                self.logger.error(f"执行阶段异常: {e}")
            return None
    
    async def _verification_phase(self, execution_result: ExecutionResult) -> bool:
        """验证阶段（兼容性方法）"""
        return await self._verification_phase_core(execution_result)

    async def _verification_phase_core(self, execution_result: ExecutionResult) -> bool:
        """验证阶段核心逻辑"""
        try:
            # 基础验证：检查执行是否成功
            if not execution_result or not execution_result.success:
                if self.logger:
                    self.logger.debug("执行结果验证失败")
                return False

            # TODO: 实现更复杂的验证逻辑
            # - 屏幕状态变化验证
            # - 目标达成度验证
            # - 副作用检测

            if self.logger:
                self.logger.debug("执行结果验证成功")
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"验证阶段异常: {e}")
            return False
    
    async def _learning_phase(self, perception_result, decision_result, execution_result, verification_result):
        """学习阶段"""
        try:
            learning_engine = self.qyuan_core.engines.get("learning")
            if not learning_engine:
                self.logger.debug("学习引擎不可用，跳过学习阶段")
                return
            
            # 记录经验
            await learning_engine.record_experience(
                context=perception_result,
                action=decision_result.action_plan.actions[0] if decision_result.action_plan and decision_result.action_plan.actions else None,
                result=execution_result,
                verification={"success": verification_result}
            )
            
            self.logger.debug("学习阶段完成")
            
        except Exception as e:
            self.logger.error(f"学习阶段异常: {e}")
    
    async def _is_goal_completed(self) -> bool:
        """检查目标是否完成"""
        # TODO: 实现目标完成检查逻辑
        # 这里需要根据具体的目标类型和完成条件来判断
        return False
    
    async def _handle_interrupt(self, reason: LoopInterruptReason, message: str = ""):
        """处理循环中断"""
        self.logger.info(f"处理循环中断: {reason.value}, {message}")
        
        self.state.interrupt_reason = reason
        self.state.error_message = message
        
        # 调用中断处理器
        if reason in self.interrupt_handlers:
            try:
                await self.interrupt_handlers[reason](self.state, message)
            except Exception as e:
                self.logger.error(f"中断处理器异常: {e}")
    
    async def _handle_cycle_error(self, error: Exception):
        """处理循环错误"""
        self.logger.error(f"处理循环错误: {error}")
        
        # TODO: 实现错误恢复策略
        # - 重试机制
        # - 降级策略
        # - 错误上报
        
        await asyncio.sleep(self.config["error_retry_delay"])
    
    async def _notify_performance_callbacks(self):
        """通知性能回调"""
        for callback in self.performance_callbacks:
            try:
                await callback(self.state.metrics)
            except Exception as e:
                self.logger.error(f"性能回调异常: {e}")
    
    def register_interrupt_handler(self, reason: LoopInterruptReason, handler: Callable):
        """注册中断处理器"""
        self.interrupt_handlers[reason] = handler
    
    def register_performance_callback(self, callback: Callable):
        """注册性能回调"""
        self.performance_callbacks.append(callback)
    
    def get_status(self) -> Dict[str, Any]:
        """获取循环状态"""
        return {
            "status": self.state.status.value,
            "current_cycle": self.state.current_cycle,
            "metrics": {
                "total_cycles": self.state.metrics.total_cycles,
                "success_rate": self.state.metrics.success_rate,
                "average_cycle_time": self.state.metrics.average_cycle_time,
                "last_cycle_time": self.state.metrics.last_cycle_time.isoformat() if self.state.metrics.last_cycle_time else None
            },
            "current_goal": str(self.state.current_goal) if self.state.current_goal else None,
            "interrupt_reason": self.state.interrupt_reason.value if self.state.interrupt_reason else None,
            "optimization": self.optimizer.get_performance_report()
        }

    def get_performance_report(self) -> Dict[str, Any]:
        """获取详细性能报告"""
        return self.optimizer.get_performance_report()

    def reset_performance_metrics(self):
        """重置性能指标"""
        self.optimizer.reset_metrics()
        if self.logger:
            self.logger.info("性能指标已重置")

    def clear_optimization_cache(self):
        """清空优化缓存"""
        self.optimizer.clear_cache()
        if self.logger:
            self.logger.info("优化缓存已清空")

    def register_optimization_callback(self, callback: Callable):
        """注册优化回调"""
        self.optimizer.register_performance_callback(callback)

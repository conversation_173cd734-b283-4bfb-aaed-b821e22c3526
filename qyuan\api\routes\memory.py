#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
记忆API路由

提供QYuan记忆管理的API接口
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status

from ..models import MemorySearchRequest, MemorySearchResponse, MemoryItem, APIResponse
from ..app import get_qyuan_core, verify_api_key
from ...core.qyuan_core import QYuanCore

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/search",
    response_model=MemorySearchResponse,
    summary="搜索记忆",
    description="在QYuan的记忆中搜索相关信息"
)
async def search_memories(
    request: MemorySearchRequest,
    qyuan: QYuanCore = Depends(get_qyuan_core),
    _: bool = Depends(verify_api_key)
):
    """搜索记忆"""
    try:
        logger.info(f"搜索记忆: {request.query}")
        
        # 获取通信引擎
        communication_engine = qyuan.engines.get("communication")
        if not communication_engine:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="通信引擎不可用"
            )
        
        # 检查记忆管理器
        if not hasattr(communication_engine, 'memory_manager') or not communication_engine.memory_manager:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="记忆管理器不可用"
            )
        
        # 搜索记忆
        memories = communication_engine.memory_manager.search_memories(
            query=request.query,
            memory_types=request.memory_types,
            event_types=request.event_types,
            session_id=request.session_id,
            limit=request.limit,
            min_relevance=request.min_relevance
        )
        
        # 转换为API模型
        memory_items = []
        for memory in memories:
            memory_item = MemoryItem(
                id=memory["id"],
                memory_type=memory["memory_type"],
                event_type=memory["event_type"],
                title=memory["title"],
                content=memory["content"],
                importance_score=memory["importance_score"],
                created_at=memory["created_at"],
                session_id=memory.get("session_id"),
                tags=memory.get("tags")
            )
            memory_items.append(memory_item)
        
        return MemorySearchResponse(
            memories=memory_items,
            total_count=len(memory_items),
            query=request.query
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索记忆失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索记忆时发生错误: {str(e)}"
        )

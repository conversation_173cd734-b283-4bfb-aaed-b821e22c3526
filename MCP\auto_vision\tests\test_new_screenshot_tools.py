#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试新的截图工具

测试新增的截图工具功能。
"""

import os
import sys
import time
import base64
from io import BytesIO
from PIL import Image

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入截图模块
from src.screen_capture import FullScreenCapture, RegionScreenCapture


def save_base64_image(base64_str, output_path):
    """
    保存base64编码的图像

    Args:
        base64_str: base64编码的图像字符串
        output_path: 输出路径
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 解码base64字符串
    img_data = base64.b64decode(base64_str)
    
    # 保存图像
    with open(output_path, "wb") as f:
        f.write(img_data)
    
    print(f"图像已保存至: {output_path}")


def test_capture_fullscreen():
    """测试全屏截图功能"""
    print("\n测试全屏截图功能...")
    
    # 创建全屏截图对象
    screen_capture = FullScreenCapture()
    
    # 调用全屏截图工具
    result = screen_capture.capture_screen_to_base64(suffix="test_fullscreen")
    
    if result["success"]:
        print(f"全屏截图已保存至: {result['path']}")
        print(f"图像尺寸: {result['width']} x {result['height']}")
        
        # 保存base64编码的图像
        test_output_path = os.path.join("tests", "output", "fullscreen.png")
        save_base64_image(result["image_base64"], test_output_path)
        
        return True
    else:
        print(f"截图失败: {result.get('error', '未知错误')}")
        return False


def test_capture_fullscreen_with_coordinates():
    """测试带坐标的全屏截图功能"""
    print("\n测试带坐标的全屏截图功能...")
    
    # 创建全屏截图对象
    screen_capture = FullScreenCapture()
    
    # 调用带坐标的全屏截图工具
    result = screen_capture.capture_fullscreen_with_coordinates()
    
    if result["success"]:
        print(f"带坐标的全屏截图已保存至: {result['path']}")
        print(f"原始截图路径: {result['original_path']}")
        print(f"图像尺寸: {result['width']} x {result['height']}")
        
        # 保存base64编码的图像
        test_output_path = os.path.join("tests", "output", "fullscreen_with_coordinates.png")
        save_base64_image(result["image_base64"], test_output_path)
        
        return True
    else:
        print(f"截图失败: {result.get('error', '未知错误')}")
        return False


def test_capture_region():
    """测试区域截图功能"""
    print("\n测试区域截图功能...")
    
    # 创建区域截图对象
    region_capture = RegionScreenCapture()
    
    # 获取屏幕尺寸
    width, height = region_capture.get_screen_size()
    
    # 定义区域（屏幕中央区域）
    x = width // 4
    y = height // 4
    region_width = width // 2
    region_height = height // 2
    
    print(f"截图区域: 左上角({x}, {y}), 尺寸({region_width} x {region_height})")
    
    # 调用区域截图工具
    region = (x, y, region_width, region_height)
    result = region_capture.capture_screen_to_base64(region=region, suffix="test_region")
    
    if result["success"]:
        print(f"区域截图已保存至: {result['path']}")
        print(f"图像尺寸: {result['width']} x {result['height']}")
        
        # 保存base64编码的图像
        test_output_path = os.path.join("tests", "output", "region.png")
        save_base64_image(result["image_base64"], test_output_path)
        
        return True
    else:
        print(f"截图失败: {result.get('error', '未知错误')}")
        return False


def test_capture_region_with_coordinates():
    """测试带坐标的区域截图功能"""
    print("\n测试带坐标的区域截图功能...")
    
    # 创建区域截图对象
    region_capture = RegionScreenCapture()
    
    # 获取屏幕尺寸
    width, height = region_capture.get_screen_size()
    
    # 定义区域（屏幕中央区域）
    x = width // 4
    y = height // 4
    region_width = width // 2
    region_height = height // 2
    
    print(f"截图区域: 左上角({x}, {y}), 尺寸({region_width} x {region_height})")
    
    # 调用带坐标的区域截图工具
    result = region_capture.capture_region_with_coordinates(x, y, region_width, region_height)
    
    if result["success"]:
        print(f"带坐标的区域截图已保存至: {result['path']}")
        print(f"原始截图路径: {result['original_path']}")
        print(f"图像尺寸: {result['width']} x {result['height']}")
        
        # 保存base64编码的图像
        test_output_path = os.path.join("tests", "output", "region_with_coordinates.png")
        save_base64_image(result["image_base64"], test_output_path)
        
        return True
    else:
        print(f"截图失败: {result.get('error', '未知错误')}")
        return False


def run_tests():
    """运行所有测试"""
    # 确保输出目录存在
    os.makedirs(os.path.join("tests", "output"), exist_ok=True)
    
    # 测试全屏截图功能
    fullscreen_result = test_capture_fullscreen()
    
    # 测试带坐标的全屏截图功能
    fullscreen_coords_result = test_capture_fullscreen_with_coordinates()
    
    # 测试区域截图功能
    region_result = test_capture_region()
    
    # 测试带坐标的区域截图功能
    region_coords_result = test_capture_region_with_coordinates()
    
    # 输出总结果
    print("\n测试结果:")
    print(f"全屏截图功能: {'成功' if fullscreen_result else '失败'}")
    print(f"带坐标的全屏截图功能: {'成功' if fullscreen_coords_result else '失败'}")
    print(f"区域截图功能: {'成功' if region_result else '失败'}")
    print(f"带坐标的区域截图功能: {'成功' if region_coords_result else '失败'}")


if __name__ == "__main__":
    run_tests()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OCR文本识别包

提供OCR文本识别功能，支持Windows OCR和EasyOCR引擎。
"""

# 导出主要的类和接口
from .ocr_recognition_base import OCRRecognitionBase
from .ocr_recognition_factory import OCRRecognitionFactory
from .ocr_recognition import OCRRecognition

# 根据平台可用性导出具体实现
import platform
if platform.system() == "Windows":
    try:
        from .ocr_recognition_windows import OCRRecognitionWindows
    except ImportError:
        pass

try:
    from .ocr_recognition_easyocr import OCRRecognitionEasyOCR
except ImportError:
    pass

# 明确指定导出的符号
__all__ = [
    'OCRRecognition',
    'OCRRecognitionBase',
    'OCRRecognitionFactory'
]

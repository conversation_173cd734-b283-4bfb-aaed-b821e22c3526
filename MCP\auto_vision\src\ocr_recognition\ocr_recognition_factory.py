#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OCR文本识别工厂模块

提供创建不同OCR引擎的工厂类。
"""

import os
import sys
import platform


class OCRRecognitionFactory:
    """OCR文本识别工厂类，用于创建不同OCR引擎的实例"""

    @staticmethod
    def create(engine="auto", lang=None):
        """
        创建OCR引擎实例

        Args:
            engine: OCR引擎类型，支持 "easyocr", "windows", "auto"
            lang: 识别语言，根据引擎不同有不同的格式
                - easyocr: 语言代码列表，如 ["en", "ch_sim"]
                - windows: 语言代码，如 "en-US"

        Returns:
            OCRRecognitionBase: OCR引擎实例
        """
        # 设置默认语言
        if lang is None:
            if engine == "windows":
                lang = "en-US"
            elif engine == "baidu":
                lang = None  # 百度OCR不需要指定语言
            else:  # easyocr
                lang = ["en", "ch_sim"]

        # 自动选择引擎
        if engine == "auto":
            # 优先使用百度OCR
            try:
                from .ocr_recognition_baidu import OCRRecognitionBaidu
                return OCRRecognitionBaidu()
            except Exception as e:
                print(f"百度OCR初始化失败: {e}")

                # 在Windows平台上尝试使用Windows OCR
                if platform.system() == "Windows":
                    # 尝试使用comtypes版本的Windows OCR
                    try:
                        from .ocr_recognition_windows import OCRRecognitionWindows
                        return OCRRecognitionWindows(lang=lang)
                    except Exception as e2:
                        print(f"Windows OCR初始化失败: {e2}")
                        print("尝试使用EasyOCR...")
                        from .ocr_recognition_easyocr import OCRRecognitionEasyOCR
                        return OCRRecognitionEasyOCR(lang=lang)
                else:
                    # 非Windows平台使用EasyOCR
                    from .ocr_recognition_easyocr import OCRRecognitionEasyOCR
                    return OCRRecognitionEasyOCR(lang=lang)

        # 明确指定引擎
        if engine == "windows":
            if platform.system() != "Windows":
                raise ValueError("Windows OCR仅支持Windows平台")

            try:
                from .ocr_recognition_windows import OCRRecognitionWindows
                return OCRRecognitionWindows(lang=lang)
            except Exception as e:
                print(f"Windows OCR初始化失败: {e}")
                print("可能的原因:")
                print("1. Windows OCR组件未安装或不可用")
                print("2. 系统未安装所需的OCR语言包")
                print("3. Windows版本不支持Windows OCR API")
                print("\n解决方案:")
                print("1. 确保Windows系统已安装OCR语言包")
                print("   可以通过'设置 > 时间和语言 > 语言 > 添加语言'安装所需的语言包")
                print("2. 确保Windows版本为Windows 10 1803或更高版本")
                print("3. 尝试使用EasyOCR作为替代方案")
                raise

        elif engine == "windows_net":
            if platform.system() != "Windows":
                raise ValueError("Windows OCR仅支持Windows平台")

            try:
                from .ocr_recognition_windows_net import OCRRecognitionWindowsNet
                return OCRRecognitionWindowsNet(lang=lang)
            except Exception as e:
                print(f"Windows OCR (.NET)初始化失败: {e}")
                print("可能的原因:")
                print("1. Windows OCR组件未安装或不可用")
                print("2. 系统未安装所需的OCR语言包")
                print("3. Windows版本不支持Windows OCR API")
                print("4. pythonnet库安装不正确或版本不兼容")
                print("\n解决方案:")
                print("1. 确保Windows系统已安装OCR语言包")
                print("   可以通过'设置 > 时间和语言 > 语言 > 添加语言'安装所需的语言包")
                print("2. 确保Windows版本为Windows 10 1803或更高版本")
                print("3. 尝试重新安装pythonnet库: pip install --upgrade pythonnet")
                print("4. 尝试使用EasyOCR作为替代方案")
                raise

        elif engine == "baidu":
            try:
                from .ocr_recognition_baidu import OCRRecognitionBaidu
                return OCRRecognitionBaidu(use_high_accuracy=False)
            except Exception as e:
                print(f"百度OCR初始化失败: {e}")
                raise

        elif engine == "baidu_high":
            try:
                from .ocr_recognition_baidu import OCRRecognitionBaidu
                return OCRRecognitionBaidu(use_high_accuracy=True)
            except Exception as e:
                print(f"百度OCR（高精度）初始化失败: {e}")
                raise

        elif engine == "easyocr":
            try:
                from .ocr_recognition_easyocr import OCRRecognitionEasyOCR
                return OCRRecognitionEasyOCR(lang=lang)
            except Exception as e:
                print(f"EasyOCR初始化失败: {e}")
                raise

        else:
            raise ValueError(f"不支持的OCR引擎: {engine}")


# 测试代码
if __name__ == "__main__":
    # 测试工厂类
    print("测试OCR工厂类...")

    # 自动选择引擎
    print("\n1. 自动选择引擎:")
    try:
        ocr = OCRRecognitionFactory.create(engine="auto")
        print(f"成功创建OCR引擎: {ocr.get_engine_info()['name']}")
    except Exception as e:
        print(f"创建OCR引擎失败: {e}")

    # 指定使用Windows OCR
    if platform.system() == "Windows":
        print("\n2. 指定使用Windows OCR:")
        try:
            ocr = OCRRecognitionFactory.create(engine="windows", lang="en-US")
            print(f"成功创建Windows OCR引擎")
            print(f"引擎信息: {ocr.get_engine_info()}")
        except Exception as e:
            print(f"创建Windows OCR引擎失败: {e}")

    # 指定使用EasyOCR
    print("\n3. 指定使用EasyOCR:")
    try:
        ocr = OCRRecognitionFactory.create(engine="easyocr", lang=["en", "ch_sim"])
        print(f"成功创建EasyOCR引擎")
        print(f"引擎信息: {ocr.get_engine_info()}")
    except Exception as e:
        print(f"创建EasyOCR引擎失败: {e}")

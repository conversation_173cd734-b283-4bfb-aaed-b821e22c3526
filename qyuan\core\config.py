# -*- coding: utf-8 -*-
"""
QYuan配置管理模块
"""

import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path
from dotenv import load_dotenv

@dataclass
class DatabaseConfig:
    """数据库配置"""
    url: str
    redis_url: str = "redis://localhost:6379"
    qdrant_url: str = "http://localhost:6333"
    pool_size: int = 10
    max_overflow: int = 20

@dataclass
class LLMConfig:
    """LLM配置"""
    api_base: str
    api_key: str
    model: str
    timeout: int = 60  # 增加超时时间到60秒
    max_retries: int = 2  # 减少重试次数

@dataclass
class MCPConfig:
    """MCP服务配置"""
    mouse_port: int = 8001
    keyboard_port: int = 8002
    vision_port: int = 8003
    host: str = "localhost"

@dataclass
class AppConfig:
    """应用配置"""
    debug: bool = False
    log_level: str = "INFO"
    secret_key: str = "default-secret-key"
    host: str = "0.0.0.0"
    port: int = 8000

class QYuanConfig:
    """QYuan配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or ".env"
        self.logger: Optional[logging.Logger] = None
        self.load_config()
        self.setup_logging()
    
    def load_config(self):
        """加载配置"""
        # 加载环境变量
        if Path(self.config_file).exists():
            load_dotenv(self.config_file)
        
        # 数据库配置
        self.database = DatabaseConfig(
            url=os.getenv("DATABASE_URL", "sqlite:///./qyuan.db"),
            redis_url=os.getenv("REDIS_URL", "redis://localhost:6379"),
            qdrant_url=os.getenv("QDRANT_URL", "http://localhost:6333")
        )
        
        # LLM配置
        self.llm = LLMConfig(
            api_base=os.getenv("LLM_API_BASE", "https://xiaoai.plus"),
            api_key=os.getenv("LLM_API_KEY", ""),
            model=os.getenv("LLM_MODEL", "gpt-4.1")
        )
        
        # MCP配置
        self.mcp = MCPConfig(
            mouse_port=int(os.getenv("MCP_MOUSE_PORT", "8001")),
            keyboard_port=int(os.getenv("MCP_KEYBOARD_PORT", "8002")),
            vision_port=int(os.getenv("MCP_VISION_PORT", "8003")),
            host=os.getenv("MCP_HOST", "localhost")
        )
        
        # 应用配置
        self.app = AppConfig(
            debug=os.getenv("DEBUG", "false").lower() == "true",
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            secret_key=os.getenv("SECRET_KEY", "qyuan-default-secret-key"),
            host=os.getenv("APP_HOST", "0.0.0.0"),
            port=int(os.getenv("APP_PORT", "8000"))
        )
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建logs目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 日志格式
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        
        # 配置根日志器
        logging.basicConfig(
            level=getattr(logging, self.app.log_level.upper()),
            format=log_format,
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_dir / "qyuan.log", encoding='utf-8')
            ]
        )
        
        # 创建QYuan专用日志器
        self.logger = logging.getLogger("QYuan")
        self.logger.setLevel(getattr(logging, self.app.log_level.upper()))
        
        # 如果是调试模式，添加详细日志
        if self.app.debug:
            debug_handler = logging.FileHandler(log_dir / "debug.log", encoding='utf-8')
            debug_handler.setLevel(logging.DEBUG)
            debug_formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
            )
            debug_handler.setFormatter(debug_formatter)
            self.logger.addHandler(debug_handler)
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        errors = []
        
        # 验证LLM配置
        if not self.llm.api_key:
            errors.append("LLM API Key未配置")
        
        if not self.llm.api_base:
            errors.append("LLM API Base URL未配置")
        
        # 验证必要的目录
        required_dirs = ["logs", "qyuan", "tests"]
        for dir_name in required_dirs:
            if not Path(dir_name).exists():
                errors.append(f"必要目录不存在: {dir_name}")
        
        if errors:
            for error in errors:
                self.logger.error(f"配置验证失败: {error}")
            return False
        
        self.logger.info("配置验证通过")
        return True
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要（隐藏敏感信息）"""
        return {
            "database": {
                "type": "sqlite" if "sqlite" in self.database.url else "postgresql",
                "redis_configured": bool(self.database.redis_url),
                "qdrant_configured": bool(self.database.qdrant_url)
            },
            "llm": {
                "api_base": self.llm.api_base,
                "model": self.llm.model,
                "api_key_configured": bool(self.llm.api_key)
            },
            "mcp": {
                "host": self.mcp.host,
                "ports": {
                    "mouse": self.mcp.mouse_port,
                    "keyboard": self.mcp.keyboard_port,
                    "vision": self.mcp.vision_port
                }
            },
            "app": {
                "debug": self.app.debug,
                "log_level": self.app.log_level,
                "host": self.app.host,
                "port": self.app.port
            }
        }

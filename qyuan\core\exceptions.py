# -*- coding: utf-8 -*-
"""
QYuan异常定义模块
"""

from typing import Optional, Any, Dict

class QYuanException(Exception):
    """QYuan基础异常类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details
        }

class ConfigurationError(QYuanException):
    """配置错误"""
    pass

class InitializationError(QYuanException):
    """初始化错误"""
    pass

class EngineError(QYuanException):
    """引擎错误基类"""
    pass

class PerceptionError(EngineError):
    """感知引擎错误"""
    pass

class DecisionError(EngineError):
    """决策引擎错误"""
    pass

class ExecutionError(EngineError):
    """执行引擎错误"""
    pass

class LearningError(EngineError):
    """学习引擎错误"""
    pass

class CommunicationError(EngineError):
    """通信引擎错误"""
    pass

class LLMError(QYuanException):
    """LLM相关错误基类"""
    pass

class LLMAPIError(LLMError):
    """LLM API错误"""
    pass

class LLMNetworkError(LLMError):
    """LLM网络错误"""
    pass

class LLMTimeoutError(LLMError):
    """LLM超时错误"""
    pass

class MCPError(QYuanException):
    """MCP相关错误基类"""
    pass

class MCPConnectionError(MCPError):
    """MCP连接错误"""
    pass

class MCPOperationError(MCPError):
    """MCP操作错误"""
    pass

class DatabaseError(QYuanException):
    """数据库错误基类"""
    pass

class MemoryError(QYuanException):
    """记忆系统错误"""
    pass

class ValidationError(QYuanException):
    """验证错误"""
    pass

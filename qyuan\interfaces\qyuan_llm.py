# -*- coding: utf-8 -*-
"""
QYuan专用LLM接口 - 为QYuan定制的智能对话和推理能力
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass

from .llm_client import LLMClient, LLMMessage, LLMResponse
from ..core.exceptions import LLMError

@dataclass
class IntentResult:
    """意图识别结果"""
    intent: str
    confidence: float
    entities: Dict[str, Any]
    metadata: Dict[str, Any]

@dataclass
class ActionPlan:
    """行动计划"""
    goal: str
    steps: List[Dict[str, Any]]
    priority: int
    estimated_time: Optional[int] = None
    resources_needed: Optional[List[str]] = None

class QYuanLLM:
    """QYuan专用LLM接口"""
    
    def __init__(self, llm_client: LLMClient):
        self.client = llm_client
        self.logger = logging.getLogger("QYuan.QYuanLLM")
        
        # QYuan的系统提示词
        self.system_prompt = self._build_system_prompt()
        
        # 对话历史
        self.conversation_history: List[LLMMessage] = []
        self.max_history_length = 10  # 减少历史长度以提高响应速度
    
    def _build_system_prompt(self) -> str:
        """构建QYuan的系统提示词"""
        return """你是QYuan，一个硅基CEO助手，具有以下特点和能力：

## 身份定位
- 你是一个智能的CEO助手，专注于提高工作效率和决策质量
- 你具有感知、决策、执行、学习、沟通五大核心能力
- 你能够理解用户意图，制定行动计划，并协助执行任务

## 核心能力
1. **感知能力**: 理解屏幕内容、文档信息、用户输入
2. **决策能力**: 分析问题、制定策略、优化方案
3. **执行能力**: 操作鼠标键盘、处理文件、自动化任务
4. **学习能力**: 记忆重要信息、总结经验、持续改进
5. **沟通能力**: 清晰表达、主动汇报、友好交互

## 交互原则
- 始终保持专业、高效、友好的态度
- 主动理解用户需求，提供有价值的建议
- 在执行任务前确认关键细节
- 及时汇报进展和结果
- 遇到问题时主动寻求澄清

## 响应格式
请根据用户输入的类型，采用相应的响应格式：
- 日常对话：自然、友好的回复
- 任务请求：确认需求 → 制定计划 → 执行步骤
- 问题咨询：分析问题 → 提供方案 → 给出建议
- 状态查询：简洁明了的状态报告

现在，请以QYuan的身份与用户交互。"""
    
    async def chat(self, user_message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """智能对话接口"""
        try:
            # 添加用户消息到历史
            user_msg = LLMMessage(role="user", content=user_message)
            self.conversation_history.append(user_msg)
            
            # 构建完整的消息列表
            messages = self._build_messages(context)
            
            # 调用LLM
            response = await self.client.chat_completion(
                messages=messages,
                temperature=0.7,
                max_tokens=1000
            )
            
            # 添加助手回复到历史
            assistant_msg = LLMMessage(role="assistant", content=response.content)
            self.conversation_history.append(assistant_msg)
            
            # 限制历史长度
            self._trim_history()
            
            self.logger.info(f"QYuan回复: {response.content[:100]}...")
            return response.content
            
        except Exception as e:
            self.logger.error(f"QYuan对话失败: {e}")
            return f"抱歉，我遇到了一些技术问题：{e}。请稍后再试。"
    
    async def analyze_intent(self, user_message: str) -> IntentResult:
        """分析用户意图"""
        intent_prompt = f"""请分析以下用户消息的意图，并以JSON格式返回结果：

用户消息: "{user_message}"

请识别：
1. 主要意图类型（如：task_request, question, chat, command等）
2. 置信度（0-1之间的数值）
3. 提取的实体信息
4. 其他相关元数据

返回格式：
{{
    "intent": "意图类型",
    "confidence": 0.95,
    "entities": {{
        "action": "具体动作",
        "target": "目标对象",
        "parameters": {{}}
    }},
    "metadata": {{
        "urgency": "紧急程度",
        "complexity": "复杂度"
    }}
}}"""
        
        try:
            messages = [
                LLMMessage(role="system", content="你是一个专业的意图识别助手，请准确分析用户意图。"),
                LLMMessage(role="user", content=intent_prompt)
            ]
            
            response = await self.client.chat_completion(
                messages=messages,
                temperature=0.3,
                max_tokens=500
            )
            
            # 解析JSON响应
            result_data = json.loads(response.content)
            
            return IntentResult(
                intent=result_data.get("intent", "unknown"),
                confidence=result_data.get("confidence", 0.5),
                entities=result_data.get("entities", {}),
                metadata=result_data.get("metadata", {})
            )
            
        except Exception as e:
            self.logger.error(f"意图分析失败: {e}")
            return IntentResult(
                intent="unknown",
                confidence=0.0,
                entities={},
                metadata={"error": str(e)}
            )
    
    async def create_action_plan(self, goal: str, context: Optional[Dict[str, Any]] = None) -> ActionPlan:
        """创建行动计划"""
        planning_prompt = f"""作为QYuan，请为以下目标制定详细的行动计划：

目标: {goal}

上下文信息: {json.dumps(self._make_json_serializable(context or {}), ensure_ascii=False, indent=2)}

请制定一个可执行的行动计划，包括：
1. 具体的执行步骤
2. 每个步骤的详细说明
3. 所需的资源和工具
4. 预估的执行时间
5. 优先级评估

返回JSON格式：
{{
    "goal": "目标描述",
    "steps": [
        {{
            "step": 1,
            "action": "具体动作",
            "description": "详细说明",
            "tools": ["所需工具"],
            "estimated_time": "预估时间(分钟)"
        }}
    ],
    "priority": 5,
    "estimated_time": 30,
    "resources_needed": ["资源列表"]
}}"""
        
        try:
            messages = [
                LLMMessage(role="system", content=self.system_prompt),
                LLMMessage(role="user", content=planning_prompt)
            ]
            
            response = await self.client.chat_completion(
                messages=messages,
                temperature=0.5,
                max_tokens=1500
            )
            
            # 解析JSON响应
            plan_data = json.loads(response.content)
            
            return ActionPlan(
                goal=plan_data.get("goal", goal),
                steps=plan_data.get("steps", []),
                priority=plan_data.get("priority", 3),
                estimated_time=plan_data.get("estimated_time"),
                resources_needed=plan_data.get("resources_needed")
            )
            
        except Exception as e:
            self.logger.error(f"行动计划创建失败: {e}")
            return ActionPlan(
                goal=goal,
                steps=[{"step": 1, "action": "分析问题", "description": f"由于技术问题，需要手动处理: {e}"}],
                priority=3
            )
    
    async def analyze_screen_content(self, screen_description: str) -> Dict[str, Any]:
        """分析屏幕内容"""
        analysis_prompt = f"""请分析以下屏幕内容描述，提取关键信息：

屏幕内容: {screen_description}

请识别：
1. 当前应用程序或网页
2. 主要UI元素
3. 可执行的操作
4. 重要的文本信息
5. 异常或错误信息

返回JSON格式的分析结果。"""
        
        try:
            messages = [
                LLMMessage(role="system", content="你是一个专业的屏幕内容分析助手。"),
                LLMMessage(role="user", content=analysis_prompt)
            ]
            
            response = await self.client.chat_completion(
                messages=messages,
                temperature=0.3,
                max_tokens=800
            )
            
            return json.loads(response.content)
            
        except Exception as e:
            self.logger.error(f"屏幕内容分析失败: {e}")
            return {"error": str(e), "raw_content": screen_description}
    
    def _build_messages(self, context: Optional[Dict[str, Any]] = None) -> List[LLMMessage]:
        """构建消息列表"""
        messages = [LLMMessage(role="system", content=self.system_prompt)]
        
        # 添加上下文信息
        if context:
            # 处理datetime对象，转换为字符串
            safe_context = self._make_json_serializable(context)
            context_msg = f"当前上下文信息：\n{json.dumps(safe_context, ensure_ascii=False, indent=2)}"
            messages.append(LLMMessage(role="system", content=context_msg))
        
        # 添加对话历史
        messages.extend(self.conversation_history)
        
        return messages
    
    def _trim_history(self):
        """修剪对话历史"""
        if len(self.conversation_history) > self.max_history_length:
            # 保留最近的对话，但确保成对保留（用户-助手）
            self.conversation_history = self.conversation_history[-self.max_history_length:]
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history.clear()
        self.logger.info("对话历史已清空")
    
    def get_conversation_summary(self) -> str:
        """获取对话摘要"""
        if not self.conversation_history:
            return "暂无对话历史"

        summary = f"对话历史（最近{len(self.conversation_history)}条消息）：\n"
        for i, msg in enumerate(self.conversation_history[-10:], 1):
            role_name = "用户" if msg.role == "user" else "QYuan"
            content_preview = msg.content[:50] + "..." if len(msg.content) > 50 else msg.content
            summary += f"{i}. {role_name}: {content_preview}\n"

        return summary

    def _make_json_serializable(self, obj: Any) -> Any:
        """将对象转换为JSON可序列化的格式"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            # 处理自定义对象
            return self._make_json_serializable(obj.__dict__)
        else:
            return obj

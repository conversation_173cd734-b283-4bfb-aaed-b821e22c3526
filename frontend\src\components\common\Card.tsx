// -*- coding: utf-8 -*-

import React from 'react';
import { cn } from '../../utils';
import { BaseComponentProps } from '../../types';

/**
 * Card组件属性接口
 */
interface CardProps extends BaseComponentProps {
  title?: string;
  subtitle?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  border?: boolean;
  hover?: boolean;
}

/**
 * CardHeader组件属性接口
 */
interface CardHeaderProps extends BaseComponentProps {
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
}

/**
 * CardContent组件属性接口
 */
interface CardContentProps extends BaseComponentProps {}

/**
 * CardFooter组件属性接口
 */
interface CardFooterProps extends BaseComponentProps {}

/**
 * Card组件
 * 通用卡片组件，支持标题、内容和底部区域
 * 严格按照代码规范的单一职责原则
 */
export function Card({
  children,
  className,
  title,
  subtitle,
  padding = 'md',
  shadow = 'sm',
  border = true,
  hover = false,
  ...props
}: CardProps) {
  /**
   * 获取卡片样式类名
   */
  const getCardClasses = () => {
    const baseClasses = [
      'bg-white',
      'dark:bg-gray-800',
      'rounded-lg',
      'transition-all',
      'duration-200',
    ];

    // 内边距样式
    const paddingClasses = {
      none: [],
      sm: ['p-3'],
      md: ['p-4'],
      lg: ['p-6'],
    };

    // 阴影样式
    const shadowClasses = {
      none: [],
      sm: ['shadow-sm'],
      md: ['shadow-md'],
      lg: ['shadow-lg'],
    };

    // 边框样式
    const borderClasses = border
      ? ['border', 'border-gray-200', 'dark:border-gray-700']
      : [];

    // 悬停效果
    const hoverClasses = hover
      ? ['hover:shadow-md', 'hover:scale-[1.02]', 'cursor-pointer']
      : [];

    return cn(
      baseClasses,
      paddingClasses[padding],
      shadowClasses[shadow],
      borderClasses,
      hoverClasses,
      className
    );
  };

  return (
    <div className={getCardClasses()} {...props}>
      {(title || subtitle) && (
        <CardHeader title={title} subtitle={subtitle} />
      )}
      {children}
    </div>
  );
}

/**
 * CardHeader组件
 * 卡片头部组件
 */
export function CardHeader({
  children,
  className,
  title,
  subtitle,
  actions,
  ...props
}: CardHeaderProps) {
  const getHeaderClasses = () => {
    return cn(
      'flex',
      'items-start',
      'justify-between',
      'mb-4',
      className
    );
  };

  return (
    <div className={getHeaderClasses()} {...props}>
      <div className="flex-1">
        {title && (
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {title}
          </h3>
        )}
        {subtitle && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {subtitle}
          </p>
        )}
        {children}
      </div>
      {actions && (
        <div className="flex items-center space-x-2 ml-4">
          {actions}
        </div>
      )}
    </div>
  );
}

/**
 * CardContent组件
 * 卡片内容组件
 */
export function CardContent({
  children,
  className,
  ...props
}: CardContentProps) {
  const getContentClasses = () => {
    return cn('text-gray-700', 'dark:text-gray-300', className);
  };

  return (
    <div className={getContentClasses()} {...props}>
      {children}
    </div>
  );
}

/**
 * CardFooter组件
 * 卡片底部组件
 */
export function CardFooter({
  children,
  className,
  ...props
}: CardFooterProps) {
  const getFooterClasses = () => {
    return cn(
      'flex',
      'items-center',
      'justify-between',
      'pt-4',
      'mt-4',
      'border-t',
      'border-gray-200',
      'dark:border-gray-700',
      className
    );
  };

  return (
    <div className={getFooterClasses()} {...props}>
      {children}
    </div>
  );
}

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基本功能测试脚本

该脚本用于测试键盘控制器的基本功能，不需要用户交互。
"""

import time
import logging
import sys
from keyboard_controller import KeyboardController

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("BasicFunctionTest")

def test_controller_initialization():
    """测试控制器初始化"""
    logger.info("测试控制器初始化...")
    try:
        controller = KeyboardController()
        logger.info("✅ 控制器初始化成功")
        return True, controller
    except Exception as e:
        logger.error(f"❌ 控制器初始化失败: {str(e)}")
        return False, None

def test_key_conversion():
    """测试键名转换功能"""
    logger.info("测试键名转换功能...")
    try:
        controller = KeyboardController()
        
        # 测试普通字符
        key_obj = controller._convert_to_key('a')
        assert key_obj == 'a', "普通字符转换失败"
        
        # 测试特殊键
        key_obj = controller._convert_to_key('enter')
        assert hasattr(key_obj, 'name'), "特殊键转换失败"
        
        # 测试功能键
        key_obj = controller._convert_to_key('f1')
        assert hasattr(key_obj, 'name'), "功能键转换失败"
        
        logger.info("✅ 键名转换功能测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 键名转换功能测试失败: {str(e)}")
        return False

def test_clipboard_operations():
    """测试剪贴板操作"""
    logger.info("测试剪贴板操作...")
    try:
        controller = KeyboardController()
        
        # 保存原始剪贴板内容
        original_content = controller.get_clipboard_text()
        
        # 测试设置剪贴板
        test_text = "这是一个测试文本"
        controller.set_clipboard_text(test_text)
        
        # 测试获取剪贴板
        retrieved_text = controller.get_clipboard_text()
        assert retrieved_text == test_text, f"剪贴板内容不匹配: 期望 '{test_text}', 实际 '{retrieved_text}'"
        
        # 恢复原始剪贴板内容
        controller.set_clipboard_text(original_content)
        
        logger.info("✅ 剪贴板操作测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 剪贴板操作测试失败: {str(e)}")
        return False

def test_ime_detection():
    """测试输入法检测功能"""
    logger.info("测试输入法检测功能...")
    try:
        controller = KeyboardController()
        
        # 测试输入法状态检测
        ime_status = controller.is_ime_enabled()
        logger.info(f"当前输入法状态: {'中文' if ime_status else '英文'}")
        
        logger.info("✅ 输入法检测功能测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 输入法检测功能测试失败: {str(e)}")
        return False

def test_wait_functions():
    """测试等待功能"""
    logger.info("测试等待功能...")
    try:
        controller = KeyboardController()
        
        # 测试基本等待
        start_time = time.time()
        controller._wait(100)  # 等待100毫秒
        elapsed_time = (time.time() - start_time) * 1000
        assert 90 <= elapsed_time <= 150, f"等待时间不准确: {elapsed_time}ms"
        
        # 测试随机等待
        start_time = time.time()
        controller._add_random_wait(50, 10)  # 基础50ms，变化10ms
        elapsed_time = (time.time() - start_time) * 1000
        assert 40 <= elapsed_time <= 80, f"随机等待时间不在预期范围: {elapsed_time}ms"
        
        logger.info("✅ 等待功能测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 等待功能测试失败: {str(e)}")
        return False

def test_special_keys_mapping():
    """测试特殊键映射"""
    logger.info("测试特殊键映射...")
    try:
        controller = KeyboardController()
        
        # 测试一些常用特殊键
        special_keys = ['enter', 'tab', 'space', 'ctrl', 'alt', 'shift', 'f1', 'f12']
        
        for key_name in special_keys:
            key_obj = controller._convert_to_key(key_name)
            assert key_obj is not None, f"特殊键 '{key_name}' 转换失败"
        
        logger.info("✅ 特殊键映射测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 特殊键映射测试失败: {str(e)}")
        return False

def main():
    """主函数，运行所有基本功能测试"""
    logger.info("开始基本功能测试")
    print("=" * 50)
    print("Auto Keyboard Controller 基本功能测试")
    print("=" * 50)
    
    tests = [
        ("控制器初始化", test_controller_initialization),
        ("键名转换功能", test_key_conversion),
        ("剪贴板操作", test_clipboard_operations),
        ("输入法检测功能", test_ime_detection),
        ("等待功能", test_wait_functions),
        ("特殊键映射", test_special_keys_mapping),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n正在运行: {test_name}")
        try:
            if test_name == "控制器初始化":
                success, controller = test_func()
            else:
                success = test_func()
            
            if success:
                passed_tests += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed_tests}/{total_tests} 通过")
    print("=" * 50)
    
    if passed_tests == total_tests:
        print("🎉 所有基本功能测试通过！")
        logger.info("所有基本功能测试通过")
        return 0
    else:
        print(f"⚠️  有 {total_tests - passed_tests} 个测试失败")
        logger.warning(f"有 {total_tests - passed_tests} 个测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())

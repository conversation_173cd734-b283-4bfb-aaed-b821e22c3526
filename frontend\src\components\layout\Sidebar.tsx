// -*- coding: utf-8 -*-

import React, { useState } from 'react';
import {
  MessageSquare,
  Activity,
  Settings,
  Brain,
  Eye,
  Zap,
  BookOpen,
  BarChart3,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { cn } from '../../utils';
import { BaseComponentProps } from '../../types';
import { Button } from '../common/Button';

/**
 * 导航菜单项接口
 */
interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  active?: boolean;
  badge?: string | number;
}

/**
 * Sidebar组件属性接口
 */
interface SidebarProps extends BaseComponentProps {
  isOpen: boolean;
  onToggle: () => void;
}

/**
 * Sidebar组件
 * 侧边栏导航组件，包含主要功能导航
 * 严格按照代码规范的单一职责原则
 */
export function Sidebar({
  className,
  isOpen,
  onToggle,
  ...props
}: SidebarProps) {
  const [activeItem, setActiveItem] = useState<string>('chat');

  /**
   * 导航菜单项配置
   */
  const navigationItems: NavigationItem[] = [
    {
      id: 'chat',
      label: '对话交互',
      icon: MessageSquare,
      active: activeItem === 'chat',
    },
    {
      id: 'status',
      label: '系统状态',
      icon: Activity,
      active: activeItem === 'status',
      badge: '5',
    },
    {
      id: 'perception',
      label: '感知引擎',
      icon: Eye,
      active: activeItem === 'perception',
    },
    {
      id: 'decision',
      label: '决策引擎',
      icon: Brain,
      active: activeItem === 'decision',
    },
    {
      id: 'execution',
      label: '执行引擎',
      icon: Zap,
      active: activeItem === 'execution',
    },
    {
      id: 'learning',
      label: '学习引擎',
      icon: BookOpen,
      active: activeItem === 'learning',
    },
    {
      id: 'analytics',
      label: '数据分析',
      icon: BarChart3,
      active: activeItem === 'analytics',
    },
    {
      id: 'settings',
      label: '系统设置',
      icon: Settings,
      active: activeItem === 'settings',
    },
  ];

  /**
   * 获取侧边栏样式类名
   */
  const getSidebarClasses = () => {
    return cn(
      'fixed',
      'left-0',
      'top-0',
      'h-full',
      'bg-white',
      'dark:bg-gray-800',
      'border-r',
      'border-gray-200',
      'dark:border-gray-700',
      'shadow-lg',
      'transition-all',
      'duration-300',
      'z-50',
      isOpen ? 'w-64' : 'w-16',
      className
    );
  };

  /**
   * 获取导航项样式类名
   */
  const getNavItemClasses = (item: NavigationItem) => {
    return cn(
      'flex',
      'items-center',
      'w-full',
      'px-3',
      'py-2',
      'text-left',
      'rounded-md',
      'transition-colors',
      'duration-200',
      'group',
      item.active
        ? [
            'bg-primary-100',
            'text-primary-700',
            'dark:bg-primary-900',
            'dark:text-primary-300',
          ]
        : [
            'text-gray-700',
            'dark:text-gray-300',
            'hover:bg-gray-100',
            'dark:hover:bg-gray-700',
            'hover:text-gray-900',
            'dark:hover:text-gray-100',
          ]
    );
  };

  /**
   * 处理导航项点击
   */
  const handleNavItemClick = (itemId: string) => {
    setActiveItem(itemId);
  };

  /**
   * 渲染导航项
   */
  const renderNavItem = (item: NavigationItem) => {
    const IconComponent = item.icon;

    return (
      <Button
        key={item.id}
        variant="ghost"
        className={getNavItemClasses(item)}
        onClick={() => handleNavItemClick(item.id)}
      >
        <IconComponent className="h-5 w-5 flex-shrink-0" />
        
        {isOpen && (
          <>
            <span className="ml-3 flex-1 text-sm font-medium">
              {item.label}
            </span>
            
            {item.badge && (
              <span className="ml-auto bg-primary-600 text-white text-xs rounded-full px-2 py-1">
                {item.badge}
              </span>
            )}
          </>
        )}
      </Button>
    );
  };

  return (
    <aside className={getSidebarClasses()} {...props}>
      {/* 侧边栏头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        {isOpen && (
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <Brain className="h-5 w-5 text-white" />
            </div>
            <span className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              QYuan
            </span>
          </div>
        )}
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggle}
          className="p-1"
        >
          {isOpen ? (
            <ChevronLeft className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 p-4 space-y-1">
        {navigationItems.map(renderNavItem)}
      </nav>

      {/* 侧边栏底部 */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        {isOpen && (
          <div className="text-xs text-gray-500 dark:text-gray-400">
            <p>QYuan v2.0.0</p>
            <p>硅基CEO系统</p>
          </div>
        )}
      </div>
    </aside>
  );
}

# -*- coding: utf-8 -*-
"""
决策引擎基础类和接口定义
严格按照代码规范，每个文件不超过200行，单一职责原则
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

class IntentType(Enum):
    """意图类型枚举"""
    CLICK = "click"
    TYPE = "type"
    SCROLL = "scroll"
    NAVIGATE = "navigate"
    SEARCH = "search"
    COPY = "copy"
    PASTE = "paste"
    OPEN = "open"
    CLOSE = "close"
    WAIT = "wait"
    UNKNOWN = "unknown"

class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

class ActionType(Enum):
    """行动类型枚举"""
    UI_INTERACTION = "ui_interaction"
    SYSTEM_OPERATION = "system_operation"
    DATA_PROCESSING = "data_processing"
    NAVIGATION = "navigation"
    VERIFICATION = "verification"

@dataclass
class UserIntent:
    """用户意图"""
    intent_type: IntentType
    description: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    confidence: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    context: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.context is None:
            self.context = {}

@dataclass
class Task:
    """任务定义"""
    id: str
    title: str
    description: str
    intent: UserIntent
    priority: TaskPriority = TaskPriority.NORMAL
    estimated_duration: float = 0.0
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)

@dataclass
class Action:
    """行动定义"""
    id: str
    action_type: ActionType
    description: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    expected_result: Optional[str] = None
    timeout: float = 30.0
    retry_count: int = 3
    validation_criteria: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.validation_criteria is None:
            self.validation_criteria = {}

@dataclass
class ActionPlan:
    """行动计划"""
    id: str
    task_id: str
    actions: List[Action] = field(default_factory=list)
    estimated_duration: float = 0.0
    success_criteria: Dict[str, Any] = field(default_factory=dict)
    fallback_plan: Optional['ActionPlan'] = None
    created_at: datetime = field(default_factory=datetime.now)
    
    def add_action(self, action: Action):
        """添加行动"""
        self.actions.append(action)
        self.estimated_duration += action.timeout

@dataclass
class DecisionContext:
    """决策上下文"""
    current_screen_state: Optional[Dict[str, Any]] = None
    system_state: Optional[Dict[str, Any]] = None
    user_history: List[Dict[str, Any]] = field(default_factory=list)
    available_actions: List[str] = field(default_factory=list)
    constraints: Dict[str, Any] = field(default_factory=dict)
    goals: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class DecisionResult:
    """决策结果"""
    success: bool
    action_plan: Optional[ActionPlan] = None
    confidence: float = 0.0
    reasoning: str = ""
    alternatives: List[ActionPlan] = field(default_factory=list)
    processing_time: float = 0.0
    error_message: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)

class IntentParserBase(ABC):
    """意图解析器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
    
    @abstractmethod
    async def parse_intent(self, user_input: str, context: Optional[DecisionContext] = None) -> UserIntent:
        """解析用户意图"""
        pass
    
    async def is_available(self) -> bool:
        """检查解析器是否可用"""
        return self.enabled

class TaskDecomposerBase(ABC):
    """任务分解器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
    
    @abstractmethod
    async def decompose_task(self, intent: UserIntent, context: DecisionContext) -> List[Task]:
        """分解任务"""
        pass
    
    async def is_available(self) -> bool:
        """检查分解器是否可用"""
        return self.enabled

class ActionPlannerBase(ABC):
    """行动规划器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
    
    @abstractmethod
    async def create_plan(self, task: Task, context: DecisionContext) -> ActionPlan:
        """创建行动计划"""
        pass
    
    @abstractmethod
    async def optimize_plan(self, plan: ActionPlan, context: DecisionContext) -> ActionPlan:
        """优化行动计划"""
        pass
    
    async def is_available(self) -> bool:
        """检查规划器是否可用"""
        return self.enabled

class ActionSelectorBase(ABC):
    """行动选择器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
    
    @abstractmethod
    async def select_best_action(self, actions: List[Action], context: DecisionContext) -> Optional[Action]:
        """选择最佳行动"""
        pass
    
    @abstractmethod
    async def evaluate_action(self, action: Action, context: DecisionContext) -> float:
        """评估行动质量"""
        pass
    
    async def is_available(self) -> bool:
        """检查选择器是否可用"""
        return self.enabled

class ContextAnalyzerBase(ABC):
    """上下文分析器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
    
    @abstractmethod
    async def analyze_context(self, perception_data: Dict[str, Any]) -> DecisionContext:
        """分析决策上下文"""
        pass
    
    @abstractmethod
    async def update_context(self, context: DecisionContext, new_data: Dict[str, Any]) -> DecisionContext:
        """更新上下文"""
        pass
    
    async def is_available(self) -> bool:
        """检查分析器是否可用"""
        return self.enabled

class MemoryIntegratorBase(ABC):
    """记忆集成器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
    
    @abstractmethod
    async def store_decision(self, intent: UserIntent, plan: ActionPlan, result: Dict[str, Any]):
        """存储决策记录"""
        pass
    
    @abstractmethod
    async def retrieve_similar_cases(self, intent: UserIntent, limit: int = 5) -> List[Dict[str, Any]]:
        """检索相似案例"""
        pass
    
    @abstractmethod
    async def learn_from_feedback(self, decision_id: str, feedback: Dict[str, Any]):
        """从反馈中学习"""
        pass
    
    async def is_available(self) -> bool:
        """检查集成器是否可用"""
        return self.enabled

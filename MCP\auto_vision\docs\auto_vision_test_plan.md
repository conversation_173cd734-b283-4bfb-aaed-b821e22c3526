# Auto Vision 功能测试计划

本文档详细说明 Auto Vision 项目的功能测试计划，包括测试范围、测试用例和测试方法。

## 1. 测试范围

Auto Vision 项目提供以下主要功能，本测试计划将覆盖所有这些功能：

1. **屏幕截图功能**：
   - 全屏截图
   - 区域截图
   - 带坐标的全屏截图
   - 带坐标的区域截图

2. **OCR文本识别功能**：
   - Windows OCR引擎
   - EasyOCR引擎
   - 百度OCR引擎
   - 自动选择引擎和备用引擎机制
   - 结构化返回格式

3. **UI元素识别功能**：
   - Win32GUI后端（窗口级操作）
   - PyWinAuto后端（窗口内元素识别）
   - 窗口查找和操作
   - 元素查找和属性获取

4. **视觉LLM分析功能**：
   - 全屏分析
   - 区域分析
   - 最新截图分析
   - 组合分析（同时获取OCR、UI元素和视觉LLM分析结果）

5. **MCP服务器功能**：
   - 提供屏幕截图、OCR文本识别和UI元素识别功能的MCP服务器
   - 支持stdio和tcp传输方式

## 2. 测试用例

### 2.1 屏幕截图功能测试

#### 2.1.1 全屏截图测试
- **测试目的**：验证全屏截图功能是否正常工作
- **测试步骤**：
  1. 调用全屏截图功能
  2. 检查返回的截图对象和保存的文件
- **预期结果**：成功捕获全屏，返回有效的截图对象和文件路径

#### 2.1.2 区域截图测试
- **测试目的**：验证区域截图功能是否正常工作
- **测试步骤**：
  1. 定义一个区域（如屏幕中央区域）
  2. 调用区域截图功能
  3. 检查返回的截图对象和保存的文件
- **预期结果**：成功捕获指定区域，返回有效的截图对象和文件路径

#### 2.1.3 带坐标的全屏截图测试
- **测试目的**：验证带坐标的全屏截图功能是否正常工作
- **测试步骤**：
  1. 调用带坐标的全屏截图功能
  2. 检查返回的结果和保存的文件
- **预期结果**：成功捕获全屏并添加坐标网格，返回有效的结果和文件路径

#### 2.1.4 带坐标的区域截图测试
- **测试目的**：验证带坐标的区域截图功能是否正常工作
- **测试步骤**：
  1. 定义一个区域
  2. 调用带坐标的区域截图功能
  3. 检查返回的结果和保存的文件
- **预期结果**：成功捕获指定区域并添加坐标网格，返回有效的结果和文件路径

### 2.2 OCR文本识别功能测试

#### 2.2.1 Windows OCR引擎测试
- **测试目的**：验证Windows OCR引擎是否正常工作
- **测试步骤**：
  1. 创建OCR对象（指定Windows OCR引擎）
  2. 识别一个包含文本的图像
  3. 检查返回的结果
- **预期结果**：成功识别图像中的文本，返回有效的结果

#### 2.2.2 EasyOCR引擎测试
- **测试目的**：验证EasyOCR引擎是否正常工作
- **测试步骤**：
  1. 创建OCR对象（指定EasyOCR引擎）
  2. 识别一个包含文本的图像
  3. 检查返回的结果
- **预期结果**：成功识别图像中的文本，返回有效的结果

#### 2.2.3 百度OCR引擎测试
- **测试目的**：验证百度OCR引擎是否正常工作
- **测试步骤**：
  1. 创建OCR对象（指定百度OCR引擎）
  2. 识别一个包含文本的图像
  3. 检查返回的结果
- **预期结果**：成功识别图像中的文本，返回有效的结果

#### 2.2.4 自动选择引擎测试
- **测试目的**：验证自动选择引擎功能是否正常工作
- **测试步骤**：
  1. 创建OCR对象（指定自动选择引擎）
  2. 识别一个包含文本的图像
  3. 检查返回的结果
- **预期结果**：成功识别图像中的文本，返回有效的结果

#### 2.2.5 备用引擎机制测试
- **测试目的**：验证备用引擎机制是否正常工作
- **测试步骤**：
  1. 创建OCR对象（启用备用引擎）
  2. 故意使主引擎失败
  3. 检查是否自动切换到备用引擎
- **预期结果**：主引擎失败后自动切换到备用引擎，成功识别图像中的文本

#### 2.2.6 结构化返回格式测试
- **测试目的**：验证结构化返回格式是否正确
- **测试步骤**：
  1. 创建OCR对象
  2. 识别一个包含文本的图像，指定结构化返回格式
  3. 检查返回的结果
- **预期结果**：返回结构化的结果，包含文本、位置和置信度信息

### 2.3 UI元素识别功能测试

#### 2.3.1 Win32GUI后端测试
- **测试目的**：验证Win32GUI后端是否正常工作
- **测试步骤**：
  1. 创建UI自动化对象（指定Win32GUI后端）
  2. 获取前台窗口
  3. 检查返回的窗口对象
- **预期结果**：成功获取前台窗口，返回有效的窗口对象

#### 2.3.2 PyWinAuto后端测试
- **测试目的**：验证PyWinAuto后端是否正常工作
- **测试步骤**：
  1. 创建UI自动化对象（指定PyWinAuto后端）
  2. 获取前台窗口
  3. 检查返回的窗口对象
- **预期结果**：成功获取前台窗口，返回有效的窗口对象

#### 2.3.3 窗口查找测试
- **测试目的**：验证窗口查找功能是否正常工作
- **测试步骤**：
  1. 创建UI自动化对象
  2. 通过标题查找一个已知窗口
  3. 检查返回的窗口对象
- **预期结果**：成功找到指定窗口，返回有效的窗口对象

#### 2.3.4 窗口操作测试
- **测试目的**：验证窗口操作功能是否正常工作
- **测试步骤**：
  1. 创建UI自动化对象
  2. 获取一个窗口
  3. 执行激活、最大化、最小化、还原等操作
- **预期结果**：成功执行窗口操作，窗口状态按预期变化

#### 2.3.5 元素查找测试
- **测试目的**：验证元素查找功能是否正常工作
- **测试步骤**：
  1. 创建UI自动化对象（指定PyWinAuto后端）
  2. 获取一个窗口
  3. 查找窗口中的元素
- **预期结果**：成功找到窗口中的元素，返回有效的元素属性

#### 2.3.6 元素属性获取测试
- **测试目的**：验证元素属性获取功能是否正常工作
- **测试步骤**：
  1. 创建UI自动化对象
  2. 获取一个元素
  3. 检查元素的属性（如名称、类型、位置等）
- **预期结果**：成功获取元素的属性，返回有效的属性值

### 2.4 视觉LLM分析功能测试

#### 2.4.1 全屏分析测试
- **测试目的**：验证全屏分析功能是否正常工作
- **测试步骤**：
  1. 创建屏幕分析对象
  2. 调用全屏分析功能
  3. 检查返回的分析结果
- **预期结果**：成功分析全屏，返回有效的分析结果

#### 2.4.2 区域分析测试
- **测试目的**：验证区域分析功能是否正常工作
- **测试步骤**：
  1. 创建屏幕分析对象
  2. 定义一个区域
  3. 调用区域分析功能
  4. 检查返回的分析结果
- **预期结果**：成功分析指定区域，返回有效的分析结果

#### 2.4.3 最新截图分析测试
- **测试目的**：验证最新截图分析功能是否正常工作
- **测试步骤**：
  1. 创建屏幕分析对象
  2. 先捕获一个截图
  3. 调用最新截图分析功能
  4. 检查返回的分析结果
- **预期结果**：成功分析最新截图，返回有效的分析结果

#### 2.4.4 组合分析测试
- **测试目的**：验证组合分析功能是否正常工作
- **测试步骤**：
  1. 创建屏幕分析对象
  2. 调用组合分析功能
  3. 检查返回的OCR、UI元素和视觉LLM分析结果
- **预期结果**：成功执行组合分析，返回有效的OCR、UI元素和视觉LLM分析结果

### 2.5 MCP服务器功能测试

#### 2.5.1 服务器启动测试
- **测试目的**：验证MCP服务器是否能正常启动
- **测试步骤**：
  1. 启动MCP服务器
  2. 检查服务器是否成功运行
- **预期结果**：服务器成功启动，无错误信息

#### 2.5.2 屏幕截图工具测试
- **测试目的**：验证MCP服务器提供的屏幕截图工具是否正常工作
- **测试步骤**：
  1. 启动MCP服务器
  2. 调用屏幕截图工具
  3. 检查返回的结果
- **预期结果**：成功捕获屏幕，返回有效的结果

#### 2.5.3 OCR文本识别工具测试
- **测试目的**：验证MCP服务器提供的OCR文本识别工具是否正常工作
- **测试步骤**：
  1. 启动MCP服务器
  2. 调用OCR文本识别工具
  3. 检查返回的结果
- **预期结果**：成功识别文本，返回有效的结果

#### 2.5.4 UI元素识别工具测试
- **测试目的**：验证MCP服务器提供的UI元素识别工具是否正常工作
- **测试步骤**：
  1. 启动MCP服务器
  2. 调用UI元素识别工具
  3. 检查返回的结果
- **预期结果**：成功识别UI元素，返回有效的结果

#### 2.5.5 视觉LLM分析工具测试
- **测试目的**：验证MCP服务器提供的视觉LLM分析工具是否正常工作
- **测试步骤**：
  1. 启动MCP服务器
  2. 调用视觉LLM分析工具
  3. 检查返回的结果
- **预期结果**：成功分析屏幕，返回有效的结果

#### 2.5.6 TCP传输测试
- **测试目的**：验证MCP服务器的TCP传输方式是否正常工作
- **测试步骤**：
  1. 以TCP方式启动MCP服务器
  2. 通过TCP连接调用服务器功能
  3. 检查返回的结果
- **预期结果**：成功通过TCP连接调用服务器功能，返回有效的结果

## 3. 测试环境

- **操作系统**：Windows 10/11
- **Python版本**：3.8+
- **依赖库**：PIL, pyautogui, pywin32, pywinauto, easyocr, requests, numpy
- **API密钥**：Gemini API密钥（用于视觉LLM分析）、百度OCR API密钥（用于百度OCR引擎）

## 4. 测试执行

测试将使用Python的unittest框架执行，测试脚本位于`tests`目录下。

## 5. 测试报告

测试完成后，将生成测试报告，包括测试结果摘要和详细的测试结果。

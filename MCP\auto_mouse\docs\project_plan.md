# 自动控制Windows鼠标的MCP服务器项目规划

## 1. 项目概述

我们将创建一个MCP服务器，提供一系列工具，使大型语言模型和其他应用程序（如VSCode）能够控制Windows系统上的鼠标操作。这将使AI助手和其他应用能够执行各种鼠标相关任务，如移动鼠标、点击、拖拽等。

## 2. 技术选择

- **编程语言**：Python（易于使用，有丰富的库支持）
- **MCP SDK**：使用Python的FastMCP库
- **鼠标控制库**：PyAutoGUI（跨平台的GUI自动化库）
- **传输方式**：标准输入/输出（Stdio）传输

## 3. 项目结构

```
auto_mouse/
├── docs/
│   ├── mcp_server_guide.md
│   └── project_plan.md         # 项目规划文档
├── src/
│   ├── __init__.py
│   ├── auto_mouse_server.py    # MCP服务器主文件
│   ├── mouse_controller.py     # 鼠标控制逻辑
│   └── utils/
│       ├── __init__.py
│       └── helpers.py          # 辅助函数
├── tests/
│   ├── __init__.py
│   └── test_mouse_controller.py # 单元测试
├── requirements.txt            # 项目依赖
└── README.md                   # 项目说明
```

## 4. 功能规划

我们将实现以下鼠标控制功能，专注于基于坐标的操作：

### 基础功能
1. **移动鼠标**：将鼠标移动到指定坐标
2. **鼠标点击**：在当前位置或指定位置执行点击（左键、右键、中键）
3. **鼠标拖拽**：从一个位置拖拽到另一个位置
4. **鼠标滚动**：执行滚轮滚动操作

### 辅助功能
1. **获取鼠标位置**：返回当前鼠标坐标
2. **获取屏幕分辨率**：返回屏幕尺寸信息

## 5. 工具定义

每个工具将使用JSON Schema定义其输入参数。以下是一些主要工具的定义示例：

### 移动鼠标工具
```json
{
  "name": "move_mouse",
  "description": "将鼠标移动到指定坐标。支持绝对坐标或区域坐标，并会添加随机偏移。如果未指定坐标，则返回当前鼠标位置。",
  "inputSchema": {
    "type": "object",
    "properties": {
      "x": {
        "type": ["integer", "null"],
        "description": "目标X坐标（可选，如果不提供则返回当前位置）"
      },
      "y": {
        "type": ["integer", "null"],
        "description": "目标Y坐标（可选，如果不提供则返回当前位置）"
      },
      "left": {
        "type": ["integer", "null"],
        "description": "区域左边界坐标（与x/y互斥，用于区域定位）"
      },
      "top": {
        "type": ["integer", "null"],
        "description": "区域上边界坐标（与x/y互斥，用于区域定位）"
      },
      "right": {
        "type": ["integer", "null"],
        "description": "区域右边界坐标（与x/y互斥，用于区域定位）"
      },
      "bottom": {
        "type": ["integer", "null"],
        "description": "区域下边界坐标（与x/y互斥，用于区域定位）"
      },
      "duration": {
        "type": "number",
        "description": "移动持续时间（秒）",
        "default": 0.5
      },
      "random_offset": {
        "type": "boolean",
        "description": "是否添加随机偏移（1-3像素）",
        "default": true
      }
    }
  },
  "annotations": {
    "title": "移动鼠标",
    "readOnlyHint": false,
    "destructiveHint": false,
    "idempotentHint": false,
    "openWorldHint": true
  }
}
```

### 鼠标点击工具
```json
{
  "name": "mouse_click",
  "description": "在指定位置执行鼠标点击。如果未指定坐标，则在当前位置点击。",
  "inputSchema": {
    "type": "object",
    "properties": {
      "x": {
        "type": ["integer", "null"],
        "description": "点击的X坐标（可选）"
      },
      "y": {
        "type": ["integer", "null"],
        "description": "点击的Y坐标（可选）"
      },
      "button": {
        "type": "string",
        "description": "鼠标按钮 (\"left\", \"right\", \"middle\")",
        "default": "left"
      },
      "clicks": {
        "type": "integer",
        "description": "点击次数",
        "default": 1
      },
      "interval": {
        "type": "number",
        "description": "点击之间的间隔（秒）",
        "default": 0.1
      }
    }
  },
  "annotations": {
    "title": "鼠标点击",
    "readOnlyHint": false,
    "destructiveHint": true,
    "idempotentHint": false,
    "openWorldHint": true
  }
}
```

### 鼠标拖拽工具
```json
{
  "name": "mouse_drag",
  "description": "执行鼠标拖拽操作。如果未指定起始坐标，则使用当前鼠标位置。",
  "inputSchema": {
    "type": "object",
    "properties": {
      "start_x": {
        "type": ["integer", "null"],
        "description": "起始X坐标（可选，如果不提供则使用当前位置）"
      },
      "start_y": {
        "type": ["integer", "null"],
        "description": "起始Y坐标（可选，如果不提供则使用当前位置）"
      },
      "end_x": {
        "type": "integer",
        "description": "结束X坐标"
      },
      "end_y": {
        "type": "integer",
        "description": "结束Y坐标"
      },
      "duration": {
        "type": "number",
        "description": "拖拽持续时间（秒）",
        "default": 0.5
      },
      "button": {
        "type": "string",
        "description": "鼠标按钮 (\"left\", \"right\", \"middle\")",
        "default": "left"
      }
    },
    "required": ["end_x", "end_y"]
  },
  "annotations": {
    "title": "鼠标拖拽",
    "readOnlyHint": false,
    "destructiveHint": true,
    "idempotentHint": false,
    "openWorldHint": true
  }
}
```

### 鼠标滚动工具
```json
{
  "name": "mouse_scroll",
  "description": "执行鼠标滚动操作。",
  "inputSchema": {
    "type": "object",
    "properties": {
      "amount": {
        "type": ["integer", "null"],
        "description": "滚动的数量（默认为3）",
        "default": 3
      },
      "x": {
        "type": ["integer", "null"],
        "description": "滚动位置的X坐标（可选，如果不提供则使用当前位置）"
      },
      "y": {
        "type": ["integer", "null"],
        "description": "滚动位置的Y坐标（可选，如果不提供则使用当前位置）"
      },
      "direction": {
        "type": "string",
        "description": "滚动方向 (\"up\" 向上, \"down\" 向下)",
        "default": "up"
      }
    }
  },
  "annotations": {
    "title": "鼠标滚动",
    "readOnlyHint": false,
    "destructiveHint": false,
    "idempotentHint": false,
    "openWorldHint": true
  }
}
```

## 6. 实现计划

### 第一阶段：基础设施
1. 设置项目结构和依赖
2. 实现基本的MCP服务器框架
3. 创建鼠标控制器类

### 第二阶段：基础功能
1. 实现移动鼠标功能
2. 实现鼠标点击功能
3. 实现鼠标拖拽功能
4. 实现鼠标滚动功能

### 第三阶段：辅助功能
1. 实现获取鼠标位置功能
2. 实现获取屏幕分辨率功能
3. 实现区域坐标和随机偏移功能

### 第四阶段：测试和优化
1. 编写单元测试
2. 进行集成测试
3. 优化性能和错误处理
4. 完善文档

## 7. 安全考虑

1. **输入验证**：严格验证所有工具参数
2. **操作限制**：可能需要限制鼠标移动范围
3. **用户确认**：对于某些敏感操作可能需要用户确认
4. **错误处理**：妥善处理所有可能的异常情况

## 8. 与其他应用集成

1. 配置VSCode连接到我们的MCP服务器
2. 配置Claude Desktop连接到我们的MCP服务器
3. 测试工具在不同环境中的可用性和性能
4. 收集用户反馈并进行改进

## 9. 后续扩展可能性

1. 添加更多高级鼠标操作
2. 添加窗口管理功能
3. 实现更复杂的自动化序列
4. 增强随机性和人类化操作模拟

## 10. 注意事项

- 专注于基于坐标的鼠标操作，不包含图像识别功能
- 确保工具设计简洁明了，每个工具只负责一个功能
- 保持代码模块化，便于维护和扩展
- 提供详细的文档和示例，方便用户理解和使用

## 11. 随机性和人类化操作

为了使鼠标操作更加自然，模拟人类行为，我们实现了以下特性：

1. **区域坐标定位**：
   - 不需要精确的像素坐标，可以指定一个区域（如按钮或链接）
   - 系统会在区域的中心30%范围内随机选择一个点
   - 这更符合人类点击UI元素的行为模式

2. **随机偏移**：
   - 即使提供了精确坐标，系统也会添加1-3像素的随机偏移
   - 这避免了机械化的精确点击，使操作更自然
   - 可以通过参数控制是否启用此功能

3. **移动持续时间**：
   - 鼠标移动不是瞬时的，而是有一个持续时间
   - 这模拟了人类操作鼠标的速度和加速度特性

4. **随机等待时间**：
   - 所有操作都支持执行前和执行后的等待时间参数
   - 默认等待时间为50ms，但会添加1-50ms的随机额外时间
   - 这模拟了人类操作的不规则性和思考时间

5. **点击拆解**：
   - 点击操作被拆解为按下和抬起两个动作
   - 按下和抬起之间有1-15ms的随机间隔
   - 双击时，两次点击之间有10-20ms的随机间隔
   - 这更接近人类的实际点击行为

6. **连续滚动间隔**：
   - 连续滚动时，每次滚动之间有10-25ms的随机间隔
   - 这避免了机械化的精确滚动，更接近人类操作

7. **高级轨迹混合算法**：
   - 记录真实人类的鼠标移动轨迹，包括坐标和停留时间
   - 首先计算起点到终点的直线路径坐标点
   - 在每个直线路径点周围20像素范围内查找轨迹库中的点
   - 将直线路径点和找到的轨迹点进行加权平均，生成新的混合点
   - 对混合后的路径进行平滑处理，使移动更加自然
   - 根据轨迹点的时间信息，为整个移动分配合理的时间
   - 移动过程中模拟人类的停顿和加速，使移动更加真实
   - 整个移动过程看起来平滑自然，但实际上包含了复杂的微小变化

8. **时间和停留模拟**：
   - 根据移动距离动态调整总移动时间（0.7-1秒）
   - 保留轨迹中的停留时间信息，在相应位置模拟人类的短暂停顿
   - 路径点之间的时间分配考虑距离因素，并添加随机性
   - 移动速度不均匀，模拟人类的加速和减速
   - 整个移动过程的时间分布更接近真实人类操作

这些特性使得自动化鼠标操作更加自然，不易被检测为机器行为，同时也提高了与UI元素交互的成功率。所有的随机性和等待时间都可以通过参数进行控制，以适应不同的应用场景。

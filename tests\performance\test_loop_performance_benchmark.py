# -*- coding: utf-8 -*-
"""
感知-行动循环性能基准测试
测试循环在不同场景下的性能表现
"""

import pytest
import asyncio
import time
import statistics
from unittest.mock import AsyncMock
from datetime import datetime

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from qyuan.core.perception_action_loop import PerceptionActionLoop, OptimizationConfig
from qyuan.core.loop_optimizer import LoopOptimizer
from qyuan.core.parallel_task_manager import ParallelTaskManager, TaskPriority
from qyuan.engines.perception.base import ScreenAnalysis, UIElement
from qyuan.engines.decision.base import DecisionResult, UserIntent, ActionPlan, Action
from qyuan.engines.execution.base import ExecutionResult


class TestLoopPerformanceBenchmark:
    """感知-行动循环性能基准测试"""
    
    @pytest.fixture
    def mock_qyuan_core(self):
        """创建模拟的QYuan核心"""
        core = AsyncMock()
        core.logger = None
        
        # 模拟引擎
        core.engines = {
            "perception": AsyncMock(),
            "decision": AsyncMock(),
            "execution": AsyncMock(),
            "learning": AsyncMock()
        }
        
        # 设置引擎返回值
        core.engines["perception"].analyze_current_screen.return_value = ScreenAnalysis(
            ui_elements=[UIElement(id="test", type="button", text="Test", bounds=(0, 0, 100, 50))],
            screenshot_path="test.png",
            timestamp=datetime.now()
        )
        
        core.engines["decision"].make_decision.return_value = DecisionResult(
            intent=UserIntent(intent_type="click", confidence=0.9),
            action_plan=ActionPlan(actions=[Action(type="click", target="test")])
        )
        
        core.engines["execution"].execute_action.return_value = ExecutionResult(
            success=True, action_type="click", execution_time=0.1
        )
        
        return core
    
    @pytest.fixture
    def perception_action_loop(self, mock_qyuan_core):
        """创建感知-行动循环实例"""
        return PerceptionActionLoop(mock_qyuan_core)
    
    @pytest.mark.asyncio
    async def test_single_cycle_performance(self, perception_action_loop):
        """测试单次循环性能"""
        # 执行单次循环并测量时间
        start_time = time.time()
        result = await perception_action_loop._execute_single_cycle()
        end_time = time.time()
        
        cycle_time = end_time - start_time
        
        # 验证结果
        assert result is True or result is False  # 应该返回布尔值
        assert cycle_time < 1.0  # 单次循环应在1秒内完成
        
        print(f"单次循环时间: {cycle_time:.3f}秒")
    
    @pytest.mark.asyncio
    async def test_multiple_cycles_performance(self, perception_action_loop):
        """测试多次循环性能"""
        cycle_count = 10
        cycle_times = []
        
        for i in range(cycle_count):
            start_time = time.time()
            await perception_action_loop._execute_single_cycle()
            end_time = time.time()
            
            cycle_time = end_time - start_time
            cycle_times.append(cycle_time)
        
        # 计算统计信息
        avg_time = statistics.mean(cycle_times)
        min_time = min(cycle_times)
        max_time = max(cycle_times)
        std_dev = statistics.stdev(cycle_times) if len(cycle_times) > 1 else 0
        
        # 验证性能指标
        assert avg_time < 0.5  # 平均时间应小于0.5秒
        assert max_time < 1.0  # 最大时间应小于1秒
        assert std_dev < 0.2   # 标准差应较小，表示性能稳定
        
        print(f"多次循环性能统计:")
        print(f"  平均时间: {avg_time:.3f}秒")
        print(f"  最小时间: {min_time:.3f}秒")
        print(f"  最大时间: {max_time:.3f}秒")
        print(f"  标准差: {std_dev:.3f}秒")
    
    @pytest.mark.asyncio
    async def test_optimization_effectiveness(self):
        """测试优化效果"""
        # 创建优化器
        config = OptimizationConfig(
            enable_parallel_processing=True,
            enable_caching=True,
            enable_adaptive_timing=True
        )
        optimizer = LoopOptimizer(config)
        
        # 模拟函数
        async def mock_perception():
            await asyncio.sleep(0.1)  # 模拟感知时间
            return ScreenAnalysis(
                ui_elements=[],
                screenshot_path="test.png",
                timestamp=datetime.now()
            )
        
        async def mock_decision(perception_result):
            await asyncio.sleep(0.05)  # 模拟决策时间
            return DecisionResult(
                intent=UserIntent(intent_type="click", confidence=0.9),
                action_plan=ActionPlan(actions=[])
            )
        
        # 测试未优化的执行时间
        unoptimized_times = []
        for _ in range(5):
            start_time = time.time()
            await mock_perception()
            await mock_decision(None)
            end_time = time.time()
            unoptimized_times.append(end_time - start_time)
        
        # 测试优化后的执行时间
        optimized_times = []
        for _ in range(5):
            start_time = time.time()
            await optimizer.optimize_perception_phase(mock_perception)
            await optimizer.optimize_decision_phase(mock_decision, None)
            end_time = time.time()
            optimized_times.append(end_time - start_time)
        
        # 比较性能
        avg_unoptimized = statistics.mean(unoptimized_times)
        avg_optimized = statistics.mean(optimized_times)
        improvement = (avg_unoptimized - avg_optimized) / avg_unoptimized * 100
        
        print(f"优化效果测试:")
        print(f"  未优化平均时间: {avg_unoptimized:.3f}秒")
        print(f"  优化后平均时间: {avg_optimized:.3f}秒")
        print(f"  性能提升: {improvement:.1f}%")
        
        # 验证优化效果（第二次执行应该更快，因为有缓存）
        assert avg_optimized <= avg_unoptimized  # 优化后不应该更慢
    
    @pytest.mark.asyncio
    async def test_cache_performance(self):
        """测试缓存性能"""
        optimizer = LoopOptimizer(OptimizationConfig(enable_caching=True))
        
        # 模拟相同的感知任务
        async def mock_perception_task():
            await asyncio.sleep(0.1)  # 模拟处理时间
            return "perception_result"
        
        # 第一次执行（无缓存）
        start_time = time.time()
        result1 = await optimizer.optimize_perception_phase(mock_perception_task)
        first_time = time.time() - start_time
        
        # 第二次执行（有缓存）
        start_time = time.time()
        result2 = await optimizer.optimize_perception_phase(mock_perception_task)
        second_time = time.time() - start_time
        
        # 验证缓存效果
        assert result1 == result2  # 结果应该相同
        assert second_time < first_time  # 第二次应该更快
        
        # 获取缓存统计
        report = optimizer.get_performance_report()
        assert report["metrics"]["cache_hit_rate"] > 0  # 应该有缓存命中
        
        print(f"缓存性能测试:")
        print(f"  首次执行时间: {first_time:.3f}秒")
        print(f"  缓存执行时间: {second_time:.3f}秒")
        print(f"  缓存命中率: {report['metrics']['cache_hit_rate']:.1%}")
    
    @pytest.mark.asyncio
    async def test_parallel_task_manager_performance(self):
        """测试并行任务管理器性能"""
        manager = ParallelTaskManager(max_concurrent_tasks=4)
        await manager.start()
        
        try:
            # 提交多个任务
            task_count = 10
            task_ids = []
            
            async def mock_task(task_id):
                await asyncio.sleep(0.1)  # 模拟任务执行时间
                return f"result_{task_id}"
            
            # 记录开始时间
            start_time = time.time()
            
            # 提交所有任务
            for i in range(task_count):
                task_id = manager.submit_task(
                    f"task_{i}",
                    mock_task,
                    i,
                    priority=TaskPriority.NORMAL
                )
                task_ids.append(task_id)
            
            # 等待所有任务完成
            results = []
            for task_id in task_ids:
                result = await manager.wait_for_task(task_id, timeout=5.0)
                results.append(result)
            
            # 记录结束时间
            end_time = time.time()
            total_time = end_time - start_time
            
            # 验证结果
            assert len(results) == task_count
            assert all(f"result_{i}" in results for i in range(task_count))
            
            # 验证并行效果（应该比串行执行快）
            expected_serial_time = task_count * 0.1  # 串行执行时间
            assert total_time < expected_serial_time  # 并行执行应该更快
            
            # 获取统计信息
            stats = manager.get_statistics()
            assert stats["completed_tasks"] == task_count
            assert stats["success_rate"] == 1.0
            
            print(f"并行任务管理器性能测试:")
            print(f"  任务数量: {task_count}")
            print(f"  总执行时间: {total_time:.3f}秒")
            print(f"  预期串行时间: {expected_serial_time:.3f}秒")
            print(f"  性能提升: {(expected_serial_time - total_time) / expected_serial_time * 100:.1f}%")
            
        finally:
            await manager.stop()
    
    @pytest.mark.asyncio
    async def test_stress_test(self, perception_action_loop):
        """压力测试"""
        # 高频率执行循环
        cycle_count = 50
        start_time = time.time()
        
        successful_cycles = 0
        failed_cycles = 0
        
        for i in range(cycle_count):
            try:
                result = await perception_action_loop._execute_single_cycle()
                if result:
                    successful_cycles += 1
                else:
                    failed_cycles += 1
            except Exception as e:
                failed_cycles += 1
                print(f"循环 {i} 失败: {e}")
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 计算性能指标
        avg_cycle_time = total_time / cycle_count
        success_rate = successful_cycles / cycle_count
        throughput = cycle_count / total_time  # 每秒循环数
        
        # 验证压力测试结果
        assert success_rate > 0.8  # 成功率应大于80%
        assert avg_cycle_time < 0.5  # 平均循环时间应小于0.5秒
        assert throughput > 2  # 吞吐量应大于每秒2次循环
        
        print(f"压力测试结果:")
        print(f"  循环总数: {cycle_count}")
        print(f"  成功循环: {successful_cycles}")
        print(f"  失败循环: {failed_cycles}")
        print(f"  成功率: {success_rate:.1%}")
        print(f"  总时间: {total_time:.3f}秒")
        print(f"  平均循环时间: {avg_cycle_time:.3f}秒")
        print(f"  吞吐量: {throughput:.1f} 循环/秒")
    
    @pytest.mark.asyncio
    async def test_memory_efficiency(self, perception_action_loop):
        """测试内存效率"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行大量循环
        cycle_count = 100
        for i in range(cycle_count):
            await perception_action_loop._execute_single_cycle()
            
            # 每20次循环检查内存
            if i % 20 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_growth = current_memory - initial_memory
                
                # 内存增长应该控制在合理范围内
                assert memory_growth < 50  # 不超过50MB
        
        # 最终内存检查
        final_memory = process.memory_info().rss / 1024 / 1024
        total_growth = final_memory - initial_memory
        memory_per_cycle = total_growth / cycle_count
        
        print(f"内存效率测试:")
        print(f"  初始内存: {initial_memory:.1f}MB")
        print(f"  最终内存: {final_memory:.1f}MB")
        print(f"  总增长: {total_growth:.1f}MB")
        print(f"  每循环内存增长: {memory_per_cycle:.3f}MB")
        
        # 验证内存效率
        assert total_growth < 30  # 总内存增长应小于30MB
        assert memory_per_cycle < 0.5  # 每循环内存增长应小于0.5MB


if __name__ == "__main__":
    # 运行性能基准测试
    pytest.main([__file__, "-v", "-s", "--tb=short"])

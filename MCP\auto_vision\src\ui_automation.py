#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI元素识别模块

提供UI元素识别功能，基于Windows UI Automation API。
支持多种后端实现：uiautomation, pywinauto, win32gui, comtypes。
"""

import os
import sys

# 添加当前目录到路径，确保可以导入子模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui_automation.ui_automation_factory import UIAutomationFactory


class UIAutomation:
    """UI元素识别类，提供UI元素识别功能"""

    def __init__(self, backend="win32gui"):
        """
        初始化UI元素识别类

        Args:
            backend: 后端实现，支持 "pywinauto", "win32gui"
        """
        self.backend = backend

        # 使用工厂类创建UI自动化引擎
        self.ui_engine = UIAutomationFactory.create(backend)

    def get_foreground_window(self):
        """
        获取前台窗口

        Returns:
            窗口对象
        """
        return self.ui_engine.get_foreground_window()

    def get_window_by_title(self, title, partial_match=True):
        """
        通过标题获取窗口

        Args:
            title: 窗口标题
            partial_match: 是否部分匹配

        Returns:
            窗口对象
        """
        return self.ui_engine.get_window_by_title(title, partial_match)

    def get_ui_elements(self, window=None, element_type=None, depth=3, visible_only=True):
        """
        获取UI元素及其属性

        Args:
            window: 窗口对象，为None则获取前台窗口
            element_type: 元素类型，必须指定，例如："Button", "Edit", "ComboBox"等
            depth: 树的深度
            visible_only: 是否只返回可见元素

        Returns:
            元素列表，每个元素包含其属性（包括坐标信息）
        """
        if window is None:
            window = self.get_foreground_window()

        if element_type is None:
            raise ValueError("必须指定element_type参数，以避免返回过多数据")

        return self.ui_engine.get_ui_elements(window, element_type, depth, visible_only)

    def find_element_by_text(self, text, element_type=None, window=None, match_type="contains", visible_only=True, timeout=5):
        """
        通过文本查找UI元素

        Args:
            text: 要查找的文本
            element_type: 元素类型，必须指定，例如："Button", "Edit", "ComboBox"等
            window: 窗口对象，为None则在前台窗口中查找
            match_type: 匹配类型，可选值：exact, contains, starts_with, ends_with
            visible_only: 是否只查找可见元素
            timeout: 超时时间（秒）

        Returns:
            元素属性字典，包含元素的坐标信息
        """
        if window is None:
            window = self.get_foreground_window()

        if element_type is None:
            raise ValueError("必须指定element_type参数，以避免返回过多数据")

        return self.ui_engine.find_element_by_text(text, element_type, window, match_type, visible_only, timeout)

    def activate_window(self, window):
        """
        激活窗口

        Args:
            window: 窗口对象

        Returns:
            操作是否成功
        """
        return self.ui_engine.activate_window(window)

    def maximize_window(self, window):
        """
        最大化窗口

        Args:
            window: 窗口对象

        Returns:
            操作是否成功
        """
        return self.ui_engine.maximize_window(window)

    def minimize_window(self, window):
        """
        最小化窗口

        Args:
            window: 窗口对象

        Returns:
            操作是否成功
        """
        return self.ui_engine.minimize_window(window)

    def restore_window(self, window):
        """
        还原窗口

        Args:
            window: 窗口对象

        Returns:
            操作是否成功
        """
        return self.ui_engine.restore_window(window)

    def resize_window(self, window, width, height):
        """
        调整窗口大小

        Args:
            window: 窗口对象
            width: 宽度
            height: 高度

        Returns:
            操作是否成功
        """
        return self.ui_engine.resize_window(window, width, height)

    def move_window(self, window, x, y):
        """
        移动窗口

        Args:
            window: 窗口对象
            x: x坐标
            y: y坐标

        Returns:
            操作是否成功
        """
        return self.ui_engine.move_window(window, x, y)


# 如果直接运行此模块
if __name__ == "__main__":
    print("UI自动化模块")
    print("请使用test_ui_automation.py进行测试")

// -*- coding: utf-8 -*-

import { WebSocketMessage } from '../types';

/**
 * WebSocket服务类
 * 专门负责WebSocket连接管理和消息处理
 * 严格按照代码规范的单一职责原则
 */
export class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectInterval: number = 3000;
  private heartbeatInterval: number = 30000;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private messageHandlers: Map<string, (message: WebSocketMessage) => void> = new Map();
  private connectionStateHandlers: ((connected: boolean) => void)[] = [];

  constructor(url: string) {
    this.url = url;
  }

  /**
   * 连接WebSocket服务器
   */
  public connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.url);
        
        this.ws.onopen = () => {
          console.log('WebSocket连接已建立');
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.notifyConnectionState(true);
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data);
        };

        this.ws.onclose = () => {
          console.log('WebSocket连接已关闭');
          this.stopHeartbeat();
          this.notifyConnectionState(false);
          this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket连接错误:', error);
          reject(error);
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 断开WebSocket连接
   */
  public disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.stopHeartbeat();
    this.notifyConnectionState(false);
  }

  /**
   * 发送消息
   * @param message - 要发送的消息
   */
  public sendMessage(message: WebSocketMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket未连接，无法发送消息');
    }
  }

  /**
   * 注册消息处理器
   * @param type - 消息类型
   * @param handler - 处理函数
   */
  public onMessage(type: string, handler: (message: WebSocketMessage) => void): void {
    this.messageHandlers.set(type, handler);
  }

  /**
   * 移除消息处理器
   * @param type - 消息类型
   */
  public offMessage(type: string): void {
    this.messageHandlers.delete(type);
  }

  /**
   * 注册连接状态变化处理器
   * @param handler - 处理函数
   */
  public onConnectionStateChange(handler: (connected: boolean) => void): void {
    this.connectionStateHandlers.push(handler);
  }

  /**
   * 获取连接状态
   * @returns 是否已连接
   */
  public isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * 处理接收到的消息
   * @param data - 消息数据
   */
  private handleMessage(data: string): void {
    try {
      const message: WebSocketMessage = JSON.parse(data);
      
      // 处理心跳消息
      if (message.type === 'heartbeat') {
        this.handleHeartbeat();
        return;
      }

      // 调用对应的消息处理器
      const handler = this.messageHandlers.get(message.type);
      if (handler) {
        handler(message);
      } else {
        console.warn(`未找到消息类型 ${message.type} 的处理器`);
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error);
    }
  }

  /**
   * 处理心跳消息
   */
  private handleHeartbeat(): void {
    // 回复心跳
    this.sendMessage({
      type: 'heartbeat',
      data: { pong: true },
      timestamp: new Date().toISOString(),
      sessionId: 'system',
    });
  }

  /**
   * 开始心跳检测
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected()) {
        this.sendMessage({
          type: 'heartbeat',
          data: { ping: true },
          timestamp: new Date().toISOString(),
          sessionId: 'system',
        });
      }
    }, this.heartbeatInterval);
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 尝试重新连接
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`尝试重新连接 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect().catch((error) => {
          console.error('重新连接失败:', error);
        });
      }, this.reconnectInterval);
    } else {
      console.error('达到最大重连次数，停止重连');
    }
  }

  /**
   * 通知连接状态变化
   * @param connected - 是否已连接
   */
  private notifyConnectionState(connected: boolean): void {
    this.connectionStateHandlers.forEach(handler => {
      try {
        handler(connected);
      } catch (error) {
        console.error('连接状态处理器执行失败:', error);
      }
    });
  }
}

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
键盘控制器模块

该模块提供了一个键盘控制器类，用于模拟键盘操作，包括按键、释放键、组合键等功能。
设计考虑了仿人化操作，添加了随机等待时间和操作拆解等特性。
"""

import time
import random
import logging
import pyperclip
import ctypes
from typing import List, Union, Optional, Dict, Any, Tuple
from pynput.keyboard import Key, Controller, KeyCode

# 导入win32相关模块
try:
    import win32api
    import win32con
    import win32gui
    HAS_WIN32 = True
except ImportError:
    HAS_WIN32 = False
    logger = logging.getLogger("KeyboardController")
    logger.warning("win32api模块未安装，输入法检测和切换功能将不可用")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("KeyboardController")

class KeyboardController:
    """
    键盘控制器类，提供模拟键盘操作的功能。

    该类封装了pynput库的键盘控制功能，并添加了仿人化设计，如随机等待时间和操作拆解。
    """

    def __init__(self):
        """初始化键盘控制器"""
        self.keyboard = Controller()
        logger.info("键盘控制器初始化完成")

    def _wait(self, ms: int) -> None:
        """
        等待指定的毫秒数

        Args:
            ms: 等待的毫秒数
        """
        if ms > 0:
            time.sleep(ms / 1000)

    def _add_random_wait(self, base_ms: int, variation_ms: int = 5) -> None:
        """
        添加随机等待时间

        Args:
            base_ms: 基础等待时间（毫秒）
            variation_ms: 随机变化范围（毫秒）
        """
        wait_time = base_ms + random.randint(0, variation_ms)
        self._wait(wait_time)

    def _convert_to_key(self, key: Union[str, Key]) -> Union[KeyCode, Key]:
        """
        将字符串转换为键码对象

        Args:
            key: 键名，可以是字符串或Key枚举

        Returns:
            转换后的键码对象
        """
        if isinstance(key, Key):
            return key
        elif isinstance(key, str) and len(key) == 1:
            return key
        elif isinstance(key, str):
            # 处理特殊键名
            special_keys = {
                'alt': Key.alt,
                'alt_l': Key.alt_l,
                'alt_r': Key.alt_r,
                'backspace': Key.backspace,
                'caps_lock': Key.caps_lock,
                'cmd': Key.cmd,
                'cmd_l': Key.cmd_l,
                'cmd_r': Key.cmd_r,
                'ctrl': Key.ctrl,
                'ctrl_l': Key.ctrl_l,
                'ctrl_r': Key.ctrl_r,
                'delete': Key.delete,
                'down': Key.down,
                'end': Key.end,
                'enter': Key.enter,
                'esc': Key.esc,
                'f1': Key.f1,
                'f2': Key.f2,
                'f3': Key.f3,
                'f4': Key.f4,
                'f5': Key.f5,
                'f6': Key.f6,
                'f7': Key.f7,
                'f8': Key.f8,
                'f9': Key.f9,
                'f10': Key.f10,
                'f11': Key.f11,
                'f12': Key.f12,
                'home': Key.home,
                'insert': Key.insert,
                'left': Key.left,
                'menu': Key.menu,
                'num_lock': Key.num_lock,
                'page_down': Key.page_down,
                'page_up': Key.page_up,
                'pause': Key.pause,
                'print_screen': Key.print_screen,
                'right': Key.right,
                'scroll_lock': Key.scroll_lock,
                'shift': Key.shift,
                'shift_l': Key.shift_l,
                'shift_r': Key.shift_r,
                'space': Key.space,
                'tab': Key.tab,
                'up': Key.up
            }

            if key.lower() in special_keys:
                return special_keys[key.lower()]
            else:
                # 尝试处理功能键 (F1-F12)
                if key.upper().startswith('F') and key[1:].isdigit():
                    key_num = int(key[1:])
                    if 1 <= key_num <= 12:
                        return getattr(Key, f'f{key_num}')

                logger.warning(f"未知的键名: {key}")
                return key
        else:
            logger.warning(f"不支持的键类型: {type(key)}")
            return key

    def press_key(self,
                  key: Union[str, Key],
                  wait_before_ms: int = 50,
                  wait_after_ms: int = 50) -> None:
        """
        按下指定的键

        Args:
            key: 要按下的键，可以是字符串或Key枚举
            wait_before_ms: 按键前等待时间（毫秒）
            wait_after_ms: 按键后等待时间（毫秒）
        """
        # 添加随机等待时间
        self._add_random_wait(wait_before_ms)

        # 转换键名并按下
        key_obj = self._convert_to_key(key)
        logger.info(f"按下键: {key}")
        self.keyboard.press(key_obj)

        # 添加随机等待时间
        self._add_random_wait(wait_after_ms)

    def release_key(self,
                    key: Union[str, Key],
                    wait_before_ms: int = 50,
                    wait_after_ms: int = 50) -> None:
        """
        释放指定的键

        Args:
            key: 要释放的键，可以是字符串或Key枚举
            wait_before_ms: 释放前等待时间（毫秒）
            wait_after_ms: 释放后等待时间（毫秒）
        """
        # 添加随机等待时间
        self._add_random_wait(wait_before_ms)

        # 转换键名并释放
        key_obj = self._convert_to_key(key)
        logger.info(f"释放键: {key}")
        self.keyboard.release(key_obj)

        # 添加随机等待时间
        self._add_random_wait(wait_after_ms)

    def type_key(self,
                 key: Union[str, Key],
                 wait_before_ms: int = 50,
                 wait_after_ms: int = 50) -> None:
        """
        按下并释放指定的键（完整的按键操作）

        Args:
            key: 要按下并释放的键，可以是字符串或Key枚举
            wait_before_ms: 操作前等待时间（毫秒）
            wait_after_ms: 操作后等待时间（毫秒）
        """
        # 添加随机等待时间
        self._add_random_wait(wait_before_ms)

        # 转换键名并执行按键操作
        key_obj = self._convert_to_key(key)
        logger.info(f"按键: {key}")

        # 拆解为按下和释放两个操作
        self.keyboard.press(key_obj)
        # 按下和释放之间添加随机等待时间（1-15毫秒）
        self._add_random_wait(random.randint(1, 15))
        self.keyboard.release(key_obj)

        # 添加随机等待时间
        self._add_random_wait(wait_after_ms)

    def type_text(self,
                  text: str,
                  interval_ms: int = 100,
                  wait_before_ms: int = 50,
                  wait_after_ms: int = 50) -> None:
        """
        输入一段文本

        Args:
            text: 要输入的文本
            interval_ms: 字符间隔时间（毫秒）
            wait_before_ms: 操作前等待时间（毫秒）
            wait_after_ms: 操作后等待时间（毫秒）
        """
        # 添加随机等待时间
        self._add_random_wait(wait_before_ms)

        logger.info(f"输入文本: {text}")

        # 逐个字符输入，模拟人工输入
        for char in text:
            self.keyboard.press(char)
            # 按下和释放之间添加随机等待时间（1-15毫秒）
            self._add_random_wait(random.randint(1, 15))
            self.keyboard.release(char)
            # 字符之间添加随机等待时间
            self._add_random_wait(interval_ms, variation_ms=20)

        # 添加随机等待时间
        self._add_random_wait(wait_after_ms)

    def hotkey(self,
               *keys: Union[str, Key],
               wait_before_ms: int = 50,
               wait_after_ms: int = 50) -> None:
        """
        执行组合键操作

        Args:
            *keys: 要按下的键序列，可以是字符串或Key枚举
            wait_before_ms: 操作前等待时间（毫秒）
            wait_after_ms: 操作后等待时间（毫秒）
        """
        if not keys:
            logger.warning("未提供任何键")
            return

        # 添加随机等待时间
        self._add_random_wait(wait_before_ms)

        key_objs = [self._convert_to_key(key) for key in keys]
        logger.info(f"组合键: {keys}")

        # 按顺序按下所有键
        for key in key_objs:
            self.keyboard.press(key)
            # 按键之间添加随机等待时间（5-15毫秒）
            self._add_random_wait(random.randint(5, 15))

        # 按相反顺序释放所有键
        for key in reversed(key_objs):
            self.keyboard.release(key)
            # 释放之间添加随机等待时间（5-15毫秒）
            self._add_random_wait(random.randint(5, 15))

        # 添加随机等待时间
        self._add_random_wait(wait_after_ms)

    def get_key_state(self, key: Union[str, Key]) -> bool:
        """
        获取指定键的状态（是否按下）

        Args:
            key: 要检查的键，可以是字符串或Key枚举

        Returns:
            如果键被按下，返回True；否则返回False
        """
        # 注意：pynput不直接支持获取键状态，这里提供一个占位实现
        # 实际上需要使用操作系统特定的API来实现
        logger.warning("get_key_state方法尚未实现完整功能")
        return False

    def set_clipboard_text(self, text: str) -> None:
        """
        设置剪贴板文本

        Args:
            text: 要设置到剪贴板的文本
        """
        logger.info(f"设置剪贴板文本: {text[:50]}..." if len(text) > 50 else f"设置剪贴板文本: {text}")
        pyperclip.copy(text)

    def get_clipboard_text(self) -> str:
        """
        获取剪贴板文本

        Returns:
            剪贴板中的文本
        """
        text = pyperclip.paste()
        logger.info(f"获取剪贴板文本: {text[:50]}..." if len(text) > 50 else f"获取剪贴板文本: {text}")
        return text

    def is_ime_enabled(self) -> bool:
        """
        检测当前输入法状态是否为中文输入法

        Returns:
            如果当前是中文输入法，返回True；否则返回False
        """
        if not HAS_WIN32:
            logger.warning("win32api模块未安装，无法检测输入法状态")
            return False

        try:
            # 获取当前活动窗口的句柄
            hwnd = win32gui.GetForegroundWindow()

            # 获取输入法上下文
            ime_context = win32api.SendMessage(hwnd, win32con.WM_IME_CONTROL, 0x0005, 0)

            # 如果ime_context不为0，则表示中文输入法处于开启状态
            return ime_context != 0
        except Exception as e:
            logger.error(f"检测输入法状态失败: {str(e)}")
            return False

    def switch_to_english_input(self, max_attempts: int = 3) -> bool:
        """
        切换到英文输入法，并验证切换是否成功

        Args:
            max_attempts: 最大尝试次数

        Returns:
            如果切换成功，返回True；否则返回False
        """
        if not HAS_WIN32:
            logger.warning("win32api模块未安装，无法切换输入法")
            return False

        try:
            # 如果已经是英文输入法，直接返回成功
            if not self.is_ime_enabled():
                logger.info("当前已经是英文输入法")
                return True

            # 尝试切换输入法，最多尝试max_attempts次
            for attempt in range(max_attempts):
                logger.info(f"尝试切换到英文输入法 (尝试 {attempt+1}/{max_attempts})")

                # 获取当前活动窗口的句柄
                hwnd = win32gui.GetForegroundWindow()

                # 模拟按下Alt+Shift切换输入法
                self.hotkey('alt', 'shift')
                time.sleep(0.3)  # 增加等待时间，确保输入法切换完成

                # 验证是否切换成功
                if not self.is_ime_enabled():
                    logger.info("成功切换到英文输入法")
                    return True

                # 如果切换失败，等待一段时间后重试
                logger.warning(f"切换到英文输入法失败，将重试 ({attempt+1}/{max_attempts})")
                time.sleep(0.5)

            logger.error(f"在{max_attempts}次尝试后仍无法切换到英文输入法")
            return False
        except Exception as e:
            logger.error(f"切换输入法失败: {str(e)}")
            return False

    def ensure_english_input_before_typing(self, text: str, interval_ms: int = 100, wait_before_ms: int = 50, wait_after_ms: int = 50) -> bool:
        """
        确保在英文输入法状态下输入文本

        注意：此方法适用于输入英文文本。对于中文文本，由于需要通过拼音选字，
        无法精确自动输入。如需输入中文，建议使用剪贴板功能。

        Args:
            text: 要输入的文本
            interval_ms: 字符间隔时间（毫秒）
            wait_before_ms: 操作前等待时间（毫秒）
            wait_after_ms: 操作后等待时间（毫秒）

        Returns:
            如果成功切换到英文输入法并输入文本，返回True；否则返回False
        """
        # 尝试切换到英文输入法
        if HAS_WIN32:
            switched = self.switch_to_english_input()
            if not switched:
                logger.warning("无法切换到英文输入法，可能会导致输入问题")
                return False

            # 再次确认是否成功切换到英文输入法
            if self.is_ime_enabled():
                logger.error("切换到英文输入法失败，取消输入操作")
                return False

            logger.info("已确认切换到英文输入法，开始输入文本")
        else:
            logger.warning("win32api模块未安装，无法确认输入法状态")

        # 输入文本
        self.type_text(text, interval_ms, wait_before_ms, wait_after_ms)
        return True

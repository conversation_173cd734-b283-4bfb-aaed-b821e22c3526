#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
真实场景测试脚本

模拟之前失败的场景：打开PowerShell并输入命令
"""

import time
from keyboard_controller import KeyboardController
from improved_ime_handler import ImprovedIME<PERSON><PERSON>ler

def test_powershell_scenario():
    """测试PowerShell场景"""
    print("=" * 60)
    print("真实场景测试：打开PowerShell并输入命令")
    print("=" * 60)
    
    controller = KeyboardController()
    ime_handler = ImprovedIMEHandler()
    
    # 1. 检查初始输入法状态
    print("1. 检查初始输入法状态")
    is_chinese, status = ime_handler.is_chinese_ime_active()
    print(f"   当前状态: {status}")
    
    # 2. 如果是中文输入法，切换到英文
    if is_chinese:
        print("\n2. 检测到中文输入法，切换到英文...")
        success, message = ime_handler.switch_to_english_layout()
        print(f"   切换结果: {message}")
        
        if not success:
            print("   尝试循环切换...")
            success, message = ime_handler.cycle_input_methods()
            print(f"   循环切换结果: {message}")
    else:
        print("\n2. 当前已是英文输入法，无需切换")
    
    # 3. 再次确认输入法状态
    print("\n3. 确认当前输入法状态")
    is_chinese_after, status_after = ime_handler.is_chinese_ime_active()
    print(f"   确认状态: {status_after}")
    
    if is_chinese_after:
        print("   ⚠️  仍为中文输入法，可能影响后续输入")
        return False
    else:
        print("   ✅ 已确认为英文输入法，可以安全输入")
    
    # 4. 模拟打开运行对话框
    print("\n4. 打开运行对话框 (Win+R)")
    controller.hotkey('cmd', 'r')
    time.sleep(2)  # 等待对话框打开
    
    # 5. 输入powershell
    print("\n5. 输入 'powershell'")
    controller.type_text('powershell', interval_ms=80)
    time.sleep(1)
    
    # 6. 按回车
    print("\n6. 按回车执行")
    controller.type_key('enter')
    time.sleep(3)  # 等待PowerShell启动
    
    # 7. 输入测试命令
    print("\n7. 输入测试命令 'Get-Date'")
    controller.type_text('Get-Date', interval_ms=80)
    time.sleep(1)
    
    # 8. 执行命令
    print("\n8. 执行命令")
    controller.type_key('enter')
    time.sleep(2)
    
    # 9. 清理：关闭PowerShell
    print("\n9. 清理：关闭PowerShell")
    controller.type_text('exit', interval_ms=80)
    controller.type_key('enter')
    
    print("\n✅ 测试完成！")
    return True

def test_input_method_switching():
    """测试输入法切换功能"""
    print("\n" + "=" * 60)
    print("输入法切换功能测试")
    print("=" * 60)
    
    ime_handler = ImprovedIMEHandler()
    
    # 获取初始状态
    is_chinese_initial, status_initial = ime_handler.is_chinese_ime_active()
    print(f"初始状态: {status_initial}")
    
    # 如果当前是英文，尝试切换到中文（通过快捷键）
    if not is_chinese_initial:
        print("\n尝试切换到中文输入法...")
        # 这里我们不实际切换，只是演示检测功能
        print("（为了测试安全，跳过实际切换）")
    
    # 测试切换到英文的功能
    print("\n测试切换到英文输入法功能...")
    success, message = ime_handler.switch_to_english_layout()
    print(f"切换结果: {message}")
    
    # 最终状态
    is_chinese_final, status_final = ime_handler.is_chinese_ime_active()
    print(f"最终状态: {status_final}")
    
    return True

def main():
    """主函数"""
    try:
        # 询问用户是否要进行实际测试
        print("这个测试将实际操作您的系统（打开PowerShell等）")
        choice = input("是否继续？(y/n): ").lower().strip()
        
        if choice == 'y':
            # 实际场景测试
            test_powershell_scenario()
        else:
            print("跳过实际操作测试")
        
        # 输入法切换测试（安全）
        test_input_method_switching()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()

// -*- coding: utf-8 -*-

import React, { forwardRef } from 'react';
import { cn } from '../../utils';
import { BaseComponentProps } from '../../types';

/**
 * Input组件属性接口
 */
interface InputProps extends BaseComponentProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'search' | 'url' | 'tel';
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  error?: boolean;
  errorMessage?: string;
  label?: string;
  helperText?: string;
  size?: 'sm' | 'md' | 'lg';
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
}

/**
 * Input组件
 * 通用输入框组件，支持多种类型和状态
 * 严格按照代码规范的单一职责原则
 */
export const Input = forwardRef<HTMLInputElement, InputProps>(({
  className,
  type = 'text',
  placeholder,
  value,
  defaultValue,
  disabled = false,
  readOnly = false,
  required = false,
  error = false,
  errorMessage,
  label,
  helperText,
  size = 'md',
  onChange,
  onFocus,
  onBlur,
  onKeyDown,
  ...props
}, ref) => {
  /**
   * 获取输入框样式类名
   */
  const getInputClasses = () => {
    const baseClasses = [
      'flex',
      'w-full',
      'rounded-md',
      'border',
      'bg-transparent',
      'px-3',
      'text-sm',
      'ring-offset-background',
      'file:border-0',
      'file:bg-transparent',
      'file:text-sm',
      'file:font-medium',
      'placeholder:text-secondary-500',
      'focus-visible:outline-none',
      'focus-visible:ring-2',
      'focus-visible:ring-ring',
      'focus-visible:ring-offset-2',
      'disabled:cursor-not-allowed',
      'disabled:opacity-50',
    ];

    // 尺寸样式
    const sizeClasses = {
      sm: ['h-8', 'text-xs'],
      md: ['h-10', 'text-sm'],
      lg: ['h-12', 'text-base'],
    };

    // 状态样式
    const stateClasses = error
      ? ['border-red-300', 'focus-visible:ring-red-500']
      : ['border-secondary-300', 'focus-visible:ring-primary-500'];

    return cn(
      baseClasses,
      sizeClasses[size],
      stateClasses,
      className
    );
  };

  /**
   * 获取标签样式类名
   */
  const getLabelClasses = () => {
    return cn(
      'block',
      'text-sm',
      'font-medium',
      'text-secondary-700',
      'mb-1',
      error && 'text-red-700'
    );
  };

  /**
   * 获取帮助文本样式类名
   */
  const getHelperTextClasses = () => {
    return cn(
      'mt-1',
      'text-xs',
      error ? 'text-red-600' : 'text-secondary-500'
    );
  };

  return (
    <div className="w-full">
      {label && (
        <label className={getLabelClasses()}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <input
        ref={ref}
        type={type}
        className={getInputClasses()}
        placeholder={placeholder}
        value={value}
        defaultValue={defaultValue}
        disabled={disabled}
        readOnly={readOnly}
        required={required}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
        onKeyDown={onKeyDown}
        {...props}
      />
      
      {(errorMessage || helperText) && (
        <p className={getHelperTextClasses()}>
          {error ? errorMessage : helperText}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

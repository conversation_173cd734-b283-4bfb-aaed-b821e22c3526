#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Auto Vision MCP服务器启动脚本
"""

import os
import sys
import argparse


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Auto Vision MCP服务器启动脚本')
    parser.add_argument('--transport', type=str, default='stdio',
                        choices=['stdio'],
                        help='传输方式，目前只支持stdio')
    parser.add_argument('--ocr-engine', type=str, default='auto',
                        choices=['auto', 'easyocr', 'windows', 'baidu'],
                        help='OCR引擎')
    parser.add_argument('--ui-backend', type=str, default='win32gui',
                        choices=['win32gui', 'pywinauto', 'uiautomation'],
                        help='UI自动化后端')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')

    args = parser.parse_args()

    # 添加src目录到Python路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    src_dir = os.path.join(current_dir, 'src')
    sys.path.insert(0, src_dir)

    # 导入服务器模块
    try:
        from server import main as server_main
    except ImportError as e:
        print(f"导入服务器模块失败: {e}")
        return 1

    # 设置命令行参数
    sys.argv = [sys.argv[0]]
    sys.argv.extend(['--transport', 'stdio'])  # 始终使用stdio传输
    if args.ocr_engine:
        sys.argv.extend(['--ocr-engine', args.ocr_engine])
    if args.ui_backend:
        sys.argv.extend(['--ui-backend', args.ui_backend])
    if args.debug:
        sys.argv.append('--debug')

    # 运行服务器
    return server_main()


if __name__ == "__main__":
    exit(main())

"""
鼠标控制器模块，提供鼠标操作的基本功能，包含仿人化设置。
"""

import pyautogui
import random
import time
import os
import json
import math
import numpy as np
from typing import List, Dict, Any, Optional, Tuple

# 导入轨迹处理工具
from src.trajectory_utils import (
    calculate_linear_path,
    find_nearest_trajectory_points,
    blend_trajectory_points,
    distribute_time,
    smooth_path
)

# 设置PyAutoGUI的安全特性
pyautogui.FAILSAFE = True  # 将鼠标移动到屏幕左上角将引发异常，防止失控
pyautogui.PAUSE = 0.0  # 设置为0，我们将手动控制暂停时间


class MouseController:
    """鼠标控制器类，封装所有鼠标操作功能。"""

    def __init__(self, recordings_dir="recordings"):
        """
        初始化鼠标控制器。

        Args:
            recordings_dir: 轨迹记录文件目录
        """
        # 获取屏幕尺寸
        self.screen_width, self.screen_height = pyautogui.size()

        # 轨迹相关设置
        self.recordings_dir = recordings_dir
        self.trajectories = []
        self.load_all_trajectories()

    def _wait_with_randomness(self, wait_time_ms=50):
        """
        等待指定的毫秒数，并添加1-50ms的随机时间。

        Args:
            wait_time_ms: 基础等待时间（毫秒）
        """
        if wait_time_ms <= 0:
            return

        # 添加1-50ms的随机时间
        random_addition = random.randint(1, 50)
        total_wait_time_ms = wait_time_ms + random_addition

        # 转换为秒并等待
        time.sleep(total_wait_time_ms / 1000.0)

    def load_all_trajectories(self):
        """加载所有轨迹记录文件。"""
        if not os.path.exists(self.recordings_dir):
            os.makedirs(self.recordings_dir)
            print(f"创建轨迹记录目录: {self.recordings_dir}")
            return

        # 获取所有JSON文件
        json_files = [f for f in os.listdir(self.recordings_dir) if f.endswith(".json")]

        if not json_files:
            print(f"在 {self.recordings_dir} 中没有找到轨迹文件")
            return

        # 加载所有轨迹
        for filename in json_files:
            filepath = os.path.join(self.recordings_dir, filename)
            try:
                with open(filepath, "r") as f:
                    data = json.load(f)
                    if "trajectory" in data and data["trajectory"]:
                        self.trajectories.append(data["trajectory"])
            except Exception as e:
                print(f"加载轨迹文件 {filename} 时发生错误: {e}")

        print(f"已加载 {len(self.trajectories)} 个轨迹记录")

    def get_random_trajectory(self):
        """
        获取一个随机轨迹。

        Returns:
            list: 轨迹数据，如果没有可用轨迹则返回空列表
        """
        if not self.trajectories:
            return []

        return random.choice(self.trajectories)

    def normalize_trajectory(self, trajectory, start_x, start_y, end_x, end_y):
        """
        归一化轨迹，使其适应指定的起点和终点。
        限制缩放比例，避免不自然的移动。

        Args:
            trajectory: 原始轨迹数据
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标

        Returns:
            list: 归一化后的轨迹，如果不适合则返回空列表
        """
        if not trajectory:
            return []

        # 提取原始轨迹的起点和终点
        orig_start_x = trajectory[0]["x"]
        orig_start_y = trajectory[0]["y"]
        orig_end_x = trajectory[-1]["x"]
        orig_end_y = trajectory[-1]["y"]

        # 计算距离
        orig_distance = math.sqrt((orig_end_x - orig_start_x) ** 2 + (orig_end_y - orig_start_y) ** 2)
        target_distance = math.sqrt((end_x - start_x) ** 2 + (end_y - start_y) ** 2)

        # 如果原始距离为0，无法进行缩放
        if orig_distance == 0:
            return []

        # 计算缩放比例
        scale = target_distance / orig_distance

        # 限制缩放比例，避免过度缩放
        # 如果缩放比例超出合理范围(0.2-5)，则认为轨迹不适合当前移动
        if scale < 0.2 or scale > 5:
            return []

        # 计算旋转角度
        if orig_distance == 0 or target_distance == 0:
            angle = 0
        else:
            orig_angle = math.atan2(orig_end_y - orig_start_y, orig_end_x - orig_start_x)
            target_angle = math.atan2(end_y - start_y, end_x - start_x)
            angle = target_angle - orig_angle

        # 归一化轨迹
        normalized = []
        for point in trajectory:
            # 相对于原始起点的偏移
            rel_x = point["x"] - orig_start_x
            rel_y = point["y"] - orig_start_y

            # 应用缩放和旋转
            rot_x = rel_x * math.cos(angle) - rel_y * math.sin(angle)
            rot_y = rel_x * math.sin(angle) + rel_y * math.cos(angle)

            scaled_x = rot_x * scale
            scaled_y = rot_y * scale

            # 应用到新的起点
            new_x = int(start_x + scaled_x)
            new_y = int(start_y + scaled_y)

            # 确保坐标在屏幕范围内
            new_x = max(0, min(new_x, self.screen_width - 1))
            new_y = max(0, min(new_y, self.screen_height - 1))

            normalized.append({
                "x": new_x,
                "y": new_y,
                "time": point["time"]  # 保持原始时间戳
            })

        return normalized

    def get_position(self):
        """
        获取当前鼠标位置。

        Returns:
            tuple: 包含x和y坐标的元组
        """
        return pyautogui.position()

    def move_mouse(self, x=None, y=None, left=None, top=None, right=None, bottom=None,
                 duration=0.5, random_offset=True, wait_before_ms=50, wait_after_ms=50):
        """
        将鼠标移动到指定坐标或区域。支持绝对坐标或区域坐标，并会添加随机偏移。如果未指定坐标，则返回当前鼠标位置。

        Args:
            x: 目标X坐标（可选，如果不提供则返回当前位置）
            y: 目标Y坐标（可选，如果不提供则返回当前位置）
            left: 区域左边界坐标（与x/y互斥，用于区域定位）
            top: 区域上边界坐标（与x/y互斥，用于区域定位）
            right: 区域右边界坐标（与x/y互斥，用于区域定位）
            bottom: 区域下边界坐标（与x/y互斥，用于区域定位）
            duration: 移动持续时间（秒）
            random_offset: 是否添加随机偏移（1-3像素）
            wait_before_ms: 执行前等待时间（毫秒），默认50ms
            wait_after_ms: 执行后等待时间（毫秒），默认50ms

        Returns:
            dict: 包含操作结果的字典
        """
        # 如果未提供任何坐标，返回当前位置
        if x is None and y is None and left is None and top is None and right is None and bottom is None:
            current_x, current_y = self.get_position()
            return {
                "success": True,
                "position": {"x": current_x, "y": current_y},
                "message": "当前鼠标位置"
            }

        try:
            # 确定目标坐标
            target_x, target_y = None, None

            # 处理区域坐标
            if left is not None and top is not None and right is not None and bottom is not None:
                # 确保坐标在屏幕范围内
                left = max(0, min(left, self.screen_width - 1))
                top = max(0, min(top, self.screen_height - 1))
                right = max(0, min(right, self.screen_width - 1))
                bottom = max(0, min(bottom, self.screen_height - 1))

                # 确保left <= right, top <= bottom
                if left > right:
                    left, right = right, left
                if top > bottom:
                    top, bottom = bottom, top

                # 计算区域宽度和高度
                width = right - left
                height = bottom - top

                # 计算中心30%区域
                center_left = left + width * 0.35
                center_right = left + width * 0.65
                center_top = top + height * 0.35
                center_bottom = top + height * 0.65

                # 在中心区域随机选择一个点
                target_x = random.uniform(center_left, center_right)
                target_y = random.uniform(center_top, center_bottom)

                # 转换为整数
                target_x = int(target_x)
                target_y = int(target_y)

            # 处理绝对坐标
            elif x is not None and y is not None:
                # 确保坐标在屏幕范围内
                target_x = max(0, min(x, self.screen_width - 1))
                target_y = max(0, min(y, self.screen_height - 1))

            # 添加随机偏移（如果启用）
            if random_offset and target_x is not None and target_y is not None:
                offset_x = random.randint(-3, 3)
                offset_y = random.randint(-3, 3)

                # 应用偏移，确保仍在屏幕范围内
                target_x = max(0, min(target_x + offset_x, self.screen_width - 1))
                target_y = max(0, min(target_y + offset_y, self.screen_height - 1))

            # 如果无法确定目标坐标，返回错误
            if target_x is None or target_y is None:
                return {
                    "success": False,
                    "error": "无法确定目标坐标",
                    "message": "请提供有效的坐标参数"
                }

            # 执行前等待
            self._wait_with_randomness(wait_before_ms)

            # 获取当前鼠标位置作为起点
            current_x, current_y = self.get_position()

            # 使用轨迹移动鼠标
            self._move_with_trajectory(current_x, current_y, target_x, target_y)

            # 执行后等待
            self._wait_with_randomness(wait_after_ms)

            # 构建返回消息
            if left is not None:
                message = f"鼠标已移动到区域 ({left}, {top}, {right}, {bottom}) 内的坐标 ({target_x}, {target_y})"
            else:
                original_coords = f"({x}, {y})"
                if random_offset:
                    message = f"鼠标已移动到坐标 {original_coords} 附近的 ({target_x}, {target_y})"
                else:
                    message = f"鼠标已移动到坐标 ({target_x}, {target_y})"

            return {
                "success": True,
                "position": {"x": target_x, "y": target_y},
                "wait_info": {
                    "before_ms": wait_before_ms,
                    "after_ms": wait_after_ms
                },
                "message": message
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "移动鼠标时发生错误"
            }

    def mouse_click(self, x=None, y=None, button="left", clicks=1, interval=0.1,
                   wait_before_ms=50, wait_after_ms=50):
        """
        在指定位置执行鼠标点击。如果未指定坐标，则在当前位置点击。
        点击会被拆解为按下-抬起，中间间隔1-15ms随机时间。
        双击时，两次点击之间间隔10-20ms随机时间。

        Args:
            x: 点击的X坐标（可选）
            y: 点击的Y坐标（可选）
            button: 鼠标按钮 ("left", "right", "middle")
            clicks: 点击次数
            interval: 点击之间的间隔（秒），仅当clicks>2时使用
            wait_before_ms: 执行前等待时间（毫秒），默认50ms
            wait_after_ms: 执行后等待时间（毫秒），默认50ms

        Returns:
            dict: 包含操作结果的字典
        """
        try:
            # 执行前等待
            self._wait_with_randomness(wait_before_ms)

            # 如果提供了坐标，先移动到该位置
            if x is not None and y is not None:
                # 确保坐标在屏幕范围内
                x = max(0, min(x, self.screen_width - 1))
                y = max(0, min(y, self.screen_height - 1))

                pyautogui.moveTo(x, y)
            else:
                # 获取当前位置
                x, y = self.get_position()

            # 执行点击，拆解为按下-抬起
            for i in range(clicks):
                if i > 0:
                    # 如果是双击，使用10-20ms的随机间隔
                    if clicks == 2:
                        time.sleep(random.randint(10, 20) / 1000.0)
                    else:
                        # 如果是多次点击（>2），使用指定的间隔
                        time.sleep(interval)

                # 按下鼠标
                pyautogui.mouseDown(x=x, y=y, button=button)

                # 按下和抬起之间的随机间隔（1-15ms）
                time.sleep(random.randint(1, 15) / 1000.0)

                # 抬起鼠标
                pyautogui.mouseUp(x=x, y=y, button=button)

            # 执行后等待
            self._wait_with_randomness(wait_after_ms)

            return {
                "success": True,
                "position": {"x": x, "y": y},
                "button": button,
                "clicks": clicks,
                "wait_info": {
                    "before_ms": wait_before_ms,
                    "after_ms": wait_after_ms,
                    "press_release_ms": "1-15 (随机)",
                    "between_clicks_ms": "10-20 (随机，双击时)" if clicks == 2 else interval * 1000
                },
                "message": f"在坐标 ({x}, {y}) 执行了 {clicks} 次 {button} 键点击"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "执行鼠标点击时发生错误"
            }

    def mouse_drag(self, end_x, end_y, start_x=None, start_y=None, duration=0.5, button="left",
                  wait_before_ms=50, wait_after_ms=50):
        """
        执行鼠标拖拽操作。如果未指定起始坐标，则使用当前鼠标位置。

        Args:
            end_x: 结束X坐标
            end_y: 结束Y坐标
            start_x: 起始X坐标（可选，如果不提供则使用当前位置）
            start_y: 起始Y坐标（可选，如果不提供则使用当前位置）
            duration: 拖拽持续时间（秒）
            button: 鼠标按钮 ("left", "right", "middle")
            wait_before_ms: 执行前等待时间（毫秒），默认50ms
            wait_after_ms: 执行后等待时间（毫秒），默认50ms

        Returns:
            dict: 包含操作结果的字典
        """
        try:
            # 执行前等待
            self._wait_with_randomness(wait_before_ms)

            # 如果未提供起始坐标，使用当前位置
            if start_x is None or start_y is None:
                start_x, start_y = self.get_position()

            # 确保坐标在屏幕范围内
            start_x = max(0, min(start_x, self.screen_width - 1))
            start_y = max(0, min(start_y, self.screen_height - 1))
            end_x = max(0, min(end_x, self.screen_width - 1))
            end_y = max(0, min(end_y, self.screen_height - 1))

            # 移动到起始位置
            pyautogui.moveTo(start_x, start_y)

            # 执行拖拽（手动实现，以便更好地控制）
            # 按下鼠标
            pyautogui.mouseDown(x=start_x, y=start_y, button=button)

            # 移动到目标位置
            pyautogui.moveTo(end_x, end_y, duration=duration)

            # 抬起鼠标
            pyautogui.mouseUp(x=end_x, y=end_y, button=button)

            # 执行后等待
            self._wait_with_randomness(wait_after_ms)

            return {
                "success": True,
                "start_position": {"x": start_x, "y": start_y},
                "end_position": {"x": end_x, "y": end_y},
                "button": button,
                "wait_info": {
                    "before_ms": wait_before_ms,
                    "after_ms": wait_after_ms
                },
                "message": f"从坐标 ({start_x}, {start_y}) 拖拽到 ({end_x}, {end_y})"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "执行鼠标拖拽时发生错误"
            }

    def mouse_scroll(self, amount=3, x=None, y=None, direction="up",
                    wait_before_ms=50, wait_after_ms=50):
        """
        执行鼠标滚动操作。连续滚动时，两次之间间隔10-25ms随机时间。

        Args:
            amount: 滚动的数量（默认为3）
            x: 滚动位置的X坐标（可选，如果不提供则使用当前位置）
            y: 滚动位置的Y坐标（可选，如果不提供则使用当前位置）
            direction: 滚动方向 ("up" 向上, "down" 向下)
            wait_before_ms: 执行前等待时间（毫秒），默认50ms
            wait_after_ms: 执行后等待时间（毫秒），默认50ms

        Returns:
            dict: 包含操作结果的字典
        """
        try:
            # 执行前等待
            self._wait_with_randomness(wait_before_ms)

            # 如果提供了坐标，先移动到该位置
            if x is not None and y is not None:
                # 确保坐标在屏幕范围内
                x = max(0, min(x, self.screen_width - 1))
                y = max(0, min(y, self.screen_height - 1))

                pyautogui.moveTo(x, y)
            else:
                # 获取当前位置
                x, y = self.get_position()

            # 执行滚动，每次滚动一个单位，中间添加随机间隔
            scroll_value = 1 if direction.lower() == "up" else -1

            for i in range(amount):
                if i > 0:
                    # 连续滚动之间添加10-25ms的随机间隔
                    time.sleep(random.randint(10, 25) / 1000.0)

                # 执行单次滚动
                pyautogui.scroll(scroll_value)

            # 执行后等待
            self._wait_with_randomness(wait_after_ms)

            return {
                "success": True,
                "position": {"x": x, "y": y},
                "direction": direction,
                "amount": amount,
                "wait_info": {
                    "before_ms": wait_before_ms,
                    "after_ms": wait_after_ms,
                    "between_scrolls_ms": "10-25 (随机)"
                },
                "message": f"在坐标 ({x}, {y}) 向{direction}滚动了 {amount} 单位"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "执行鼠标滚动时发生错误"
            }

    def _move_with_trajectory(self, start_x, start_y, end_x, end_y):
        """
        使用记录的轨迹移动鼠标，模拟真实的人类移动。
        根据直线路径和轨迹库中的点生成自然的移动曲线。

        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
        """
        # 如果起点和终点相同，不需要移动
        if start_x == end_x and start_y == end_y:
            return

        # 计算移动距离
        distance = math.sqrt((end_x - start_x) ** 2 + (end_y - start_y) ** 2)

        # 根据距离确定路径点数量
        num_points = max(10, min(100, int(distance / 10)))

        # 1. 计算直线路径
        linear_path = calculate_linear_path(start_x, start_y, end_x, end_y, num_points)

        # 2. 获取所有轨迹点
        all_trajectory_points = []
        for trajectory in self.trajectories:
            all_trajectory_points.extend(trajectory)

        # 如果没有轨迹点，使用直线移动
        if not all_trajectory_points:
            move_duration = random.uniform(0.7, 1.0)
            pyautogui.moveTo(end_x, end_y, duration=move_duration)
            return

        # 3. 为每个路径点找到最近的轨迹点
        matched_points = find_nearest_trajectory_points(linear_path, all_trajectory_points, max_distance=20)

        # 4. 混合路径点和轨迹点
        blended_path = blend_trajectory_points(matched_points, self.screen_width, self.screen_height)

        # 5. 平滑路径
        smoothed_path = smooth_path(blended_path, smoothing_factor=0.3)

        # 6. 计算总移动时间（根据距离调整）
        # 距离越长，移动时间越长，但有上限
        base_time = min(0.7 + distance / 2000, 1.0)
        total_time = random.uniform(base_time, base_time + 0.3)

        # 7. 分配时间
        timed_path = distribute_time(smoothed_path, total_time)

        # 8. 移动鼠标，遵循路径
        start_time = time.time()
        last_point = None

        for point in timed_path:
            # 计算当前点应该在什么时间到达
            target_time = start_time + point["time"]

            # 等待直到应该移动到这个点
            now = time.time()
            if target_time > now:
                time.sleep(target_time - now)

            # 移动到这个点
            pyautogui.moveTo(point["x"], point["y"])

            # 如果有停留时间，等待
            if point.get("dwell_time", 0) > 0:
                time.sleep(point["dwell_time"] * 0.5)  # 缩短停留时间，避免移动过慢

            last_point = point

        # 确保移动到最终目标点
        if last_point and (last_point["x"] != end_x or last_point["y"] != end_y):
            pyautogui.moveTo(end_x, end_y)

    def _move_with_bezier(self, start_x, start_y, end_x, end_y):
        """
        使用简单的贝塞尔曲线移动鼠标。

        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
        """
        # 计算控制点（在起点和终点之间随机偏移）
        mid_x = (start_x + end_x) / 2
        mid_y = (start_y + end_y) / 2

        # 添加随机偏移（垂直于移动方向）
        dx = end_x - start_x
        dy = end_y - start_y

        # 计算垂直方向的单位向量
        length = math.sqrt(dx*dx + dy*dy)
        if length > 0:
            perpendicular_x = -dy / length
            perpendicular_y = dx / length

            # 随机偏移量（距离的10-20%）
            offset = random.uniform(0.1, 0.2) * length
            control_x = mid_x + perpendicular_x * offset
            control_y = mid_y + perpendicular_y * offset
        else:
            control_x = mid_x
            control_y = mid_y

        # 计算移动时间（0.5-0.8秒）
        move_duration = random.uniform(0.5, 0.8)

        # 生成贝塞尔曲线上的点
        steps = int(move_duration * 60)  # 每秒约60个点
        start_time = time.time()

        for i in range(steps + 1):
            t = i / steps
            # 二次贝塞尔曲线公式
            x = (1-t)**2 * start_x + 2*(1-t)*t * control_x + t**2 * end_x
            y = (1-t)**2 * start_y + 2*(1-t)*t * control_y + t**2 * end_y

            # 移动到这个点
            pyautogui.moveTo(int(x), int(y))

            # 计算下一个点的时间
            target_time = start_time + (i+1) / steps * move_duration
            now = time.time()
            if target_time > now:
                time.sleep(target_time - now)

    def _move_with_segments(self, start_x, start_y, end_x, end_y):
        """
        使用分段移动，将长距离移动分解为多个短距离移动。

        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
        """
        # 计算距离
        distance = math.sqrt((end_x - start_x) ** 2 + (end_y - start_y) ** 2)

        # 确定分段数（距离越长，分段越多）
        segments = max(2, min(5, int(distance / 300)))

        # 生成中间点
        points = [(start_x, start_y)]

        for i in range(1, segments):
            # 计算理想的中间点
            ideal_x = start_x + (end_x - start_x) * i / segments
            ideal_y = start_y + (end_y - start_y) * i / segments

            # 添加随机偏移（距离的5-10%）
            max_offset = 0.1 * distance / segments
            offset_x = random.uniform(-max_offset, max_offset)
            offset_y = random.uniform(-max_offset, max_offset)

            # 确保点在屏幕范围内
            point_x = max(0, min(int(ideal_x + offset_x), self.screen_width - 1))
            point_y = max(0, min(int(ideal_y + offset_y), self.screen_height - 1))

            points.append((point_x, point_y))

        points.append((end_x, end_y))

        # 移动经过所有点
        for i in range(1, len(points)):
            # 对于每个分段，使用贝塞尔曲线移动
            self._move_with_bezier(points[i-1][0], points[i-1][1], points[i][0], points[i][1])

    def get_screen_size(self):
        """
        获取屏幕尺寸。

        Returns:
            dict: 包含屏幕尺寸的字典
        """
        return {
            "width": self.screen_width,
            "height": self.screen_height,
            "message": f"屏幕尺寸: {self.screen_width}x{self.screen_height}"
        }

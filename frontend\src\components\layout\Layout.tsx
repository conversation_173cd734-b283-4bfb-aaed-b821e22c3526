// -*- coding: utf-8 -*-

import React, { useState } from 'react';
import { cn } from '../../utils';
import { BaseComponentProps } from '../../types';
import { Sidebar } from './Sidebar';
import { Header } from './Header';

/**
 * Layout组件属性接口
 */
interface LayoutProps extends BaseComponentProps {
  title?: string;
  showSidebar?: boolean;
}

/**
 * Layout组件
 * 主布局组件，包含头部、侧边栏和主内容区域
 * 严格按照代码规范的单一职责原则
 */
export function Layout({
  children,
  className,
  title = 'QYuan - 硅基CEO',
  showSidebar = true,
  ...props
}: LayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(true);

  /**
   * 切换侧边栏显示状态
   */
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  /**
   * 获取主容器样式类名
   */
  const getMainContainerClasses = () => {
    return cn(
      'flex',
      'h-screen',
      'bg-gray-50',
      'dark:bg-gray-900',
      className
    );
  };

  /**
   * 获取内容区域样式类名
   */
  const getContentAreaClasses = () => {
    return cn(
      'flex',
      'flex-col',
      'flex-1',
      'overflow-hidden',
      showSidebar && sidebarOpen && 'ml-64'
    );
  };

  /**
   * 获取主内容样式类名
   */
  const getMainContentClasses = () => {
    return cn(
      'flex-1',
      'overflow-auto',
      'p-6',
      'bg-gray-50',
      'dark:bg-gray-900'
    );
  };

  return (
    <div className={getMainContainerClasses()} {...props}>
      {/* 侧边栏 */}
      {showSidebar && (
        <Sidebar
          isOpen={sidebarOpen}
          onToggle={toggleSidebar}
        />
      )}

      {/* 主内容区域 */}
      <div className={getContentAreaClasses()}>
        {/* 头部 */}
        <Header
          title={title}
          showMenuButton={showSidebar}
          onMenuClick={toggleSidebar}
          sidebarOpen={sidebarOpen}
        />

        {/* 主内容 */}
        <main className={getMainContentClasses()}>
          {children}
        </main>
      </div>
    </div>
  );
}

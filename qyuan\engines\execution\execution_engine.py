#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行引擎实现

集成MCP适配器，提供统一的操作执行能力
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from ...engines import BaseEngine
from ...interfaces.mcp_adapter import <PERSON>PManager, MouseMCPAdapter, KeyboardMCPAdapter, VisionMCPAdapter
from ...core.events import EventType

logger = logging.getLogger(__name__)


class ExecutionEngine(BaseEngine):
    """执行引擎 - 集成MCP适配器提供操作能力"""

    def __init__(self, name: str, qyuan_core=None):
        """
        初始化执行引擎

        Args:
            name: 引擎名称
            qyuan_core: QYuan核心实例
        """
        super().__init__(name, qyuan_core)
        self.mcp_manager = MCPManager()
        self.capabilities = {
            "mouse_control": False,
            "keyboard_control": False,
            "vision_perception": False
        }
    
    async def _initialize(self):
        """初始化执行引擎"""
        try:
            self.logger.info("开始初始化执行引擎...")
            
            # 注册MCP适配器
            await self._register_mcp_adapters()
            
            self.logger.info("执行引擎初始化完成")
            
        except Exception as e:
            self.logger.error(f"执行引擎初始化失败: {e}")
            raise
    
    async def _start(self):
        """启动执行引擎"""
        try:
            self.logger.info("开始启动执行引擎...")
            
            # 启动所有MCP服务
            results = await self.mcp_manager.start_all_services()
            
            # 更新能力状态
            self.capabilities["mouse_control"] = results.get("auto_mouse", False)
            self.capabilities["keyboard_control"] = results.get("auto_keyboard", False)
            self.capabilities["vision_perception"] = results.get("auto_vision", False)
            
            # 记录启动结果
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            self.logger.info(f"MCP服务启动完成: {success_count}/{total_count} 成功")
            for service, success in results.items():
                status = "✅" if success else "❌"
                self.logger.info(f"  {status} {service}")
            
            # 发送启动事件
            if self.event_bus:
                await self.event_bus.emit(EventType.ENGINE_START, {
                    "engine": self.name,
                    "capabilities": self.capabilities,
                    "mcp_services": results
                }, source=self.name)
            
            self.logger.info("执行引擎启动完成")
            
        except Exception as e:
            self.logger.error(f"执行引擎启动失败: {e}")
            raise
    
    async def _stop(self):
        """停止执行引擎"""
        try:
            self.logger.info("开始停止执行引擎...")
            
            # 停止所有MCP服务
            results = await self.mcp_manager.stop_all_services()
            
            # 重置能力状态
            self.capabilities = {
                "mouse_control": False,
                "keyboard_control": False,
                "vision_perception": False
            }
            
            # 发送停止事件
            if self.event_bus:
                await self.event_bus.emit(EventType.ENGINE_STOP, {
                    "engine": self.name,
                    "mcp_services": results
                }, source=self.name)
            
            self.logger.info("执行引擎停止完成")
            
        except Exception as e:
            self.logger.error(f"执行引擎停止失败: {e}")
            raise
    
    async def _health_check(self) -> bool:
        """健康检查"""
        try:
            # 检查所有MCP服务健康状态
            health_results = await self.mcp_manager.health_check_all()
            
            # 如果至少有一个服务健康，则认为引擎健康
            is_healthy = any(health_results.values())
            
            if not is_healthy:
                self.logger.warning("执行引擎健康检查失败：所有MCP服务都不健康")
            
            return is_healthy
            
        except Exception as e:
            self.logger.error(f"执行引擎健康检查失败: {e}")
            return False
    
    async def _register_mcp_adapters(self):
        """注册MCP适配器"""
        try:
            # 注册鼠标适配器
            mouse_adapter = MouseMCPAdapter()
            self.mcp_manager.register_adapter(mouse_adapter)
            
            # 注册键盘适配器
            keyboard_adapter = KeyboardMCPAdapter()
            self.mcp_manager.register_adapter(keyboard_adapter)
            
            # 注册视觉适配器
            vision_adapter = VisionMCPAdapter()
            self.mcp_manager.register_adapter(vision_adapter)
            
            self.logger.info("MCP适配器注册完成")
            
        except Exception as e:
            self.logger.error(f"MCP适配器注册失败: {e}")
            raise
    
    # 公共接口方法
    
    async def execute_mouse_action(self, action: str, **kwargs) -> Dict[str, Any]:
        """执行鼠标操作"""
        try:
            if not self.capabilities["mouse_control"]:
                return {"success": False, "error": "鼠标控制功能不可用"}
            
            result = await self.mcp_manager.call_tool("auto_mouse", action, **kwargs)
            
            # 发送执行事件
            if self.event_bus:
                await self.event_bus.emit(EventType.ENGINE_ACTION, {
                    "engine": self.name,
                    "action_type": "mouse",
                    "action": action,
                    "parameters": kwargs,
                    "result": result
                }, source=self.name)
            
            return result
            
        except Exception as e:
            self.logger.error(f"执行鼠标操作失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def execute_keyboard_action(self, action: str, **kwargs) -> Dict[str, Any]:
        """执行键盘操作"""
        try:
            if not self.capabilities["keyboard_control"]:
                return {"success": False, "error": "键盘控制功能不可用"}
            
            result = await self.mcp_manager.call_tool("auto_keyboard", action, **kwargs)
            
            # 发送执行事件
            if self.event_bus:
                await self.event_bus.emit(EventType.ENGINE_ACTION, {
                    "engine": self.name,
                    "action_type": "keyboard",
                    "action": action,
                    "parameters": kwargs,
                    "result": result
                }, source=self.name)
            
            return result
            
        except Exception as e:
            self.logger.error(f"执行键盘操作失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def execute_vision_action(self, action: str, **kwargs) -> Dict[str, Any]:
        """执行视觉操作"""
        try:
            if not self.capabilities["vision_perception"]:
                return {"success": False, "error": "视觉感知功能不可用"}
            
            result = await self.mcp_manager.call_tool("auto_vision", action, **kwargs)
            
            # 发送执行事件
            if self.event_bus:
                await self.event_bus.emit(EventType.ENGINE_ACTION, {
                    "engine": self.name,
                    "action_type": "vision",
                    "action": action,
                    "parameters": kwargs,
                    "result": result
                }, source=self.name)
            
            return result
            
        except Exception as e:
            self.logger.error(f"执行视觉操作失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_capabilities(self) -> Dict[str, bool]:
        """获取当前能力状态"""
        return self.capabilities.copy()
    
    def get_available_tools(self) -> Dict[str, List[str]]:
        """获取所有可用工具"""
        return self.mcp_manager.get_all_tools()
    
    def get_mcp_status(self) -> Dict[str, Dict[str, Any]]:
        """获取MCP服务状态"""
        return self.mcp_manager.get_all_status()
    
    async def test_basic_operations(self) -> Dict[str, Any]:
        """测试基础操作功能"""
        test_results = {}
        
        # 测试鼠标功能
        if self.capabilities["mouse_control"]:
            try:
                result = await self.execute_mouse_action("get_mouse_position")
                test_results["mouse"] = {"success": result.get("success", False), "details": result}
            except Exception as e:
                test_results["mouse"] = {"success": False, "error": str(e)}
        else:
            test_results["mouse"] = {"success": False, "error": "鼠标控制功能不可用"}
        
        # 测试键盘功能
        if self.capabilities["keyboard_control"]:
            try:
                result = await self.execute_keyboard_action("get_clipboard_text")
                test_results["keyboard"] = {"success": result.get("success", False), "details": result}
            except Exception as e:
                test_results["keyboard"] = {"success": False, "error": str(e)}
        else:
            test_results["keyboard"] = {"success": False, "error": "键盘控制功能不可用"}
        
        # 测试视觉功能
        if self.capabilities["vision_perception"]:
            try:
                result = await self.execute_vision_action("get_screen_size")
                test_results["vision"] = {"success": result.get("success", False), "details": result}
            except Exception as e:
                test_results["vision"] = {"success": False, "error": str(e)}
        else:
            test_results["vision"] = {"success": False, "error": "视觉感知功能不可用"}
        
        return test_results

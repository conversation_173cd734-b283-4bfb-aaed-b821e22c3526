#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试图像增强功能
"""

import os
import sys
import time

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 导入模块
from src.screen_capture import ScreenCapture, ImageEnhancer

def test_image_enhancer():
    """测试图像增强功能"""
    print("测试图像增强功能...")
    
    # 创建屏幕分析对象
    screen_capture = ScreenCapture()
    
    # 捕获全屏
    print("捕获全屏...")
    result = screen_capture.capture_and_analyze_screen(
        question="这个屏幕上有什么内容？请描述主要元素及其位置。"
    )
    
    if result["success"]:
        print(f"屏幕截图已保存至: {result['screenshot_path']}")
        print(f"增强后的图像已保存至: {result['enhanced_path']}")
        print(f"分析结果:\n{result['analysis']}")
        
        # 检查文件是否存在
        if os.path.exists(result['enhanced_path']):
            print(f"增强后的图像文件存在，大小: {os.path.getsize(result['enhanced_path']) / 1024:.2f} KB")
            return True
        else:
            print("增强后的图像文件不存在！")
            return False
    else:
        print(f"分析失败: {result.get('error', '未知错误')}")
        return False

if __name__ == "__main__":
    # 测试图像增强功能
    success = test_image_enhancer()
    
    # 输出总结果
    print("\n测试结果:")
    print(f"图像增强功能: {'成功' if success else '失败'}")

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP服务器功能测试脚本

该脚本用于测试MCP服务器的工具定义和基本功能。
"""

import asyncio
import logging
import sys
import inspect
from start_server import main as server_main
from fastmcp import FastMCP
from keyboard_controller import KeyboardController

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("MCPServerTest")

def test_server_imports():
    """测试服务器相关模块导入"""
    logger.info("测试服务器相关模块导入...")
    try:
        from fastmcp import FastMCP
        from keyboard_controller import KeyboardController
        import start_server

        logger.info("✅ 服务器相关模块导入成功")
        return True
    except Exception as e:
        logger.error(f"❌ 服务器相关模块导入失败: {str(e)}")
        return False

def test_mcp_server_initialization():
    """测试MCP服务器初始化"""
    logger.info("测试MCP服务器初始化...")
    try:
        # 初始化键盘控制器
        keyboard_controller = KeyboardController()

        # 初始化MCP服务器
        mcp = FastMCP("Auto Keyboard Controller Test")

        logger.info("✅ MCP服务器初始化成功")
        return True, mcp, keyboard_controller
    except Exception as e:
        logger.error(f"❌ MCP服务器初始化失败: {str(e)}")
        return False, None, None

def test_tool_definitions():
    """测试工具定义"""
    logger.info("测试工具定义...")
    try:
        # 检查start_server.py中定义的工具
        # 由于工具是通过装饰器定义的，我们需要检查源代码
        import start_server
        import inspect

        # 获取start_server的源代码
        source = inspect.getsource(start_server)

        # 预期的工具函数
        expected_tools = [
            'press_key',
            'release_key',
            'type_key',
            'type_text',
            'hotkey',
            'set_clipboard_text',
            'get_clipboard_text',
            'is_ime_enabled',
            'switch_to_english_input',
            'ensure_english_input_before_typing'
        ]

        # 检查每个工具是否在源代码中定义
        missing_tools = []
        for tool_name in expected_tools:
            # 查找 @mcp.tool() 装饰器后面跟着的函数定义
            pattern = f"async def {tool_name}("
            if pattern not in source:
                missing_tools.append(tool_name)

        if missing_tools:
            logger.error(f"❌ 缺少工具定义: {missing_tools}")
            return False

        logger.info(f"✅ 所有工具定义检查通过，共 {len(expected_tools)} 个工具")
        return True
    except Exception as e:
        logger.error(f"❌ 工具定义检查失败: {str(e)}")
        return False

def test_argument_parsing():
    """测试命令行参数解析"""
    logger.info("测试命令行参数解析...")
    try:
        from start_server import parse_args

        # 测试默认参数
        import sys
        original_argv = sys.argv
        sys.argv = ['start_server.py']
        args = parse_args()

        assert args.transport == 'stdio', f"默认传输方式错误: {args.transport}"
        assert args.host == 'localhost', f"默认主机错误: {args.host}"
        assert args.port == 8000, f"默认端口错误: {args.port}"
        assert args.debug == False, f"默认调试模式错误: {args.debug}"

        # 恢复原始argv
        sys.argv = original_argv

        logger.info("✅ 命令行参数解析测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 命令行参数解析测试失败: {str(e)}")
        return False

def test_keyboard_controller_integration():
    """测试键盘控制器集成"""
    logger.info("测试键盘控制器集成...")
    try:
        controller = KeyboardController()

        # 测试所有公共方法是否存在
        expected_methods = [
            'press_key',
            'release_key',
            'type_key',
            'type_text',
            'hotkey',
            'set_clipboard_text',
            'get_clipboard_text',
            'is_ime_enabled',
            'switch_to_english_input',
            'ensure_english_input_before_typing'
        ]

        for method_name in expected_methods:
            assert hasattr(controller, method_name), f"缺少方法: {method_name}"
            method = getattr(controller, method_name)
            assert callable(method), f"方法不可调用: {method_name}"

        logger.info("✅ 键盘控制器集成测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 键盘控制器集成测试失败: {str(e)}")
        return False

def test_async_tool_simulation():
    """测试异步工具模拟"""
    logger.info("测试异步工具模拟...")
    try:
        async def simulate_tools():
            controller = KeyboardController()

            # 模拟一些工具调用（不实际执行键盘操作）

            # 测试剪贴板工具
            original_clipboard = controller.get_clipboard_text()
            controller.set_clipboard_text("测试文本")
            result = controller.get_clipboard_text()
            assert result == "测试文本", "剪贴板工具模拟失败"
            controller.set_clipboard_text(original_clipboard)

            # 测试输入法检测
            ime_status = controller.is_ime_enabled()
            assert isinstance(ime_status, bool), "输入法检测返回类型错误"

            return True

        # 运行异步测试
        result = asyncio.run(simulate_tools())

        logger.info("✅ 异步工具模拟测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 异步工具模拟测试失败: {str(e)}")
        return False

def test_error_handling():
    """测试错误处理"""
    logger.info("测试错误处理...")
    try:
        controller = KeyboardController()

        # 测试无效键名处理
        try:
            key_obj = controller._convert_to_key("invalid_key_name_12345")
            # 应该返回原始键名而不是抛出异常
            assert key_obj == "invalid_key_name_12345", "无效键名处理错误"
        except Exception:
            logger.error("无效键名应该被优雅处理，而不是抛出异常")
            return False

        logger.info("✅ 错误处理测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 错误处理测试失败: {str(e)}")
        return False

def main():
    """主函数，运行所有MCP服务器功能测试"""
    logger.info("开始MCP服务器功能测试")
    print("=" * 50)
    print("Auto Keyboard Controller MCP服务器功能测试")
    print("=" * 50)

    tests = [
        ("服务器模块导入", test_server_imports),
        ("MCP服务器初始化", test_mcp_server_initialization),
        ("工具定义检查", test_tool_definitions),
        ("命令行参数解析", test_argument_parsing),
        ("键盘控制器集成", test_keyboard_controller_integration),
        ("异步工具模拟", test_async_tool_simulation),
        ("错误处理", test_error_handling),
    ]

    passed_tests = 0
    total_tests = len(tests)

    for test_name, test_func in tests:
        print(f"\n正在运行: {test_name}")
        try:
            if test_name == "MCP服务器初始化":
                success, mcp, controller = test_func()
            else:
                success = test_func()

            if success:
                passed_tests += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")

    print("\n" + "=" * 50)
    print(f"测试结果: {passed_tests}/{total_tests} 通过")
    print("=" * 50)

    if passed_tests == total_tests:
        print("🎉 所有MCP服务器功能测试通过！")
        logger.info("所有MCP服务器功能测试通过")
        return 0
    else:
        print(f"⚠️  有 {total_tests - passed_tests} 个测试失败")
        logger.warning(f"有 {total_tests - passed_tests} 个测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())

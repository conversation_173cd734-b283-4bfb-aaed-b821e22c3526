# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS specific
.DS_Store
Thumbs.db

# Project specific
# 允许上传recordings目录，但忽略其中的大部分JSON文件
# 不要忽略.gitkeep文件
!recordings/.gitkeep
# 忽略recordings目录中的JSON文件，但可以选择性地添加例外
recordings/*.json
*.log

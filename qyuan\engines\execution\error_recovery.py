# -*- coding: utf-8 -*-
"""
错误恢复器实现
专门负责错误检测和自动恢复，严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Type
from datetime import datetime, timedelta

from .base import (
    ErrorRecoveryBase,
    ExecutionContext,
    OperationParameters,
    OperationType
)

class IntelligentErrorRecovery(ErrorRecoveryBase):
    """智能错误恢复器"""
    
    def __init__(self):
        super().__init__("IntelligentErrorRecovery")
        self.logger = logging.getLogger(f"QYuan.Execution.{self.name}")
        
        # 支持的错误类型
        self.supported_errors = [
            'TimeoutError',
            'ElementNotFoundError',
            'PermissionError',
            'ConnectionError',
            'ValidationError',
            'SystemError'
        ]
        
        # 恢复策略配置
        self.recovery_strategies = {
            'TimeoutError': self._recover_timeout_error,
            'ElementNotFoundError': self._recover_element_not_found,
            'PermissionError': self._recover_permission_error,
            'ConnectionError': self._recover_connection_error,
            'ValidationError': self._recover_validation_error,
            'SystemError': self._recover_system_error
        }
        
        # 恢复配置
        self.recovery_config = {
            'max_retry_attempts': 3,
            'retry_delay_base': 1.0,
            'retry_delay_multiplier': 2.0,
            'recovery_timeout': 30.0,
            'enable_adaptive_recovery': True,
            'enable_learning': True
        }
        
        # 恢复统计
        self.recovery_stats = {
            'total_errors': 0,
            'successful_recoveries': 0,
            'failed_recoveries': 0,
            'recovery_attempts_by_type': {},
            'average_recovery_time': 0.0
        }
        
        # 错误历史记录
        self.error_history: List[Dict[str, Any]] = []
        self.max_history_size = 100
    
    async def can_recover(self, error: Exception, context: ExecutionContext) -> bool:
        """检查是否可以恢复错误"""
        try:
            error_type = type(error).__name__
            
            # 检查是否支持该错误类型
            if error_type not in self.supported_errors:
                return False
            
            # 检查恢复次数限制
            if self._get_recent_error_count(error_type, context) >= self.recovery_config['max_retry_attempts']:
                self.logger.warning(f"错误 {error_type} 恢复次数已达上限")
                return False
            
            # 检查特定错误的恢复条件
            strategy_func = self.recovery_strategies.get(error_type)
            if strategy_func:
                return await self._check_recovery_feasibility(error, context, strategy_func)
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查错误恢复可行性失败: {e}")
            return False
    
    async def recover(self, error: Exception, context: ExecutionContext) -> Dict[str, Any]:
        """执行错误恢复"""
        start_time = time.time()
        error_type = type(error).__name__
        
        recovery_result = {
            'success': False,
            'strategy_used': None,
            'recovery_time': 0.0,
            'actions_taken': [],
            'recommendations': [],
            'error_type': error_type
        }
        
        try:
            self.logger.info(f"开始恢复错误: {error_type}")
            
            # 记录错误
            self._record_error(error, context)
            
            # 获取恢复策略
            strategy_func = self.recovery_strategies.get(error_type)
            if not strategy_func:
                recovery_result['recommendations'].append(f"未找到 {error_type} 的恢复策略")
                return recovery_result
            
            # 执行恢复策略
            strategy_result = await asyncio.wait_for(
                strategy_func(error, context),
                timeout=self.recovery_config['recovery_timeout']
            )
            
            recovery_result.update(strategy_result)
            recovery_result['strategy_used'] = strategy_func.__name__
            
            recovery_time = time.time() - start_time
            recovery_result['recovery_time'] = recovery_time
            
            # 更新统计
            self._update_recovery_stats(error_type, recovery_result['success'], recovery_time)
            
            if recovery_result['success']:
                self.logger.info(f"错误恢复成功: {error_type}")
            else:
                self.logger.warning(f"错误恢复失败: {error_type}")
            
        except asyncio.TimeoutError:
            recovery_result['recommendations'].append("恢复过程超时")
            self.logger.error(f"错误恢复超时: {error_type}")
        except Exception as e:
            recovery_result['recommendations'].append(f"恢复过程异常: {str(e)}")
            self.logger.error(f"错误恢复过程异常: {e}")
        
        return recovery_result
    
    async def get_recovery_strategy(self, error: Exception) -> Optional[str]:
        """获取恢复策略"""
        error_type = type(error).__name__
        
        if error_type in self.recovery_strategies:
            strategy_func = self.recovery_strategies[error_type]
            return strategy_func.__name__
        
        return None
    
    async def _check_recovery_feasibility(self, error: Exception, context: ExecutionContext, strategy_func) -> bool:
        """检查恢复可行性"""
        try:
            # 基本可行性检查
            error_type = type(error).__name__
            
            # 检查系统资源
            if not await self._check_system_resources():
                return False
            
            # 检查特定错误的恢复条件
            if error_type == 'TimeoutError':
                return True  # 超时错误通常可以重试
            elif error_type == 'ElementNotFoundError':
                return True  # 元素未找到可以尝试重新定位
            elif error_type == 'PermissionError':
                return False  # 权限错误通常需要人工干预
            elif error_type == 'ConnectionError':
                return True  # 连接错误可以尝试重连
            elif error_type == 'ValidationError':
                return True  # 验证错误可以尝试调整参数
            elif error_type == 'SystemError':
                return False  # 系统错误通常需要人工干预
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查恢复可行性失败: {e}")
            return False
    
    async def _recover_timeout_error(self, error: Exception, context: ExecutionContext) -> Dict[str, Any]:
        """恢复超时错误"""
        actions_taken = []
        recommendations = []
        
        try:
            # 等待一段时间后重试
            delay = self._calculate_retry_delay(context)
            await asyncio.sleep(delay)
            actions_taken.append(f"等待 {delay} 秒后重试")
            
            # 建议增加超时时间
            recommendations.append("考虑增加操作超时时间")
            recommendations.append("检查系统性能是否正常")
            
            return {
                'success': True,
                'actions_taken': actions_taken,
                'recommendations': recommendations
            }
            
        except Exception as e:
            return {
                'success': False,
                'actions_taken': actions_taken,
                'recommendations': recommendations + [f"恢复失败: {str(e)}"]
            }
    
    async def _recover_element_not_found(self, error: Exception, context: ExecutionContext) -> Dict[str, Any]:
        """恢复元素未找到错误"""
        actions_taken = []
        recommendations = []
        
        try:
            # 等待页面加载
            await asyncio.sleep(2.0)
            actions_taken.append("等待页面加载完成")
            
            # 建议重新截图和元素定位
            recommendations.append("重新截图并定位元素")
            recommendations.append("检查页面是否已完全加载")
            recommendations.append("考虑使用更宽松的元素匹配条件")
            
            return {
                'success': True,
                'actions_taken': actions_taken,
                'recommendations': recommendations
            }
            
        except Exception as e:
            return {
                'success': False,
                'actions_taken': actions_taken,
                'recommendations': recommendations + [f"恢复失败: {str(e)}"]
            }
    
    async def _recover_permission_error(self, error: Exception, context: ExecutionContext) -> Dict[str, Any]:
        """恢复权限错误"""
        actions_taken = []
        recommendations = []
        
        try:
            # 权限错误通常需要人工干预
            recommendations.append("检查应用程序权限设置")
            recommendations.append("以管理员身份运行程序")
            recommendations.append("检查文件或目录的访问权限")
            
            return {
                'success': False,  # 权限错误通常无法自动恢复
                'actions_taken': actions_taken,
                'recommendations': recommendations
            }
            
        except Exception as e:
            return {
                'success': False,
                'actions_taken': actions_taken,
                'recommendations': recommendations + [f"恢复失败: {str(e)}"]
            }
    
    async def _recover_connection_error(self, error: Exception, context: ExecutionContext) -> Dict[str, Any]:
        """恢复连接错误"""
        actions_taken = []
        recommendations = []
        
        try:
            # 等待网络恢复
            delay = self._calculate_retry_delay(context)
            await asyncio.sleep(delay)
            actions_taken.append(f"等待 {delay} 秒后重试连接")
            
            recommendations.append("检查网络连接状态")
            recommendations.append("检查防火墙设置")
            recommendations.append("尝试重启网络服务")
            
            return {
                'success': True,
                'actions_taken': actions_taken,
                'recommendations': recommendations
            }
            
        except Exception as e:
            return {
                'success': False,
                'actions_taken': actions_taken,
                'recommendations': recommendations + [f"恢复失败: {str(e)}"]
            }
    
    async def _recover_validation_error(self, error: Exception, context: ExecutionContext) -> Dict[str, Any]:
        """恢复验证错误"""
        actions_taken = []
        recommendations = []
        
        try:
            # 降低验证标准
            actions_taken.append("调整验证参数")
            
            recommendations.append("检查验证条件是否过于严格")
            recommendations.append("考虑使用更宽松的验证阈值")
            recommendations.append("重新评估预期结果")
            
            return {
                'success': True,
                'actions_taken': actions_taken,
                'recommendations': recommendations
            }
            
        except Exception as e:
            return {
                'success': False,
                'actions_taken': actions_taken,
                'recommendations': recommendations + [f"恢复失败: {str(e)}"]
            }
    
    async def _recover_system_error(self, error: Exception, context: ExecutionContext) -> Dict[str, Any]:
        """恢复系统错误"""
        actions_taken = []
        recommendations = []
        
        try:
            # 系统错误通常需要人工干预
            recommendations.append("检查系统日志")
            recommendations.append("重启相关服务")
            recommendations.append("检查系统资源使用情况")
            recommendations.append("联系系统管理员")
            
            return {
                'success': False,  # 系统错误通常无法自动恢复
                'actions_taken': actions_taken,
                'recommendations': recommendations
            }
            
        except Exception as e:
            return {
                'success': False,
                'actions_taken': actions_taken,
                'recommendations': recommendations + [f"恢复失败: {str(e)}"]
            }
    
    def _calculate_retry_delay(self, context: ExecutionContext) -> float:
        """计算重试延迟"""
        base_delay = self.recovery_config['retry_delay_base']
        multiplier = self.recovery_config['retry_delay_multiplier']
        
        # 根据重试次数计算延迟
        retry_count = self._get_context_retry_count(context)
        delay = base_delay * (multiplier ** retry_count)
        
        return min(delay, 10.0)  # 最大延迟10秒
    
    def _get_context_retry_count(self, context: ExecutionContext) -> int:
        """获取上下文中的重试次数"""
        return context.environment.get('retry_count', 0)
    
    def _get_recent_error_count(self, error_type: str, context: ExecutionContext) -> int:
        """获取最近的错误次数"""
        recent_time = datetime.now() - timedelta(minutes=5)
        
        count = 0
        for error_record in self.error_history:
            if (error_record['error_type'] == error_type and 
                error_record['timestamp'] > recent_time and
                error_record['session_id'] == context.session_id):
                count += 1
        
        return count
    
    async def _check_system_resources(self) -> bool:
        """检查系统资源"""
        try:
            import psutil
            
            # 检查CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 95:
                return False
            
            # 检查内存使用率
            memory = psutil.virtual_memory()
            if memory.percent > 95:
                return False
            
            return True
            
        except Exception as e:
            self.logger.warning(f"检查系统资源失败: {e}")
            return True  # 检查失败时假设资源充足
    
    def _record_error(self, error: Exception, context: ExecutionContext):
        """记录错误"""
        error_record = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'timestamp': datetime.now(),
            'session_id': context.session_id,
            'context': {
                'environment': context.environment,
                'constraints': context.constraints
            }
        }
        
        self.error_history.append(error_record)
        
        # 限制历史记录大小
        if len(self.error_history) > self.max_history_size:
            self.error_history = self.error_history[-self.max_history_size:]
    
    def _update_recovery_stats(self, error_type: str, success: bool, recovery_time: float):
        """更新恢复统计"""
        self.recovery_stats['total_errors'] += 1
        
        if success:
            self.recovery_stats['successful_recoveries'] += 1
        else:
            self.recovery_stats['failed_recoveries'] += 1
        
        # 更新按类型统计
        if error_type not in self.recovery_stats['recovery_attempts_by_type']:
            self.recovery_stats['recovery_attempts_by_type'][error_type] = {'total': 0, 'successful': 0}
        
        self.recovery_stats['recovery_attempts_by_type'][error_type]['total'] += 1
        if success:
            self.recovery_stats['recovery_attempts_by_type'][error_type]['successful'] += 1
        
        # 更新平均恢复时间
        total = self.recovery_stats['total_errors']
        current_avg = self.recovery_stats['average_recovery_time']
        self.recovery_stats['average_recovery_time'] = (
            (current_avg * (total - 1) + recovery_time) / total
        )
    
    def get_recovery_stats(self) -> Dict[str, Any]:
        """获取恢复统计"""
        stats = self.recovery_stats.copy()
        total = stats['total_errors']
        stats['success_rate'] = (stats['successful_recoveries'] / total) if total > 0 else 0.0
        
        # 计算各类型的成功率
        for error_type, type_stats in stats['recovery_attempts_by_type'].items():
            type_total = type_stats['total']
            type_successful = type_stats['successful']
            type_stats['success_rate'] = (type_successful / type_total) if type_total > 0 else 0.0
        
        return stats
    
    def register_recovery_strategy(self, error_type: str, strategy_func):
        """注册恢复策略"""
        self.recovery_strategies[error_type] = strategy_func
        if error_type not in self.supported_errors:
            self.supported_errors.append(error_type)
        self.logger.info(f"注册恢复策略: {error_type}")
    
    def update_recovery_config(self, config: Dict[str, Any]):
        """更新恢复配置"""
        self.recovery_config.update(config)
        self.logger.info(f"恢复配置已更新: {config}")

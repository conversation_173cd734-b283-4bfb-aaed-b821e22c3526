#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket API路由

提供QYuan的实时WebSocket通信接口
"""

import logging
import json
import uuid
import asyncio
from typing import Dict, Set
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from fastapi.websockets import WebSocketState

from ..models import WebSocketMessage, WebSocketResponse
from ..app import get_qyuan_core
from ...core.qyuan_core import QYuanCore

logger = logging.getLogger(__name__)

router = APIRouter()

# 活跃的WebSocket连接管理
class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, Set[str]] = {}  # user_id -> set of session_ids
    
    async def connect(self, websocket: WebSocket, session_id: str, user_id: str = "default"):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[session_id] = websocket
        
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = set()
        self.user_sessions[user_id].add(session_id)
        
        logger.info(f"WebSocket连接建立: {session_id} (用户: {user_id})")
    
    def disconnect(self, session_id: str, user_id: str = "default"):
        """断开WebSocket连接"""
        if session_id in self.active_connections:
            del self.active_connections[session_id]
        
        if user_id in self.user_sessions:
            self.user_sessions[user_id].discard(session_id)
            if not self.user_sessions[user_id]:
                del self.user_sessions[user_id]
        
        logger.info(f"WebSocket连接断开: {session_id} (用户: {user_id})")
    
    async def send_personal_message(self, message: dict, session_id: str):
        """发送个人消息"""
        if session_id in self.active_connections:
            websocket = self.active_connections[session_id]
            if websocket.client_state == WebSocketState.CONNECTED:
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"发送WebSocket消息失败: {e}")
                    # 连接可能已断开，清理连接
                    self.disconnect(session_id)
    
    async def broadcast_to_user(self, message: dict, user_id: str):
        """向用户的所有会话广播消息"""
        if user_id in self.user_sessions:
            for session_id in self.user_sessions[user_id].copy():
                await self.send_personal_message(message, session_id)
    
    async def broadcast_all(self, message: dict):
        """向所有连接广播消息"""
        for session_id in list(self.active_connections.keys()):
            await self.send_personal_message(message, session_id)
    
    def get_connection_count(self) -> int:
        """获取活跃连接数"""
        return len(self.active_connections)
    
    def get_user_count(self) -> int:
        """获取活跃用户数"""
        return len(self.user_sessions)


# 全局连接管理器
manager = ConnectionManager()


@router.websocket("/chat")
async def websocket_chat(
    websocket: WebSocket,
    session_id: str = Query(None, description="会话ID"),
    user_id: str = Query("default", description="用户ID"),
    api_key: str = Query(None, description="API密钥")
):
    """WebSocket聊天接口"""
    
    # 生成会话ID（如果没有提供）
    if not session_id:
        session_id = str(uuid.uuid4())
    
    try:
        # 验证API密钥（如果配置了）
        # TODO: 实现API密钥验证
        
        # 建立连接
        await manager.connect(websocket, session_id, user_id)
        
        # 发送连接成功消息
        welcome_message = WebSocketResponse(
            type="connection",
            data={
                "status": "connected",
                "session_id": session_id,
                "user_id": user_id,
                "message": "WebSocket连接建立成功"
            }
        )
        await manager.send_personal_message(welcome_message.dict(), session_id)
        
        # 获取QYuan核心
        try:
            qyuan = get_qyuan_core()
            communication_engine = qyuan.engines.get("communication")
            
            if not communication_engine:
                error_message = WebSocketResponse(
                    type="error",
                    data={"message": "通信引擎不可用"},
                    success=False,
                    error="通信引擎不可用"
                )
                await manager.send_personal_message(error_message.dict(), session_id)
                return
                
        except Exception as e:
            error_message = WebSocketResponse(
                type="error",
                data={"message": f"QYuan核心不可用: {e}"},
                success=False,
                error=str(e)
            )
            await manager.send_personal_message(error_message.dict(), session_id)
            return
        
        # 消息处理循环
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # 解析消息
                message = WebSocketMessage(**message_data)
                logger.info(f"收到WebSocket消息: {message.type} (会话: {session_id})")
                
                # 处理不同类型的消息
                if message.type == "chat":
                    await handle_chat_message(message, session_id, user_id, communication_engine)
                elif message.type == "ping":
                    await handle_ping_message(session_id)
                elif message.type == "status":
                    await handle_status_request(session_id, qyuan)
                else:
                    # 未知消息类型
                    error_response = WebSocketResponse(
                        type="error",
                        data={"message": f"未知消息类型: {message.type}"},
                        success=False,
                        error="UNKNOWN_MESSAGE_TYPE"
                    )
                    await manager.send_personal_message(error_response.dict(), session_id)
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket客户端断开连接: {session_id}")
                break
            except json.JSONDecodeError as e:
                logger.error(f"WebSocket消息JSON解析失败: {e}")
                error_response = WebSocketResponse(
                    type="error",
                    data={"message": "消息格式错误"},
                    success=False,
                    error="INVALID_JSON"
                )
                await manager.send_personal_message(error_response.dict(), session_id)
            except Exception as e:
                logger.error(f"WebSocket消息处理失败: {e}")
                error_response = WebSocketResponse(
                    type="error",
                    data={"message": f"消息处理失败: {e}"},
                    success=False,
                    error="MESSAGE_PROCESSING_ERROR"
                )
                await manager.send_personal_message(error_response.dict(), session_id)
    
    except Exception as e:
        logger.error(f"WebSocket连接处理失败: {e}")
    
    finally:
        # 清理连接
        manager.disconnect(session_id, user_id)


async def handle_chat_message(message: WebSocketMessage, session_id: str, user_id: str, communication_engine):
    """处理聊天消息"""
    try:
        user_message = message.data.get("message", "")
        if not user_message:
            error_response = WebSocketResponse(
                type="error",
                data={"message": "消息内容不能为空"},
                success=False,
                error="EMPTY_MESSAGE"
            )
            await manager.send_personal_message(error_response.dict(), session_id)
            return
        
        # 发送消息接收确认
        ack_response = WebSocketResponse(
            type="message_received",
            data={
                "message": user_message,
                "timestamp": message.timestamp.isoformat()
            }
        )
        await manager.send_personal_message(ack_response.dict(), session_id)
        
        # 发送"正在思考"状态
        thinking_response = WebSocketResponse(
            type="thinking",
            data={"status": "processing", "message": "QYuan正在思考..."}
        )
        await manager.send_personal_message(thinking_response.dict(), session_id)
        
        # 处理消息
        response_text = await communication_engine.process_user_message(
            message=user_message,
            user_id=user_id
        )
        
        # 发送回复
        chat_response = WebSocketResponse(
            type="chat_response",
            data={
                "response": response_text,
                "session_id": session_id,
                "user_message": user_message
            }
        )
        await manager.send_personal_message(chat_response.dict(), session_id)
        
    except Exception as e:
        logger.error(f"处理聊天消息失败: {e}")
        error_response = WebSocketResponse(
            type="error",
            data={"message": f"处理聊天消息失败: {e}"},
            success=False,
            error="CHAT_PROCESSING_ERROR"
        )
        await manager.send_personal_message(error_response.dict(), session_id)


async def handle_ping_message(session_id: str):
    """处理ping消息"""
    pong_response = WebSocketResponse(
        type="pong",
        data={"message": "pong"}
    )
    await manager.send_personal_message(pong_response.dict(), session_id)


async def handle_status_request(session_id: str, qyuan: QYuanCore):
    """处理状态请求"""
    try:
        status_info = await qyuan.get_status()
        
        status_response = WebSocketResponse(
            type="status",
            data={
                "is_running": status_info.is_running,
                "uptime": status_info.uptime,
                "engines_status": status_info.engines_status,
                "current_goal": status_info.current_goal,
                "current_task": status_info.current_task,
                "connection_count": manager.get_connection_count(),
                "user_count": manager.get_user_count()
            }
        )
        await manager.send_personal_message(status_response.dict(), session_id)
        
    except Exception as e:
        logger.error(f"获取状态失败: {e}")
        error_response = WebSocketResponse(
            type="error",
            data={"message": f"获取状态失败: {e}"},
            success=False,
            error="STATUS_ERROR"
        )
        await manager.send_personal_message(error_response.dict(), session_id)


@router.websocket("/system")
async def websocket_system(
    websocket: WebSocket,
    api_key: str = Query(None, description="API密钥")
):
    """WebSocket系统监控接口"""
    
    session_id = f"system_{uuid.uuid4()}"
    
    try:
        # 验证API密钥（如果配置了）
        # TODO: 实现API密钥验证
        
        # 建立连接
        await manager.connect(websocket, session_id, "system")
        
        # 发送连接成功消息
        welcome_message = WebSocketResponse(
            type="connection",
            data={
                "status": "connected",
                "session_id": session_id,
                "type": "system_monitor",
                "message": "系统监控WebSocket连接建立成功"
            }
        )
        await manager.send_personal_message(welcome_message.dict(), session_id)
        
        # 获取QYuan核心
        qyuan = get_qyuan_core()
        
        # 定期发送系统状态
        async def send_system_updates():
            while session_id in manager.active_connections:
                try:
                    status_info = await qyuan.get_status()
                    
                    system_update = WebSocketResponse(
                        type="system_update",
                        data={
                            "is_running": status_info.is_running,
                            "uptime": status_info.uptime,
                            "engines_status": status_info.engines_status,
                            "connection_count": manager.get_connection_count(),
                            "user_count": manager.get_user_count()
                        }
                    )
                    await manager.send_personal_message(system_update.dict(), session_id)
                    
                    # 每5秒发送一次更新
                    await asyncio.sleep(5)
                    
                except Exception as e:
                    logger.error(f"发送系统更新失败: {e}")
                    break
        
        # 启动系统更新任务
        update_task = asyncio.create_task(send_system_updates())
        
        try:
            # 保持连接
            while True:
                data = await websocket.receive_text()
                # 系统监控接口主要用于接收更新，不处理客户端消息
                
        except WebSocketDisconnect:
            logger.info(f"系统监控WebSocket客户端断开连接: {session_id}")
        finally:
            # 取消更新任务
            update_task.cancel()
    
    except Exception as e:
        logger.error(f"系统监控WebSocket连接处理失败: {e}")
    
    finally:
        # 清理连接
        manager.disconnect(session_id, "system")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QYuan主程序入口
"""

import asyncio
import sys
import signal
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 确保能找到依赖模块
try:
    import dotenv
except ImportError:
    print("警告: python-dotenv未安装，尝试安装...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-dotenv"])

from qyuan.core.config import QYuanConfig
from qyuan.core.qyuan_core import QYuanCore

# 全局QYuan实例
qyuan_instance = None

async def main():
    """主函数"""
    global qyuan_instance

    print("🤖 QYuan - 硅基CEO 正在启动...")

    try:
        # 加载配置
        config = QYuanConfig()
        print("✅ 配置加载完成")

        # 创建QYuan实例
        qyuan_instance = QYuanCore(config)
        print("✅ QYuan核心创建完成")

        # 设置信号处理
        def signal_handler(signum, frame):
            print(f"\n收到信号 {signum}，正在优雅关闭...")
            if qyuan_instance:
                asyncio.create_task(qyuan_instance.stop())

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # 启动QYuan
        await qyuan_instance.start()

        # 保持运行
        print("🚀 QYuan启动完成！按 Ctrl+C 停止")

        # 简单的交互循环
        while qyuan_instance.is_running:
            try:
                # 等待用户输入或系统事件
                await asyncio.sleep(1)

                # 这里可以添加简单的命令行交互
                # 例如：检查状态、设置目标等

            except KeyboardInterrupt:
                print("\n收到中断信号，正在停止...")
                break

        # 停止QYuan
        if qyuan_instance.is_running:
            await qyuan_instance.stop()

    except Exception as e:
        print(f"❌ QYuan启动失败: {e}")
        if qyuan_instance:
            await qyuan_instance.stop()
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序异常退出: {e}")
        sys.exit(1)

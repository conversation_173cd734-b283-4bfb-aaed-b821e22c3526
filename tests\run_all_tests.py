#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QYuan第二阶段测试运行器
统一执行所有端到端测试、性能测试和复杂场景测试
"""

import os
import sys
import time
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.test_dir = Path(__file__).parent
        self.results = {}
        
    def run_test_suite(self, test_path, suite_name, verbose=True):
        """运行测试套件"""
        print(f"\n{'='*60}")
        print(f"运行 {suite_name} 测试套件")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        # 构建pytest命令
        cmd = [
            sys.executable, "-m", "pytest",
            str(test_path),
            "-v" if verbose else "-q",
            "--tb=short",
            "--disable-warnings"
        ]
        
        try:
            # 运行测试
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=str(project_root)
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 记录结果
            self.results[suite_name] = {
                "success": result.returncode == 0,
                "duration": duration,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
            # 输出结果
            if result.returncode == 0:
                print(f"✅ {suite_name} 测试通过 (耗时: {duration:.2f}秒)")
            else:
                print(f"❌ {suite_name} 测试失败 (耗时: {duration:.2f}秒)")
            
            if verbose:
                print("\n测试输出:")
                print(result.stdout)
                if result.stderr:
                    print("\n错误输出:")
                    print(result.stderr)
            
        except Exception as e:
            print(f"❌ 运行 {suite_name} 测试时出错: {e}")
            self.results[suite_name] = {
                "success": False,
                "duration": 0,
                "error": str(e)
            }
    
    def run_all_tests(self, verbose=True, quick=False):
        """运行所有测试"""
        print("🚀 开始运行QYuan第二阶段完整测试套件")
        print(f"项目根目录: {project_root}")
        
        # 检查必要的依赖
        self._check_dependencies()
        
        # 定义测试套件
        test_suites = [
            (self.test_dir / "integration" / "test_perception_action_loop_e2e.py", "端到端集成测试"),
        ]
        
        if not quick:
            test_suites.extend([
                (self.test_dir / "performance" / "test_loop_performance_benchmark.py", "性能基准测试"),
                (self.test_dir / "scenarios" / "test_complex_scenarios.py", "复杂场景测试"),
            ])
        
        # 运行每个测试套件
        for test_path, suite_name in test_suites:
            if test_path.exists():
                self.run_test_suite(test_path, suite_name, verbose)
            else:
                print(f"⚠️  测试文件不存在: {test_path}")
                self.results[suite_name] = {
                    "success": False,
                    "duration": 0,
                    "error": "测试文件不存在"
                }
        
        # 输出总结
        self._print_summary()
    
    def _check_dependencies(self):
        """检查测试依赖"""
        required_packages = ["pytest", "asyncio"]
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"⚠️  缺少必要的依赖包: {', '.join(missing_packages)}")
            print("请运行: pip install pytest")
            sys.exit(1)
    
    def _print_summary(self):
        """打印测试总结"""
        print(f"\n{'='*60}")
        print("测试总结")
        print(f"{'='*60}")
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results.values() if r["success"])
        failed_tests = total_tests - passed_tests
        total_duration = sum(r.get("duration", 0) for r in self.results.values())
        
        print(f"总测试套件数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"总耗时: {total_duration:.2f}秒")
        
        # 详细结果
        print(f"\n{'套件名称':<20} {'状态':<8} {'耗时':<10}")
        print("-" * 40)
        
        for suite_name, result in self.results.items():
            status = "✅ 通过" if result["success"] else "❌ 失败"
            duration = f"{result.get('duration', 0):.2f}s"
            print(f"{suite_name:<20} {status:<8} {duration:<10}")
        
        # 失败详情
        failed_suites = [name for name, result in self.results.items() if not result["success"]]
        if failed_suites:
            print(f"\n失败的测试套件:")
            for suite_name in failed_suites:
                result = self.results[suite_name]
                print(f"\n{suite_name}:")
                if "error" in result:
                    print(f"  错误: {result['error']}")
                if "stderr" in result and result["stderr"]:
                    print(f"  错误输出: {result['stderr'][:200]}...")
        
        # 总体结果
        if failed_tests == 0:
            print(f"\n🎉 所有测试都通过了！QYuan第二阶段开发质量良好。")
            return True
        else:
            print(f"\n⚠️  有 {failed_tests} 个测试套件失败，需要修复。")
            return False
    
    def run_specific_test(self, test_name, verbose=True):
        """运行特定测试"""
        test_mapping = {
            "e2e": self.test_dir / "integration" / "test_perception_action_loop_e2e.py",
            "performance": self.test_dir / "performance" / "test_loop_performance_benchmark.py", 
            "scenarios": self.test_dir / "scenarios" / "test_complex_scenarios.py",
        }
        
        if test_name not in test_mapping:
            print(f"❌ 未知的测试名称: {test_name}")
            print(f"可用的测试: {', '.join(test_mapping.keys())}")
            return False
        
        test_path = test_mapping[test_name]
        suite_name = f"{test_name} 测试"
        
        self.run_test_suite(test_path, suite_name, verbose)
        return self.results[suite_name]["success"]


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="QYuan第二阶段测试运行器")
    parser.add_argument(
        "--test", "-t",
        choices=["all", "e2e", "performance", "scenarios"],
        default="all",
        help="要运行的测试类型"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    parser.add_argument(
        "--quick", "-q",
        action="store_true",
        help="快速测试（仅运行核心测试）"
    )
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    if args.test == "all":
        success = runner.run_all_tests(verbose=args.verbose, quick=args.quick)
    else:
        success = runner.run_specific_test(args.test, verbose=args.verbose)
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

"""
自动控制Windows鼠标的MCP服务器主文件。
"""

import logging
from typing import Optional

# 假设fastmcp库已安装
try:
    from fastmcp import FastMCP
except ImportError:
    # 如果未安装，可以使用模拟实现进行开发
    print("警告: fastmcp库未安装，使用模拟实现")
    from typing import Callable

    class FastMCP:
        def __init__(self, name, version=None):
            self.name = name
            self.version = version
            self.tools = {}

        def tool(self):
            def decorator(func):
                self.tools[func.__name__] = func
                return func
            return decorator

        def run(self, transport=None):
            print(f"模拟运行 {self.name} MCP服务器 (版本: {self.version})")
            print(f"可用工具: {list(self.tools.keys())}")

# 导入本地模块
from src.mouse_controller import MouseController
from src.utils.helpers import setup_logging

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

# 初始化FastMCP服务器
mcp = FastMCP("auto-mouse-server", version="0.1.0")

# 初始化鼠标控制器
mouse = MouseController()


@mcp.tool()
async def move_mouse(
    x: Optional[int] = None,
    y: Optional[int] = None,
    left: Optional[int] = None,
    top: Optional[int] = None,
    right: Optional[int] = None,
    bottom: Optional[int] = None,
    duration: float = 0.5,
    random_offset: bool = True,
    wait_before_ms: int = 50,
    wait_after_ms: int = 50
) -> str:
    """将鼠标移动到指定坐标或区域。支持绝对坐标或区域坐标，并会添加随机偏移。如果未指定坐标，则返回当前鼠标位置。

    Args:
        x: 目标X坐标（可选，如果不提供则返回当前位置）
        y: 目标Y坐标（可选，如果不提供则返回当前位置）
        left: 区域左边界坐标（与x/y互斥，用于区域定位）
        top: 区域上边界坐标（与x/y互斥，用于区域定位）
        right: 区域右边界坐标（与x/y互斥，用于区域定位）
        bottom: 区域下边界坐标（与x/y互斥，用于区域定位）
        duration: 移动持续时间（秒）
        random_offset: 是否添加随机偏移（1-3像素）
        wait_before_ms: 执行前等待时间（毫秒），默认50ms
        wait_after_ms: 执行后等待时间（毫秒），默认50ms
    """
    if left is not None and top is not None and right is not None and bottom is not None:
        logger.info(f"移动鼠标到区域 ({left}, {top}, {right}, {bottom})，持续时间 {duration}秒，随机偏移: {random_offset}")
    else:
        logger.info(f"移动鼠标到坐标 ({x}, {y})，持续时间 {duration}秒，随机偏移: {random_offset}")

    result = mouse.move_mouse(
        x, y, left, top, right, bottom,
        duration, random_offset,
        wait_before_ms, wait_after_ms
    )
    return str(result)


@mcp.tool()
async def mouse_click(
    x: Optional[int] = None,
    y: Optional[int] = None,
    button: str = "left",
    clicks: int = 1,
    interval: float = 0.1,
    wait_before_ms: int = 50,
    wait_after_ms: int = 50
) -> str:
    """在指定位置执行鼠标点击。如果未指定坐标，则在当前位置点击。
    点击会被拆解为按下-抬起，中间间隔1-15ms随机时间。
    双击时，两次点击之间间隔10-20ms随机时间。

    Args:
        x: 点击的X坐标（可选）
        y: 点击的Y坐标（可选）
        button: 鼠标按钮 ("left", "right", "middle")
        clicks: 点击次数
        interval: 点击之间的间隔（秒），仅当clicks>2时使用
        wait_before_ms: 执行前等待时间（毫秒），默认50ms
        wait_after_ms: 执行后等待时间（毫秒），默认50ms
    """
    logger.info(f"在坐标 ({x}, {y}) 执行 {clicks} 次 {button} 键点击，间隔 {interval}秒")
    result = mouse.mouse_click(x, y, button, clicks, interval, wait_before_ms, wait_after_ms)
    return str(result)


@mcp.tool()
async def mouse_drag(
    end_x: int,
    end_y: int,
    start_x: Optional[int] = None,
    start_y: Optional[int] = None,
    duration: float = 0.5,
    button: str = "left",
    wait_before_ms: int = 50,
    wait_after_ms: int = 50
) -> str:
    """执行鼠标拖拽操作。如果未指定起始坐标，则使用当前鼠标位置。

    Args:
        end_x: 结束X坐标
        end_y: 结束Y坐标
        start_x: 起始X坐标（可选，如果不提供则使用当前位置）
        start_y: 起始Y坐标（可选，如果不提供则使用当前位置）
        duration: 拖拽持续时间（秒）
        button: 鼠标按钮 ("left", "right", "middle")
        wait_before_ms: 执行前等待时间（毫秒），默认50ms
        wait_after_ms: 执行后等待时间（毫秒），默认50ms
    """
    logger.info(f"从坐标 ({start_x}, {start_y}) 拖拽到 ({end_x}, {end_y})，持续时间 {duration}秒，使用 {button} 键")
    result = mouse.mouse_drag(end_x, end_y, start_x, start_y, duration, button, wait_before_ms, wait_after_ms)
    return str(result)


@mcp.tool()
async def mouse_scroll(
    amount: Optional[int] = 3,
    x: Optional[int] = None,
    y: Optional[int] = None,
    direction: str = "up",
    wait_before_ms: int = 50,
    wait_after_ms: int = 50
) -> str:
    """执行鼠标滚动操作。连续滚动时，两次之间间隔10-25ms随机时间。

    Args:
        amount: 滚动的数量（默认为3）
        x: 滚动位置的X坐标（可选，如果不提供则使用当前位置）
        y: 滚动位置的Y坐标（可选，如果不提供则使用当前位置）
        direction: 滚动方向 ("up" 向上, "down" 向下)
        wait_before_ms: 执行前等待时间（毫秒），默认50ms
        wait_after_ms: 执行后等待时间（毫秒），默认50ms
    """
    logger.info(f"在坐标 ({x}, {y}) 向{direction}滚动 {amount} 单位")
    result = mouse.mouse_scroll(amount, x, y, direction, wait_before_ms, wait_after_ms)
    return str(result)


@mcp.tool()
async def get_mouse_position() -> str:
    """获取当前鼠标位置。"""
    logger.info("获取当前鼠标位置")
    x, y = mouse.get_position()
    result = {
        "success": True,
        "position": {"x": x, "y": y},
        "message": f"当前鼠标位置: ({x}, {y})"
    }
    return str(result)


@mcp.tool()
async def get_screen_size() -> str:
    """获取屏幕尺寸。"""
    logger.info("获取屏幕尺寸")
    result = mouse.get_screen_size()
    return str(result)


if __name__ == "__main__":
    logger.info("启动自动控制Windows鼠标的MCP服务器...")
    try:
        # 初始化并运行服务器
        mcp.run(transport='stdio')
    except Exception as e:
        logger.error(f"服务器运行出错: {e}")

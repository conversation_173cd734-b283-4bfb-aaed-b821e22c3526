#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试MCP截图工具

测试新增的MCP截图工具功能。
"""

import os
import sys
import time
import base64
from io import BytesIO
from PIL import Image

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入服务器类
from src.server import AutoVisionServer


def save_base64_image(base64_str, output_path):
    """
    保存base64编码的图像

    Args:
        base64_str: base64编码的图像字符串
        output_path: 输出路径
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 解码base64字符串
    img_data = base64.b64decode(base64_str)
    
    # 保存图像
    with open(output_path, "wb") as f:
        f.write(img_data)
    
    print(f"图像已保存至: {output_path}")


async def test_capture_fullscreen(server):
    """测试全屏截图功能"""
    print("\n测试全屏截图功能...")
    
    # 调用全屏截图工具
    result = await server.capture_fullscreen()
    
    if result["success"]:
        print(f"全屏截图已保存至: {result['path']}")
        print(f"图像尺寸: {result['width']} x {result['height']}")
        
        # 保存base64编码的图像
        test_output_path = os.path.join("tests", "output", "fullscreen.png")
        save_base64_image(result["image_base64"], test_output_path)
        
        return True
    else:
        print(f"截图失败: {result.get('error', '未知错误')}")
        return False


async def test_capture_fullscreen_with_coordinates(server):
    """测试带坐标的全屏截图功能"""
    print("\n测试带坐标的全屏截图功能...")
    
    # 调用带坐标的全屏截图工具
    result = await server.capture_fullscreen_with_coordinates()
    
    if result["success"]:
        print(f"带坐标的全屏截图已保存至: {result['path']}")
        print(f"原始截图路径: {result['original_path']}")
        print(f"图像尺寸: {result['width']} x {result['height']}")
        
        # 保存base64编码的图像
        test_output_path = os.path.join("tests", "output", "fullscreen_with_coordinates.png")
        save_base64_image(result["image_base64"], test_output_path)
        
        return True
    else:
        print(f"截图失败: {result.get('error', '未知错误')}")
        return False


async def test_capture_region(server):
    """测试区域截图功能"""
    print("\n测试区域截图功能...")
    
    # 获取屏幕尺寸
    screen_size = await server.get_screen_size()
    width, height = screen_size["width"], screen_size["height"]
    
    # 定义区域（屏幕中央区域）
    x = width // 4
    y = height // 4
    region_width = width // 2
    region_height = height // 2
    
    print(f"截图区域: 左上角({x}, {y}), 尺寸({region_width} x {region_height})")
    
    # 调用区域截图工具
    result = await server.capture_region(x, y, region_width, region_height)
    
    if result["success"]:
        print(f"区域截图已保存至: {result['path']}")
        print(f"图像尺寸: {result['width']} x {result['height']}")
        
        # 保存base64编码的图像
        test_output_path = os.path.join("tests", "output", "region.png")
        save_base64_image(result["image_base64"], test_output_path)
        
        return True
    else:
        print(f"截图失败: {result.get('error', '未知错误')}")
        return False


async def test_capture_region_with_coordinates(server):
    """测试带坐标的区域截图功能"""
    print("\n测试带坐标的区域截图功能...")
    
    # 获取屏幕尺寸
    screen_size = await server.get_screen_size()
    width, height = screen_size["width"], screen_size["height"]
    
    # 定义区域（屏幕中央区域）
    x = width // 4
    y = height // 4
    region_width = width // 2
    region_height = height // 2
    
    print(f"截图区域: 左上角({x}, {y}), 尺寸({region_width} x {region_height})")
    
    # 调用带坐标的区域截图工具
    result = await server.capture_region_with_coordinates(x, y, region_width, region_height)
    
    if result["success"]:
        print(f"带坐标的区域截图已保存至: {result['path']}")
        print(f"原始截图路径: {result['original_path']}")
        print(f"图像尺寸: {result['width']} x {result['height']}")
        
        # 保存base64编码的图像
        test_output_path = os.path.join("tests", "output", "region_with_coordinates.png")
        save_base64_image(result["image_base64"], test_output_path)
        
        return True
    else:
        print(f"截图失败: {result.get('error', '未知错误')}")
        return False


async def run_tests():
    """运行所有测试"""
    # 创建服务器对象
    server = AutoVisionServer()
    
    # 确保输出目录存在
    os.makedirs(os.path.join("tests", "output"), exist_ok=True)
    
    # 测试全屏截图功能
    fullscreen_result = await test_capture_fullscreen(server)
    
    # 测试带坐标的全屏截图功能
    fullscreen_coords_result = await test_capture_fullscreen_with_coordinates(server)
    
    # 测试区域截图功能
    region_result = await test_capture_region(server)
    
    # 测试带坐标的区域截图功能
    region_coords_result = await test_capture_region_with_coordinates(server)
    
    # 输出总结果
    print("\n测试结果:")
    print(f"全屏截图功能: {'成功' if fullscreen_result else '失败'}")
    print(f"带坐标的全屏截图功能: {'成功' if fullscreen_coords_result else '失败'}")
    print(f"区域截图功能: {'成功' if region_result else '失败'}")
    print(f"带坐标的区域截图功能: {'成功' if region_coords_result else '失败'}")


if __name__ == "__main__":
    # 导入异步运行库
    import asyncio
    
    # 运行测试
    asyncio.run(run_tests())

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OCR文本识别模块

提供OCR文本识别功能，支持Windows OCR和EasyOCR引擎。
"""

# 导入OCR识别模块
from .ocr_recognition.ocr_recognition import OCRRecognition

# 为了向后兼容，保留原有的导入方式
__all__ = ['OCRRecognition']

# 测试代码
if __name__ == "__main__":
    # 导入屏幕截图模块
    from screen_capture import ScreenCapture

    # 创建截图对象
    screen_capture = ScreenCapture()

    # 全屏截图
    screenshot, save_path = screen_capture.capture_screen()
    print(f"全屏截图已保存至: {save_path}")

    # 创建OCR对象（自动选择引擎）
    ocr = OCRRecognition(engine="auto", fallback=True)

    # 获取引擎信息
    engine_info = ocr.get_engine_info()
    print("\n引擎信息:")
    print(f"主引擎: {engine_info['primary_engine']['name']}")
    if engine_info['fallback_engine']:
        print(f"备用引擎: {engine_info['fallback_engine']['name']}")

    # 识别文本
    results = ocr.recognize_text(screenshot, return_format="structured")

    # 打印识别结果
    print("\n识别结果:")
    for item in results:
        print(f"文本: {item['text']}")
        print(f"位置: {item['box']}")
        print(f"置信度: {item['confidence']}")
        print("-" * 50)

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI元素识别模块 (PyWinAuto实现)

提供UI元素识别功能，基于PyWinAuto API。
专注于窗口内控件的识别，不做控制。
"""

import time
import os
import sys
import ctypes
import win32gui
import win32process
import traceback
from pywinauto import Application, Desktop, findwindows
from pywinauto.timings import Timings

# 添加当前目录到路径，确保可以导入同级模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from .ui_automation_base import UIAutomationBase

class UIAutomationPyWinAuto(UIAutomationBase):
    """UI元素识别类，提供基于PyWinAuto的控件识别功能"""

    def __init__(self):
        """初始化UI元素识别类"""
        # 设置PyWinAuto超时时间
        Timings.window_find_timeout = 5.0  # 增加窗口查找超时时间
        Timings.app_connect_timeout = 5.0  # 增加应用连接超时时间
        Timings.app_start_timeout = 10.0   # 增加应用启动超时时间

        # 初始化应用程序对象
        self.app = None

        print("PyWinAuto UI自动化引擎初始化成功")

    def get_foreground_window(self, max_retries=3, retry_interval=0.5):
        """
        获取前台窗口，使用多种方法尝试获取，提高可靠性

        Args:
            max_retries: 最大重试次数
            retry_interval: 重试间隔时间（秒）

        Returns:
            窗口对象
        """
        # 方法1: 使用Desktop.top_window()
        for retry in range(max_retries):
            try:
                desktop = Desktop(backend='uia')
                foreground_window = desktop.top_window()
                if foreground_window:
                    window_text = foreground_window.window_text()
                    print(f"前台窗口: {window_text} ({foreground_window.element_info.class_name})")
                    return foreground_window
            except Exception as e:
                print(f"方法1获取前台窗口失败 (尝试 {retry+1}/{max_retries}): {e}")
                if retry < max_retries - 1:
                    time.sleep(retry_interval)

        # 方法2: 使用win32gui.GetForegroundWindow()和Application.connect
        try:
            hwnd = win32gui.GetForegroundWindow()
            if hwnd:
                # 获取窗口标题
                window_title = win32gui.GetWindowText(hwnd)
                # 获取窗口类名
                window_class = win32gui.GetClassName(hwnd)

                print(f"Win32 API获取到前台窗口: {window_title} ({window_class})")

                # 尝试连接到窗口
                try:
                    app = Application(backend='uia').connect(handle=hwnd)
                    window = app.window(handle=hwnd)
                    return window
                except Exception as e:
                    print(f"连接到窗口失败: {e}")

                    # 尝试使用进程ID连接
                    try:
                        _, process_id = win32process.GetWindowThreadProcessId(hwnd)
                        app = Application(backend='uia').connect(process=process_id)
                        # 尝试获取主窗口
                        main_window = None
                        for window in app.windows():
                            if window.handle == hwnd:
                                main_window = window
                                break

                        if main_window:
                            return main_window
                    except Exception as e2:
                        print(f"通过进程ID连接失败: {e2}")
        except Exception as e:
            print(f"方法2获取前台窗口失败: {e}")
            traceback.print_exc()

        # 方法3: 使用findwindows.find_windows()查找所有顶级窗口
        try:
            # 查找所有顶级窗口
            windows = findwindows.find_windows(top_level_only=True)
            if windows:
                # 尝试连接到第一个可见的窗口
                for hwnd in windows:
                    try:
                        if win32gui.IsWindowVisible(hwnd):
                            window_title = win32gui.GetWindowText(hwnd)
                            if window_title:  # 只处理有标题的窗口
                                app = Application(backend='uia').connect(handle=hwnd)
                                window = app.window(handle=hwnd)
                                print(f"找到可见窗口: {window_title}")
                                return window
                    except Exception:
                        continue
        except Exception as e:
            print(f"方法3获取前台窗口失败: {e}")

        print("所有方法都失败，无法获取前台窗口")
        return None

    def get_window_by_title(self, title, partial_match=True, max_retries=3, retry_interval=0.5):
        """
        通过标题获取窗口，使用多种方法尝试获取，提高可靠性

        Args:
            title: 窗口标题
            partial_match: 是否部分匹配
            max_retries: 最大重试次数
            retry_interval: 重试间隔时间（秒）

        Returns:
            窗口对象
        """
        # 方法1: 使用Desktop.windows()
        for retry in range(max_retries):
            try:
                desktop = Desktop(backend='uia')
                windows = desktop.windows()

                for window in windows:
                    try:
                        window_text = window.window_text()
                        if (partial_match and title in window_text) or (not partial_match and title == window_text):
                            print(f"找到窗口: {window_text}")
                            return window
                    except Exception:
                        continue

                print(f"方法1未找到标题为'{title}'的窗口 (尝试 {retry+1}/{max_retries})")
                if retry < max_retries - 1:
                    time.sleep(retry_interval)
            except Exception as e:
                print(f"方法1查找窗口失败 (尝试 {retry+1}/{max_retries}): {e}")
                if retry < max_retries - 1:
                    time.sleep(retry_interval)

        # 方法2: 使用win32gui.FindWindow和EnumWindows
        try:
            # 定义回调函数
            found_windows = []

            def enum_windows_callback(hwnd, results):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    if window_text:
                        if (partial_match and title in window_text) or (not partial_match and title == window_text):
                            results.append(hwnd)

            # 枚举所有窗口
            win32gui.EnumWindows(enum_windows_callback, found_windows)

            if found_windows:
                hwnd = found_windows[0]
                window_text = win32gui.GetWindowText(hwnd)
                print(f"Win32 API找到窗口: {window_text}")

                # 尝试连接到窗口
                try:
                    app = Application(backend='uia').connect(handle=hwnd)
                    window = app.window(handle=hwnd)
                    return window
                except Exception as e:
                    print(f"连接到窗口失败: {e}")
        except Exception as e:
            print(f"方法2查找窗口失败: {e}")

        # 方法3: 使用findwindows.find_windows()
        try:
            # 查找匹配标题的窗口
            if partial_match:
                windows = findwindows.find_windows(title_re=f".*{title}.*", top_level_only=True)
            else:
                windows = findwindows.find_windows(title=title, top_level_only=True)

            if windows:
                hwnd = windows[0]
                window_title = win32gui.GetWindowText(hwnd)
                print(f"findwindows找到窗口: {window_title}")

                # 尝试连接到窗口
                try:
                    app = Application(backend='uia').connect(handle=hwnd)
                    window = app.window(handle=hwnd)
                    return window
                except Exception as e:
                    print(f"连接到窗口失败: {e}")
        except Exception as e:
            print(f"方法3查找窗口失败: {e}")

        print(f"所有方法都失败，未找到标题为'{title}'的窗口")
        return None

    def get_ui_elements(self, window, element_type=None, depth=3, visible_only=True):
        """
        获取UI元素及其属性

        Args:
            window: 窗口对象
            element_type: 元素类型，必须指定，例如："Button", "Edit", "ComboBox"等
            depth: 树的深度
            visible_only: 是否只返回可见元素

        Returns:
            元素列表，每个元素包含其属性（包括坐标信息）
        """
        elements = []

        if element_type is None:
            raise ValueError("必须指定element_type参数，以避免返回过多数据")

        try:
            # 确保window是pywinauto窗口对象
            if isinstance(window, int):  # 如果是窗口句柄
                try:
                    app = Application(backend='uia').connect(handle=window)
                    window = app.window(handle=window)
                except Exception as e:
                    print(f"连接到窗口句柄失败: {e}")
                    return []

            # 尝试使用descendants()方法获取所有控件
            try:
                all_controls = window.descendants()

                # 过滤控件
                for control in all_controls:
                    try:
                        # 检查可见性
                        is_visible = True
                        if visible_only:
                            try:
                                if hasattr(control, 'is_visible'):
                                    is_visible = control.is_visible()
                            except:
                                is_visible = True  # 如果无法确定可见性，则假设可见

                        if is_visible:
                            # 检查控件类型
                            control_type = "Unknown"
                            try:
                                if hasattr(control, 'control_type'):
                                    control_type = control.control_type()
                                elif hasattr(control, 'element_info') and hasattr(control.element_info, 'control_type'):
                                    control_type = control.element_info.control_type
                                elif hasattr(control, 'friendly_class_name'):
                                    control_type = control.friendly_class_name()
                            except:
                                pass

                            # 如果控件类型匹配
                            if control_type.lower() == element_type.lower():
                                # 获取控件属性
                                element_info = self._get_control_properties(control)
                                if element_info:
                                    elements.append(element_info)
                    except Exception as e:
                        print(f"处理控件时出错: {e}")
                        continue

                print(f"找到{len(elements)}个{element_type}元素")
                return elements

            except Exception as e:
                print(f"使用descendants()方法获取控件失败: {e}")

                # 备用方法：使用递归遍历
                def collect_control(control, current_depth=1, parent_path=""):
                    if current_depth > depth:
                        return

                    local_path = parent_path

                    try:
                        # 检查可见性
                        is_visible = True
                        if visible_only:
                            try:
                                if hasattr(control, 'is_visible'):
                                    is_visible = control.is_visible()
                            except:
                                is_visible = True  # 如果无法确定可见性，则假设可见

                        if is_visible:
                            # 检查控件类型
                            control_type = "Unknown"
                            try:
                                if hasattr(control, 'control_type'):
                                    control_type = control.control_type()
                                elif hasattr(control, 'element_info') and hasattr(control.element_info, 'control_type'):
                                    control_type = control.element_info.control_type
                                elif hasattr(control, 'friendly_class_name'):
                                    control_type = control.friendly_class_name()
                            except:
                                pass

                            # 获取控件名称
                            name = ""
                            try:
                                if hasattr(control, 'element_info') and hasattr(control.element_info, 'name'):
                                    name = control.element_info.name
                                elif hasattr(control, 'window_text'):
                                    name = control.window_text()
                            except:
                                pass

                            # 构建控件路径
                            if name:
                                if local_path:
                                    local_path += " > "
                                local_path += name
                            elif control_type:
                                if local_path:
                                    local_path += " > "
                                local_path += control_type

                            # 如果控件类型匹配
                            if control_type.lower() == element_type.lower():
                                # 获取控件属性
                                element_info = self._get_control_properties(control)
                                if element_info:
                                    element_info["path"] = local_path
                                    element_info["depth"] = current_depth
                                    elements.append(element_info)
                    except Exception as e:
                        print(f"获取控件属性时出错: {e}")

                    # 递归获取子控件
                    try:
                        if hasattr(control, 'children'):
                            for child in control.children():
                                collect_control(child, current_depth + 1, local_path)
                    except Exception as e:
                        print(f"获取子控件时出错: {e}")

                # 从窗口开始收集控件
                collect_control(window)

                print(f"找到{len(elements)}个{element_type}元素")
                return elements

        except Exception as e:
            print(f"PyWinAuto获取UI元素时出错: {e}")
            traceback.print_exc()

        return elements

    def _get_control_properties(self, control):
        """
        获取控件的属性

        Args:
            control: 控件对象

        Returns:
            dict: 控件属性字典
        """
        try:
            # 获取控件类型
            control_type = "Unknown"
            try:
                if hasattr(control, 'control_type'):
                    control_type = control.control_type()
                elif hasattr(control, 'element_info') and hasattr(control.element_info, 'control_type'):
                    control_type = control.element_info.control_type
                elif hasattr(control, 'friendly_class_name'):
                    control_type = control.friendly_class_name()
            except:
                pass

            # 获取控件矩形
            rectangle = {"left": 0, "top": 0, "right": 0, "bottom": 0, "width": 0, "height": 0}
            center = {"x": 0, "y": 0}
            try:
                if hasattr(control, 'rectangle'):
                    rect = control.rectangle()
                    rectangle = {
                        "left": rect.left,
                        "top": rect.top,
                        "right": rect.right,
                        "bottom": rect.bottom,
                        "width": rect.width(),
                        "height": rect.height()
                    }
                    center = {
                        "x": (rect.left + rect.right) // 2,
                        "y": (rect.top + rect.bottom) // 2
                    }
            except:
                pass

            # 获取控件值
            value = None
            try:
                if hasattr(control, 'texts'):
                    texts = control.texts()
                    if texts:
                        value = texts[0]
                elif hasattr(control, 'window_text'):
                    value = control.window_text()
            except:
                pass

            # 获取控件名称
            name = ""
            try:
                if hasattr(control, 'element_info') and hasattr(control.element_info, 'name'):
                    name = control.element_info.name
                elif hasattr(control, 'window_text'):
                    name = control.window_text()
            except:
                pass

            # 获取控件类名
            class_name = ""
            try:
                if hasattr(control, 'class_name'):
                    class_name = control.class_name()
                elif hasattr(control, 'element_info') and hasattr(control.element_info, 'class_name'):
                    class_name = control.element_info.class_name
            except:
                pass

            # 获取控件自动化ID
            automation_id = ""
            try:
                if hasattr(control, 'element_info') and hasattr(control.element_info, 'automation_id'):
                    automation_id = control.element_info.automation_id
            except:
                pass

            # 获取控件启用状态
            enabled = False
            try:
                if hasattr(control, 'is_enabled'):
                    enabled = control.is_enabled()
            except:
                pass

            # 获取控件可见状态
            visible = False
            try:
                if hasattr(control, 'is_visible'):
                    visible = control.is_visible()
            except:
                pass

            # 返回控件属性
            return {
                "name": name,
                "control_type": control_type,
                "automation_id": automation_id,
                "class_name": class_name,
                "rectangle": rectangle,
                "center": center,
                "value": value,
                "enabled": enabled,
                "visible": visible,
                "handle": control.handle if hasattr(control, 'handle') else None
            }
        except Exception as e:
            print(f"获取控件属性时出错: {e}")
            return None

    def find_element_by_text(self, text, element_type=None, window=None, match_type="contains", visible_only=True, timeout=5):
        """
        通过文本查找UI元素

        Args:
            text: 要查找的文本
            element_type: 元素类型，例如："Button", "Edit", "ComboBox"等
            window: 窗口对象，为None则在前台窗口中查找
            match_type: 匹配类型，可选值：exact, contains, starts_with, ends_with
            visible_only: 是否只查找可见元素
            timeout: 超时时间（秒）

        Returns:
            元素属性字典，包含元素的坐标信息
        """
        print(f"开始查找文本为'{text}'的{element_type or '任意类型'}元素")

        # 如果未指定窗口，则使用前台窗口
        if window is None:
            window = self.get_foreground_window()
            if window is None:
                print("未能获取前台窗口")
                return None

        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # 确保window是pywinauto窗口对象
                if isinstance(window, int):  # 如果是窗口句柄
                    try:
                        app = Application(backend='uia').connect(handle=window)
                        window = app.window(handle=window)
                    except Exception as e:
                        print(f"连接到窗口句柄失败: {e}")
                        return None

                # 获取所有控件
                try:
                    all_controls = window.descendants()
                except Exception as e:
                    print(f"获取控件失败: {e}")
                    time.sleep(0.5)
                    continue

                # 根据匹配类型查找控件
                found_control = None

                for control in all_controls:
                    try:
                        # 获取控件类型
                        control_type = "Unknown"
                        try:
                            if hasattr(control, 'control_type'):
                                control_type = control.control_type()
                            elif hasattr(control, 'element_info') and hasattr(control.element_info, 'control_type'):
                                control_type = control.element_info.control_type
                            elif hasattr(control, 'friendly_class_name'):
                                control_type = control.friendly_class_name()
                        except:
                            pass

                        # 检查控件类型
                        if element_type and control_type.lower() != element_type.lower():
                            continue

                        # 检查可见性
                        if visible_only:
                            try:
                                if hasattr(control, 'is_visible'):
                                    if not control.is_visible():
                                        continue
                            except:
                                pass  # 如果无法确定可见性，则假设可见

                        # 获取控件文本
                        control_text = ""
                        try:
                            if hasattr(control, 'window_text'):
                                control_text = control.window_text()
                            elif hasattr(control, 'texts'):
                                texts = control.texts()
                                if texts:
                                    control_text = texts[0]
                        except:
                            pass

                        # 获取控件名称
                        control_name = ""
                        try:
                            if hasattr(control, 'element_info') and hasattr(control.element_info, 'name'):
                                control_name = control.element_info.name
                        except:
                            pass

                        # 合并文本和名称
                        all_text = control_text or control_name
                        if not all_text:
                            continue

                        # 根据匹配类型进行匹配
                        matched = False

                        if match_type == "exact":
                            matched = (all_text == text)
                        elif match_type == "contains":
                            matched = (text in all_text)
                        elif match_type == "starts_with" or match_type == "startswith":
                            matched = all_text.startswith(text)
                        elif match_type == "ends_with" or match_type == "endswith":
                            matched = all_text.endswith(text)

                        if matched:
                            print(f"找到匹配的控件: {all_text} ({control_type})")
                            found_control = control
                            break

                    except Exception as e:
                        print(f"检查控件时出错: {e}")
                        continue

                if found_control:
                    # 使用_get_control_properties获取控件属性
                    element_info = self._get_control_properties(found_control)
                    if element_info:
                        print(f"成功获取控件属性")
                        return element_info
                    else:
                        print(f"获取控件属性失败")

            except Exception as e:
                print(f"PyWinAuto查找元素时出错: {e}")
                traceback.print_exc()

            time.sleep(0.5)

        print(f"未找到文本为'{text}'的{element_type or '任意类型'}元素")
        return None

    def activate_window(self, window):
        """
        激活窗口

        Args:
            window: 窗口对象

        Returns:
            操作是否成功
        """
        try:
            if hasattr(window, 'set_focus'):
                window.set_focus()
                return True
            return False
        except Exception as e:
            print(f"激活窗口时出错: {e}")
            return False

    def maximize_window(self, window):
        """
        最大化窗口

        Args:
            window: 窗口对象

        Returns:
            操作是否成功
        """
        try:
            if hasattr(window, 'maximize'):
                window.maximize()
                return True
            return False
        except Exception as e:
            print(f"最大化窗口时出错: {e}")
            return False

    def minimize_window(self, window):
        """
        最小化窗口

        Args:
            window: 窗口对象

        Returns:
            操作是否成功
        """
        try:
            if hasattr(window, 'minimize'):
                window.minimize()
                return True
            return False
        except Exception as e:
            print(f"最小化窗口时出错: {e}")
            return False

    def restore_window(self, window):
        """
        还原窗口

        Args:
            window: 窗口对象

        Returns:
            操作是否成功
        """
        try:
            if hasattr(window, 'restore'):
                window.restore()
                return True
            return False
        except Exception as e:
            print(f"还原窗口时出错: {e}")
            return False

    def resize_window(self, window, width, height):
        """
        调整窗口大小

        Args:
            window: 窗口对象
            width: 宽度
            height: 高度

        Returns:
            操作是否成功
        """
        try:
            if hasattr(window, 'move_window'):
                # 获取当前位置
                rect = window.rectangle()
                window.move_window(rect.left, rect.top, width, height)
                return True
            return False
        except Exception as e:
            print(f"调整窗口大小时出错: {e}")
            return False

    def move_window(self, window, x, y):
        """
        移动窗口

        Args:
            window: 窗口对象
            x: x坐标
            y: y坐标

        Returns:
            操作是否成功
        """
        try:
            if hasattr(window, 'move_window'):
                # 获取当前大小
                rect = window.rectangle()
                width = rect.width()
                height = rect.height()
                window.move_window(x, y, width, height)
                return True
            return False
        except Exception as e:
            print(f"移动窗口时出错: {e}")
            return False
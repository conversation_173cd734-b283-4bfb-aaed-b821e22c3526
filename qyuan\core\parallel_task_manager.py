# -*- coding: utf-8 -*-
"""
并行任务管理器
管理并行执行的任务，提供任务调度、资源管理和负载均衡功能
严格按照代码规范的单一职责原则
"""

import asyncio
import time
import uuid
from typing import Dict, Any, Optional, List, Callable, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor

from ..utils.logger import get_logger


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ParallelTask:
    """并行任务"""
    id: str
    name: str
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    timeout: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Any = None
    error: Optional[Exception] = None
    
    @property
    def execution_time(self) -> Optional[float]:
        """执行时间"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None


@dataclass
class TaskManagerStats:
    """任务管理器统计"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    cancelled_tasks: int = 0
    active_tasks: int = 0
    
    total_execution_time: float = 0.0
    average_execution_time: float = 0.0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_tasks == 0:
            return 0.0
        return self.completed_tasks / self.total_tasks


class ParallelTaskManager:
    """并行任务管理器"""
    
    def __init__(self, max_concurrent_tasks: int = 4, max_thread_workers: int = 2):
        """初始化并行任务管理器"""
        self.logger = get_logger(self.__class__.__name__)
        
        # 配置
        self.max_concurrent_tasks = max_concurrent_tasks
        self.max_thread_workers = max_thread_workers
        
        # 任务存储
        self.tasks: Dict[str, ParallelTask] = {}
        self.task_queue: List[str] = []  # 按优先级排序的任务队列
        self.running_tasks: Dict[str, asyncio.Task] = {}
        
        # 线程池（用于CPU密集型任务）
        self.thread_pool = ThreadPoolExecutor(max_workers=max_thread_workers)
        
        # 统计信息
        self.stats = TaskManagerStats()
        
        # 任务完成回调
        self.completion_callbacks: List[Callable] = []
        
        # 管理器状态
        self.is_running = False
        self._scheduler_task: Optional[asyncio.Task] = None
    
    async def start(self):
        """启动任务管理器"""
        if self.is_running:
            return
        
        self.is_running = True
        self._scheduler_task = asyncio.create_task(self._task_scheduler())
        self.logger.info("并行任务管理器已启动")
    
    async def stop(self):
        """停止任务管理器"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 停止调度器
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        
        # 取消所有运行中的任务
        for task_id, task in self.running_tasks.items():
            task.cancel()
            self.tasks[task_id].status = TaskStatus.CANCELLED
            self.stats.cancelled_tasks += 1
        
        # 等待所有任务完成
        if self.running_tasks:
            await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)
        
        # 关闭线程池
        self.thread_pool.shutdown(wait=True)
        
        self.logger.info("并行任务管理器已停止")
    
    def submit_task(self, name: str, func: Callable, *args, 
                   priority: TaskPriority = TaskPriority.NORMAL,
                   timeout: Optional[float] = None,
                   max_retries: int = 3,
                   **kwargs) -> str:
        """提交任务"""
        task_id = str(uuid.uuid4())
        
        task = ParallelTask(
            id=task_id,
            name=name,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            timeout=timeout,
            max_retries=max_retries
        )
        
        self.tasks[task_id] = task
        self._add_to_queue(task_id)
        
        self.stats.total_tasks += 1
        
        self.logger.debug(f"提交任务: {name} (ID: {task_id})")
        return task_id
    
    def submit_cpu_intensive_task(self, name: str, func: Callable, *args,
                                 priority: TaskPriority = TaskPriority.NORMAL,
                                 timeout: Optional[float] = None,
                                 **kwargs) -> str:
        """提交CPU密集型任务（在线程池中执行）"""
        async def thread_wrapper():
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.thread_pool, func, *args)
        
        return self.submit_task(name, thread_wrapper, priority=priority, timeout=timeout, **kwargs)
    
    async def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> Any:
        """等待任务完成"""
        if task_id not in self.tasks:
            raise ValueError(f"任务不存在: {task_id}")
        
        task = self.tasks[task_id]
        
        # 如果任务已完成，直接返回结果
        if task.status == TaskStatus.COMPLETED:
            return task.result
        elif task.status == TaskStatus.FAILED:
            raise task.error
        elif task.status == TaskStatus.CANCELLED:
            raise asyncio.CancelledError("任务已取消")
        
        # 等待任务完成
        start_time = time.time()
        while task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            if timeout and (time.time() - start_time) > timeout:
                raise asyncio.TimeoutError("等待任务超时")
            
            await asyncio.sleep(0.1)
        
        if task.status == TaskStatus.COMPLETED:
            return task.result
        elif task.status == TaskStatus.FAILED:
            raise task.error
        else:
            raise asyncio.CancelledError("任务已取消")
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        
        if task.status == TaskStatus.PENDING:
            # 从队列中移除
            if task_id in self.task_queue:
                self.task_queue.remove(task_id)
            task.status = TaskStatus.CANCELLED
            self.stats.cancelled_tasks += 1
            return True
        
        elif task.status == TaskStatus.RUNNING:
            # 取消运行中的任务
            if task_id in self.running_tasks:
                self.running_tasks[task_id].cancel()
                task.status = TaskStatus.CANCELLED
                self.stats.cancelled_tasks += 1
                return True
        
        return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id not in self.tasks:
            return None
        
        task = self.tasks[task_id]
        return {
            "id": task.id,
            "name": task.name,
            "status": task.status.value,
            "priority": task.priority.value,
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "execution_time": task.execution_time,
            "retry_count": task.retry_count,
            "error": str(task.error) if task.error else None
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        # 更新活动任务数
        self.stats.active_tasks = len(self.running_tasks)
        
        return {
            "total_tasks": self.stats.total_tasks,
            "completed_tasks": self.stats.completed_tasks,
            "failed_tasks": self.stats.failed_tasks,
            "cancelled_tasks": self.stats.cancelled_tasks,
            "active_tasks": self.stats.active_tasks,
            "pending_tasks": len(self.task_queue),
            "success_rate": self.stats.success_rate,
            "average_execution_time": self.stats.average_execution_time,
            "max_concurrent_tasks": self.max_concurrent_tasks,
            "thread_pool_size": self.max_thread_workers
        }
    
    async def _task_scheduler(self):
        """任务调度器"""
        self.logger.debug("任务调度器启动")
        
        while self.is_running:
            try:
                # 检查是否有可执行的任务
                if (len(self.running_tasks) < self.max_concurrent_tasks and 
                    self.task_queue):
                    
                    # 获取下一个任务
                    task_id = self.task_queue.pop(0)
                    task = self.tasks[task_id]
                    
                    # 启动任务
                    asyncio_task = asyncio.create_task(self._execute_task(task))
                    self.running_tasks[task_id] = asyncio_task
                    
                    self.logger.debug(f"启动任务: {task.name}")
                
                # 清理已完成的任务
                completed_tasks = []
                for task_id, asyncio_task in self.running_tasks.items():
                    if asyncio_task.done():
                        completed_tasks.append(task_id)
                
                for task_id in completed_tasks:
                    del self.running_tasks[task_id]
                
                # 短暂等待
                await asyncio.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"任务调度器异常: {e}")
                await asyncio.sleep(1)
    
    async def _execute_task(self, task: ParallelTask):
        """执行任务"""
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        
        try:
            # 执行任务
            if task.timeout:
                result = await asyncio.wait_for(
                    task.func(*task.args, **task.kwargs),
                    timeout=task.timeout
                )
            else:
                result = await task.func(*task.args, **task.kwargs)
            
            # 任务成功完成
            task.result = result
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            
            self.stats.completed_tasks += 1
            
            # 更新执行时间统计
            if task.execution_time:
                self.stats.total_execution_time += task.execution_time
                self.stats.average_execution_time = (
                    self.stats.total_execution_time / self.stats.completed_tasks
                )
            
            # 触发完成回调
            await self._notify_completion_callbacks(task, True)
            
            self.logger.debug(f"任务完成: {task.name}")
            
        except Exception as e:
            # 任务失败
            task.error = e
            task.retry_count += 1
            
            if task.retry_count <= task.max_retries:
                # 重试任务
                task.status = TaskStatus.PENDING
                task.started_at = None
                self._add_to_queue(task.id)
                self.logger.warning(f"任务失败，准备重试: {task.name} (重试次数: {task.retry_count})")
            else:
                # 任务最终失败
                task.status = TaskStatus.FAILED
                task.completed_at = datetime.now()
                self.stats.failed_tasks += 1
                
                # 触发完成回调
                await self._notify_completion_callbacks(task, False)
                
                self.logger.error(f"任务失败: {task.name}, 错误: {e}")
    
    def _add_to_queue(self, task_id: str):
        """添加任务到队列（按优先级排序）"""
        task = self.tasks[task_id]
        
        # 找到合适的插入位置
        insert_index = 0
        for i, existing_task_id in enumerate(self.task_queue):
            existing_task = self.tasks[existing_task_id]
            if task.priority.value > existing_task.priority.value:
                insert_index = i
                break
            insert_index = i + 1
        
        self.task_queue.insert(insert_index, task_id)
    
    async def _notify_completion_callbacks(self, task: ParallelTask, success: bool):
        """通知完成回调"""
        for callback in self.completion_callbacks:
            try:
                await callback(task, success)
            except Exception as e:
                self.logger.error(f"完成回调异常: {e}")
    
    def register_completion_callback(self, callback: Callable):
        """注册完成回调"""
        self.completion_callbacks.append(callback)

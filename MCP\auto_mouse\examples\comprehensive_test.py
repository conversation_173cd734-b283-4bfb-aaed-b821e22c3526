"""
综合测试脚本，测试所有鼠标操作功能。
"""

import sys
import os
import time
import random

# 添加src目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入鼠标控制器和记录器
from src.mouse_controller import MouseController
from src.mouse_recorder import MouseRecorder

def test_mouse_movement():
    """测试鼠标移动功能。"""
    print("\n=== 测试鼠标移动功能 ===")
    
    # 创建鼠标控制器
    mouse = MouseController()
    
    # 获取屏幕尺寸
    screen_size = mouse.get_screen_size()
    width, height = screen_size["width"], screen_size["height"]
    print(f"屏幕尺寸: {width}x{height}")
    
    # 测试1: 移动到绝对坐标
    print("\n测试1: 移动到绝对坐标")
    target_x, target_y = width // 2, height // 2
    print(f"移动到屏幕中心 ({target_x}, {target_y})...")
    result = mouse.move_mouse(target_x, target_y)
    print(result["message"])
    time.sleep(1)
    
    # 测试2: 移动到绝对坐标（带随机偏移）
    print("\n测试2: 移动到绝对坐标（带随机偏移）")
    target_x, target_y = width // 4, height // 4
    print(f"移动到坐标 ({target_x}, {target_y})，带随机偏移...")
    result = mouse.move_mouse(target_x, target_y, random_offset=True)
    print(result["message"])
    time.sleep(1)
    
    # 测试3: 移动到区域中心
    print("\n测试3: 移动到区域中心")
    left = width // 4
    top = height // 4
    right = width // 4 * 3
    bottom = height // 4 * 3
    print(f"移动到区域 ({left}, {top}, {right}, {bottom}) 中心...")
    result = mouse.move_mouse(left=left, top=top, right=right, bottom=bottom)
    print(result["message"])
    time.sleep(1)
    
    # 测试4: 使用高级轨迹混合算法移动
    print("\n测试4: 使用高级轨迹混合算法移动")
    target_x, target_y = width // 4 * 3, height // 4 * 3
    print(f"使用高级轨迹混合算法移动到 ({target_x}, {target_y})...")
    result = mouse.move_mouse(target_x, target_y)
    print(result["message"])
    time.sleep(1)
    
    print("\n鼠标移动功能测试完成")
    return True

def test_mouse_click():
    """测试鼠标点击功能。"""
    print("\n=== 测试鼠标点击功能 ===")
    
    # 创建鼠标控制器
    mouse = MouseController()
    
    # 获取屏幕尺寸
    screen_size = mouse.get_screen_size()
    width, height = screen_size["width"], screen_size["height"]
    
    # 移动到屏幕中心
    center_x, center_y = width // 2, height // 2
    mouse.move_mouse(center_x, center_y)
    time.sleep(1)
    
    # 测试1: 单击
    print("\n测试1: 单击")
    print("在当前位置执行单击...")
    result = mouse.mouse_click(button="left", clicks=1)
    print(result["message"])
    time.sleep(1)
    
    # 测试2: 双击
    print("\n测试2: 双击")
    print("在当前位置执行双击...")
    result = mouse.mouse_click(button="left", clicks=2)
    print(result["message"])
    time.sleep(1)
    
    # 测试3: 右键点击
    print("\n测试3: 右键点击")
    print("在当前位置执行右键点击...")
    result = mouse.mouse_click(button="right", clicks=1)
    print(result["message"])
    time.sleep(1)
    
    # 测试4: 在指定位置点击
    print("\n测试4: 在指定位置点击")
    target_x, target_y = width // 4 * 3, height // 4
    print(f"移动到 ({target_x}, {target_y}) 并点击...")
    result = mouse.mouse_click(x=target_x, y=target_y, button="left", clicks=1)
    print(result["message"])
    time.sleep(1)
    
    print("\n鼠标点击功能测试完成")
    return True

def test_mouse_drag():
    """测试鼠标拖拽功能。"""
    print("\n=== 测试鼠标拖拽功能 ===")
    
    # 创建鼠标控制器
    mouse = MouseController()
    
    # 获取屏幕尺寸
    screen_size = mouse.get_screen_size()
    width, height = screen_size["width"], screen_size["height"]
    
    # 测试1: 从左上到右下拖拽
    print("\n测试1: 从左上到右下拖拽")
    start_x, start_y = width // 4, height // 4
    end_x, end_y = width // 4 * 3, height // 4 * 3
    print(f"从 ({start_x}, {start_y}) 拖拽到 ({end_x}, {end_y})...")
    
    # 先移动到起点
    mouse.move_mouse(start_x, start_y)
    time.sleep(1)
    
    # 执行拖拽
    result = mouse.mouse_drag(end_x, end_y)
    print(result["message"])
    time.sleep(1)
    
    # 测试2: 从右上到左下拖拽
    print("\n测试2: 从右上到左下拖拽")
    start_x, start_y = width // 4 * 3, height // 4
    end_x, end_y = width // 4, height // 4 * 3
    print(f"从 ({start_x}, {start_y}) 拖拽到 ({end_x}, {end_y})...")
    
    # 先移动到起点
    mouse.move_mouse(start_x, start_y)
    time.sleep(1)
    
    # 执行拖拽
    result = mouse.mouse_drag(end_x, end_y)
    print(result["message"])
    time.sleep(1)
    
    print("\n鼠标拖拽功能测试完成")
    return True

def test_mouse_scroll():
    """测试鼠标滚动功能。"""
    print("\n=== 测试鼠标滚动功能 ===")
    
    # 创建鼠标控制器
    mouse = MouseController()
    
    # 获取屏幕尺寸
    screen_size = mouse.get_screen_size()
    width, height = screen_size["width"], screen_size["height"]
    
    # 移动到屏幕中心
    center_x, center_y = width // 2, height // 2
    mouse.move_mouse(center_x, center_y)
    time.sleep(1)
    
    # 测试1: 向上滚动
    print("\n测试1: 向上滚动")
    print("在当前位置向上滚动5单位...")
    result = mouse.mouse_scroll(amount=5, direction="up")
    print(result["message"])
    time.sleep(1)
    
    # 测试2: 向下滚动
    print("\n测试2: 向下滚动")
    print("在当前位置向下滚动5单位...")
    result = mouse.mouse_scroll(amount=5, direction="down")
    print(result["message"])
    time.sleep(1)
    
    # 测试3: 在指定位置滚动
    print("\n测试3: 在指定位置滚动")
    target_x, target_y = width // 4 * 3, height // 4 * 3
    print(f"移动到 ({target_x}, {target_y}) 并向上滚动...")
    result = mouse.mouse_scroll(amount=3, x=target_x, y=target_y, direction="up")
    print(result["message"])
    time.sleep(1)
    
    print("\n鼠标滚动功能测试完成")
    return True

def main():
    """主函数。"""
    print("综合测试 - 测试所有鼠标操作功能")
    print("-" * 50)
    
    # 获取当前鼠标位置
    mouse = MouseController()
    start_x, start_y = mouse.get_position()
    print(f"当前鼠标位置: ({start_x}, {start_y})")
    
    try:
        # 测试鼠标移动功能
        test_mouse_movement()
        
        # 测试鼠标点击功能
        test_mouse_click()
        
        # 测试鼠标拖拽功能
        test_mouse_drag()
        
        # 测试鼠标滚动功能
        test_mouse_scroll()
        
        print("\n所有测试完成！")
        
        # 返回到起始位置
        print(f"\n返回到起始位置 ({start_x}, {start_y})...")
        mouse.move_mouse(start_x, start_y)
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
    
    print("\n测试结束")

if __name__ == "__main__":
    main()

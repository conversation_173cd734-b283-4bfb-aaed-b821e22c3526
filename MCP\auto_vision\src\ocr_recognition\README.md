# OCR文本识别模块

本模块提供OCR文本识别功能，支持Windows OCR、EasyOCR和百度OCR引擎。

## 模块目的

OCR文本识别模块的主要目的是从屏幕截图中识别文本内容，并提供文本的位置信息。这使得LLM能够"看到"屏幕上的文字，并知道它们在屏幕上的位置，从而能够更好地理解屏幕内容并进行交互。

## 模块结构

- `__init__.py`: 包初始化文件，导出主要的类和接口
- `ocr_recognition_base.py`: 定义OCR文本识别的基础接口类，作为所有OCR引擎实现的基类
- `ocr_recognition_windows.py`: 实现基于Windows OCR API的文本识别功能（通过comtypes）
- `ocr_recognition_windows_net.py`: 实现基于Windows OCR API的文本识别功能（通过pythonnet）
- `ocr_recognition_easyocr.py`: 实现基于EasyOCR的文本识别功能
- `ocr_recognition_baidu.py`: 实现基于百度OCR API的文本识别功能
- `ocr_recognition_factory.py`: 提供创建不同OCR引擎的工厂类
- `ocr_recognition.py`: 主OCR接口类，提供统一的OCR文本识别接口

## 使用方法

```python
from src.ocr_recognition import OCRRecognition

# 创建OCR对象（自动选择引擎）
ocr = OCRRecognition(engine="auto", fallback=True)

# 或者指定引擎
# ocr = OCRRecognition(engine="windows", lang="en-US")  # Windows OCR
# ocr = OCRRecognition(engine="easyocr", lang=["en", "ch_sim"])  # EasyOCR
# ocr = OCRRecognition(engine="baidu")  # 百度OCR（标准版）
# ocr = OCRRecognition(engine="baidu_high")  # 百度OCR（高精度版）

# 从图像文件识别文本
results = ocr.recognize_text("screenshot.png", return_format="structured")

# 或者从PIL图像对象识别文本
from PIL import Image
image = Image.open("screenshot.png")
results = ocr.recognize_text(image, return_format="structured")

# 识别指定区域的文本
region = (100, 100, 400, 300)  # x, y, width, height
results = ocr.recognize_text(image, region=region, return_format="structured")

# 获取纯文本结果
text = ocr.recognize_text(image, return_format="text")

# 打印识别结果
print("\n识别结果:")
for item in results:
    print(f"文本: {item['text']}")
    print(f"位置: {item['box']}")
    print(f"置信度: {item['confidence']}")
    print("-" * 50)
```

## 引擎特点

### Windows OCR

Windows OCR是Windows 10/11内置的OCR引擎，通过Windows.Media.Ocr API提供。

**优点**：
- 系统集成，无需额外安装
- 速度快，资源占用低
- 准确率较高，特别是对于清晰的文本
- 支持多种语言

**缺点**：
- 仅限Windows平台
- 对复杂背景或特殊字体的识别能力有限
- 不提供置信度信息

### EasyOCR

EasyOCR是一个基于深度学习的开源OCR引擎，支持多种语言。

**优点**：
- 跨平台支持
- 多语言支持（80+种语言）
- 对复杂背景和特殊字体的适应性较强
- 提供置信度信息

**缺点**：
- 初次加载较慢（需要加载模型）
- 资源消耗较大
- 对于某些特定场景，准确率可能不如专用引擎

### 百度OCR

百度OCR是百度AI开放平台提供的OCR服务，通过API调用。

**优点**：
- 准确率高，特别是对中文的识别
- 支持多种语言和特殊场景（如手写体、表格等）
- 提供置信度信息和详细的位置信息
- 提供标准版和高精度版两种选择

**缺点**：
- 需要网络连接
- 有API调用次数限制
- 响应时间受网络影响

## 备用引擎机制

本模块实现了备用引擎机制，当主引擎识别失败或结果为空时，会自动尝试使用备用引擎。这提高了OCR识别的成功率和稳定性。

- 当主引擎为Windows OCR时，备用引擎为EasyOCR
- 当主引擎为EasyOCR时，备用引擎为Windows OCR（仅在Windows平台上）
- 当主引擎为百度OCR时，备用引擎为EasyOCR
- 当使用自动选择时，优先级顺序为：百度OCR > Windows OCR > EasyOCR

## 返回格式

OCR识别结果支持两种返回格式：

### 文本格式（text）

返回纯文本字符串，包含所有识别出的文本，按行分隔。

### 结构化格式（structured）

返回结构化数据，包含文本内容、位置信息和置信度。

```json
[
  {
    "text": "这是第一行文本",
    "box": [100, 100, 300, 130],  // [x1, y1, x2, y2]
    "confidence": 0.95,
    "words": [
      {
        "text": "这是",
        "box": [100, 100, 150, 130],
        "confidence": 0.96
      },
      {
        "text": "第一行",
        "box": [155, 100, 250, 130],
        "confidence": 0.95
      },
      {
        "text": "文本",
        "box": [255, 100, 300, 130],
        "confidence": 0.94
      }
    ]
  },
  {
    "text": "这是第二行文本",
    "box": [100, 150, 300, 180],
    "confidence": 0.93,
    "words": [
      // ...
    ]
  }
]
```

## 开发历史

### 2023-05-25

- 初始版本
- 实现了Windows OCR和EasyOCR引擎
- 实现了备用引擎机制
- 实现了结构化返回格式

### 2023-06-15

- 添加了百度OCR引擎支持
- 添加了Windows OCR的pythonnet实现
- 优化了自动选择引擎的逻辑
- 改进了错误处理和日志记录

## 依赖

- PIL (Pillow): `pip install pillow`
- NumPy: `pip install numpy`
- EasyOCR: `pip install easyocr`
- PyWin32 (Windows平台): `pip install pywin32`
- comtypes (Windows平台): `pip install comtypes`
- pythonnet (Windows平台，可选): `pip install pythonnet`
- 百度AI开放平台SDK: `pip install baidu-aip`

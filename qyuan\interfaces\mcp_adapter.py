#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP适配器模块

提供统一的MCP服务管理和调用接口，集成现有的MCP模块
(auto_mouse, auto_keyboard, auto_vision)到QYuan系统中。
"""

import asyncio
import logging
import subprocess
import json
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from enum import Enum

logger = logging.getLogger(__name__)


class MCPServiceStatus(Enum):
    """MCP服务状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    ERROR = "error"
    UNKNOWN = "unknown"


class MCPAdapter(ABC):
    """MCP适配器基类"""
    
    def __init__(self, service_name: str, service_path: str):
        """
        初始化MCP适配器
        
        Args:
            service_name: 服务名称
            service_path: 服务路径
        """
        self.service_name = service_name
        self.service_path = Path(service_path)
        self.process: Optional[subprocess.Popen] = None
        self.status = MCPServiceStatus.STOPPED
        self.last_health_check = 0
        self.health_check_interval = 30  # 30秒检查一次
        
    @abstractmethod
    async def start_service(self) -> bool:
        """启动MCP服务"""
        pass
    
    @abstractmethod
    async def stop_service(self) -> bool:
        """停止MCP服务"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass
    
    @abstractmethod
    async def call_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """调用工具"""
        pass
    
    @abstractmethod
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        pass
    
    async def is_healthy(self) -> bool:
        """检查服务是否健康"""
        current_time = time.time()
        if current_time - self.last_health_check > self.health_check_interval:
            self.last_health_check = current_time
            return await self.health_check()
        return self.status == MCPServiceStatus.RUNNING
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "service_name": self.service_name,
            "status": self.status.value,
            "process_id": self.process.pid if self.process else None,
            "last_health_check": self.last_health_check,
            "available_tools": self.get_available_tools()
        }


class MouseMCPAdapter(MCPAdapter):
    """鼠标MCP适配器"""

    def __init__(self, service_path: str = None):
        if service_path is None:
            # 使用绝对路径
            current_dir = Path(__file__).parent.parent.parent
            service_path = current_dir / "MCP" / "auto_mouse"
        super().__init__("auto_mouse", service_path)
        self.tools = [
            "move_mouse", "mouse_click", "mouse_drag", 
            "mouse_scroll", "get_mouse_position", "get_screen_size"
        ]
    
    async def start_service(self) -> bool:
        """启动鼠标MCP服务"""
        try:
            self.status = MCPServiceStatus.STARTING
            logger.info(f"启动鼠标MCP服务: {self.service_path}")
            
            # 构建启动命令
            start_script = self.service_path / "start_server.py"
            if not start_script.exists():
                logger.error(f"启动脚本不存在: {start_script}")
                self.status = MCPServiceStatus.ERROR
                return False
            
            # 启动进程 - 使用当前Python解释器确保使用相同的虚拟环境
            import sys
            python_executable = sys.executable
            self.process = subprocess.Popen(
                [python_executable, str(start_script), "--transport", "stdio"],
                cwd=str(self.service_path),
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待服务启动
            await asyncio.sleep(2)

            if self.process.poll() is None:
                self.status = MCPServiceStatus.RUNNING
                logger.info("鼠标MCP服务启动成功")
                return True
            else:
                # 进程已退出，获取错误信息
                stdout, stderr = self.process.communicate()
                self.status = MCPServiceStatus.ERROR
                logger.error(f"鼠标MCP服务启动失败，退出码: {self.process.returncode}")
                if stderr:
                    logger.error(f"错误输出: {stderr}")
                if stdout:
                    logger.info(f"标准输出: {stdout}")
                return False
                
        except Exception as e:
            logger.error(f"启动鼠标MCP服务失败: {e}")
            self.status = MCPServiceStatus.ERROR
            return False
    
    async def stop_service(self) -> bool:
        """停止鼠标MCP服务"""
        try:
            if self.process:
                self.process.terminate()
                await asyncio.sleep(1)
                if self.process.poll() is None:
                    self.process.kill()
                self.process = None
            
            self.status = MCPServiceStatus.STOPPED
            logger.info("鼠标MCP服务已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止鼠标MCP服务失败: {e}")
            return False
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.process or self.process.poll() is not None:
                self.status = MCPServiceStatus.ERROR
                return False
            
            # 尝试调用一个简单的工具来检查服务是否响应
            result = await self.call_tool("get_mouse_position")
            if result.get("success", False):
                self.status = MCPServiceStatus.RUNNING
                return True
            else:
                self.status = MCPServiceStatus.ERROR
                return False
                
        except Exception as e:
            logger.error(f"鼠标MCP服务健康检查失败: {e}")
            self.status = MCPServiceStatus.ERROR
            return False
    
    async def call_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """调用鼠标工具"""
        try:
            if not self.process or self.process.poll() is not None:
                return {"success": False, "error": "服务未运行"}
            
            # 构建MCP请求
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": kwargs
                }
            }
            
            # 发送请求
            request_json = json.dumps(request) + "\n"
            self.process.stdin.write(request_json)
            self.process.stdin.flush()
            
            # 读取响应
            response_line = self.process.stdout.readline()
            if response_line:
                response = json.loads(response_line.strip())
                if "result" in response:
                    return {"success": True, "result": response["result"]}
                else:
                    return {"success": False, "error": response.get("error", "未知错误")}
            else:
                return {"success": False, "error": "无响应"}
                
        except Exception as e:
            logger.error(f"调用鼠标工具失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return self.tools.copy()


class KeyboardMCPAdapter(MCPAdapter):
    """键盘MCP适配器"""

    def __init__(self, service_path: str = None):
        if service_path is None:
            # 使用绝对路径
            current_dir = Path(__file__).parent.parent.parent
            service_path = current_dir / "MCP" / "auto_keyboard"
        super().__init__("auto_keyboard", service_path)
        self.tools = [
            "press_key", "release_key", "type_key", "type_text", "hotkey",
            "set_clipboard_text", "get_clipboard_text", "is_ime_enabled",
            "switch_to_english_input", "ensure_english_input_before_typing",
            "get_detailed_ime_info"
        ]
    
    async def start_service(self) -> bool:
        """启动键盘MCP服务"""
        try:
            self.status = MCPServiceStatus.STARTING
            logger.info(f"启动键盘MCP服务: {self.service_path}")
            
            # 构建启动命令
            start_script = self.service_path / "start_server.py"
            if not start_script.exists():
                logger.error(f"启动脚本不存在: {start_script}")
                self.status = MCPServiceStatus.ERROR
                return False
            
            # 启动进程 - 使用当前Python解释器确保使用相同的虚拟环境
            import sys
            python_executable = sys.executable
            self.process = subprocess.Popen(
                [python_executable, str(start_script), "--transport", "stdio"],
                cwd=str(self.service_path),
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待服务启动
            await asyncio.sleep(2)

            if self.process.poll() is None:
                self.status = MCPServiceStatus.RUNNING
                logger.info("键盘MCP服务启动成功")
                return True
            else:
                # 进程已退出，获取错误信息
                stdout, stderr = self.process.communicate()
                self.status = MCPServiceStatus.ERROR
                logger.error(f"键盘MCP服务启动失败，退出码: {self.process.returncode}")
                if stderr:
                    logger.error(f"错误输出: {stderr}")
                if stdout:
                    logger.info(f"标准输出: {stdout}")
                return False
                
        except Exception as e:
            logger.error(f"启动键盘MCP服务失败: {e}")
            self.status = MCPServiceStatus.ERROR
            return False
    
    async def stop_service(self) -> bool:
        """停止键盘MCP服务"""
        try:
            if self.process:
                self.process.terminate()
                await asyncio.sleep(1)
                if self.process.poll() is None:
                    self.process.kill()
                self.process = None
            
            self.status = MCPServiceStatus.STOPPED
            logger.info("键盘MCP服务已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止键盘MCP服务失败: {e}")
            return False
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.process or self.process.poll() is not None:
                self.status = MCPServiceStatus.ERROR
                return False
            
            # 尝试调用一个简单的工具来检查服务是否响应
            result = await self.call_tool("get_clipboard_text")
            if result.get("success", False):
                self.status = MCPServiceStatus.RUNNING
                return True
            else:
                self.status = MCPServiceStatus.ERROR
                return False
                
        except Exception as e:
            logger.error(f"键盘MCP服务健康检查失败: {e}")
            self.status = MCPServiceStatus.ERROR
            return False
    
    async def call_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """调用键盘工具"""
        try:
            if not self.process or self.process.poll() is not None:
                return {"success": False, "error": "服务未运行"}
            
            # 构建MCP请求
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": kwargs
                }
            }
            
            # 发送请求
            request_json = json.dumps(request) + "\n"
            self.process.stdin.write(request_json)
            self.process.stdin.flush()
            
            # 读取响应
            response_line = self.process.stdout.readline()
            if response_line:
                response = json.loads(response_line.strip())
                if "result" in response:
                    return {"success": True, "result": response["result"]}
                else:
                    return {"success": False, "error": response.get("error", "未知错误")}
            else:
                return {"success": False, "error": "无响应"}
                
        except Exception as e:
            logger.error(f"调用键盘工具失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return self.tools.copy()


class VisionMCPAdapter(MCPAdapter):
    """视觉MCP适配器"""

    def __init__(self, service_path: str = None):
        if service_path is None:
            # 使用绝对路径
            current_dir = Path(__file__).parent.parent.parent
            service_path = current_dir / "MCP" / "auto_vision"
        super().__init__("auto_vision", service_path)
        self.tools = [
            "capture_fullscreen", "capture_fullscreen_with_coordinates",
            "capture_region", "capture_region_with_coordinates",
            "recognize_text", "get_ui_elements", "find_element_by_text",
            "interact_with_element_by_text", "analyze_screen",
            "analyze_screen_region", "analyze_latest_screenshot",
            "capture_and_analyze_screen", "get_screen_size"
        ]

    async def start_service(self) -> bool:
        """启动视觉MCP服务"""
        try:
            self.status = MCPServiceStatus.STARTING
            logger.info(f"启动视觉MCP服务: {self.service_path}")

            # 构建启动命令
            start_script = self.service_path / "start_server.py"
            if not start_script.exists():
                logger.error(f"启动脚本不存在: {start_script}")
                self.status = MCPServiceStatus.ERROR
                return False

            # 启动进程 - 使用当前Python解释器确保使用相同的虚拟环境
            import sys
            python_executable = sys.executable
            self.process = subprocess.Popen(
                [python_executable, str(start_script), "--transport", "stdio"],
                cwd=str(self.service_path),
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 等待服务启动
            await asyncio.sleep(3)  # 视觉服务需要更长的启动时间

            if self.process.poll() is None:
                self.status = MCPServiceStatus.RUNNING
                logger.info("视觉MCP服务启动成功")
                return True
            else:
                # 进程已退出，获取错误信息
                stdout, stderr = self.process.communicate()
                self.status = MCPServiceStatus.ERROR
                logger.error(f"视觉MCP服务启动失败，退出码: {self.process.returncode}")
                if stderr:
                    logger.error(f"错误输出: {stderr}")
                if stdout:
                    logger.info(f"标准输出: {stdout}")
                return False

        except Exception as e:
            logger.error(f"启动视觉MCP服务失败: {e}")
            self.status = MCPServiceStatus.ERROR
            return False

    async def stop_service(self) -> bool:
        """停止视觉MCP服务"""
        try:
            if self.process:
                self.process.terminate()
                await asyncio.sleep(1)
                if self.process.poll() is None:
                    self.process.kill()
                self.process = None

            self.status = MCPServiceStatus.STOPPED
            logger.info("视觉MCP服务已停止")
            return True

        except Exception as e:
            logger.error(f"停止视觉MCP服务失败: {e}")
            return False

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.process or self.process.poll() is not None:
                self.status = MCPServiceStatus.ERROR
                return False

            # 尝试调用一个简单的工具来检查服务是否响应
            result = await self.call_tool("get_screen_size")
            if result.get("success", False):
                self.status = MCPServiceStatus.RUNNING
                return True
            else:
                self.status = MCPServiceStatus.ERROR
                return False

        except Exception as e:
            logger.error(f"视觉MCP服务健康检查失败: {e}")
            self.status = MCPServiceStatus.ERROR
            return False

    async def call_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """调用视觉工具"""
        try:
            if not self.process or self.process.poll() is not None:
                return {"success": False, "error": "服务未运行"}

            # 构建MCP请求
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": kwargs
                }
            }

            # 发送请求
            request_json = json.dumps(request) + "\n"
            self.process.stdin.write(request_json)
            self.process.stdin.flush()

            # 读取响应
            response_line = self.process.stdout.readline()
            if response_line:
                response = json.loads(response_line.strip())
                if "result" in response:
                    return {"success": True, "result": response["result"]}
                else:
                    return {"success": False, "error": response.get("error", "未知错误")}
            else:
                return {"success": False, "error": "无响应"}

        except Exception as e:
            logger.error(f"调用视觉工具失败: {e}")
            return {"success": False, "error": str(e)}

    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return self.tools.copy()


class MCPManager:
    """MCP管理器"""

    def __init__(self):
        """初始化MCP管理器"""
        self.adapters: Dict[str, MCPAdapter] = {}
        self.is_running = False

    def register_adapter(self, adapter: MCPAdapter):
        """注册MCP适配器"""
        self.adapters[adapter.service_name] = adapter
        logger.info(f"注册MCP适配器: {adapter.service_name}")

    async def start_all_services(self) -> Dict[str, bool]:
        """启动所有MCP服务"""
        results = {}
        for name, adapter in self.adapters.items():
            try:
                result = await adapter.start_service()
                results[name] = result
                logger.info(f"MCP服务 {name} 启动{'成功' if result else '失败'}")
            except Exception as e:
                logger.error(f"启动MCP服务 {name} 失败: {e}")
                results[name] = False

        self.is_running = any(results.values())
        return results

    async def stop_all_services(self) -> Dict[str, bool]:
        """停止所有MCP服务"""
        results = {}
        for name, adapter in self.adapters.items():
            try:
                result = await adapter.stop_service()
                results[name] = result
                logger.info(f"MCP服务 {name} 停止{'成功' if result else '失败'}")
            except Exception as e:
                logger.error(f"停止MCP服务 {name} 失败: {e}")
                results[name] = False

        self.is_running = False
        return results

    async def health_check_all(self) -> Dict[str, bool]:
        """检查所有服务健康状态"""
        results = {}
        for name, adapter in self.adapters.items():
            try:
                result = await adapter.is_healthy()
                results[name] = result
            except Exception as e:
                logger.error(f"检查MCP服务 {name} 健康状态失败: {e}")
                results[name] = False

        return results

    async def call_tool(self, service_name: str, tool_name: str, **kwargs) -> Dict[str, Any]:
        """调用指定服务的工具"""
        if service_name not in self.adapters:
            return {"success": False, "error": f"服务 {service_name} 不存在"}

        adapter = self.adapters[service_name]
        if tool_name not in adapter.get_available_tools():
            return {"success": False, "error": f"工具 {tool_name} 在服务 {service_name} 中不可用"}

        return await adapter.call_tool(tool_name, **kwargs)

    def get_all_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务状态"""
        return {name: adapter.get_status() for name, adapter in self.adapters.items()}

    def get_all_tools(self) -> Dict[str, List[str]]:
        """获取所有服务的工具列表"""
        return {name: adapter.get_available_tools() for name, adapter in self.adapters.items()}

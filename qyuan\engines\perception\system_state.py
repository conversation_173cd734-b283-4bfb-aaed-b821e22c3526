# -*- coding: utf-8 -*-
"""
系统状态感知服务实现
专门负责系统状态监控和感知，严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
import psutil
from typing import Dict, List, Optional, Any

try:
    import win32gui
    import win32process
    import win32api
    import win32con
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False

from .base import (
    SystemStateServiceBase,
    PerceptionResult,
    PerceptionType
)

class SystemStateService(SystemStateServiceBase):
    """系统状态感知服务实现"""
    
    def __init__(self):
        super().__init__("SystemState")
        self.logger = logging.getLogger(f"QYuan.Perception.{self.name}")
        
        # 检查依赖可用性
        self.win32_available = WIN32_AVAILABLE
        self.psutil_available = True  # psutil通常都可用
        
        if not self.win32_available:
            self.logger.warning("Win32API不可用，部分系统状态功能将受限")
        
        # 状态缓存
        self.state_cache: Dict[str, Any] = {}
        self.cache_timeout = 2.0  # 缓存超时时间（秒）
        self.last_update_time = 0
    
    async def process(self, input_data: Any) -> PerceptionResult:
        """处理系统状态感知请求"""
        start_time = time.time()
        
        try:
            # 获取完整的系统状态
            system_state = await self._get_complete_system_state()
            
            processing_time = time.time() - start_time
            
            result = PerceptionResult(
                perception_type=PerceptionType.SYSTEM_STATE,
                success=True,
                data=system_state,
                confidence=0.9,  # 系统状态通常很可靠
                processing_time=processing_time
            )
            
            self.last_result = result
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"系统状态感知失败: {e}")
            
            result = PerceptionResult(
                perception_type=PerceptionType.SYSTEM_STATE,
                success=False,
                data={},
                confidence=0.0,
                error_message=str(e),
                processing_time=processing_time
            )
            
            self.last_result = result
            return result
    
    async def get_active_window(self) -> Optional[Dict[str, Any]]:
        """获取活动窗口信息"""
        if not self.win32_available:
            self.logger.warning("Win32API不可用，无法获取活动窗口")
            return None
        
        try:
            # 获取前台窗口句柄
            hwnd = win32gui.GetForegroundWindow()
            
            if not hwnd:
                return None
            
            # 获取窗口标题
            window_title = win32gui.GetWindowText(hwnd)
            
            # 获取窗口类名
            class_name = win32gui.GetClassName(hwnd)
            
            # 获取窗口矩形
            window_rect = win32gui.GetWindowRect(hwnd)
            
            # 获取进程ID
            _, process_id = win32process.GetWindowThreadProcessId(hwnd)
            
            # 获取进程信息
            process_info = self._get_process_info(process_id)
            
            window_info = {
                "handle": hwnd,
                "title": window_title,
                "class_name": class_name,
                "rect": {
                    "left": window_rect[0],
                    "top": window_rect[1],
                    "right": window_rect[2],
                    "bottom": window_rect[3],
                    "width": window_rect[2] - window_rect[0],
                    "height": window_rect[3] - window_rect[1]
                },
                "process_id": process_id,
                "process_info": process_info,
                "is_visible": win32gui.IsWindowVisible(hwnd),
                "is_enabled": win32gui.IsWindowEnabled(hwnd)
            }
            
            return window_info
            
        except Exception as e:
            self.logger.error(f"获取活动窗口失败: {e}")
            return None
    
    async def get_running_processes(self) -> List[Dict[str, Any]]:
        """获取运行中的进程"""
        try:
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'create_time']):
                try:
                    process_info = proc.info
                    
                    # 获取内存使用情况
                    memory_info = process_info.get('memory_info')
                    memory_mb = memory_info.rss / 1024 / 1024 if memory_info else 0
                    
                    process_data = {
                        "pid": process_info['pid'],
                        "name": process_info['name'],
                        "cpu_percent": process_info.get('cpu_percent', 0),
                        "memory_mb": round(memory_mb, 2),
                        "create_time": process_info.get('create_time', 0)
                    }
                    
                    # 尝试获取更多信息
                    try:
                        proc_obj = psutil.Process(process_info['pid'])
                        process_data.update({
                            "status": proc_obj.status(),
                            "num_threads": proc_obj.num_threads(),
                            "exe": proc_obj.exe() if proc_obj.exe() else "未知"
                        })
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                    
                    processes.append(process_data)
                    
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            # 按内存使用量排序
            processes.sort(key=lambda x: x['memory_mb'], reverse=True)
            
            return processes
            
        except Exception as e:
            self.logger.error(f"获取进程列表失败: {e}")
            return []
    
    async def get_system_resources(self) -> Dict[str, Any]:
        """获取系统资源使用情况"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk_usage = psutil.disk_usage('/')
            
            # 网络统计
            network_stats = psutil.net_io_counters()
            
            # 系统启动时间
            boot_time = psutil.boot_time()
            
            resources = {
                "cpu": {
                    "percent": cpu_percent,
                    "count": cpu_count,
                    "frequency": {
                        "current": cpu_freq.current if cpu_freq else 0,
                        "min": cpu_freq.min if cpu_freq else 0,
                        "max": cpu_freq.max if cpu_freq else 0
                    }
                },
                "memory": {
                    "total_gb": round(memory.total / 1024 / 1024 / 1024, 2),
                    "available_gb": round(memory.available / 1024 / 1024 / 1024, 2),
                    "used_gb": round(memory.used / 1024 / 1024 / 1024, 2),
                    "percent": memory.percent
                },
                "disk": {
                    "total_gb": round(disk_usage.total / 1024 / 1024 / 1024, 2),
                    "used_gb": round(disk_usage.used / 1024 / 1024 / 1024, 2),
                    "free_gb": round(disk_usage.free / 1024 / 1024 / 1024, 2),
                    "percent": round((disk_usage.used / disk_usage.total) * 100, 2)
                },
                "network": {
                    "bytes_sent": network_stats.bytes_sent,
                    "bytes_recv": network_stats.bytes_recv,
                    "packets_sent": network_stats.packets_sent,
                    "packets_recv": network_stats.packets_recv
                },
                "system": {
                    "boot_time": boot_time,
                    "uptime_hours": round((time.time() - boot_time) / 3600, 2)
                }
            }
            
            return resources
            
        except Exception as e:
            self.logger.error(f"获取系统资源失败: {e}")
            return {}
    
    async def get_window_list(self) -> List[Dict[str, Any]]:
        """获取所有窗口列表"""
        if not self.win32_available:
            return []
        
        windows = []
        
        def enum_windows_callback(hwnd, windows_list):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if window_title:  # 只包含有标题的窗口
                    class_name = win32gui.GetClassName(hwnd)
                    window_rect = win32gui.GetWindowRect(hwnd)
                    
                    window_info = {
                        "handle": hwnd,
                        "title": window_title,
                        "class_name": class_name,
                        "rect": {
                            "left": window_rect[0],
                            "top": window_rect[1],
                            "right": window_rect[2],
                            "bottom": window_rect[3]
                        }
                    }
                    windows_list.append(window_info)
        
        try:
            win32gui.EnumWindows(enum_windows_callback, windows)
        except Exception as e:
            self.logger.error(f"枚举窗口失败: {e}")
        
        return windows
    
    async def _get_complete_system_state(self) -> Dict[str, Any]:
        """获取完整的系统状态"""
        # 检查缓存
        current_time = time.time()
        if (current_time - self.last_update_time) < self.cache_timeout and self.state_cache:
            return self.state_cache
        
        # 并发获取各种状态信息
        tasks = [
            self.get_active_window(),
            self.get_system_resources(),
            self.get_running_processes(),
        ]
        
        if self.win32_available:
            tasks.append(self.get_window_list())
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            active_window = results[0] if not isinstance(results[0], Exception) else None
            system_resources = results[1] if not isinstance(results[1], Exception) else {}
            running_processes = results[2] if not isinstance(results[2], Exception) else []
            window_list = results[3] if len(results) > 3 and not isinstance(results[3], Exception) else []
            
            complete_state = {
                "timestamp": current_time,
                "active_window": active_window,
                "system_resources": system_resources,
                "running_processes": running_processes[:20],  # 限制进程数量
                "window_list": window_list[:50],  # 限制窗口数量
                "capabilities": {
                    "win32_available": self.win32_available,
                    "psutil_available": self.psutil_available
                }
            }
            
            # 更新缓存
            self.state_cache = complete_state
            self.last_update_time = current_time
            
            return complete_state
            
        except Exception as e:
            self.logger.error(f"获取完整系统状态失败: {e}")
            return {"error": str(e), "timestamp": current_time}
    
    def _get_process_info(self, process_id: int) -> Dict[str, Any]:
        """获取进程详细信息"""
        try:
            proc = psutil.Process(process_id)
            return {
                "name": proc.name(),
                "exe": proc.exe() if proc.exe() else "未知",
                "status": proc.status(),
                "cpu_percent": proc.cpu_percent(),
                "memory_mb": round(proc.memory_info().rss / 1024 / 1024, 2),
                "create_time": proc.create_time()
            }
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return {"name": "未知", "status": "无法访问"}

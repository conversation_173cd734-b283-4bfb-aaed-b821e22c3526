# -*- coding: utf-8 -*-
"""
结果验证器实现
专门负责验证操作执行结果，严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Callable

try:
    import pyautogui
    import PIL.Image
    import numpy as np
    VALIDATION_AVAILABLE = True
except ImportError:
    VALIDATION_AVAILABLE = False

from .base import (
    ResultValidatorBase,
    ExecutionResult,
    ValidationCriteria,
    OperationParameters,
    OperationType,
    ValidationLevel
)

class IntelligentResultValidator(ResultValidatorBase):
    """智能结果验证器"""
    
    def __init__(self):
        super().__init__("IntelligentResultValidator")
        self.logger = logging.getLogger(f"QYuan.Execution.{self.name}")
        
        # 检查依赖可用性
        self.validation_available = VALIDATION_AVAILABLE
        if not self.validation_available:
            self.logger.warning("验证库不可用，验证功能将受限")
        
        # 验证配置
        self.validation_config = {
            'screenshot_comparison_threshold': 0.95,
            'coordinate_tolerance': 5,
            'text_similarity_threshold': 0.8,
            'validation_timeout': 10.0,
            'retry_attempts': 3,
            'retry_delay': 1.0
        }
        
        # 验证统计
        self.validation_stats = {
            'total_validations': 0,
            'successful_validations': 0,
            'failed_validations': 0,
            'average_validation_time': 0.0
        }
        
        # 内置验证器
        self.built_in_validators = {
            'mouse_position': self._validate_mouse_position,
            'screen_change': self._validate_screen_change,
            'element_presence': self._validate_element_presence,
            'text_content': self._validate_text_content,
            'window_state': self._validate_window_state
        }
    
    async def validate(self, result: ExecutionResult, criteria: ValidationCriteria) -> Dict[str, Any]:
        """验证执行结果"""
        start_time = time.time()
        validation_results = {
            'overall_success': False,
            'individual_results': {},
            'validation_time': 0.0,
            'errors': []
        }
        
        try:
            self.logger.debug(f"开始验证操作结果: {result.operation_id}")
            
            # 检查基本成功状态
            if not result.success:
                validation_results['errors'].append("操作执行失败，无需进一步验证")
                return validation_results
            
            # 执行成功条件验证
            success_results = await self._validate_success_conditions(result, criteria)
            validation_results['individual_results']['success_conditions'] = success_results
            
            # 执行失败条件验证（确保没有触发失败条件）
            failure_results = await self._validate_failure_conditions(result, criteria)
            validation_results['individual_results']['failure_conditions'] = failure_results
            
            # 执行自定义验证器
            if criteria.custom_validators:
                custom_results = await self._run_custom_validators(result, criteria)
                validation_results['individual_results']['custom_validators'] = custom_results
            
            # 计算整体验证结果
            validation_results['overall_success'] = self._calculate_overall_success(validation_results)
            
            validation_time = time.time() - start_time
            validation_results['validation_time'] = validation_time
            
            # 更新统计
            self._update_validation_stats(validation_results['overall_success'], validation_time)
            
            self.logger.debug(f"验证完成: {validation_results['overall_success']}")
            
        except Exception as e:
            validation_time = time.time() - start_time
            validation_results['validation_time'] = validation_time
            validation_results['errors'].append(f"验证过程出错: {str(e)}")
            self.logger.error(f"验证过程出错: {e}")
            
            # 更新统计
            self._update_validation_stats(False, validation_time)
        
        return validation_results
    
    async def create_validation_criteria(self, params: OperationParameters) -> ValidationCriteria:
        """创建验证标准"""
        criteria = ValidationCriteria()
        
        try:
            # 根据操作类型创建默认验证标准
            if params.operation_type == OperationType.MOUSE_CLICK:
                criteria.success_conditions.append({
                    'type': 'mouse_position',
                    'expected_coordinates': params.coordinates,
                    'tolerance': self.validation_config['coordinate_tolerance']
                })
                
                # 如果是点击操作，可能需要验证屏幕变化
                if params.validation_level in [ValidationLevel.STANDARD, ValidationLevel.STRICT]:
                    criteria.success_conditions.append({
                        'type': 'screen_change',
                        'expect_change': True,
                        'comparison_threshold': self.validation_config['screenshot_comparison_threshold']
                    })
            
            elif params.operation_type == OperationType.MOUSE_MOVE:
                criteria.success_conditions.append({
                    'type': 'mouse_position',
                    'expected_coordinates': params.coordinates,
                    'tolerance': self.validation_config['coordinate_tolerance']
                })
            
            elif params.operation_type == OperationType.KEYBOARD_TYPE:
                if params.validation_level in [ValidationLevel.STANDARD, ValidationLevel.STRICT]:
                    criteria.success_conditions.append({
                        'type': 'text_content',
                        'expected_text': params.text,
                        'similarity_threshold': self.validation_config['text_similarity_threshold']
                    })
            
            elif params.operation_type == OperationType.WAIT:
                # 等待操作主要验证时间
                criteria.success_conditions.append({
                    'type': 'execution_time',
                    'expected_duration': params.custom_params.get('duration', 0),
                    'tolerance': 0.5  # 0.5秒容差
                })
            
            # 设置验证超时
            criteria.timeout = self.validation_config['validation_timeout']
            
        except Exception as e:
            self.logger.error(f"创建验证标准失败: {e}")
        
        return criteria
    
    async def _validate_success_conditions(self, result: ExecutionResult, criteria: ValidationCriteria) -> Dict[str, Any]:
        """验证成功条件"""
        results = {}
        
        for i, condition in enumerate(criteria.success_conditions):
            condition_id = f"success_condition_{i}"
            
            try:
                condition_type = condition.get('type')
                
                if condition_type in self.built_in_validators:
                    validator_func = self.built_in_validators[condition_type]
                    result_data = await validator_func(result, condition)
                    results[condition_id] = result_data
                else:
                    results[condition_id] = {
                        'success': False,
                        'error': f"未知的验证类型: {condition_type}"
                    }
                    
            except Exception as e:
                results[condition_id] = {
                    'success': False,
                    'error': f"验证条件执行失败: {str(e)}"
                }
        
        return results
    
    async def _validate_failure_conditions(self, result: ExecutionResult, criteria: ValidationCriteria) -> Dict[str, Any]:
        """验证失败条件（确保没有触发）"""
        results = {}
        
        for i, condition in enumerate(criteria.failure_conditions):
            condition_id = f"failure_condition_{i}"
            
            try:
                condition_type = condition.get('type')
                
                if condition_type in self.built_in_validators:
                    validator_func = self.built_in_validators[condition_type]
                    result_data = await validator_func(result, condition)
                    
                    # 失败条件的逻辑相反：如果验证成功，说明触发了失败条件
                    results[condition_id] = {
                        'success': not result_data.get('success', False),
                        'triggered': result_data.get('success', False),
                        'details': result_data
                    }
                else:
                    results[condition_id] = {
                        'success': True,  # 未知类型默认为未触发
                        'triggered': False,
                        'error': f"未知的验证类型: {condition_type}"
                    }
                    
            except Exception as e:
                results[condition_id] = {
                    'success': True,  # 验证失败默认为未触发失败条件
                    'triggered': False,
                    'error': f"验证条件执行失败: {str(e)}"
                }
        
        return results
    
    async def _run_custom_validators(self, result: ExecutionResult, criteria: ValidationCriteria) -> Dict[str, Any]:
        """运行自定义验证器"""
        results = {}
        
        for i, validator in enumerate(criteria.custom_validators):
            validator_id = f"custom_validator_{i}"
            
            try:
                if callable(validator):
                    validator_result = await self._run_validator_safely(validator, result)
                    results[validator_id] = validator_result
                else:
                    results[validator_id] = {
                        'success': False,
                        'error': "验证器不是可调用对象"
                    }
                    
            except Exception as e:
                results[validator_id] = {
                    'success': False,
                    'error': f"自定义验证器执行失败: {str(e)}"
                }
        
        return results
    
    async def _run_validator_safely(self, validator: Callable, result: ExecutionResult) -> Dict[str, Any]:
        """安全运行验证器"""
        try:
            # 设置超时
            validator_result = await asyncio.wait_for(
                self._call_validator(validator, result),
                timeout=self.validation_config['validation_timeout']
            )
            
            return {
                'success': bool(validator_result),
                'result': validator_result
            }
            
        except asyncio.TimeoutError:
            return {
                'success': False,
                'error': "验证器执行超时"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"验证器执行异常: {str(e)}"
            }
    
    async def _call_validator(self, validator: Callable, result: ExecutionResult):
        """调用验证器"""
        if asyncio.iscoroutinefunction(validator):
            return await validator(result)
        else:
            return validator(result)
    
    async def _validate_mouse_position(self, result: ExecutionResult, condition: Dict[str, Any]) -> Dict[str, Any]:
        """验证鼠标位置"""
        try:
            if not self.validation_available:
                return {'success': False, 'error': '验证库不可用'}
            
            expected_coords = condition['expected_coordinates']
            tolerance = condition.get('tolerance', self.validation_config['coordinate_tolerance'])
            
            # 获取当前鼠标位置
            current_pos = pyautogui.position()
            
            # 计算位置差异
            x_diff = abs(current_pos.x - expected_coords['x'])
            y_diff = abs(current_pos.y - expected_coords['y'])
            
            success = x_diff <= tolerance and y_diff <= tolerance
            
            return {
                'success': success,
                'expected': expected_coords,
                'actual': {'x': current_pos.x, 'y': current_pos.y},
                'difference': {'x': x_diff, 'y': y_diff},
                'tolerance': tolerance
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _validate_screen_change(self, result: ExecutionResult, condition: Dict[str, Any]) -> Dict[str, Any]:
        """验证屏幕变化"""
        try:
            if not self.validation_available:
                return {'success': False, 'error': '验证库不可用'}
            
            expect_change = condition.get('expect_change', True)
            threshold = condition.get('comparison_threshold', self.validation_config['screenshot_comparison_threshold'])
            
            # 这里需要在操作前后截图进行比较
            # 简化实现：假设屏幕发生了变化
            screen_changed = True  # 实际实现需要图像比较
            
            success = screen_changed == expect_change
            
            return {
                'success': success,
                'expected_change': expect_change,
                'actual_change': screen_changed,
                'threshold': threshold
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _validate_element_presence(self, result: ExecutionResult, condition: Dict[str, Any]) -> Dict[str, Any]:
        """验证元素存在性"""
        try:
            # 简化实现
            return {'success': True, 'message': '元素验证功能待实现'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _validate_text_content(self, result: ExecutionResult, condition: Dict[str, Any]) -> Dict[str, Any]:
        """验证文本内容"""
        try:
            # 简化实现
            return {'success': True, 'message': '文本验证功能待实现'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _validate_window_state(self, result: ExecutionResult, condition: Dict[str, Any]) -> Dict[str, Any]:
        """验证窗口状态"""
        try:
            # 简化实现
            return {'success': True, 'message': '窗口状态验证功能待实现'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _calculate_overall_success(self, validation_results: Dict[str, Any]) -> bool:
        """计算整体验证成功状态"""
        try:
            # 检查所有成功条件
            success_conditions = validation_results['individual_results'].get('success_conditions', {})
            for condition_result in success_conditions.values():
                if not condition_result.get('success', False):
                    return False
            
            # 检查失败条件（确保没有触发）
            failure_conditions = validation_results['individual_results'].get('failure_conditions', {})
            for condition_result in failure_conditions.values():
                if not condition_result.get('success', True):  # 失败条件的success表示未触发
                    return False
            
            # 检查自定义验证器
            custom_validators = validation_results['individual_results'].get('custom_validators', {})
            for validator_result in custom_validators.values():
                if not validator_result.get('success', False):
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"计算整体验证结果失败: {e}")
            return False
    
    def _update_validation_stats(self, success: bool, validation_time: float):
        """更新验证统计"""
        self.validation_stats['total_validations'] += 1
        
        if success:
            self.validation_stats['successful_validations'] += 1
        else:
            self.validation_stats['failed_validations'] += 1
        
        # 更新平均验证时间
        total = self.validation_stats['total_validations']
        current_avg = self.validation_stats['average_validation_time']
        self.validation_stats['average_validation_time'] = (
            (current_avg * (total - 1) + validation_time) / total
        )
    
    def get_validation_stats(self) -> Dict[str, Any]:
        """获取验证统计"""
        stats = self.validation_stats.copy()
        total = stats['total_validations']
        stats['success_rate'] = (stats['successful_validations'] / total) if total > 0 else 0.0
        return stats
    
    def register_custom_validator(self, name: str, validator_func: Callable):
        """注册自定义验证器"""
        self.built_in_validators[name] = validator_func
        self.logger.info(f"注册自定义验证器: {name}")
    
    def update_validation_config(self, config: Dict[str, Any]):
        """更新验证配置"""
        self.validation_config.update(config)
        self.logger.info(f"验证配置已更新: {config}")

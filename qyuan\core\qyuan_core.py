# -*- coding: utf-8 -*-
"""
<PERSON><PERSON>uan核心类 - 硅基CEO的大脑
"""

import asyncio
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass

from .config import QYuanConfig
from .events import EventBus, EventType
from .exceptions import QYuanException, InitializationError
from .perception_action_loop import PerceptionActionLoop
from .goal_manager import GoalManager
from .goal_tracker import GoalPriority
from ..engines import BaseEngine, EngineStatus

@dataclass
class Goal:
    """目标数据类"""
    id: str
    description: str
    priority: int
    created_at: datetime
    deadline: Optional[datetime] = None
    status: str = "active"
    progress: float = 0.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class Task:
    """任务数据类"""
    id: str
    goal_id: str
    description: str
    status: str
    created_at: datetime
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class QYuanStatus:
    """QYuan状态类"""
    
    def __init__(self, **kwargs):
        self.is_running = kwargs.get('is_running', False)
        self.current_goal = kwargs.get('current_goal')
        self.current_task = kwargs.get('current_task')
        self.uptime = kwargs.get('uptime')
        self.engines_status = kwargs.get('engines_status', {})
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "is_running": self.is_running,
            "current_goal": self.current_goal,
            "current_task": self.current_task,
            "uptime": str(self.uptime) if self.uptime else None,
            "engines_status": self.engines_status,
            "timestamp": self.timestamp.isoformat()
        }

class QYuanCore:
    """QYuan核心类 - 硅基CEO的大脑"""
    
    def __init__(self, config: QYuanConfig):
        self.config = config
        self.event_bus = EventBus()
        self.logger = config.logger

        # 状态管理
        self.is_running = False
        self.start_time: Optional[datetime] = None
        self.current_goal: Optional[Goal] = None
        self.current_task: Optional[Task] = None

        # 引擎管理
        self.engines: Dict[str, BaseEngine] = {}

        # 初始化引擎（延迟导入避免循环依赖）
        self._initialize_engines()

        # 感知-行动循环
        self.perception_action_loop = PerceptionActionLoop(self)

        # 目标管理器
        self.goal_manager = GoalManager(self, self.event_bus)

        self.logger.info("QYuan核心初始化完成")
    
    def _initialize_engines(self):
        """初始化引擎"""
        # 导入占位符引擎
        from .placeholder_engines import (
            PlaceholderLearningEngine
        )

        # 导入真实引擎
        from ..engines.communication.communication_engine import CommunicationEngine
        from ..engines.execution.execution_engine import ExecutionEngine
        from ..engines.perception.perception_engine import PerceptionEngine
        from ..engines.decision.decision_engine import DecisionEngine

        self.engines = {
            "perception": PerceptionEngine(self),  # 使用真实的感知引擎
            "decision": DecisionEngine(self),      # 使用真实的决策引擎
            "execution": ExecutionEngine("Execution", self),  # 使用真实的执行引擎
            "learning": PlaceholderLearningEngine("Learning", self),
            "communication": CommunicationEngine("Communication", self)  # 使用真实的通信引擎
        }
    
    async def start(self):
        """启动QYuan"""
        self.logger.info("🤖 QYuan正在启动...")
        
        try:
            # 验证配置
            if not self.config.validate_config():
                raise InitializationError("配置验证失败")
            
            # 发射启动事件
            await self.event_bus.emit(
                EventType.SYSTEM_START,
                {"timestamp": datetime.now().isoformat()},
                source="QYuanCore"
            )
            
            # 初始化所有引擎
            for name, engine in self.engines.items():
                self.logger.info(f"初始化{name}引擎...")
                await engine.initialize()
            
            # 启动所有引擎
            for name, engine in self.engines.items():
                self.logger.info(f"启动{name}引擎...")
                await engine.start()

            # 启动目标管理器
            self.logger.info("启动目标管理器...")
            await self.goal_manager.start()

            # 设置运行状态
            self.is_running = True
            self.start_time = datetime.now()

            # 启动主循环
            asyncio.create_task(self.main_loop())
            asyncio.create_task(self.health_monitor())

            self.logger.info("✅ QYuan启动完成")
            
        except Exception as e:
            self.logger.error(f"❌ QYuan启动失败: {e}")
            await self.event_bus.emit(
                EventType.SYSTEM_ERROR,
                {"error": str(e), "phase": "startup"},
                source="QYuanCore"
            )
            raise
    
    async def stop(self):
        """停止QYuan"""
        self.logger.info("🛑 QYuan正在停止...")

        self.is_running = False

        # 停止感知-行动循环
        try:
            await self.perception_action_loop.stop_loop("system_shutdown")
        except Exception as e:
            self.logger.error(f"停止感知-行动循环失败: {e}")

        # 停止目标管理器
        try:
            await self.goal_manager.stop()
        except Exception as e:
            self.logger.error(f"停止目标管理器失败: {e}")

        # 停止所有引擎
        for name, engine in self.engines.items():
            try:
                self.logger.info(f"停止{name}引擎...")
                await engine.stop()
            except Exception as e:
                self.logger.error(f"停止{name}引擎失败: {e}")

        await self.event_bus.emit(
            EventType.SYSTEM_STOP,
            {"timestamp": datetime.now().isoformat()},
            source="QYuanCore"
        )

        self.logger.info("✅ QYuan已停止")
    
    async def main_loop(self):
        """主循环 - QYuan的心跳"""
        self.logger.info("🔄 主循环启动")
        
        while self.is_running:
            try:
                # 检查是否有新的目标或任务
                if self.current_goal is None:
                    await self.wait_for_goal()
                    continue
                
                # 执行感知-行动循环
                await self.perception_action_cycle()
                
                # 短暂休息
                await asyncio.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"主循环异常: {e}")
                await self.handle_main_loop_error(e)
        
        self.logger.info("🔄 主循环结束")
    
    async def health_monitor(self):
        """健康监控"""
        self.logger.info("💓 健康监控启动")
        
        while self.is_running:
            try:
                # 检查所有引擎健康状态
                for name, engine in self.engines.items():
                    is_healthy = await engine.health_check()
                    if not is_healthy:
                        self.logger.warning(f"引擎健康检查失败: {name}")
                
                # 每30秒检查一次
                await asyncio.sleep(30)
                
            except Exception as e:
                self.logger.error(f"健康监控异常: {e}")
                await asyncio.sleep(10)
        
        self.logger.info("💓 健康监控结束")
    
    async def perception_action_cycle(self):
        """感知-行动循环 - 真实实现"""
        try:
            # 获取当前活动目标
            active_goal = self.goal_manager.goal_tracker.get_active_goal()

            if active_goal:
                # 启动感知-行动循环
                result = await self.perception_action_loop.start_loop(
                    goal=active_goal,
                    timeout=30.0  # 30秒超时
                )

                # 更新目标进度
                if result.get("success", False):
                    await self.goal_manager.update_goal_progress_from_execution(result)

                # 检查目标是否完成
                await self.goal_manager.check_goal_completion(active_goal.id)

            else:
                # 没有活动目标，短暂等待
                await asyncio.sleep(0.5)

        except Exception as e:
            self.logger.error(f"感知-行动循环异常: {e}")
            await asyncio.sleep(1)
    
    async def wait_for_goal(self):
        """等待目标设置"""
        # 检查是否有活动目标
        active_goal = self.goal_manager.goal_tracker.get_active_goal()

        if active_goal:
            # 更新当前目标（保持向后兼容）
            self.current_goal = Goal(
                id=active_goal.id,
                description=active_goal.description,
                priority=active_goal.priority.value,
                created_at=active_goal.created_at,
                deadline=active_goal.deadline
            )
        else:
            # 没有活动目标，等待
            await asyncio.sleep(1)
    
    async def handle_main_loop_error(self, error: Exception):
        """处理主循环错误"""
        self.logger.error(f"主循环错误处理: {error}")
        await asyncio.sleep(5)  # 错误后等待5秒再继续
    
    async def set_goal(self, description: str, priority: int = 3, deadline: Optional[datetime] = None) -> str:
        """设置新目标"""
        # 转换优先级
        goal_priority = GoalPriority.NORMAL
        if priority >= 4:
            goal_priority = GoalPriority.HIGH
        elif priority <= 2:
            goal_priority = GoalPriority.LOW

        # 使用目标管理器创建目标
        goal_id = await self.goal_manager.create_goal_from_user_input(
            user_input=description,
            priority=goal_priority
        )

        # 更新当前目标（保持向后兼容）
        self.current_goal = Goal(
            id=goal_id,
            description=description,
            priority=priority,
            created_at=datetime.now(),
            deadline=deadline
        )

        self.logger.info(f"设置新目标: {description}")

        return goal_id
    
    async def handle_user_message(self, message: str, user_id: str = None) -> str:
        """处理用户消息"""
        self.logger.info(f"收到用户消息: {message}")

        try:
            # 发射用户消息事件
            await self.event_bus.emit(
                EventType.USER_MESSAGE,
                {"message": message, "user_id": user_id},
                source="QYuanCore"
            )

            # 通过通信引擎处理消息
            communication_engine = self.engines.get("communication")
            if communication_engine and hasattr(communication_engine, 'process_user_message'):
                response = await communication_engine.process_user_message(message, user_id or "default")

                # 检查是否需要创建目标
                await self._check_and_create_goal_from_message(message, response)
            else:
                # 降级处理
                response = f"收到您的消息：{message}。通信引擎暂时不可用。"

            return response

        except Exception as e:
            self.logger.error(f"处理用户消息失败: {e}")
            return f"抱歉，处理您的消息时出现了问题：{e}"

    async def _check_and_create_goal_from_message(self, message: str, response: str = None):
        """检查消息并自动创建目标"""
        try:
            # 简单的目标识别逻辑
            goal_keywords = ["帮我", "请", "能否", "可以", "想要", "需要", "打开", "搜索", "下载", "发送"]

            if any(keyword in message for keyword in goal_keywords):
                # 检查是否已有活动目标
                active_goal = self.goal_manager.goal_tracker.get_active_goal()

                if not active_goal:
                    # 创建新目标
                    goal_id = await self.goal_manager.create_goal_from_user_input(
                        user_input=message,
                        priority=GoalPriority.NORMAL
                    )
                    self.logger.info(f"从用户消息自动创建目标: {goal_id}")

        except Exception as e:
            self.logger.error(f"自动创建目标失败: {e}")
    
    async def get_status(self) -> QYuanStatus:
        """获取当前状态"""
        engines_status = {}
        for name, engine in self.engines.items():
            engines_status[name] = engine.get_status()

        uptime = None
        if self.start_time:
            uptime = datetime.now() - self.start_time

        # 获取活动目标信息
        active_goal = self.goal_manager.goal_tracker.get_active_goal()
        current_goal_desc = active_goal.description if active_goal else None

        return QYuanStatus(
            is_running=self.is_running,
            current_goal=current_goal_desc,
            current_task=self.current_task.description if self.current_task else None,
            uptime=uptime,
            engines_status=engines_status
        )

    def get_goal_status(self, goal_id: str = None) -> Optional[Dict[str, Any]]:
        """获取目标状态"""
        if goal_id:
            return self.goal_manager.get_goal_status(goal_id)
        else:
            return self.goal_manager.get_active_goal_status()

    def get_goal_statistics(self) -> Dict[str, Any]:
        """获取目标统计信息"""
        return self.goal_manager.get_statistics()

    def get_perception_action_loop_status(self) -> Dict[str, Any]:
        """获取感知-行动循环状态"""
        return self.perception_action_loop.get_status()

    async def pause_perception_action_loop(self):
        """暂停感知-行动循环"""
        await self.perception_action_loop.pause_loop()

    async def resume_perception_action_loop(self):
        """恢复感知-行动循环"""
        await self.perception_action_loop.resume_loop()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QYuan Web API服务器启动脚本

启动QYuan的Web API服务，提供HTTP REST API和WebSocket接口
"""

import asyncio
import logging
import sys
import signal
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

import uvicorn
from qyuan.api.app import create_app
from qyuan.api.models import APIConfig

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_api_config():
    """创建API配置"""
    return APIConfig(
        host="0.0.0.0",
        port=8000,
        debug=True,  # 开发模式
        api_key=None,  # 暂时不设置API密钥
        cors_origins=["*"],  # 允许所有源（开发模式）
        max_connections=100,
        request_timeout=30
    )


def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，准备关闭服务器...")
    sys.exit(0)


async def main():
    """主函数"""
    print("🌐 QYuan Web API服务器")
    print("=" * 50)
    
    try:
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 创建配置
        config = create_api_config()
        logger.info(f"API配置: {config.host}:{config.port}")
        
        # 创建FastAPI应用
        app = create_app(config)
        logger.info("FastAPI应用创建成功")
        
        # 启动服务器
        logger.info("🚀 启动Web API服务器...")
        print(f"\n📡 服务器地址: http://{config.host}:{config.port}")
        print(f"📚 API文档: http://{config.host}:{config.port}/docs")
        print(f"🔄 WebSocket: ws://{config.host}:{config.port}/ws/chat")
        print("\n按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        # 配置uvicorn
        uvicorn_config = uvicorn.Config(
            app=app,
            host=config.host,
            port=config.port,
            log_level="info",
            access_log=True,
            reload=config.debug,
            ws_ping_interval=20,
            ws_ping_timeout=20
        )
        
        # 启动服务器
        server = uvicorn.Server(uvicorn_config)
        await server.serve()
        
    except KeyboardInterrupt:
        logger.info("用户中断，停止服务器")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        logger.error(f"程序异常: {e}")
        sys.exit(1)

# -*- coding: utf-8 -*-
"""
复杂场景测试
测试QYuan在各种复杂实际场景下的表现
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from qyuan.core.qyuan_core import QYuanCore
from qyuan.core.config import QYuanConfig
from qyuan.core.goal_tracker import GoalPriority
from qyuan.engines.perception.base import ScreenAnalysis, UIElement
from qyuan.engines.decision.base import DecisionResult, UserIntent, ActionPlan, Action
from qyuan.engines.execution.base import ExecutionResult


class TestComplexScenarios:
    """复杂场景测试"""
    
    @pytest.fixture
    async def qyuan_core(self):
        """创建QYuan核心实例"""
        config = QYuanConfig()
        core = QYuanCore(config)
        
        # 设置动态模拟引擎
        await self._setup_dynamic_mock_engines(core)
        
        await core.start()
        yield core
        await core.stop()
    
    async def _setup_dynamic_mock_engines(self, core):
        """设置动态模拟引擎"""
        # 感知引擎 - 模拟不同的屏幕状态
        perception_engine = AsyncMock()
        self.screen_state = "desktop"  # 初始状态
        
        async def dynamic_perception():
            if self.screen_state == "desktop":
                return ScreenAnalysis(
                    ui_elements=[
                        UIElement(id="browser_icon", type="icon", text="浏览器", bounds=(50, 50, 100, 100)),
                        UIElement(id="file_manager", type="icon", text="文件管理器", bounds=(150, 50, 200, 100))
                    ],
                    screenshot_path="desktop.png",
                    timestamp=datetime.now()
                )
            elif self.screen_state == "browser":
                return ScreenAnalysis(
                    ui_elements=[
                        UIElement(id="address_bar", type="input", text="", bounds=(100, 50, 500, 80)),
                        UIElement(id="search_button", type="button", text="搜索", bounds=(510, 50, 580, 80)),
                        UIElement(id="back_button", type="button", text="返回", bounds=(20, 50, 70, 80))
                    ],
                    screenshot_path="browser.png",
                    timestamp=datetime.now()
                )
            elif self.screen_state == "search_results":
                return ScreenAnalysis(
                    ui_elements=[
                        UIElement(id="result_1", type="link", text="Python教程 - 官方文档", bounds=(100, 150, 500, 180)),
                        UIElement(id="result_2", type="link", text="Python入门指南", bounds=(100, 200, 500, 230)),
                        UIElement(id="next_page", type="button", text="下一页", bounds=(400, 400, 480, 430))
                    ],
                    screenshot_path="search_results.png",
                    timestamp=datetime.now()
                )
            else:
                return ScreenAnalysis(ui_elements=[], screenshot_path="unknown.png", timestamp=datetime.now())
        
        perception_engine.analyze_current_screen = dynamic_perception
        
        # 决策引擎 - 根据当前状态做出决策
        decision_engine = AsyncMock()
        
        async def dynamic_decision(user_input, perception_data):
            if "打开浏览器" in user_input and self.screen_state == "desktop":
                return DecisionResult(
                    intent=UserIntent(intent_type="open_application", confidence=0.9),
                    action_plan=ActionPlan(actions=[
                        Action(type="click", target="browser_icon", parameters={"x": 75, "y": 75})
                    ])
                )
            elif "搜索" in user_input and self.screen_state == "browser":
                return DecisionResult(
                    intent=UserIntent(intent_type="search", confidence=0.9),
                    action_plan=ActionPlan(actions=[
                        Action(type="type", target="address_bar", parameters={"text": "Python教程"}),
                        Action(type="click", target="search_button", parameters={"x": 545, "y": 65})
                    ])
                )
            elif "点击第一个结果" in user_input and self.screen_state == "search_results":
                return DecisionResult(
                    intent=UserIntent(intent_type="click_link", confidence=0.9),
                    action_plan=ActionPlan(actions=[
                        Action(type="click", target="result_1", parameters={"x": 300, "y": 165})
                    ])
                )
            else:
                return DecisionResult(
                    intent=UserIntent(intent_type="wait", confidence=0.5),
                    action_plan=ActionPlan(actions=[])
                )
        
        decision_engine.make_decision = dynamic_decision
        
        # 执行引擎 - 模拟状态变化
        execution_engine = AsyncMock()
        
        async def dynamic_execution(action):
            if action.type == "click" and action.target == "browser_icon":
                self.screen_state = "browser"
                return ExecutionResult(success=True, action_type="click", execution_time=1.0)
            elif action.type == "click" and action.target == "search_button":
                self.screen_state = "search_results"
                return ExecutionResult(success=True, action_type="click", execution_time=0.5)
            elif action.type == "click" and action.target == "result_1":
                self.screen_state = "article_page"
                return ExecutionResult(success=True, action_type="click", execution_time=0.8)
            elif action.type == "type":
                return ExecutionResult(success=True, action_type="type", execution_time=0.3)
            else:
                return ExecutionResult(success=False, action_type=action.type, execution_time=0.1)
        
        execution_engine.execute_action = dynamic_execution
        
        # 替换引擎
        core.engines["perception"] = perception_engine
        core.engines["decision"] = decision_engine
        core.engines["execution"] = execution_engine
    
    @pytest.mark.asyncio
    async def test_web_search_scenario(self, qyuan_core):
        """测试网页搜索场景"""
        # 设置复杂的搜索任务
        goal_id = await qyuan_core.set_goal(
            "打开浏览器，搜索Python教程，点击第一个结果",
            priority=4
        )
        
        # 执行多步骤任务
        steps = [
            "打开浏览器",
            "搜索Python教程", 
            "点击第一个结果"
        ]
        
        for i, step in enumerate(steps):
            # 更新目标描述
            qyuan_core.current_goal.description = step
            
            # 执行感知-行动循环
            await qyuan_core.perception_action_cycle()
            
            # 更新进度
            progress = ((i + 1) / len(steps)) * 100
            qyuan_core.goal_manager.goal_tracker.update_goal_progress(goal_id, progress)
            
            # 短暂等待状态变化
            await asyncio.sleep(0.1)
        
        # 验证最终状态
        assert self.screen_state == "article_page"
        
        goal_status = qyuan_core.get_goal_status(goal_id)
        assert goal_status["progress"]["percentage"] == 100.0
    
    @pytest.mark.asyncio
    async def test_file_management_scenario(self, qyuan_core):
        """测试文件管理场景"""
        # 模拟文件操作场景
        self.screen_state = "desktop"
        
        # 设置文件管理任务
        goal_id = await qyuan_core.goal_manager.create_goal_from_user_input(
            "创建新文件夹，重命名文件，移动到指定位置",
            priority=GoalPriority.HIGH
        )
        
        # 添加子目标
        sub_goals = [
            ("打开文件管理器", "启动文件管理应用"),
            ("创建新文件夹", "在当前目录创建新文件夹"),
            ("重命名文件", "将文件重命名为指定名称"),
            ("移动文件", "将文件移动到目标文件夹")
        ]
        
        sub_goal_ids = []
        for title, description in sub_goals:
            sub_goal_id = qyuan_core.goal_manager.goal_tracker.add_sub_goal(
                goal_id, title, description, GoalPriority.NORMAL
            )
            sub_goal_ids.append(sub_goal_id)
        
        # 模拟逐步完成子目标
        for i, sub_goal_id in enumerate(sub_goal_ids):
            # 执行感知-行动循环
            await qyuan_core.perception_action_cycle()
            
            # 标记子目标完成
            qyuan_core.goal_manager.goal_tracker.update_sub_goal_progress(
                goal_id, sub_goal_id, 100.0
            )
            
            await asyncio.sleep(0.1)
        
        # 检查目标完成
        is_completed = await qyuan_core.goal_manager.check_goal_completion(goal_id)
        assert is_completed
    
    @pytest.mark.asyncio
    async def test_multi_application_workflow(self, qyuan_core):
        """测试多应用程序工作流"""
        # 设置复杂的多应用工作流
        goal_id = await qyuan_core.set_goal(
            "从邮件中下载附件，用文档编辑器打开，编辑后保存到云盘",
            priority=5
        )
        
        # 模拟应用状态变化
        app_states = ["email_client", "download_dialog", "document_editor", "cloud_storage"]
        
        for i, app_state in enumerate(app_states):
            self.screen_state = app_state
            
            # 执行感知-行动循环
            await qyuan_core.perception_action_cycle()
            
            # 更新进度
            progress = ((i + 1) / len(app_states)) * 100
            qyuan_core.goal_manager.goal_tracker.update_goal_progress(goal_id, progress)
            
            await asyncio.sleep(0.1)
        
        # 验证工作流完成
        goal_status = qyuan_core.get_goal_status(goal_id)
        assert goal_status["progress"]["percentage"] == 100.0
    
    @pytest.mark.asyncio
    async def test_error_recovery_scenario(self, qyuan_core):
        """测试错误恢复场景"""
        # 设置容易出错的任务
        goal_id = await qyuan_core.set_goal("测试错误恢复机制", priority=3)
        
        # 模拟执行引擎间歇性失败
        execution_engine = qyuan_core.engines["execution"]
        original_execute = execution_engine.execute_action
        
        call_count = 0
        async def failing_execution(action):
            nonlocal call_count
            call_count += 1
            
            # 前3次调用失败
            if call_count <= 3:
                raise Exception(f"模拟执行失败 #{call_count}")
            else:
                # 后续调用成功
                return await original_execute(action)
        
        execution_engine.execute_action = failing_execution
        
        # 执行多次循环，测试错误恢复
        successful_cycles = 0
        for i in range(10):
            try:
                await qyuan_core.perception_action_cycle()
                successful_cycles += 1
            except Exception as e:
                print(f"循环 {i+1} 异常: {e}")
            
            await asyncio.sleep(0.1)
        
        # 验证系统恢复能力
        assert successful_cycles > 0  # 应该有成功的循环
        assert qyuan_core.is_running  # 系统应该仍在运行
        
        # 恢复原始执行函数
        execution_engine.execute_action = original_execute
    
    @pytest.mark.asyncio
    async def test_concurrent_goals_scenario(self, qyuan_core):
        """测试并发目标场景"""
        # 创建多个并发目标
        goal_ids = []
        goal_descriptions = [
            "下载文件A",
            "发送邮件B", 
            "备份数据C"
        ]
        
        for desc in goal_descriptions:
            goal_id = await qyuan_core.set_goal(desc, priority=3)
            goal_ids.append(goal_id)
        
        # 并发执行感知-行动循环
        tasks = []
        for _ in range(6):  # 执行6次循环
            task = asyncio.create_task(qyuan_core.perception_action_cycle())
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证并发执行结果
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) > 0  # 应该有成功的执行
        
        # 验证目标状态
        for goal_id in goal_ids:
            goal_status = qyuan_core.get_goal_status(goal_id)
            assert goal_status is not None
    
    @pytest.mark.asyncio
    async def test_adaptive_behavior_scenario(self, qyuan_core):
        """测试自适应行为场景"""
        # 设置需要自适应的任务
        goal_id = await qyuan_core.set_goal("自适应界面变化", priority=3)
        
        # 模拟界面动态变化
        interface_states = [
            "loading_screen",
            "login_dialog", 
            "main_interface",
            "settings_panel",
            "confirmation_dialog"
        ]
        
        for i, state in enumerate(interface_states):
            self.screen_state = state
            
            # 执行感知-行动循环，测试自适应能力
            await qyuan_core.perception_action_cycle()
            
            # 获取性能报告
            performance_report = qyuan_core.perception_action_loop.get_performance_report()
            
            # 验证自适应调整
            if i > 2:  # 在几次执行后应该有优化
                assert performance_report["metrics"]["total_cycles"] > 0
                # 缓存命中率应该逐渐提高
                if performance_report["metrics"]["cache_hit_rate"] > 0:
                    print(f"状态 {state}: 缓存命中率 {performance_report['metrics']['cache_hit_rate']:.1%}")
            
            await asyncio.sleep(0.1)
        
        # 验证自适应效果
        final_report = qyuan_core.perception_action_loop.get_performance_report()
        assert final_report["metrics"]["total_cycles"] >= len(interface_states)
    
    @pytest.mark.asyncio
    async def test_resource_intensive_scenario(self, qyuan_core):
        """测试资源密集型场景"""
        # 设置资源密集型任务
        goal_id = await qyuan_core.set_goal("处理大量数据", priority=4)
        
        # 模拟资源密集型操作
        async def resource_intensive_perception():
            # 模拟复杂的图像分析
            await asyncio.sleep(0.2)
            return ScreenAnalysis(
                ui_elements=[
                    UIElement(id=f"item_{i}", type="data", text=f"数据项{i}", bounds=(i*10, i*10, i*10+50, i*10+30))
                    for i in range(20)  # 大量UI元素
                ],
                screenshot_path="complex_interface.png",
                timestamp=datetime.now()
            )
        
        # 替换感知引擎
        qyuan_core.engines["perception"].analyze_current_screen = resource_intensive_perception
        
        # 执行资源密集型循环
        start_time = time.time()
        cycle_count = 5
        
        for i in range(cycle_count):
            await qyuan_core.perception_action_cycle()
            
            # 检查性能指标
            performance_report = qyuan_core.perception_action_loop.get_performance_report()
            avg_cycle_time = performance_report["metrics"]["average_cycle_time"]
            
            # 验证性能优化生效
            if i > 1 and avg_cycle_time > 0:
                assert avg_cycle_time < 1.0  # 平均循环时间应该合理
            
            await asyncio.sleep(0.1)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 验证资源使用效率
        assert total_time < cycle_count * 0.5  # 总时间应该合理
        
        print(f"资源密集型场景测试:")
        print(f"  循环次数: {cycle_count}")
        print(f"  总时间: {total_time:.3f}秒")
        print(f"  平均循环时间: {total_time/cycle_count:.3f}秒")


if __name__ == "__main__":
    # 运行复杂场景测试
    pytest.main([__file__, "-v", "-s"])

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图像增强模块

提供图像增强功能，包括添加经纬线和坐标标注。
"""

import os
from PIL import Image, ImageDraw, ImageFont


class ImageEnhancer:
    """图像增强类，提供图像增强功能"""

    def __init__(self, font_path=None, margin_size=100, grid_spacing=200, line_width=2, grid_color="red", text_color="black"):
        """
        初始化图像增强类

        Args:
            font_path: 字体路径，为None则使用默认字体
            margin_size: 边距大小（像素），默认100像素
            grid_spacing: 网格间距（像素）
            line_width: 网格线宽度（像素）
            grid_color: 网格线颜色
            text_color: 文本颜色
        """
        self.margin_size = margin_size
        self.grid_spacing = grid_spacing
        self.line_width = line_width
        self.grid_color = grid_color
        self.text_color = text_color

        # 设置字体 - 尝试使用系统字体，确保字体大小设置生效
        self.font_size = 36  # 使用较大字体，配合扩大的白边

        # 首先尝试使用提供的字体路径
        if font_path and os.path.exists(font_path):
            try:
                self.font = ImageFont.truetype(font_path, self.font_size)
                return
            except Exception:
                pass  # 如果失败，继续尝试其他方法

        # 然后尝试使用系统字体
        try:
            # Windows系统字体路径
            if os.name == 'nt':
                windows_font_path = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts', 'Arial.ttf')
                if os.path.exists(windows_font_path):
                    self.font = ImageFont.truetype(windows_font_path, self.font_size)
                    return
        except Exception:
            pass  # 如果失败，继续尝试其他方法

        # 最后使用默认字体
        self.font = ImageFont.load_default()

    def add_grid_and_coordinates(self, image, region_coords=None):
        """
        添加网格和坐标标注

        Args:
            image: PIL.Image对象
            region_coords: 区域坐标 (x, y, width, height)，为None则使用(0, 0)作为左上角坐标

        Returns:
            PIL.Image: 增强后的图像
        """
        # 设置区域左上角坐标
        region_x = 0
        region_y = 0
        if region_coords:
            region_x, region_y = region_coords[0], region_coords[1]
        # 获取原始图像尺寸
        width, height = image.size

        # 创建带边距的新图像（白色背景）
        new_width = width + 2 * self.margin_size
        new_height = height + 2 * self.margin_size
        enhanced_image = Image.new('RGB', (new_width, new_height), color='white')

        # 将原始图像粘贴到中央
        enhanced_image.paste(image, (self.margin_size, self.margin_size))

        # 创建绘图对象
        draw = ImageDraw.Draw(enhanced_image)

        # 绘制边框
        draw.rectangle(
            [(self.margin_size, self.margin_size),
             (self.margin_size + width, self.margin_size + height)],
            outline='black', width=2
        )

        # 绘制水平网格线和坐标标注
        for y in range(0, height + 1, self.grid_spacing):  # +1 确保包含最后一条线
            # 如果超出图像高度，则使用图像高度
            if y > height:
                y = height

            # 绘制水平线
            draw.line(
                [(self.margin_size, self.margin_size + y),
                 (self.margin_size + width, self.margin_size + y)],
                fill=self.grid_color, width=self.line_width
            )

            # 在左侧边距添加Y坐标标注
            actual_y = region_y + y  # 使用实际坐标
            y_text = str(actual_y)
            text_width = self.font.getbbox(y_text)[2]
            text_height = self.font.getbbox(y_text)[3]
            text_y = self.margin_size + y - text_height // 2  # 垂直居中

            # 绘制背景色块
            draw.rectangle(
                [(2, text_y - 2), (text_width + 8, text_y + text_height + 2)],
                fill="lightblue"
            )
            # 绘制文字
            draw.text(
                (5, text_y),
                y_text,
                fill="black",
                font=self.font
            )

            # 在右侧边距添加Y坐标标注
            # 绘制背景色块
            draw.rectangle(
                [(new_width - text_width - 8, text_y - 2), (new_width - 2, text_y + text_height + 2)],
                fill="lightblue"
            )
            # 绘制文字
            draw.text(
                (new_width - text_width - 5, text_y),
                y_text,
                fill="black",
                font=self.font
            )

        # 绘制垂直网格线和坐标标注
        for x in range(0, width + 1, self.grid_spacing):  # +1 确保包含最后一条线
            # 如果超出图像宽度，则使用图像宽度
            if x > width:
                x = width

            # 绘制垂直线
            draw.line(
                [(self.margin_size + x, self.margin_size),
                 (self.margin_size + x, self.margin_size + height)],
                fill=self.grid_color, width=self.line_width
            )

            # 在上方边距添加X坐标标注
            actual_x = region_x + x  # 使用实际坐标
            x_text = str(actual_x)
            text_width = self.font.getbbox(x_text)[2]
            text_height = self.font.getbbox(x_text)[3]
            text_x = self.margin_size + x - text_width // 2  # 水平居中

            # 绘制背景色块
            draw.rectangle(
                [(text_x - 2, 2), (text_x + text_width + 2, text_height + 8)],
                fill="lightgreen"
            )
            # 绘制文字
            draw.text(
                (text_x, 5),
                x_text,
                fill="black",
                font=self.font
            )

            # 在下方边距添加X坐标标注
            # 绘制背景色块
            draw.rectangle(
                [(text_x - 2, new_height - text_height - 8), (text_x + text_width + 2, new_height - 2)],
                fill="lightgreen"
            )
            # 绘制文字
            draw.text(
                (text_x, new_height - text_height - 5),
                x_text,
                fill="black",
                font=self.font
            )

        # 添加屏幕边界坐标标注 - 使用背景色块使文字更加醒目

        # 左上角坐标 - 放在左上角白边区域
        left_top_text = f"({region_x}, {region_y})"
        text_width = self.font.getbbox(left_top_text)[2]
        text_height = self.font.getbbox(left_top_text)[3]
        # 绘制背景色块
        draw.rectangle(
            [(2, 2), (text_width + 8, text_height + 8)],
            fill="yellow"
        )
        # 绘制文字
        draw.text(
            (5, 5),
            left_top_text,
            fill="black",
            font=self.font
        )

        # 右上角坐标 - 放在右上角白边区域
        right_top_text = f"({region_x + width}, {region_y})"
        text_width = self.font.getbbox(right_top_text)[2]
        text_height = self.font.getbbox(right_top_text)[3]
        # 绘制背景色块
        draw.rectangle(
            [(new_width - text_width - 8, 2), (new_width - 2, text_height + 8)],
            fill="yellow"
        )
        # 绘制文字
        draw.text(
            (new_width - text_width - 5, 5),
            right_top_text,
            fill="black",
            font=self.font
        )

        # 左下角坐标 - 放在左下角白边区域
        left_bottom_text = f"({region_x}, {region_y + height})"
        text_width = self.font.getbbox(left_bottom_text)[2]
        text_height = self.font.getbbox(left_bottom_text)[3]
        # 绘制背景色块
        draw.rectangle(
            [(2, new_height - text_height - 8), (text_width + 8, new_height - 2)],
            fill="yellow"
        )
        # 绘制文字
        draw.text(
            (5, new_height - text_height - 5),
            left_bottom_text,
            fill="black",
            font=self.font
        )

        # 右下角坐标 - 放在右下角白边区域
        right_bottom_text = f"({region_x + width}, {region_y + height})"
        text_width = self.font.getbbox(right_bottom_text)[2]
        text_height = self.font.getbbox(right_bottom_text)[3]
        # 绘制背景色块
        draw.rectangle(
            [(new_width - text_width - 8, new_height - text_height - 8), (new_width - 2, new_height - 2)],
            fill="yellow"
        )
        # 绘制文字
        draw.text(
            (new_width - text_width - 5, new_height - text_height - 5),
            right_bottom_text,
            fill="black",
            font=self.font
        )

        return enhanced_image

    def enhance_image(self, image_path, output_path=None, region_coords=None):
        """
        增强图像（添加网格和坐标标注）

        Args:
            image_path: 原始图像路径
            output_path: 输出路径，为None则自动生成
            region_coords: 区域坐标 (x, y, width, height)，为None则使用(0, 0)作为左上角坐标

        Returns:
            str: 增强后的图像路径
        """
        try:
            # 加载原始图像
            image = Image.open(image_path)

            # 添加网格和坐标标注
            enhanced_image = self.add_grid_and_coordinates(image, region_coords)

            # 保存增强后的图像
            if output_path is None:
                # 生成输出路径
                filename, ext = os.path.splitext(image_path)
                output_path = f"{filename}_enhanced{ext}"

            enhanced_image.save(output_path)
            return output_path

        except Exception as e:
            print(f"增强图像时出错: {e}")
            return None


# 测试代码
if __name__ == "__main__":
    # 创建图像增强对象
    enhancer = ImageEnhancer()

    # 测试图像路径
    test_image_path = "screenshots/screenshot_latest.png"

    # 如果测试图像不存在，则创建一个测试图像
    if not os.path.exists(test_image_path):
        # 确保目录存在
        os.makedirs(os.path.dirname(test_image_path), exist_ok=True)

        # 创建一个简单的测试图像
        test_image = Image.new('RGB', (800, 600), color='lightblue')
        draw = ImageDraw.Draw(test_image)

        # 绘制一些形状
        draw.rectangle([(100, 100), (300, 200)], fill='red', outline='black')
        draw.ellipse([(400, 300), (600, 400)], fill='green', outline='black')
        draw.line([(100, 500), (700, 500)], fill='blue', width=5)

        # 保存测试图像
        test_image.save(test_image_path)
        print(f"创建测试图像: {test_image_path}")

    # 增强图像
    enhanced_path = enhancer.enhance_image(test_image_path)
    print(f"增强后的图像已保存至: {enhanced_path}")

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
改进的输入法处理模块

该模块提供更可靠的输入法检测和切换功能，解决原有实现中的问题。
"""

import time
import logging
import ctypes
from ctypes import wintypes
from typing import Optional, List, Tuple

# 配置日志
logger = logging.getLogger("ImprovedIMEHandler")

# Windows API 常量
WM_INPUTLANGCHANGEREQUEST = 0x0050
HWND_BROADCAST = 0xFFFF
HKL_NEXT = 1
HKL_PREV = 0

# 导入Windows API
try:
    import win32api
    import win32con
    import win32gui
    import win32process
    HAS_WIN32 = True
except ImportError:
    HAS_WIN32 = False
    logger.warning("win32api模块未安装，输入法功能将受限")

class ImprovedIMEHandler:
    """改进的输入法处理器"""

    def __init__(self):
        """初始化输入法处理器"""
        self.available_methods = []
        self._detect_available_methods()
        logger.info(f"输入法处理器初始化完成，可用方法: {self.available_methods}")

    def _detect_available_methods(self):
        """检测可用的输入法处理方法"""
        if HAS_WIN32:
            self.available_methods.append("win32api")

        # 检测ctypes方法
        try:
            user32 = ctypes.windll.user32
            self.available_methods.append("ctypes")
        except:
            pass

    def get_current_keyboard_layout(self) -> Optional[int]:
        """
        获取当前键盘布局

        Returns:
            当前键盘布局的句柄，失败返回None
        """
        if not HAS_WIN32:
            return None

        try:
            # 获取当前活动窗口
            hwnd = win32gui.GetForegroundWindow()
            if hwnd == 0:
                return None

            # 获取窗口所属的线程ID
            thread_id, process_id = win32process.GetWindowThreadProcessId(hwnd)

            # 获取该线程的键盘布局
            hkl = win32api.GetKeyboardLayout(thread_id)
            return hkl
        except Exception as e:
            logger.error(f"获取键盘布局失败: {str(e)}")
            return None

    def get_installed_keyboard_layouts(self) -> List[int]:
        """
        获取系统中安装的所有键盘布局

        Returns:
            键盘布局句柄列表
        """
        if not HAS_WIN32:
            return []

        try:
            # 使用win32api获取键盘布局列表
            layouts = win32api.GetKeyboardLayoutList()
            return layouts
        except Exception as e:
            logger.error(f"获取键盘布局列表失败: {str(e)}")
            return []

    def is_chinese_ime_active(self) -> Tuple[bool, str]:
        """
        检测当前是否为中文输入法

        Returns:
            (是否为中文输入法, 检测方法说明)
        """
        current_layout = self.get_current_keyboard_layout()
        if current_layout is None:
            return False, "无法获取键盘布局"

        # 提取语言ID (低16位)
        lang_id = current_layout & 0xFFFF

        # 中文相关的语言ID
        chinese_lang_ids = {
            0x0804: "中文(简体，中国)",
            0x0404: "中文(繁体，台湾)",
            0x0C04: "中文(繁体，香港)",
            0x1004: "中文(简体，新加坡)",
            0x1404: "中文(繁体，澳门)"
        }

        if lang_id in chinese_lang_ids:
            return True, f"检测到中文输入法: {chinese_lang_ids[lang_id]} (0x{lang_id:04X})"
        else:
            return False, f"当前为非中文输入法 (0x{lang_id:04X})"

    def find_english_layout(self) -> Optional[int]:
        """
        查找英文键盘布局

        Returns:
            英文键盘布局句柄，未找到返回None
        """
        layouts = self.get_installed_keyboard_layouts()

        # 英文相关的语言ID
        english_lang_ids = {0x0409, 0x0809, 0x0C09, 0x1009, 0x1409, 0x1809, 0x1C09, 0x2009, 0x2409, 0x2809, 0x2C09, 0x3009, 0x3409}

        for layout in layouts:
            lang_id = layout & 0xFFFF
            if lang_id in english_lang_ids:
                logger.info(f"找到英文键盘布局: 0x{layout:08X}")
                return layout

        logger.warning("未找到英文键盘布局")
        return None

    def switch_to_english_layout(self) -> Tuple[bool, str]:
        """
        切换到英文键盘布局

        Returns:
            (是否成功, 操作说明)
        """
        if not HAS_WIN32:
            return False, "win32api模块不可用"

        try:
            # 检查当前是否已经是英文输入法
            is_chinese, current_status = self.is_chinese_ime_active()
            if not is_chinese:
                return True, f"当前已是英文输入法: {current_status}"

            # 查找英文布局
            english_layout = self.find_english_layout()
            if english_layout is None:
                return False, "系统中未找到英文键盘布局"

            # 获取当前活动窗口
            hwnd = win32gui.GetForegroundWindow()
            if hwnd == 0:
                return False, "无法获取当前活动窗口"

            # 切换到英文布局
            result = win32api.SendMessage(
                hwnd,
                WM_INPUTLANGCHANGEREQUEST,
                0,
                english_layout
            )

            # 等待切换完成
            time.sleep(0.5)

            # 验证切换结果
            is_chinese_after, status_after = self.is_chinese_ime_active()
            if not is_chinese_after:
                return True, f"成功切换到英文输入法: {status_after}"
            else:
                return False, f"切换失败，仍为中文输入法: {status_after}"

        except Exception as e:
            logger.error(f"切换输入法失败: {str(e)}")
            return False, f"切换过程中发生异常: {str(e)}"

    def cycle_input_methods(self, max_attempts: int = 5) -> Tuple[bool, str]:
        """
        通过循环切换的方式尝试切换到英文输入法

        Args:
            max_attempts: 最大尝试次数

        Returns:
            (是否成功, 操作说明)
        """
        if not HAS_WIN32:
            return False, "win32api模块不可用"

        try:
            # 记录初始状态
            initial_chinese, initial_status = self.is_chinese_ime_active()
            logger.info(f"初始状态: {initial_status}")

            if not initial_chinese:
                return True, f"当前已是英文输入法: {initial_status}"

            # 尝试多种切换方法
            switch_methods = [
                ("Alt+Shift", lambda: self._send_alt_shift()),
                ("Ctrl+Shift", lambda: self._send_ctrl_shift()),
                ("Ctrl+Space", lambda: self._send_ctrl_space()),
                ("Win+Space", lambda: self._send_win_space()),
            ]

            for attempt in range(max_attempts):
                for method_name, method_func in switch_methods:
                    logger.info(f"尝试 {attempt+1}/{max_attempts}: 使用 {method_name}")

                    try:
                        method_func()
                        time.sleep(0.8)  # 等待切换完成

                        # 检查是否切换成功
                        is_chinese, status = self.is_chinese_ime_active()
                        if not is_chinese:
                            return True, f"使用 {method_name} 成功切换到英文输入法: {status}"

                    except Exception as e:
                        logger.warning(f"{method_name} 方法失败: {str(e)}")
                        continue

                # 如果一轮尝试后仍未成功，等待一下再继续
                if attempt < max_attempts - 1:
                    time.sleep(1.0)

            # 所有尝试都失败
            final_chinese, final_status = self.is_chinese_ime_active()
            return False, f"经过 {max_attempts} 轮尝试仍无法切换到英文输入法: {final_status}"

        except Exception as e:
            logger.error(f"循环切换输入法失败: {str(e)}")
            return False, f"切换过程中发生异常: {str(e)}"

    def _send_alt_shift(self):
        """发送Alt+Shift组合键"""
        if HAS_WIN32:
            # 使用keybd_event发送按键
            win32api.keybd_event(win32con.VK_MENU, 0, 0, 0)  # Alt down
            win32api.keybd_event(win32con.VK_SHIFT, 0, 0, 0)  # Shift down
            time.sleep(0.1)
            win32api.keybd_event(win32con.VK_SHIFT, 0, win32con.KEYEVENTF_KEYUP, 0)  # Shift up
            win32api.keybd_event(win32con.VK_MENU, 0, win32con.KEYEVENTF_KEYUP, 0)  # Alt up

    def _send_ctrl_shift(self):
        """发送Ctrl+Shift组合键"""
        if HAS_WIN32:
            win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)  # Ctrl down
            win32api.keybd_event(win32con.VK_SHIFT, 0, 0, 0)  # Shift down
            time.sleep(0.1)
            win32api.keybd_event(win32con.VK_SHIFT, 0, win32con.KEYEVENTF_KEYUP, 0)  # Shift up
            win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)  # Ctrl up

    def _send_ctrl_space(self):
        """发送Ctrl+Space组合键"""
        if HAS_WIN32:
            win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)  # Ctrl down
            win32api.keybd_event(win32con.VK_SPACE, 0, 0, 0)  # Space down
            time.sleep(0.1)
            win32api.keybd_event(win32con.VK_SPACE, 0, win32con.KEYEVENTF_KEYUP, 0)  # Space up
            win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)  # Ctrl up

    def _send_win_space(self):
        """发送Win+Space组合键"""
        if HAS_WIN32:
            win32api.keybd_event(win32con.VK_LWIN, 0, 0, 0)  # Win down
            win32api.keybd_event(win32con.VK_SPACE, 0, 0, 0)  # Space down
            time.sleep(0.1)
            win32api.keybd_event(win32con.VK_SPACE, 0, win32con.KEYEVENTF_KEYUP, 0)  # Space up
            win32api.keybd_event(win32con.VK_LWIN, 0, win32con.KEYEVENTF_KEYUP, 0)  # Win up

    def get_detailed_ime_info(self) -> dict:
        """
        获取详细的输入法信息

        Returns:
            包含输入法详细信息的字典
        """
        info = {
            "has_win32": HAS_WIN32,
            "available_methods": self.available_methods,
            "current_layout": None,
            "current_layout_hex": None,
            "is_chinese": False,
            "status_message": "",
            "installed_layouts": [],
            "english_layout_available": False
        }

        if HAS_WIN32:
            current_layout = self.get_current_keyboard_layout()
            if current_layout:
                info["current_layout"] = current_layout
                info["current_layout_hex"] = f"0x{current_layout:08X}"

            is_chinese, status = self.is_chinese_ime_active()
            info["is_chinese"] = is_chinese
            info["status_message"] = status

            layouts = self.get_installed_keyboard_layouts()
            info["installed_layouts"] = [f"0x{layout:08X}" for layout in layouts]

            english_layout = self.find_english_layout()
            info["english_layout_available"] = english_layout is not None

        return info

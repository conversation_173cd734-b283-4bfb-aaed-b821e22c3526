# QYuan项目依赖
# 核心框架
fastapi>=0.104.0
uvicorn>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# 数据库
sqlalchemy>=2.0.0
alembic>=1.13.0
asyncpg>=0.29.0
redis>=5.0.0

# 向量数据库
qdrant-client>=1.7.1

# LLM客户端
openai>=1.3.0
aiohttp>=3.9.0

# 工具库
python-dotenv>=1.0.0
python-multipart>=0.0.6
websockets>=12.0
numpy>=1.26.0

# MCP模块依赖
fastmcp>=2.3.4
pyautogui>=0.9.53
pynput>=1.8.1
pyperclip>=1.8.2

# 感知引擎依赖
pillow>=10.0.0
opencv-python>=4.8.0
pytesseract>=0.3.10
psutil>=5.9.0

# Windows特定依赖
pywin32>=310; platform_system=="Windows"
uiautomation>=2.0.0; platform_system=="Windows"
pywinauto>=0.6.8; platform_system=="Windows"
comtypes>=1.1.10; platform_system=="Windows"



"""
集成测试，测试MCP服务器与鼠标控制器的交互。
"""

import unittest
import json
import sys
import os
import time
from unittest.mock import patch, MagicMock

# 添加src目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入MCP服务器和鼠标控制器
from src.auto_mouse_server import mcp, move_mouse, mouse_click, mouse_drag, mouse_scroll


class TestIntegration(unittest.TestCase):
    """集成测试类。"""
    
    @patch('src.mouse_controller.pyautogui')
    def test_move_mouse_tool(self, mock_pyautogui):
        """测试移动鼠标工具。"""
        # 设置模拟返回值
        mock_pyautogui.position.return_value = (100, 200)
        
        # 测试绝对坐标
        result = move_mouse(x=500, y=600, random_offset=False)
        result_dict = json.loads(result.replace("'", "\""))
        
        self.assertTrue(result_dict["success"])
        self.assertEqual(result_dict["position"]["x"], 500)
        self.assertEqual(result_dict["position"]["y"], 600)
        
        # 测试区域坐标
        result = move_mouse(left=100, top=200, right=300, bottom=400, random_offset=False)
        result_dict = json.loads(result.replace("'", "\""))
        
        self.assertTrue(result_dict["success"])
        # 坐标应该在区域的中心30%范围内
        self.assertGreaterEqual(result_dict["position"]["x"], 170)
        self.assertLessEqual(result_dict["position"]["x"], 230)
        self.assertGreaterEqual(result_dict["position"]["y"], 270)
        self.assertLessEqual(result_dict["position"]["y"], 330)
    
    @patch('src.mouse_controller.pyautogui')
    def test_mouse_click_tool(self, mock_pyautogui):
        """测试鼠标点击工具。"""
        # 设置模拟返回值
        mock_pyautogui.position.return_value = (100, 200)
        
        # 测试点击
        result = mouse_click(x=500, y=600, button="right", clicks=2)
        result_dict = json.loads(result.replace("'", "\""))
        
        self.assertTrue(result_dict["success"])
        self.assertEqual(result_dict["position"]["x"], 500)
        self.assertEqual(result_dict["position"]["y"], 600)
        self.assertEqual(result_dict["button"], "right")
        self.assertEqual(result_dict["clicks"], 2)
    
    @patch('src.mouse_controller.pyautogui')
    def test_mouse_drag_tool(self, mock_pyautogui):
        """测试鼠标拖拽工具。"""
        # 设置模拟返回值
        mock_pyautogui.position.return_value = (100, 200)
        
        # 测试拖拽
        result = mouse_drag(end_x=500, end_y=600, start_x=300, start_y=400, duration=0.2)
        result_dict = json.loads(result.replace("'", "\""))
        
        self.assertTrue(result_dict["success"])
        self.assertEqual(result_dict["start_position"]["x"], 300)
        self.assertEqual(result_dict["start_position"]["y"], 400)
        self.assertEqual(result_dict["end_position"]["x"], 500)
        self.assertEqual(result_dict["end_position"]["y"], 600)
    
    @patch('src.mouse_controller.pyautogui')
    def test_mouse_scroll_tool(self, mock_pyautogui):
        """测试鼠标滚动工具。"""
        # 设置模拟返回值
        mock_pyautogui.position.return_value = (100, 200)
        
        # 测试滚动
        result = mouse_scroll(amount=5, direction="down")
        result_dict = json.loads(result.replace("'", "\""))
        
        self.assertTrue(result_dict["success"])
        self.assertEqual(result_dict["position"]["x"], 100)
        self.assertEqual(result_dict["position"]["y"], 200)
        self.assertEqual(result_dict["direction"], "down")
        self.assertEqual(result_dict["amount"], 5)


if __name__ == '__main__':
    unittest.main()

# -*- coding: utf-8 -*-
"""
自然语言意图解析服务实现
专门负责解析用户的自然语言输入，识别意图和参数
严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
import re
from typing import Dict, List, Optional, Any, Tuple

from .base import (
    IntentParserBase,
    UserIntent,
    IntentType,
    DecisionContext
)

class NLPIntentParser(IntentParserBase):
    """自然语言意图解析器"""
    
    def __init__(self, llm_client=None):
        super().__init__("NLPIntentParser")
        self.logger = logging.getLogger(f"QYuan.Decision.{self.name}")
        self.llm_client = llm_client
        
        # 意图模式匹配规则
        self.intent_patterns = self._build_intent_patterns()
        
        # 参数提取规则
        self.parameter_extractors = self._build_parameter_extractors()
        
        if not self.llm_client:
            self.logger.warning("LLM客户端未配置，将使用基于规则的意图解析")
    
    async def parse_intent(self, user_input: str, context: Optional[DecisionContext] = None) -> UserIntent:
        """解析用户意图"""
        start_time = time.time()
        
        try:
            # 预处理输入
            cleaned_input = self._preprocess_input(user_input)
            
            # 尝试使用LLM解析
            if self.llm_client:
                intent = await self._parse_with_llm(cleaned_input, context)
            else:
                # 使用基于规则的解析
                intent = await self._parse_with_rules(cleaned_input, context)
            
            processing_time = time.time() - start_time
            self.logger.debug(f"意图解析完成，耗时: {processing_time:.2f}秒")
            
            return intent
            
        except Exception as e:
            self.logger.error(f"意图解析失败: {e}")
            return UserIntent(
                intent_type=IntentType.UNKNOWN,
                description=user_input,
                confidence=0.0
            )
    
    async def _parse_with_llm(self, user_input: str, context: Optional[DecisionContext]) -> UserIntent:
        """使用LLM解析意图"""
        try:
            # 构建提示词
            prompt = self._build_llm_prompt(user_input, context)
            
            # 调用LLM
            response = await self._call_llm(prompt)
            
            # 解析LLM响应
            intent = self._parse_llm_response(response, user_input)
            
            return intent
            
        except Exception as e:
            self.logger.warning(f"LLM意图解析失败，回退到规则解析: {e}")
            return await self._parse_with_rules(user_input, context)
    
    async def _parse_with_rules(self, user_input: str, context: Optional[DecisionContext]) -> UserIntent:
        """使用规则解析意图"""
        # 检测意图类型
        intent_type = self._detect_intent_type(user_input)
        
        # 提取参数
        parameters = self._extract_parameters(user_input, intent_type)
        
        # 计算置信度
        confidence = self._calculate_confidence(user_input, intent_type, parameters)
        
        return UserIntent(
            intent_type=intent_type,
            description=user_input,
            parameters=parameters,
            confidence=confidence,
            context=context.current_screen_state if context else None
        )
    
    def _detect_intent_type(self, user_input: str) -> IntentType:
        """检测意图类型"""
        input_lower = user_input.lower()
        
        # 按优先级检查各种意图模式
        for intent_type, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, input_lower):
                    return intent_type
        
        return IntentType.UNKNOWN
    
    def _extract_parameters(self, user_input: str, intent_type: IntentType) -> Dict[str, Any]:
        """提取参数"""
        parameters = {}
        
        # 根据意图类型提取相应参数
        if intent_type in self.parameter_extractors:
            extractors = self.parameter_extractors[intent_type]
            for param_name, extractor_func in extractors.items():
                try:
                    value = extractor_func(user_input)
                    if value is not None:
                        parameters[param_name] = value
                except Exception as e:
                    self.logger.warning(f"参数提取失败 {param_name}: {e}")
        
        return parameters
    
    def _calculate_confidence(self, user_input: str, intent_type: IntentType, parameters: Dict[str, Any]) -> float:
        """计算置信度"""
        if intent_type == IntentType.UNKNOWN:
            return 0.0
        
        # 基础置信度
        base_confidence = 0.6
        
        # 根据参数数量调整
        param_bonus = min(0.3, len(parameters) * 0.1)
        
        # 根据输入长度调整
        length_factor = min(1.0, len(user_input.split()) / 10)
        
        confidence = base_confidence + param_bonus
        confidence *= length_factor
        
        return min(1.0, confidence)
    
    def _build_intent_patterns(self) -> Dict[IntentType, List[str]]:
        """构建意图模式匹配规则"""
        return {
            IntentType.CLICK: [
                r'点击|click|按|press|选择|select',
                r'打开|open|启动|start',
                r'确定|确认|ok|yes'
            ],
            IntentType.TYPE: [
                r'输入|type|写|write|填写|fill',
                r'搜索|search|查找|find',
                r'编辑|edit|修改|modify'
            ],
            IntentType.SCROLL: [
                r'滚动|scroll|翻页|page',
                r'向上|向下|up|down',
                r'滑动|swipe'
            ],
            IntentType.NAVIGATE: [
                r'导航|navigate|跳转|jump|go to',
                r'返回|back|前进|forward',
                r'刷新|refresh|reload'
            ],
            IntentType.COPY: [
                r'复制|copy|拷贝',
                r'ctrl\+c|command\+c'
            ],
            IntentType.PASTE: [
                r'粘贴|paste|贴',
                r'ctrl\+v|command\+v'
            ],
            IntentType.WAIT: [
                r'等待|wait|暂停|pause',
                r'延迟|delay|sleep'
            ],
            IntentType.CLOSE: [
                r'关闭|close|退出|exit',
                r'结束|end|停止|stop'
            ]
        }
    
    def _build_parameter_extractors(self) -> Dict[IntentType, Dict[str, callable]]:
        """构建参数提取器"""
        return {
            IntentType.CLICK: {
                'target': self._extract_target_element,
                'position': self._extract_position
            },
            IntentType.TYPE: {
                'text': self._extract_text_content,
                'target': self._extract_target_element
            },
            IntentType.SCROLL: {
                'direction': self._extract_scroll_direction,
                'amount': self._extract_scroll_amount
            },
            IntentType.NAVIGATE: {
                'url': self._extract_url,
                'direction': self._extract_navigation_direction
            },
            IntentType.WAIT: {
                'duration': self._extract_wait_duration
            }
        }
    
    def _extract_target_element(self, text: str) -> Optional[str]:
        """提取目标元素"""
        # 查找引号中的内容
        quoted_match = re.search(r'["\']([^"\']+)["\']', text)
        if quoted_match:
            return quoted_match.group(1)
        
        # 查找"按钮"、"链接"等关键词前的内容
        element_match = re.search(r'(\w+)(?:按钮|链接|菜单|选项|button|link|menu)', text)
        if element_match:
            return element_match.group(1)
        
        return None
    
    def _extract_text_content(self, text: str) -> Optional[str]:
        """提取文本内容"""
        # 查找引号中的内容
        quoted_match = re.search(r'["\']([^"\']+)["\']', text)
        if quoted_match:
            return quoted_match.group(1)
        
        # 查找"输入"后的内容
        input_match = re.search(r'输入\s*[:：]?\s*(.+)', text)
        if input_match:
            return input_match.group(1).strip()
        
        return None
    
    def _extract_position(self, text: str) -> Optional[Dict[str, int]]:
        """提取位置信息"""
        # 查找坐标
        coord_match = re.search(r'\((\d+),\s*(\d+)\)', text)
        if coord_match:
            return {
                'x': int(coord_match.group(1)),
                'y': int(coord_match.group(2))
            }
        
        return None
    
    def _extract_scroll_direction(self, text: str) -> Optional[str]:
        """提取滚动方向"""
        if re.search(r'向上|up|上', text.lower()):
            return 'up'
        elif re.search(r'向下|down|下', text.lower()):
            return 'down'
        elif re.search(r'向左|left|左', text.lower()):
            return 'left'
        elif re.search(r'向右|right|右', text.lower()):
            return 'right'
        
        return None
    
    def _extract_scroll_amount(self, text: str) -> Optional[int]:
        """提取滚动量"""
        amount_match = re.search(r'(\d+)(?:行|页|次|pixel|px)', text)
        if amount_match:
            return int(amount_match.group(1))
        
        return None
    
    def _extract_url(self, text: str) -> Optional[str]:
        """提取URL"""
        url_match = re.search(r'https?://[^\s]+', text)
        if url_match:
            return url_match.group(0)
        
        return None
    
    def _extract_navigation_direction(self, text: str) -> Optional[str]:
        """提取导航方向"""
        if re.search(r'返回|back|后退', text.lower()):
            return 'back'
        elif re.search(r'前进|forward', text.lower()):
            return 'forward'
        elif re.search(r'刷新|refresh|reload', text.lower()):
            return 'refresh'
        
        return None
    
    def _extract_wait_duration(self, text: str) -> Optional[float]:
        """提取等待时长"""
        duration_match = re.search(r'(\d+(?:\.\d+)?)(?:秒|s|second)', text)
        if duration_match:
            return float(duration_match.group(1))
        
        return None
    
    def _preprocess_input(self, user_input: str) -> str:
        """预处理用户输入"""
        # 去除多余空格
        cleaned = re.sub(r'\s+', ' ', user_input.strip())
        
        # 标准化标点符号
        cleaned = re.sub(r'[，。！？；：]', ',', cleaned)
        
        return cleaned
    
    def _build_llm_prompt(self, user_input: str, context: Optional[DecisionContext]) -> str:
        """构建LLM提示词"""
        prompt = f"""请分析以下用户指令，识别用户的意图和参数：

用户指令: "{user_input}"

请以JSON格式返回分析结果：
{{
    "intent_type": "意图类型(click/type/scroll/navigate/search/copy/paste/open/close/wait/unknown)",
    "description": "意图描述",
    "parameters": {{
        "参数名": "参数值"
    }},
    "confidence": 0.0-1.0之间的置信度
}}

可用的意图类型：
- click: 点击操作
- type: 输入文本
- scroll: 滚动操作
- navigate: 导航操作
- search: 搜索操作
- copy: 复制操作
- paste: 粘贴操作
- open: 打开操作
- close: 关闭操作
- wait: 等待操作
- unknown: 未知意图

请确保返回有效的JSON格式。"""

        if context and context.current_screen_state:
            prompt += f"\n\n当前屏幕状态: {context.current_screen_state}"
        
        return prompt
    
    async def _call_llm(self, prompt: str) -> str:
        """调用LLM"""
        try:
            response = await self.llm_client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                model="gpt-3.5-turbo",
                max_tokens=500,
                temperature=0.1
            )
            
            if response and response.get('choices'):
                return response['choices'][0]['message']['content']
            else:
                raise ValueError("LLM返回无效响应")
                
        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            raise
    
    def _parse_llm_response(self, response: str, original_input: str) -> UserIntent:
        """解析LLM响应"""
        try:
            import json
            
            # 尝试解析JSON
            data = json.loads(response)
            
            # 验证和转换意图类型
            intent_type_str = data.get('intent_type', 'unknown')
            try:
                intent_type = IntentType(intent_type_str)
            except ValueError:
                intent_type = IntentType.UNKNOWN
            
            return UserIntent(
                intent_type=intent_type,
                description=data.get('description', original_input),
                parameters=data.get('parameters', {}),
                confidence=float(data.get('confidence', 0.0))
            )
            
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            self.logger.warning(f"LLM响应解析失败: {e}")
            return UserIntent(
                intent_type=IntentType.UNKNOWN,
                description=original_input,
                confidence=0.0
            )

# QYuan系统技术选型设计

## 核心架构技术选型

### 1. LLM API选择

#### 主要候选方案
**OpenAI GPT-4/GPT-4o**
- ✅ 优势：推理能力强，多模态支持好，API稳定
- ❌ 劣势：成本较高，国内访问需要代理
- 💰 成本：$0.03/1K tokens (input), $0.06/1K tokens (output)

**Google Gemini Pro/Ultra**
- ✅ 优势：多模态能力突出，视觉理解优秀，成本相对较低
- ❌ 劣势：推理能力略逊于GPT-4，API稳定性一般
- 💰 成本：$0.00125/1K tokens (input), $0.00375/1K tokens (output)

**Anthropic Claude 3.5 Sonnet**
- ✅ 优势：推理能力强，安全性好，长上下文支持
- ❌ 劣势：多模态能力一般，成本较高
- 💰 成本：$0.003/1K tokens (input), $0.015/1K tokens (output)

**本地部署方案（Ollama + Llama 3.1/Qwen2.5）**
- ✅ 优势：完全私有，无API调用成本，响应速度可控
- ❌ 劣势：需要高性能硬件，多模态能力有限
- 💰 成本：硬件投入，电费

#### 当前配置方案
**主力模型：GPT-4.1**
- **服务地址**：https://xiaoai.plus
- **API协议**：OpenAI兼容
- **API Key**：sk-oQx9XqkIHyMFabGTFByNVsxZHw1q4YpTvFdIQoupRkEVjDd3
- **优势**：自建中转服务，稳定可控，推理能力强
- **使用场景**：所有核心决策和推理任务

**辅助模型策略**：
- **其他LLM**：由QYuan根据需要调用（Gemini、Claude等）
- **本地模型**：隐私敏感任务，离线场景备用
- **调用策略**：根据任务特点和性能要求动态选择

### 2. 数据库设计

#### 主数据库：PostgreSQL
```sql
-- 用户对话记录表
CREATE TABLE conversations (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_message TEXT,
    assistant_message TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    message_type VARCHAR(50), -- 'command', 'response', 'system'
    metadata JSONB -- 额外信息
);

-- 操作记录表
CREATE TABLE operations (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255),
    operation_type VARCHAR(100), -- 'mouse_click', 'keyboard_input', 'screenshot'
    operation_data JSONB, -- 操作详细数据
    execution_time TIMESTAMP,
    success BOOLEAN,
    error_message TEXT,
    screenshot_before TEXT, -- base64编码的截图
    screenshot_after TEXT,
    execution_duration_ms INTEGER
);

-- 经验知识表
CREATE TABLE experiences (
    id SERIAL PRIMARY KEY,
    context_hash VARCHAR(255), -- 上下文的哈希值
    action_sequence JSONB, -- 操作序列
    success_rate FLOAT, -- 成功率
    usage_count INTEGER DEFAULT 1,
    last_used TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tags TEXT[], -- 标签
    confidence_score FLOAT -- 置信度
);

-- 错误模式表
CREATE TABLE error_patterns (
    id SERIAL PRIMARY KEY,
    error_type VARCHAR(100),
    error_context JSONB, -- 错误发生的上下文
    error_description TEXT,
    recovery_strategy JSONB, -- 恢复策略
    occurrence_count INTEGER DEFAULT 1,
    last_occurred TIMESTAMP,
    resolution_success_rate FLOAT
);

-- 工具使用记录表
CREATE TABLE tool_usage (
    id SERIAL PRIMARY KEY,
    tool_name VARCHAR(100),
    tool_version VARCHAR(50),
    usage_context JSONB,
    performance_metrics JSONB, -- 性能指标
    user_satisfaction INTEGER, -- 1-5评分
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 向量数据库：Qdrant
```python
# 经验向量化存储
class ExperienceVector:
    context_embedding: List[float]  # 上下文向量
    action_embedding: List[float]   # 操作向量
    metadata: Dict[str, Any]        # 元数据
    success_score: float            # 成功分数
```

### 3. RAG系统设计

#### 向量化策略
```python
class QYuanRAG:
    def __init__(self):
        self.text_encoder = SentenceTransformer('all-MiniLM-L6-v2')
        self.image_encoder = CLIPModel.from_pretrained('openai/clip-vit-base-patch32')
        self.vector_db = QdrantClient("localhost", port=6333)
    
    def encode_experience(self, experience: Experience) -> ExperienceVector:
        """将经验编码为向量"""
        context_text = self.extract_context_text(experience)
        context_embedding = self.text_encoder.encode(context_text)
        
        if experience.screenshot:
            image_embedding = self.encode_screenshot(experience.screenshot)
            context_embedding = np.concatenate([context_embedding, image_embedding])
        
        return ExperienceVector(
            context_embedding=context_embedding.tolist(),
            action_embedding=self.encode_actions(experience.actions),
            metadata=experience.metadata,
            success_score=experience.success_score
        )
    
    def retrieve_similar_experiences(self, current_context: Context, top_k: int = 5) -> List[Experience]:
        """检索相似经验"""
        query_vector = self.encode_context(current_context)
        results = self.vector_db.search(
            collection_name="experiences",
            query_vector=query_vector,
            limit=top_k
        )
        return [self.decode_experience(result) for result in results]
```

### 4. 协议选择

#### 基础控制层：自建协议
```python
class QYuanControlProtocol:
    """QYuan专用控制协议"""
    
    def __init__(self):
        self.mouse_controller = MouseController()
        self.keyboard_controller = KeyboardController()
        self.vision_system = VisionSystem()
    
    async def execute_action(self, action: Action) -> ActionResult:
        """执行操作并返回结果"""
        pre_state = await self.capture_state()
        
        try:
            if action.type == "MOUSE_CLICK":
                result = await self.mouse_controller.click(action.coordinates)
            elif action.type == "KEYBOARD_INPUT":
                result = await self.keyboard_controller.type(action.text)
            elif action.type == "SCREENSHOT":
                result = await self.vision_system.capture_screen()
            
            post_state = await self.capture_state()
            
            return ActionResult(
                success=True,
                result=result,
                pre_state=pre_state,
                post_state=post_state,
                execution_time=time.time() - action.start_time
            )
        except Exception as e:
            return ActionResult(
                success=False,
                error=str(e),
                pre_state=pre_state
            )
```

#### 高级工具层：MCP兼容
```python
class MCPToolAdapter:
    """MCP工具适配器"""
    
    def __init__(self):
        self.mcp_servers = {}
        self.tool_registry = ToolRegistry()
    
    def register_mcp_server(self, name: str, server_config: MCPServerConfig):
        """注册MCP服务器"""
        self.mcp_servers[name] = MCPClient(server_config)
        tools = self.mcp_servers[name].list_tools()
        for tool in tools:
            self.tool_registry.register_tool(tool)
    
    async def call_mcp_tool(self, tool_name: str, parameters: Dict) -> Any:
        """调用MCP工具"""
        server = self.find_server_for_tool(tool_name)
        return await server.call_tool(tool_name, parameters)
```

## 系统架构设计

### 1. 核心模块架构
```
QYuan Core
├── Perception Engine (感知引擎)
│   ├── Vision System (视觉系统)
│   ├── State Monitor (状态监控)
│   └── Context Analyzer (上下文分析)
├── Decision Engine (决策引擎)
│   ├── Goal Planner (目标规划)
│   ├── Action Selector (行动选择)
│   └── Risk Assessor (风险评估)
├── Execution Engine (执行引擎)
│   ├── Basic Control (基础控制)
│   ├── Tool Manager (工具管理)
│   └── Operation Monitor (操作监控)
├── Learning Engine (学习引擎)
│   ├── Experience Recorder (经验记录)
│   ├── Pattern Extractor (模式提取)
│   └── Knowledge Updater (知识更新)
└── Communication Engine (通信引擎)
    ├── Network Server (网络服务)
    ├── Client Manager (客户端管理)
    └── Message Router (消息路由)
```

### 2. 数据流架构
```
Client Device → WebSocket → Message Queue → Decision Engine
                                ↓
                          Perception Engine
                                ↓
                          Execution Engine
                                ↓
                          Learning Engine
                                ↓
                          Database & RAG
```

### 3. 部署架构
```
QYuan Host Machine:
├── QYuan Core Service (主服务)
├── PostgreSQL Database (主数据库)
├── Qdrant Vector DB (向量数据库)
├── Redis Cache (缓存)
├── WebSocket Server (通信服务)
└── MCP Servers (工具服务)
    ├── Auto Mouse Server
    ├── Auto Keyboard Server
    └── Auto Vision Server

Client Devices:
├── Web Client (浏览器)
├── Mobile App (手机应用)
└── Desktop Client (桌面应用)
```

## 性能与安全考虑

### 1. 性能优化
- **异步处理**：所有I/O操作使用异步模式
- **缓存策略**：Redis缓存频繁访问的数据
- **连接池**：数据库连接池管理
- **批处理**：批量处理数据库操作

### 2. 安全机制
- **权限分级**：操作权限分级管理
- **操作审计**：所有操作完整记录
- **紧急停止**：多重紧急停止机制
- **数据加密**：敏感数据加密存储

### 3. 监控告警
- **系统监控**：CPU、内存、磁盘使用率
- **服务监控**：各服务健康状态
- **操作监控**：异常操作检测
- **性能监控**：响应时间、成功率统计

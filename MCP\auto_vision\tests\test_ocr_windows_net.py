#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试Windows OCR (.NET)实现
"""

import os
import sys
import time
import platform

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 导入OCR文本识别模块
from src.ocr_recognition.ocr_recognition_factory import OCRRecognitionFactory


def test_windows_ocr_net():
    """测试Windows OCR (.NET)实现"""
    print("\n测试Windows OCR (.NET)实现...")

    # 检查是否为Windows平台
    if platform.system() != "Windows":
        print("Windows OCR仅支持Windows平台，跳过测试")
        return False

    try:
        # 创建OCR对象
        ocr = OCRRecognitionFactory.create(engine="windows_net", lang="en-US")

        # 获取引擎信息
        engine_info = ocr.get_engine_info()
        print("\n引擎信息:")
        print(f"名称: {engine_info['name']}")
        print(f"版本: {engine_info['version']}")
        print(f"当前语言: {engine_info['current_language']}")
        print(f"支持的语言: {engine_info['supported_languages']}")

        # 创建测试图像
        from PIL import Image, ImageDraw, ImageFont
        img = Image.new('RGB', (800, 600), color=(255, 255, 255))
        draw = ImageDraw.Draw(img)
        draw.text((50, 50), "Hello World! 你好，世界！", fill=(0, 0, 0))
        test_image_path = os.path.join(parent_dir, "screenshots", "test_image_net.png")
        os.makedirs(os.path.dirname(test_image_path), exist_ok=True)
        img.save(test_image_path)
        print(f"\n创建测试图像: {test_image_path}")

        # 识别文本
        print(f"\n识别图像文本: {test_image_path}")
        start_time = time.time()
        results = ocr.recognize_text(test_image_path, return_format="structured")
        end_time = time.time()

        # 打印识别结果
        print(f"识别耗时: {end_time - start_time:.2f}秒")
        print(f"识别到{len(results)}个文本块")

        # 打印所有结果
        for i, item in enumerate(results):
            print(f"{i+1}. 文本: {item['text']}")
            print(f"   位置: {item['box']}")
            print(f"   置信度: {item['confidence']}")
            print("-" * 50)

        return len(results) > 0

    except Exception as e:
        print(f"Windows OCR (.NET)测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_uac_dialog():
    """测试识别UAC对话框"""
    print("\n测试识别UAC对话框...")

    # 检查是否为Windows平台
    if platform.system() != "Windows":
        print("Windows OCR仅支持Windows平台，跳过测试")
        return False

    # 检查是否存在UAC对话框图像
    screenshot_path = os.path.join(parent_dir, "screenshots", "uac_dialog.png")
    if not os.path.exists(screenshot_path):
        print(f"UAC对话框图像不存在: {screenshot_path}")
        return False

    try:
        # 创建OCR对象
        ocr = OCRRecognitionFactory.create(engine="windows_net", lang="en-US")

        # 识别文本
        print(f"\n识别UAC对话框文本: {screenshot_path}")
        start_time = time.time()
        results = ocr.recognize_text(screenshot_path, return_format="structured")
        end_time = time.time()

        # 打印识别结果
        print(f"识别耗时: {end_time - start_time:.2f}秒")
        print(f"识别到{len(results)}个文本块")

        # 打印所有结果
        for i, item in enumerate(results):
            print(f"{i+1}. 文本: {item['text']}")
            print(f"   位置: {item['box']}")
            print(f"   置信度: {item['confidence']}")
            print("-" * 50)

        # 检查是否识别到UAC相关文本
        uac_keywords = ["用户账户控制", "User Account Control", "UAC", "管理员", "Administrator", 
                       "权限", "permission", "是否允许", "允许", "Allow", "更改", "Change"]
        
        found_uac = False
        for item in results:
            for keyword in uac_keywords:
                if keyword.lower() in item['text'].lower():
                    print(f"\n找到UAC相关文本: {item['text']}")
                    found_uac = True
                    break
            if found_uac:
                break
        
        if not found_uac:
            print("\n未找到UAC相关文本")
        
        return found_uac

    except Exception as e:
        print(f"Windows OCR (.NET)测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # 测试Windows OCR (.NET)实现
    result1 = test_windows_ocr_net()
    
    # 测试识别UAC对话框
    result2 = test_uac_dialog()
    
    # 输出总结果
    print("\n测试结果:")
    print(f"Windows OCR (.NET)实现: {'成功' if result1 else '失败'}")
    print(f"识别UAC对话框: {'成功' if result2 else '失败'}")

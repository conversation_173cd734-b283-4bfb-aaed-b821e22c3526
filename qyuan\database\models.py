#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型定义

定义QYuan的记忆管理系统数据模型，包括分层记忆架构：
- 原始记忆：对话、操作、事件记录
- 知识记忆：提炼的知识和模式
- 智慧记忆：高层次的经验和决策规则
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Float, Boolean, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum
import uuid

Base = declarative_base()


class MemoryType(Enum):
    """记忆类型枚举"""
    RAW = "raw"           # 原始记忆
    KNOWLEDGE = "knowledge"  # 知识记忆
    WISDOM = "wisdom"     # 智慧记忆


class EventType(Enum):
    """事件类型枚举"""
    CONVERSATION = "conversation"  # 对话事件
    ACTION = "action"             # 操作事件
    SYSTEM = "system"             # 系统事件
    LEARNING = "learning"         # 学习事件
    ERROR = "error"               # 错误事件


class Memory(Base):
    """记忆基础表"""
    __tablename__ = 'memories'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    memory_type = Column(String(20), nullable=False)  # MemoryType
    event_type = Column(String(20), nullable=False)   # EventType
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    metadata = Column(JSON, default=dict)
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 重要性和相关性评分
    importance_score = Column(Float, default=0.0)
    relevance_score = Column(Float, default=0.0)
    
    # 关联信息
    session_id = Column(String(36), nullable=True)
    parent_memory_id = Column(String(36), ForeignKey('memories.id'), nullable=True)
    
    # 标签和分类
    tags = Column(JSON, default=list)
    category = Column(String(50), nullable=True)
    
    # 状态
    is_active = Column(Boolean, default=True)
    is_archived = Column(Boolean, default=False)
    
    # 关系
    parent_memory = relationship("Memory", remote_side=[id])
    child_memories = relationship("Memory", back_populates="parent_memory")


class Conversation(Base):
    """对话记录表"""
    __tablename__ = 'conversations'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String(36), nullable=False)
    
    # 对话内容
    user_message = Column(Text, nullable=False)
    assistant_response = Column(Text, nullable=False)
    
    # 意图分析
    user_intent = Column(String(50), nullable=True)
    intent_confidence = Column(Float, default=0.0)
    
    # 上下文信息
    context = Column(JSON, default=dict)
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 评价信息
    user_feedback = Column(String(20), nullable=True)  # positive, negative, neutral
    response_quality = Column(Float, default=0.0)
    
    # 关联记忆
    memory_id = Column(String(36), ForeignKey('memories.id'), nullable=True)
    memory = relationship("Memory")


class ActionRecord(Base):
    """操作记录表"""
    __tablename__ = 'action_records'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String(36), nullable=False)
    
    # 操作信息
    action_type = Column(String(50), nullable=False)  # mouse, keyboard, vision
    action_name = Column(String(100), nullable=False)
    parameters = Column(JSON, default=dict)
    
    # 执行结果
    success = Column(Boolean, nullable=False)
    result = Column(JSON, default=dict)
    error_message = Column(Text, nullable=True)
    
    # 时间信息
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    duration_ms = Column(Integer, nullable=True)
    
    # 上下文信息
    context = Column(JSON, default=dict)
    goal_id = Column(String(36), nullable=True)
    
    # 关联记忆
    memory_id = Column(String(36), ForeignKey('memories.id'), nullable=True)
    memory = relationship("Memory")


class KnowledgePattern(Base):
    """知识模式表"""
    __tablename__ = 'knowledge_patterns'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # 模式信息
    pattern_type = Column(String(50), nullable=False)  # success, failure, preference
    pattern_name = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)
    
    # 模式数据
    conditions = Column(JSON, default=dict)  # 触发条件
    actions = Column(JSON, default=dict)     # 相关操作
    outcomes = Column(JSON, default=dict)    # 结果模式
    
    # 统计信息
    occurrence_count = Column(Integer, default=1)
    success_rate = Column(Float, default=0.0)
    confidence_score = Column(Float, default=0.0)
    
    # 时间信息
    first_observed = Column(DateTime, default=datetime.utcnow)
    last_observed = Column(DateTime, default=datetime.utcnow)
    
    # 关联记忆
    source_memories = Column(JSON, default=list)  # 来源记忆ID列表
    
    # 状态
    is_validated = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)


class Context(Base):
    """上下文管理表"""
    __tablename__ = 'contexts'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String(36), nullable=False)
    
    # 上下文类型
    context_type = Column(String(50), nullable=False)  # conversation, task, goal
    
    # 上下文数据
    current_state = Column(JSON, default=dict)
    history = Column(JSON, default=list)
    
    # 活跃状态
    is_active = Column(Boolean, default=True)
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    expires_at = Column(DateTime, nullable=True)
    
    # 元数据
    metadata = Column(JSON, default=dict)


class MemoryIndex(Base):
    """记忆索引表 - 用于快速检索"""
    __tablename__ = 'memory_indexes'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    memory_id = Column(String(36), ForeignKey('memories.id'), nullable=False)
    
    # 索引类型
    index_type = Column(String(50), nullable=False)  # keyword, semantic, temporal
    
    # 索引值
    index_key = Column(String(200), nullable=False)
    index_value = Column(Text, nullable=False)
    
    # 权重
    weight = Column(Float, default=1.0)
    
    # 关系
    memory = relationship("Memory")


# 创建所有表的函数
def create_tables(engine):
    """创建所有数据库表"""
    Base.metadata.create_all(engine)


# 删除所有表的函数
def drop_tables(engine):
    """删除所有数据库表"""
    Base.metadata.drop_all(engine)

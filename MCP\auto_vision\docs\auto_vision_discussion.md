# Auto Vision 功能讨论文档

## 项目背景

项目目标是让LLM像人一样操作电脑，已实现键盘鼠标操作，下一步是实现视觉功能，让LLM能够"看见"屏幕内容。

## 当前实现状态

### 已实现的功能

1. **屏幕截图**：
   - 全屏截图和区域截图
   - 带坐标的截图（添加经纬线和坐标标注）
   - 截图保存和base64编码返回
   - 支持通过视觉LLM分析截图内容

2. **OCR文本识别**：
   - 支持多种OCR引擎（Windows OCR、EasyOCR、百度OCR）
   - 自动选择最佳引擎和备用引擎机制
   - 支持结构化返回格式，包含文本、位置和置信度信息
   - 支持指定区域的文本识别

3. **UI元素识别**：
   - 窗口级操作（激活、最大化、最小化等）
   - 窗口内元素识别和坐标获取
   - 支持通过文本查找元素
   - 支持获取窗口Z序和可见性信息

4. **视觉LLM分析**：
   - 使用Gemini API分析截图
   - 支持全屏分析和区域分析
   - 支持针对特定问题的分析
   - 支持组合分析（同时获取OCR、UI元素和视觉LLM分析结果）

5. **MCP服务器**：
   - 提供屏幕截图、OCR文本识别和UI元素识别功能的MCP服务器
   - 支持stdio和tcp传输方式
   - 提供多种工具函数，方便LLM调用

### 技术实现

1. **屏幕截图**：
   - 使用`PIL`和`pyautogui`进行屏幕截图
   - 支持全屏和区域截图
   - 添加了图像增强功能，包括经纬线和坐标标注
   - 集成了Gemini视觉LLM进行屏幕分析

2. **OCR文本识别**：
   - 支持多种OCR引擎：
     - Windows OCR API（通过comtypes和pythonnet两种方式实现）
     - EasyOCR
     - 百度OCR
   - 实现了备用引擎机制，当主引擎失败时自动切换到备用引擎
   - 支持结构化返回格式，包含文本、位置和置信度信息

3. **UI元素识别**：
   - 使用Win32GUI实现窗口级操作
   - 使用PyWinAuto实现窗口内元素识别
   - 支持获取UI元素的坐标信息
   - 支持通过文本查找元素

4. **视觉LLM分析**：
   - 使用Gemini API分析截图
   - 图像增强（添加经纬线和坐标标注）
   - 屏幕分析服务

## 方案讨论

### 对现有系统的理解

1. **MCP Server架构**：
   - 采用客户端-服务器架构，服务器提供工具、资源和提示给客户端
   - 支持多种传输机制（Stdio、HTTP与SSE）
   - 使用JSON-RPC 2.0进行消息交换

2. **已实现的功能**：
   - **鼠标控制**：移动、点击、拖拽、滚动等操作
   - **键盘控制**：按键、释放、输入文本、组合键等操作
   - 这些功能都已经实现了仿人化设计，包括随机偏移、随机等待时间等

3. **仿人化设计**：
   - 添加微小随机偏移
   - 添加随机等待时间
   - 将复合操作拆解为基本操作
   - 轨迹模拟和混合

### 视觉功能方案

#### 1. 截图功能

截图是基础功能，但仅仅截图是不够的，因为MCP协议目前并不支持直接传输图像给LLM。需要将截图转换为LLM可以理解的文本描述或结构化数据。

**当前实现**：
- 使用`PIL`和`pyautogui`进行屏幕截图
- 支持全屏和区域截图
- 添加了图像增强功能，包括经纬线和坐标标注
- 集成了Gemini视觉LLM进行屏幕分析
- 支持截图保存和base64编码返回

```python
# 全屏截图示例
screenshot, screenshot_path = full_capture.capture_screen(suffix="example")

# 区域截图示例
region = (100, 100, 400, 300)  # x, y, width, height
screenshot, screenshot_path = region_capture.capture_region(
    x=region[0], y=region[1], width=region[2], height=region[3], suffix="region_example"
)

# 带坐标的全屏截图示例
result = full_capture.capture_fullscreen_with_coordinates()

# 带坐标的区域截图示例
result = region_capture.capture_region_with_coordinates(
    x=region[0], y=region[1], width=region[2], height=region[3]
)
```

#### 2. OCR文本识别

OCR是让LLM"看见"屏幕内容的基础功能。关注点应该是准确率、效率和坐标定位。

**当前实现**：
- 支持多种OCR引擎：Windows OCR、EasyOCR、百度OCR
- 自动选择最佳引擎和备用引擎机制
- 支持结构化返回格式，包含文本、位置和置信度信息
- 支持指定区域的文本识别

##### 技术选择：

1. **Windows OCR API**:
   - 优点：系统集成、速度快、准确率高
   - 缺点：仅限Windows平台
   - 坐标支持：可以获取文本元素的位置信息

2. **EasyOCR**:
   - 优点：基于深度学习、多语言支持好、对复杂背景适应性强
   - 缺点：初始加载较慢、资源消耗较大
   - 坐标支持：提供详细的文本区域坐标

3. **百度OCR**:
   - 优点：准确率高、支持多语言、API稳定
   - 缺点：需要网络连接、有API调用限制
   - 坐标支持：提供详细的文本区域坐标

```python
# OCR文本识别示例
from src.ocr_recognition import OCRRecognition

# 创建OCR对象（自动选择引擎）
ocr = OCRRecognition(engine="auto", fallback=True)

# 从图像文件识别文本
results = ocr.recognize_text("screenshot.png", return_format="structured")

# 识别指定区域的文本
region = (100, 100, 400, 300)  # x, y, width, height
results = ocr.recognize_text(image, region=region, return_format="structured")

# 获取纯文本结果
text = ocr.recognize_text(image, return_format="text")
```

#### 3. UI元素识别（Windows平台）

Windows平台上的UI元素识别可以通过UI Automation API实现，这是一个非常强大的功能。

**当前实现**：
- 使用Win32GUI实现窗口级操作
- 使用PyWinAuto实现窗口内元素识别
- 支持获取UI元素的坐标信息
- 支持通过文本查找元素
- 支持获取窗口Z序和可见性信息

##### 当前能力：

1. **元素层次结构**:
   - 可以获取完整的UI元素树
   - 包括窗口、按钮、输入框、复选框、列表等
   - 支持获取元素的父子关系和兄弟关系

2. **元素属性**:
   - 名称、ID、类名、值、状态（启用/禁用）
   - 位置和大小（坐标和尺寸）
   - 模式（可点击、可编辑等）

3. **元素查找**:
   - 按名称、ID、类名查找
   - 按角色（按钮、输入框等）查找
   - 按位置查找
   - 组合条件查找

4. **窗口操作**:
   - 激活窗口
   - 最大化/最小化/还原窗口
   - 调整窗口大小
   - 移动窗口

```python
# UI元素识别示例
from src.ui_automation import UIAutomation

# 创建UI自动化对象，默认使用win32gui后端
ui = UIAutomation()

# 获取前台窗口
window = ui.get_foreground_window()

# 通过标题获取窗口
window = ui.get_window_by_title("记事本")

# 获取UI元素（必须指定元素类型）
elements = ui.get_ui_elements(window, element_type="Button", depth=3, visible_only=True)

# 通过文本查找元素（必须指定元素类型）
properties = ui.find_element_by_text("文件", element_type="Button")

# 窗口操作
ui.activate_window(window)  # 激活窗口
ui.maximize_window(window)  # 最大化窗口
```

#### 4. 视觉LLM分析

视觉LLM分析是让LLM"看见"屏幕内容的高级功能，可以将截图发送给支持视觉的LLM（如Gemini），让其分析截图内容并返回文本描述。

**当前实现**：
- 使用Gemini API分析截图
- 支持全屏分析和区域分析
- 支持针对特定问题的分析
- 支持组合分析（同时获取OCR、UI元素和视觉LLM分析结果）

```python
# 视觉LLM分析示例
from src.screen_capture import ScreenCapture

# 创建屏幕分析对象
screen_capture = ScreenCapture()  # ScreenCapture 现在是 ScreenAnalyzer 的别名

# 捕获并分析全屏
result = screen_capture.capture_and_analyze_screen(
    question="这个屏幕上有什么内容？请描述主要元素及其位置。"
)
print(f"分析结果:\n{result['analysis']}")

# 捕获并分析区域
region = (100, 100, 400, 300)  # x, y, width, height
result = screen_capture.capture_and_analyze_screen(
    question="这个区域中有什么内容？请描述主要元素及其位置。",
    region=region
)
print(f"分析结果:\n{result['analysis']}")
```

#### 5. MCP服务器

MCP服务器是将所有功能集成在一起，提供给LLM使用的接口。

**当前实现**：
- 提供屏幕截图、OCR文本识别和UI元素识别功能的MCP服务器
- 支持stdio和tcp传输方式
- 提供多种工具函数，方便LLM调用

```python
# 启动MCP服务器示例
from src.server import AutoVisionServer

# 创建并运行服务器
server = AutoVisionServer(ocr_engine="auto", ui_backend="win32gui", vision_llm=True)
server.run(transport="stdio")
```

## 优化建议

1. **性能优化**：
   - OCR引擎预加载和缓存
   - UI元素树缓存和增量更新
   - 区域分析而非全屏分析

2. **准确性优化**：
   - OCR预处理（图像增强、去噪、二值化）
   - 多引擎结果融合
   - 上下文感知识别（如已知是数字区域，优化数字识别）

3. **用户体验**：
   - 提供调试模式，可视化识别结果
   - 详细的错误信息和建议
   - 自适应超时和重试机制

4. **安全性**：
   - 限制可访问的窗口和区域
   - 敏感信息过滤
   - 操作频率限制

## 总结

Auto Vision已经实现了相当强大的视觉感知功能：

1. **屏幕截图**：捕获全屏或指定区域，支持带坐标的截图
2. **OCR文本识别**：支持多种OCR引擎，识别屏幕上的文本并提供位置信息
3. **UI元素识别**：获取UI元素树，包括元素类型、属性和位置
4. **视觉LLM分析**：使用Gemini API分析截图，提供文本描述
5. **MCP服务器**：提供统一的接口，方便LLM调用

这些功能结合起来，可以让LLM"看见"屏幕内容，理解UI结构，并进行精确的交互。特别是UI Automation API提供的能力非常强大，可以识别大多数标准Windows应用程序的UI元素，包括按钮、输入框、菜单、列表等。

未来的发展方向包括：
1. 进一步提高OCR识别的准确率和效率
2. 增强视觉LLM分析的能力，提供更详细的屏幕描述
3. 优化UI元素识别的性能和稳定性
4. 增加更多的视觉分析功能，如图像识别、对象检测等

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OCR文本识别模块 (百度OCR实现)

提供基于百度OCR API的文本识别功能。
"""

import os
import sys
import time
import json
import base64
import numpy as np
from PIL import Image
import requests
from aip import AipOcr

# 导入基础接口类
from .ocr_recognition_base import OCRRecognitionBase


class OCRRecognitionBaidu(OCRRecognitionBase):
    """OCR文本识别类，提供基于百度OCR API的文本识别功能"""

    def __init__(self, app_id=None, api_key=None, secret_key=None, use_high_accuracy=False):
        """
        初始化百度OCR引擎

        Args:
            app_id: 百度OCR应用的AppID
            api_key: 百度OCR应用的API Key
            secret_key: 百度OCR应用的Secret Key
            use_high_accuracy: 是否使用高精度版本，默认为False（使用标准版）
        """
        # 设置默认值
        self.app_id = app_id or "118937659"
        self.api_key = api_key or "QhOoiknJ7pmZKkoQHj9WB75P"
        self.secret_key = secret_key or "EUDavhzvy7MAzAZsmMLjLL0NNYsnl9Tt"
        self.use_high_accuracy = use_high_accuracy
        self.client = None
        self.access_token = None
        self.token_expire_time = 0

        # 初始化百度OCR引擎
        self._init_engine()

    def _init_engine(self):
        """初始化百度OCR引擎"""
        try:
            # 创建AipOcr客户端
            self.client = AipOcr(self.app_id, self.api_key, self.secret_key)

            # 设置网络连接超时时间
            self.client.setConnectionTimeoutInMillis(5000)
            self.client.setSocketTimeoutInMillis(10000)

            print("百度OCR引擎初始化成功")
        except Exception as e:
            print(f"百度OCR引擎初始化失败: {e}")
            raise

    def _get_access_token(self):
        """
        获取百度OCR API的access_token

        Returns:
            str: access_token
        """
        # 如果已有有效的access_token，直接返回
        if self.access_token and time.time() < self.token_expire_time:
            return self.access_token

        # 获取新的access_token
        try:
            url = "https://aip.baidubce.com/oauth/2.0/token"
            params = {
                "grant_type": "client_credentials",
                "client_id": self.api_key,
                "client_secret": self.secret_key
            }
            response = requests.post(url, params=params)
            result = response.json()

            if "access_token" in result:
                self.access_token = result["access_token"]
                # 设置token过期时间（提前10分钟过期，以防万一）
                self.token_expire_time = time.time() + result.get("expires_in", 2592000) - 600
                return self.access_token
            else:
                raise ValueError(f"获取access_token失败: {result}")
        except Exception as e:
            print(f"获取access_token失败: {e}")
            raise

    def recognize_text(self, image, region=None, return_format="structured"):
        """
        识别图像中的文本

        Args:
            image: 图像数据或路径
            region: 识别区域 [x, y, width, height]，为None则识别整个图像
            return_format: 返回格式，支持 "text", "structured"

        Returns:
            根据return_format返回不同格式的结果:
            - "text": 返回纯文本字符串
            - "structured": 返回结构化数据，包含文本、位置和置信度
        """
        # 处理图像输入
        if isinstance(image, str):
            # 如果是路径，则加载图像
            image = Image.open(image)

        # 处理区域
        if region:
            x, y, width, height = region
            image = image.crop((x, y, x + width, y + height))

        # 将图像转换为字节流
        img_byte_arr = self._image_to_bytes(image)

        try:
            # 调用百度OCR API
            if self.use_high_accuracy:
                # 使用高精度版本
                result = self.client.accurate(img_byte_arr)
            else:
                # 使用标准版本
                result = self.client.general(img_byte_arr)

            # 检查是否有错误
            if "error_code" in result:
                error_msg = f"错误码: {result['error_code']}, 错误信息: {result.get('error_msg', '未知错误')}"
                print(f"百度OCR识别失败: {error_msg}")
                return [] if return_format == "structured" else ""

            # 处理结果
            if return_format == "text":
                # 返回纯文本
                text = ""
                for word_info in result.get("words_result", []):
                    text += word_info.get("words", "") + "\n"
                return text.strip()
            else:
                # 返回结构化数据
                structured_result = []
                for word_info in result.get("words_result", []):
                    # 获取文本
                    text = word_info.get("words", "")

                    # 获取位置信息
                    location = word_info.get("location", {})
                    left = location.get("left", 0)
                    top = location.get("top", 0)
                    width = location.get("width", 0)
                    height = location.get("height", 0)

                    # 构建边界框
                    box = [left, top, left + width, top + height]

                    # 获取置信度
                    confidence = word_info.get("probability", {}).get("average", 0)
                    if confidence:
                        confidence = float(confidence)
                    else:
                        confidence = 0.9  # 默认置信度

                    # 构建单词列表（百度OCR不提供单词级别的信息，所以这里将整行文本作为一个单词）
                    words = [{
                        "text": text,
                        "box": box,
                        "confidence": confidence
                    }]

                    # 添加到结果列表
                    structured_result.append({
                        "text": text,
                        "box": box,
                        "confidence": confidence,
                        "words": words
                    })

                return structured_result

        except Exception as e:
            print(f"百度OCR识别失败: {e}")
            return [] if return_format == "structured" else ""

    def _image_to_bytes(self, image):
        """
        将PIL图像转换为字节流

        Args:
            image: PIL图像对象

        Returns:
            bytes: 图像的字节流
        """
        # 确保图像是RGB模式
        if image.mode != "RGB":
            image = image.convert("RGB")

        # 将图像转换为字节流
        import io
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='JPEG')
        img_byte_arr.seek(0)

        return img_byte_arr.read()

    def get_engine_info(self):
        """
        获取OCR引擎信息

        Returns:
            dict: 包含引擎名称、版本、支持的语言等信息
        """
        return {
            "name": "百度OCR",
            "version": "通用文字识别" + ("（高精度含位置版）" if self.use_high_accuracy else "（标准含位置版）"),
            "supported_languages": self.get_supported_languages(),
            "app_id": self.app_id
        }

    def get_supported_languages(self):
        """
        获取支持的语言列表

        Returns:
            list: 支持的语言代码列表
        """
        # 百度OCR支持的语言
        return ["zh", "en", "ja", "ko", "fr", "es", "pt", "de", "ru", "it"]


# 测试代码
if __name__ == "__main__":
    # 创建OCR对象
    ocr = OCRRecognitionBaidu(use_high_accuracy=False)

    # 测试图像路径
    test_image_path = "screenshots/screenshot_latest.png"

    # 如果测试图像不存在，则退出
    if not os.path.exists(test_image_path):
        print(f"测试图像不存在: {test_image_path}")
        sys.exit(1)

    # 识别文本
    results = ocr.recognize_text(test_image_path, return_format="structured")

    # 打印识别结果
    print("\n识别结果:")
    for line in results:
        print(f"文本: {line['text']}")
        print(f"位置: {line['box']}")
        print(f"置信度: {line['confidence']}")
        print("-" * 50)

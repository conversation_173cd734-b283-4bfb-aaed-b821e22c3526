#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI元素识别工厂模块

提供创建不同后端UI自动化对象的工厂类。
"""

import os
import sys

# 添加当前目录到路径，确保可以导入同级模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


class UIAutomationFactory:
    """UI元素识别工厂类，用于创建不同后端的UI自动化对象"""

    @staticmethod
    def create(backend="win32gui"):
        """
        创建UI自动化对象

        Args:
            backend: 后端实现，支持 "pywinauto", "win32gui"

        Returns:
            UI自动化对象
        """
        if backend == "pywinauto":
            try:
                from .ui_automation_pywinauto import UIAutomationPyWinAuto
                return UIAutomationPyWinAuto()
            except ImportError as e:
                print(f"导入PyWinAuto后端失败: {e}")
                print("请确保ui_automation_pywinauto.py在同一目录下")
                raise

        elif backend == "win32gui":
            try:
                from .ui_automation_win32 import UIAutomationWin32
                return UIAutomationWin32()
            except ImportError as e:
                print(f"导入Win32GUI后端失败: {e}")
                print("请确保ui_automation_win32.py在同一目录下")
                raise

        else:
            raise ValueError(f"不支持的UI自动化后端: {backend}")

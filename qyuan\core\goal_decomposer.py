# -*- coding: utf-8 -*-
"""
智能目标分解器
自动将复杂目标分解为可执行的子目标，支持动态调整和优化
严格按照代码规范的单一职责原则
"""

import re
import uuid
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta

from .goal_tracker import Goal, SubGoal, GoalPriority
from ..utils.logger import get_logger


class GoalDecomposer:
    """智能目标分解器"""
    
    def __init__(self, llm_client=None):
        """初始化目标分解器"""
        self.llm_client = llm_client
        self.logger = get_logger(self.__class__.__name__)
        
        # 分解规则库
        self.decomposition_rules = {
            "文件操作": self._decompose_file_operation,
            "网页操作": self._decompose_web_operation,
            "应用程序操作": self._decompose_app_operation,
            "数据处理": self._decompose_data_processing,
            "搜索任务": self._decompose_search_task,
            "通信任务": self._decompose_communication_task,
        }
        
        # 关键词映射
        self.keyword_mapping = {
            "打开": "应用程序操作",
            "搜索": "搜索任务",
            "下载": "文件操作",
            "发送": "通信任务",
            "浏览": "网页操作",
            "编辑": "文件操作",
            "创建": "文件操作",
            "删除": "文件操作",
            "复制": "文件操作",
            "移动": "文件操作",
            "查找": "搜索任务",
            "邮件": "通信任务",
            "消息": "通信任务",
        }
        
        # 复杂度评估器
        self.complexity_evaluator = ComplexityEvaluator()
        
        # 依赖关系分析器
        self.dependency_analyzer = DependencyAnalyzer()
    
    async def decompose_goal(self, goal_description: str, context: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """分解目标为子目标"""
        self.logger.info(f"开始分解目标: {goal_description}")
        
        try:
            # 1. 分析目标类型
            goal_type = self._analyze_goal_type(goal_description)
            
            # 2. 评估复杂度
            complexity = await self.complexity_evaluator.evaluate(goal_description, context)
            
            # 3. 选择分解策略
            if complexity["level"] == "simple":
                # 简单目标不需要分解
                return []
            elif complexity["level"] == "medium":
                # 中等复杂度使用规则分解
                sub_goals = await self._rule_based_decomposition(goal_description, goal_type, context)
            else:
                # 高复杂度使用LLM分解
                sub_goals = await self._llm_based_decomposition(goal_description, context)
            
            # 4. 分析依赖关系
            sub_goals = await self.dependency_analyzer.analyze_dependencies(sub_goals)
            
            # 5. 优化子目标顺序
            sub_goals = self._optimize_sub_goal_order(sub_goals)
            
            self.logger.info(f"目标分解完成，生成 {len(sub_goals)} 个子目标")
            return sub_goals
            
        except Exception as e:
            self.logger.error(f"目标分解失败: {e}")
            return []
    
    async def refine_sub_goals(self, goal: Goal, execution_feedback: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """基于执行反馈优化子目标"""
        self.logger.debug(f"基于反馈优化子目标: {goal.title}")
        
        refinements = []
        
        # 分析执行反馈
        for feedback in execution_feedback:
            if feedback.get("success", False):
                continue
            
            # 分析失败原因
            failure_reason = feedback.get("error", "")
            failed_sub_goal_id = feedback.get("sub_goal_id")
            
            if failed_sub_goal_id:
                failed_sub_goal = goal.get_sub_goal(failed_sub_goal_id)
                if failed_sub_goal:
                    # 生成优化建议
                    suggestions = await self._generate_refinement_suggestions(
                        failed_sub_goal, failure_reason
                    )
                    refinements.extend(suggestions)
        
        return refinements
    
    def _analyze_goal_type(self, goal_description: str) -> str:
        """分析目标类型"""
        description_lower = goal_description.lower()
        
        # 基于关键词匹配
        for keyword, goal_type in self.keyword_mapping.items():
            if keyword in description_lower:
                return goal_type
        
        # 默认类型
        return "通用任务"
    
    async def _rule_based_decomposition(self, goal_description: str, goal_type: str, 
                                      context: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """基于规则的目标分解"""
        if goal_type in self.decomposition_rules:
            decomposer_func = self.decomposition_rules[goal_type]
            return await decomposer_func(goal_description, context)
        else:
            return await self._generic_decomposition(goal_description, context)
    
    async def _llm_based_decomposition(self, goal_description: str, 
                                     context: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """基于LLM的目标分解"""
        if not self.llm_client:
            self.logger.warning("LLM客户端不可用，使用通用分解")
            return await self._generic_decomposition(goal_description, context)
        
        try:
            # 构建LLM提示词
            prompt = self._build_decomposition_prompt(goal_description, context)
            
            # 调用LLM
            response = await self.llm_client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3
            )
            
            # 解析LLM响应
            sub_goals = self._parse_llm_response(response)
            
            return sub_goals
            
        except Exception as e:
            self.logger.error(f"LLM分解失败: {e}")
            return await self._generic_decomposition(goal_description, context)
    
    async def _decompose_file_operation(self, goal_description: str, 
                                      context: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分解文件操作目标"""
        sub_goals = []
        
        if "下载" in goal_description:
            sub_goals.extend([
                {"title": "打开浏览器", "description": "启动默认浏览器", "priority": GoalPriority.HIGH},
                {"title": "导航到下载页面", "description": "访问文件下载链接", "priority": GoalPriority.HIGH},
                {"title": "点击下载按钮", "description": "开始文件下载", "priority": GoalPriority.HIGH},
                {"title": "确认下载完成", "description": "验证文件已成功下载", "priority": GoalPriority.NORMAL},
            ])
        elif "创建" in goal_description:
            sub_goals.extend([
                {"title": "选择保存位置", "description": "确定文件保存路径", "priority": GoalPriority.HIGH},
                {"title": "创建新文件", "description": "生成新的文件", "priority": GoalPriority.HIGH},
                {"title": "编辑文件内容", "description": "添加或修改文件内容", "priority": GoalPriority.NORMAL},
                {"title": "保存文件", "description": "保存文件更改", "priority": GoalPriority.HIGH},
            ])
        
        return sub_goals
    
    async def _decompose_web_operation(self, goal_description: str, 
                                     context: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分解网页操作目标"""
        sub_goals = []
        
        if "搜索" in goal_description:
            sub_goals.extend([
                {"title": "打开浏览器", "description": "启动网页浏览器", "priority": GoalPriority.HIGH},
                {"title": "访问搜索引擎", "description": "导航到搜索网站", "priority": GoalPriority.HIGH},
                {"title": "输入搜索关键词", "description": "在搜索框中输入查询内容", "priority": GoalPriority.HIGH},
                {"title": "执行搜索", "description": "点击搜索按钮或按回车", "priority": GoalPriority.HIGH},
                {"title": "浏览搜索结果", "description": "查看和分析搜索结果", "priority": GoalPriority.NORMAL},
            ])
        elif "浏览" in goal_description:
            sub_goals.extend([
                {"title": "打开浏览器", "description": "启动网页浏览器", "priority": GoalPriority.HIGH},
                {"title": "输入网址", "description": "在地址栏输入目标网址", "priority": GoalPriority.HIGH},
                {"title": "加载页面", "description": "等待页面完全加载", "priority": GoalPriority.HIGH},
                {"title": "浏览页面内容", "description": "查看和交互页面内容", "priority": GoalPriority.NORMAL},
            ])
        
        return sub_goals
    
    async def _decompose_app_operation(self, goal_description: str, 
                                     context: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分解应用程序操作目标"""
        sub_goals = []
        
        if "打开" in goal_description:
            # 提取应用程序名称
            app_name = self._extract_app_name(goal_description)
            
            sub_goals.extend([
                {"title": f"启动{app_name}", "description": f"打开{app_name}应用程序", "priority": GoalPriority.HIGH},
                {"title": "等待应用加载", "description": "等待应用程序完全启动", "priority": GoalPriority.HIGH},
                {"title": "验证应用状态", "description": "确认应用程序正常运行", "priority": GoalPriority.NORMAL},
            ])
        
        return sub_goals
    
    async def _decompose_data_processing(self, goal_description: str, 
                                       context: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分解数据处理目标"""
        return [
            {"title": "准备数据源", "description": "获取和准备需要处理的数据", "priority": GoalPriority.HIGH},
            {"title": "数据预处理", "description": "清理和格式化数据", "priority": GoalPriority.HIGH},
            {"title": "执行数据处理", "description": "应用处理算法或操作", "priority": GoalPriority.HIGH},
            {"title": "验证处理结果", "description": "检查处理结果的正确性", "priority": GoalPriority.NORMAL},
            {"title": "保存处理结果", "description": "保存或导出处理后的数据", "priority": GoalPriority.NORMAL},
        ]
    
    async def _decompose_search_task(self, goal_description: str, 
                                   context: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分解搜索任务目标"""
        return [
            {"title": "确定搜索范围", "description": "明确搜索的目标和范围", "priority": GoalPriority.HIGH},
            {"title": "选择搜索工具", "description": "选择合适的搜索引擎或工具", "priority": GoalPriority.HIGH},
            {"title": "构建搜索查询", "description": "制定有效的搜索关键词", "priority": GoalPriority.HIGH},
            {"title": "执行搜索", "description": "进行实际的搜索操作", "priority": GoalPriority.HIGH},
            {"title": "筛选搜索结果", "description": "从结果中筛选相关信息", "priority": GoalPriority.NORMAL},
            {"title": "整理搜索结果", "description": "组织和总结找到的信息", "priority": GoalPriority.NORMAL},
        ]
    
    async def _decompose_communication_task(self, goal_description: str, 
                                          context: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分解通信任务目标"""
        if "邮件" in goal_description:
            return [
                {"title": "打开邮件客户端", "description": "启动邮件应用程序", "priority": GoalPriority.HIGH},
                {"title": "创建新邮件", "description": "开始撰写新邮件", "priority": GoalPriority.HIGH},
                {"title": "填写收件人", "description": "输入邮件接收者地址", "priority": GoalPriority.HIGH},
                {"title": "编写邮件内容", "description": "撰写邮件主题和正文", "priority": GoalPriority.HIGH},
                {"title": "发送邮件", "description": "发送邮件给收件人", "priority": GoalPriority.HIGH},
            ]
        else:
            return [
                {"title": "选择通信方式", "description": "确定使用的通信工具", "priority": GoalPriority.HIGH},
                {"title": "准备通信内容", "description": "组织要传达的信息", "priority": GoalPriority.HIGH},
                {"title": "发起通信", "description": "开始通信过程", "priority": GoalPriority.HIGH},
                {"title": "确认通信成功", "description": "验证信息已成功传达", "priority": GoalPriority.NORMAL},
            ]
    
    async def _generic_decomposition(self, goal_description: str, 
                                   context: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """通用目标分解"""
        return [
            {"title": "分析目标需求", "description": "理解和分析目标的具体要求", "priority": GoalPriority.HIGH},
            {"title": "准备执行环境", "description": "设置执行目标所需的环境", "priority": GoalPriority.HIGH},
            {"title": "执行主要操作", "description": "进行核心的操作步骤", "priority": GoalPriority.HIGH},
            {"title": "验证执行结果", "description": "检查操作是否成功完成", "priority": GoalPriority.NORMAL},
            {"title": "清理和总结", "description": "清理临时文件并总结结果", "priority": GoalPriority.LOW},
        ]
    
    def _extract_app_name(self, goal_description: str) -> str:
        """从目标描述中提取应用程序名称"""
        # 简单的应用程序名称提取
        common_apps = ["浏览器", "记事本", "计算器", "文件管理器", "邮件", "QQ", "微信", "Word", "Excel"]
        
        for app in common_apps:
            if app in goal_description:
                return app
        
        # 使用正则表达式提取可能的应用名称
        match = re.search(r'打开\s*([^\s，。！？]+)', goal_description)
        if match:
            return match.group(1)
        
        return "应用程序"
    
    def _build_decomposition_prompt(self, goal_description: str, context: Optional[Dict[str, Any]]) -> str:
        """构建LLM分解提示词"""
        prompt = f"""
请将以下目标分解为具体的、可执行的子目标：

目标：{goal_description}

要求：
1. 每个子目标都应该是具体的、可操作的步骤
2. 子目标之间应该有逻辑顺序
3. 每个子目标包含标题、描述和优先级
4. 优先级分为：HIGH（高）、NORMAL（普通）、LOW（低）

请以JSON格式返回，格式如下：
[
    {{"title": "子目标标题", "description": "详细描述", "priority": "HIGH"}},
    ...
]
"""
        
        if context:
            prompt += f"\n上下文信息：{context}"
        
        return prompt
    
    def _parse_llm_response(self, response: str) -> List[Dict[str, Any]]:
        """解析LLM响应"""
        try:
            import json
            
            # 尝试直接解析JSON
            if response.strip().startswith('['):
                return json.loads(response)
            
            # 提取JSON部分
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            
            # 如果无法解析，返回空列表
            return []
            
        except Exception as e:
            self.logger.error(f"解析LLM响应失败: {e}")
            return []
    
    def _optimize_sub_goal_order(self, sub_goals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """优化子目标顺序"""
        # 按优先级和依赖关系排序
        priority_order = {"HIGH": 3, "NORMAL": 2, "LOW": 1}
        
        def sort_key(sub_goal):
            priority = priority_order.get(sub_goal.get("priority", "NORMAL"), 2)
            dependencies = len(sub_goal.get("dependencies", []))
            return (-priority, dependencies)
        
        return sorted(sub_goals, key=sort_key)
    
    async def _generate_refinement_suggestions(self, sub_goal: SubGoal, 
                                             failure_reason: str) -> List[Dict[str, Any]]:
        """生成优化建议"""
        suggestions = []
        
        if "超时" in failure_reason:
            suggestions.append({
                "type": "split_sub_goal",
                "message": f"子目标 '{sub_goal.title}' 执行超时，建议进一步分解",
                "action": "split",
                "target_sub_goal_id": sub_goal.id
            })
        elif "权限" in failure_reason:
            suggestions.append({
                "type": "add_permission_step",
                "message": f"子目标 '{sub_goal.title}' 权限不足，建议添加权限获取步骤",
                "action": "add_step",
                "target_sub_goal_id": sub_goal.id
            })
        
        return suggestions


class ComplexityEvaluator:
    """复杂度评估器"""
    
    async def evaluate(self, goal_description: str, context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """评估目标复杂度"""
        score = 0
        factors = []
        
        # 基于描述长度
        if len(goal_description) > 100:
            score += 2
            factors.append("描述较长")
        
        # 基于关键词复杂度
        complex_keywords = ["批量", "自动", "定时", "条件", "循环", "多个", "复杂"]
        for keyword in complex_keywords:
            if keyword in goal_description:
                score += 1
                factors.append(f"包含复杂关键词: {keyword}")
        
        # 基于动作数量
        action_keywords = ["打开", "点击", "输入", "搜索", "下载", "发送", "创建", "删除"]
        action_count = sum(1 for keyword in action_keywords if keyword in goal_description)
        if action_count > 3:
            score += 2
            factors.append(f"包含多个动作: {action_count}")
        
        # 确定复杂度等级
        if score <= 2:
            level = "simple"
        elif score <= 5:
            level = "medium"
        else:
            level = "complex"
        
        return {
            "level": level,
            "score": score,
            "factors": factors
        }


class DependencyAnalyzer:
    """依赖关系分析器"""
    
    async def analyze_dependencies(self, sub_goals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析子目标之间的依赖关系"""
        # 为每个子目标添加依赖信息
        for i, sub_goal in enumerate(sub_goals):
            dependencies = []
            
            # 简单的依赖规则：前面的高优先级任务是后面任务的依赖
            for j in range(i):
                prev_sub_goal = sub_goals[j]
                if prev_sub_goal.get("priority") == "HIGH":
                    dependencies.append(j)
            
            sub_goal["dependencies"] = dependencies
            sub_goal["id"] = str(uuid.uuid4())
        
        return sub_goals

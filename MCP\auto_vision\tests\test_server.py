#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP服务器功能测试脚本

本脚本测试MCP服务器的所有功能，包括：
1. 服务器启动
2. 屏幕截图工具
3. OCR文本识别工具
4. UI元素识别工具
5. 视觉LLM分析工具
"""

import os
import sys
import unittest
import time
import subprocess
import json
import threading
import socket

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入被测模块
from src.server import AutoVisionServer

class ServerTest(unittest.TestCase):
    """MCP服务器功能测试类"""

    @classmethod
    def setUpClass(cls):
        """测试前的准备工作（类级别）"""
        # 创建测试目录
        cls.test_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_server")
        os.makedirs(cls.test_dir, exist_ok=True)

    def setUp(self):
        """测试前的准备工作（方法级别）"""
        # 这里可以添加每个测试方法执行前的准备工作
        pass

    def test_server_init(self):
        """测试服务器初始化"""
        try:
            # 创建服务器对象
            server = AutoVisionServer(ocr_engine="auto", ui_backend="win32gui", vision_llm=True)

            # 检查服务器对象
            self.assertIsNotNone(server, "服务器对象不应为None")

            # 检查服务器属性
            self.assertEqual(server.ocr_engine, "auto", "OCR引擎应为auto")
            self.assertEqual(server.ui_backend, "win32gui", "UI后端应为win32gui")
            self.assertTrue(server.vision_llm, "视觉LLM应为True")

            print("服务器初始化成功")
        except Exception as e:
            self.fail(f"服务器初始化失败: {e}")

    def test_server_start_stdio(self):
        """测试服务器启动（stdio模式）"""
        # 简化测试，只测试服务器对象能否创建并设置为stdio传输
        try:
            # 创建服务器对象
            server = AutoVisionServer(ocr_engine="auto", ui_backend="win32gui", vision_llm=True)

            # 检查服务器对象
            self.assertIsNotNone(server, "服务器对象不应为None")

            # 检查服务器是否有run方法
            self.assertTrue(hasattr(server, "run"), "服务器应有run方法")

            # 检查服务器是否有tools属性
            self.assertTrue(hasattr(server, "tools"), "服务器应有tools属性")

            # 检查服务器是否有必要的工具
            self.assertIn("capture_fullscreen", server.tools, "服务器应有capture_fullscreen工具")

            print("服务器（stdio模式）测试成功")
        except Exception as e:
            self.fail(f"服务器（stdio模式）测试失败: {e}")

    def test_server_start_http(self):
        """测试服务器启动（HTTP模式）"""
        # 跳过此测试，因为我们只使用stdio传输
        self.skipTest("根据需求，我们只使用stdio传输，不测试HTTP/TCP传输")

        # 以下代码保留但不执行
        server_thread = None
        try:
            # 创建服务器对象
            server = AutoVisionServer(ocr_engine="auto", ui_backend="win32gui", vision_llm=True)

            # 定义服务器线程函数
            def run_server():
                server.run(transport="streamable-http", host="localhost", port=8000)

            # 启动服务器线程
            server_thread = threading.Thread(target=run_server)
            server_thread.daemon = True
            server_thread.start()

            # 等待服务器启动
            time.sleep(3)

            # 使用requests库连接到服务器
            import requests

            # 发送一个简单的请求（ping）
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "ping",
                "params": {}
            }

            # 发送请求
            response = requests.post(
                "http://localhost:8000/",
                json=request,
                headers={"Content-Type": "application/json"}
            )

            # 检查响应
            self.assertEqual(response.status_code, 200, "HTTP状态码应为200")
            response_data = response.json()
            self.assertEqual(response_data["id"], 1, "响应ID应为1")
            self.assertEqual(response_data["result"], "pong", "响应结果应为pong")

            print("服务器（HTTP模式）响应成功")

        except Exception as e:
            self.fail(f"服务器（HTTP模式）测试失败: {e}")
        finally:
            # 停止服务器线程
            if server_thread and server_thread.is_alive():
                # 由于线程是daemon，主线程结束后它会自动终止
                pass

    def test_screen_capture_tool(self):
        """测试屏幕截图工具"""
        try:
            # 创建服务器对象
            server = AutoVisionServer(ocr_engine="auto", ui_backend="win32gui", vision_llm=True)

            # 获取屏幕截图工具
            capture_tool = server.tools.get("capture_fullscreen")

            # 检查工具是否存在
            self.assertIsNotNone(capture_tool, "屏幕截图工具不应为None")

            # 直接调用方法，而不是通过工具接口
            import asyncio
            result = asyncio.run(server.capture_fullscreen())

            # 检查结果
            self.assertTrue(result["success"], f"屏幕截图失败: {result.get('error', '未知错误')}")
            self.assertIn("path", result, "结果应包含path字段")

            # 检查文件是否存在
            self.assertTrue(os.path.exists(result["path"]), f"截图文件不存在: {result['path']}")

            print(f"屏幕截图工具测试成功，截图已保存至: {result['path']}")
        except Exception as e:
            self.fail(f"屏幕截图工具测试失败: {e}")

    def test_ocr_recognition_tool(self):
        """测试OCR文本识别工具"""
        try:
            # 创建服务器对象
            server = AutoVisionServer(ocr_engine="auto", ui_backend="win32gui", vision_llm=True)

            # 获取OCR文本识别工具
            ocr_tool = server.tools.get("recognize_text")

            # 检查工具是否存在
            self.assertIsNotNone(ocr_tool, "OCR文本识别工具不应为None")

            # 先获取一个截图
            import asyncio
            capture_result = asyncio.run(server.capture_fullscreen())

            # 调用OCR工具
            result = asyncio.run(server.recognize_text(image_path=capture_result["path"]))

            # 检查结果
            self.assertIsNotNone(result, "OCR结果不应为None")
            self.assertTrue(result["success"], "OCR识别应成功")
            self.assertIn("results", result, "结果应包含results字段")

            # 打印结果
            print(f"OCR文本识别工具测试成功，识别到{len(result['results'])}个文本块")
            for i, item in enumerate(result['results'][:3]):  # 只打印前3个结果
                print(f"{i+1}. 文本: {item['text']}")
                print(f"   位置: {item['box']}")
                print(f"   置信度: {item.get('confidence', 'N/A')}")
                print("-" * 50)
        except Exception as e:
            self.fail(f"OCR文本识别工具测试失败: {e}")

    def test_ui_automation_tool(self):
        """测试UI元素识别工具"""
        try:
            # 创建服务器对象
            server = AutoVisionServer(ocr_engine="auto", ui_backend="win32gui", vision_llm=True)

            # 获取UI元素识别工具
            ui_tool = server.tools.get("get_ui_elements")

            # 检查工具是否存在
            self.assertIsNotNone(ui_tool, "UI元素识别工具不应为None")

            # 调用工具
            import asyncio
            result = asyncio.run(server.get_ui_elements(element_type="Window"))

            # 检查结果
            self.assertIsNotNone(result, "UI元素识别结果不应为None")
            self.assertTrue(result["success"], "UI元素识别应成功")
            self.assertIn("elements", result, "结果应包含elements字段")

            # 打印结果
            print(f"UI元素识别工具测试成功，识别到{len(result['elements'])}个窗口")
            for i, element in enumerate(result['elements'][:3]):  # 只打印前3个结果
                print(f"{i+1}. 名称: {element['name']}")
                print(f"   类型: {element['control_type']}")
                print(f"   位置: {element['rectangle']}")
                print("-" * 50)
        except Exception as e:
            self.fail(f"UI元素识别工具测试失败: {e}")

    def test_vision_llm_tool(self):
        """测试视觉LLM分析工具"""
        try:
            # 创建服务器对象
            server = AutoVisionServer(ocr_engine="auto", ui_backend="win32gui", vision_llm=True)

            # 获取视觉LLM分析工具
            vision_tool = server.tools.get("analyze_screen")

            # 检查工具是否存在
            self.assertIsNotNone(vision_tool, "视觉LLM分析工具不应为None")

            # 调用工具
            import asyncio
            result = asyncio.run(server.analyze_screen(question="这个屏幕上有什么内容？请描述主要元素及其位置。"))

            # 检查结果
            self.assertIsNotNone(result, "视觉LLM分析结果不应为None")
            self.assertTrue(result.get("success", False), "视觉LLM分析应成功")
            self.assertIn("analysis", result, "结果应包含analysis字段")

            # 打印结果
            print(f"视觉LLM分析工具测试成功")
            print(f"分析结果:\n{result['analysis'][:200]}...")  # 只打印前200个字符
        except Exception as e:
            self.fail(f"视觉LLM分析工具测试失败: {e}")

    def tearDown(self):
        """测试后的清理工作（方法级别）"""
        # 这里可以添加每个测试方法执行后的清理工作
        pass

    @classmethod
    def tearDownClass(cls):
        """测试后的清理工作（类级别）"""
        # 这里可以添加所有测试完成后的清理工作
        pass

if __name__ == "__main__":
    unittest.main()

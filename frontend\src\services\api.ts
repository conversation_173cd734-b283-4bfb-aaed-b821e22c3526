// -*- coding: utf-8 -*-

import { ApiResponse, ChatMessage, SystemStatus, Task, ActionRecord } from '../types';

/**
 * API服务类
 * 专门负责与后端API的通信
 * 严格按照代码规范的单一职责原则
 */
export class ApiService {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseUrl: string = 'http://localhost:8000') {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  /**
   * 发送HTTP请求的通用方法
   * @param endpoint - API端点
   * @param options - 请求选项
   * @returns API响应
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      return data;
    } catch (error) {
      console.error(`API请求失败 [${endpoint}]:`, error);
      throw error;
    }
  }

  /**
   * GET请求
   * @param endpoint - API端点
   * @returns API响应
   */
  private async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  /**
   * POST请求
   * @param endpoint - API端点
   * @param data - 请求数据
   * @returns API响应
   */
  private async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT请求
   * @param endpoint - API端点
   * @param data - 请求数据
   * @returns API响应
   */
  private async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE请求
   * @param endpoint - API端点
   * @returns API响应
   */
  private async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // ========== 聊天相关API ==========

  /**
   * 发送聊天消息
   * @param message - 消息内容
   * @param sessionId - 会话ID
   * @returns 聊天响应
   */
  public async sendChatMessage(
    message: string,
    sessionId?: string
  ): Promise<ApiResponse<ChatMessage>> {
    return this.post<ChatMessage>('/api/v1/chat/send', {
      message,
      sessionId,
    });
  }

  /**
   * 获取聊天历史
   * @param sessionId - 会话ID
   * @param limit - 限制数量
   * @returns 聊天历史
   */
  public async getChatHistory(
    sessionId: string,
    limit: number = 50
  ): Promise<ApiResponse<ChatMessage[]>> {
    return this.get<ChatMessage[]>(`/api/v1/chat/history/${sessionId}?limit=${limit}`);
  }

  /**
   * 创建新会话
   * @returns 新会话信息
   */
  public async createSession(): Promise<ApiResponse<{ sessionId: string }>> {
    return this.post<{ sessionId: string }>('/api/v1/chat/session');
  }

  // ========== 系统状态相关API ==========

  /**
   * 获取系统状态
   * @returns 系统状态
   */
  public async getSystemStatus(): Promise<ApiResponse<SystemStatus>> {
    return this.get<SystemStatus>('/api/v1/system/status');
  }

  /**
   * 获取系统健康检查
   * @returns 健康检查结果
   */
  public async getHealthCheck(): Promise<ApiResponse<{ status: string }>> {
    return this.get<{ status: string }>('/api/v1/system/health');
  }

  /**
   * 获取系统统计信息
   * @returns 统计信息
   */
  public async getSystemStats(): Promise<ApiResponse<Record<string, any>>> {
    return this.get<Record<string, any>>('/api/v1/system/stats');
  }

  // ========== 任务相关API ==========

  /**
   * 创建新任务
   * @param taskData - 任务数据
   * @returns 创建的任务
   */
  public async createTask(taskData: {
    title: string;
    description: string;
    type?: string;
  }): Promise<ApiResponse<Task>> {
    return this.post<Task>('/api/v1/tasks', taskData);
  }

  /**
   * 获取任务列表
   * @param status - 任务状态过滤
   * @returns 任务列表
   */
  public async getTasks(status?: string): Promise<ApiResponse<Task[]>> {
    const query = status ? `?status=${status}` : '';
    return this.get<Task[]>(`/api/v1/tasks${query}`);
  }

  /**
   * 获取任务详情
   * @param taskId - 任务ID
   * @returns 任务详情
   */
  public async getTask(taskId: string): Promise<ApiResponse<Task>> {
    return this.get<Task>(`/api/v1/tasks/${taskId}`);
  }

  /**
   * 取消任务
   * @param taskId - 任务ID
   * @returns 取消结果
   */
  public async cancelTask(taskId: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.delete<{ success: boolean }>(`/api/v1/tasks/${taskId}`);
  }

  // ========== 操作记录相关API ==========

  /**
   * 获取操作记录
   * @param limit - 限制数量
   * @returns 操作记录列表
   */
  public async getActionRecords(limit: number = 100): Promise<ApiResponse<ActionRecord[]>> {
    return this.get<ActionRecord[]>(`/api/v1/actions?limit=${limit}`);
  }

  /**
   * 执行操作
   * @param actionData - 操作数据
   * @returns 执行结果
   */
  public async executeAction(actionData: {
    type: string;
    parameters: Record<string, any>;
  }): Promise<ApiResponse<ActionRecord>> {
    return this.post<ActionRecord>('/api/v1/actions/execute', actionData);
  }

  // ========== 记忆相关API ==========

  /**
   * 搜索记忆
   * @param query - 搜索查询
   * @param limit - 限制数量
   * @returns 搜索结果
   */
  public async searchMemory(
    query: string,
    limit: number = 10
  ): Promise<ApiResponse<any[]>> {
    return this.post<any[]>('/api/v1/memory/search', { query, limit });
  }
}

# -*- coding: utf-8 -*-
"""
感知-行动循环端到端测试
测试完整的任务执行流程，包括复杂场景和稳定性测试
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from qyuan.core.qyuan_core import QYuanCore
from qyuan.core.config import QYuanConfig
from qyuan.core.perception_action_loop import PerceptionActionLoop, LoopStatus
from qyuan.core.goal_tracker import GoalPriority
from qyuan.engines.perception.base import ScreenAnalysis, UIElement
from qyuan.engines.decision.base import DecisionResult, UserIntent, ActionPlan, Action
from qyuan.engines.execution.base import ExecutionResult


class TestPerceptionActionLoopE2E:
    """感知-行动循环端到端测试"""
    
    @pytest.fixture
    async def qyuan_core(self):
        """创建QYuan核心实例"""
        config = QYuanConfig()
        core = QYuanCore(config)
        
        # 模拟引擎
        await self._setup_mock_engines(core)
        
        await core.start()
        yield core
        await core.stop()
    
    async def _setup_mock_engines(self, core):
        """设置模拟引擎"""
        # 模拟感知引擎
        perception_engine = AsyncMock()
        perception_engine.analyze_current_screen = AsyncMock(return_value=ScreenAnalysis(
            ui_elements=[
                UIElement(id="btn1", type="button", text="确定", bounds=(100, 100, 200, 150)),
                UIElement(id="input1", type="input", text="", bounds=(100, 200, 300, 250))
            ],
            screenshot_path="mock_screenshot.png",
            timestamp=datetime.now()
        ))
        
        # 模拟决策引擎
        decision_engine = AsyncMock()
        decision_engine.make_decision = AsyncMock(return_value=DecisionResult(
            intent=UserIntent(intent_type="click_button", confidence=0.9),
            action_plan=ActionPlan(actions=[
                Action(type="click", target="btn1", parameters={"x": 150, "y": 125})
            ])
        ))
        
        # 模拟执行引擎
        execution_engine = AsyncMock()
        execution_engine.execute_action = AsyncMock(return_value=ExecutionResult(
            success=True,
            action_type="click",
            execution_time=0.5,
            result_data={"clicked": True}
        ))
        
        # 替换引擎
        core.engines["perception"] = perception_engine
        core.engines["decision"] = decision_engine
        core.engines["execution"] = execution_engine
    
    @pytest.mark.asyncio
    async def test_simple_task_execution(self, qyuan_core):
        """测试简单任务执行"""
        # 设置目标
        goal_id = await qyuan_core.set_goal("点击确定按钮", priority=3)
        
        # 执行一次感知-行动循环
        result = await qyuan_core.perception_action_cycle()
        
        # 验证结果
        assert result is None or result  # 循环应该成功执行
        
        # 检查目标状态
        goal_status = qyuan_core.get_goal_status(goal_id)
        assert goal_status is not None
        assert goal_status["id"] == goal_id
    
    @pytest.mark.asyncio
    async def test_complex_multi_step_task(self, qyuan_core):
        """测试复杂多步骤任务"""
        # 设置复杂目标
        goal_id = await qyuan_core.set_goal(
            "打开浏览器，搜索Python教程，点击第一个结果", 
            priority=4
        )
        
        # 模拟多步骤执行
        steps_completed = 0
        max_steps = 5
        
        for step in range(max_steps):
            # 执行感知-行动循环
            await qyuan_core.perception_action_cycle()
            steps_completed += 1
            
            # 模拟进度更新
            progress = (steps_completed / max_steps) * 100
            qyuan_core.goal_manager.goal_tracker.update_goal_progress(
                goal_id, progress
            )
            
            # 短暂等待
            await asyncio.sleep(0.1)
        
        # 验证最终状态
        goal_status = qyuan_core.get_goal_status(goal_id)
        assert goal_status["progress"]["percentage"] == 100.0
    
    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self, qyuan_core):
        """测试错误处理和恢复"""
        # 设置目标
        goal_id = await qyuan_core.set_goal("测试错误恢复", priority=3)
        
        # 模拟执行引擎失败
        execution_engine = qyuan_core.engines["execution"]
        execution_engine.execute_action.side_effect = [
            Exception("模拟执行失败"),  # 第一次失败
            ExecutionResult(success=True, action_type="click", execution_time=0.5)  # 第二次成功
        ]
        
        # 执行多次循环，测试错误恢复
        for _ in range(3):
            try:
                await qyuan_core.perception_action_cycle()
            except Exception:
                pass  # 忽略预期的异常
            await asyncio.sleep(0.1)
        
        # 验证系统仍在运行
        assert qyuan_core.is_running
        
        # 重置执行引擎
        execution_engine.execute_action.side_effect = None
        execution_engine.execute_action.return_value = ExecutionResult(
            success=True, action_type="click", execution_time=0.5
        )
    
    @pytest.mark.asyncio
    async def test_performance_optimization(self, qyuan_core):
        """测试性能优化功能"""
        # 设置目标
        await qyuan_core.set_goal("测试性能优化", priority=3)
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行多次循环
        cycle_count = 10
        for _ in range(cycle_count):
            await qyuan_core.perception_action_cycle()
            await asyncio.sleep(0.05)  # 短暂间隔
        
        # 记录结束时间
        end_time = time.time()
        total_time = end_time - start_time
        
        # 获取性能报告
        loop_status = qyuan_core.get_perception_action_loop_status()
        performance_report = qyuan_core.perception_action_loop.get_performance_report()
        
        # 验证性能指标
        assert "optimization" in loop_status
        assert "metrics" in performance_report
        assert performance_report["metrics"]["total_cycles"] > 0
        
        # 验证平均循环时间合理
        avg_cycle_time = total_time / cycle_count
        assert avg_cycle_time < 1.0  # 平均循环时间应小于1秒
    
    @pytest.mark.asyncio
    async def test_goal_tracking_and_completion(self, qyuan_core):
        """测试目标跟踪和完成"""
        # 创建带子目标的复杂目标
        goal_id = await qyuan_core.goal_manager.create_goal_from_user_input(
            "完成文件下载任务",
            priority=GoalPriority.HIGH
        )
        
        # 添加子目标
        sub_goal_1 = qyuan_core.goal_manager.goal_tracker.add_sub_goal(
            goal_id, "打开浏览器", "启动默认浏览器", GoalPriority.HIGH
        )
        sub_goal_2 = qyuan_core.goal_manager.goal_tracker.add_sub_goal(
            goal_id, "导航到下载页面", "访问文件下载链接", GoalPriority.HIGH
        )
        sub_goal_3 = qyuan_core.goal_manager.goal_tracker.add_sub_goal(
            goal_id, "点击下载按钮", "开始文件下载", GoalPriority.HIGH
        )
        
        # 模拟逐步完成子目标
        qyuan_core.goal_manager.goal_tracker.update_sub_goal_progress(
            goal_id, sub_goal_1, 100.0
        )
        await asyncio.sleep(0.1)
        
        qyuan_core.goal_manager.goal_tracker.update_sub_goal_progress(
            goal_id, sub_goal_2, 100.0
        )
        await asyncio.sleep(0.1)
        
        qyuan_core.goal_manager.goal_tracker.update_sub_goal_progress(
            goal_id, sub_goal_3, 100.0
        )
        
        # 检查目标完成
        is_completed = await qyuan_core.goal_manager.check_goal_completion(goal_id)
        assert is_completed
        
        # 验证目标状态
        goal_status = qyuan_core.get_goal_status(goal_id)
        assert goal_status["progress"]["percentage"] == 100.0
        assert len(goal_status["sub_goals"]) == 3
    
    @pytest.mark.asyncio
    async def test_concurrent_goal_execution(self, qyuan_core):
        """测试并发目标执行"""
        # 创建多个目标
        goal_ids = []
        for i in range(3):
            goal_id = await qyuan_core.set_goal(f"并发任务 {i+1}", priority=3)
            goal_ids.append(goal_id)
        
        # 并发执行感知-行动循环
        tasks = []
        for _ in range(5):
            task = asyncio.create_task(qyuan_core.perception_action_cycle())
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证没有严重错误
        for result in results:
            if isinstance(result, Exception):
                # 记录异常但不失败测试（某些异常是预期的）
                print(f"并发执行异常: {result}")
        
        # 验证系统状态
        assert qyuan_core.is_running
    
    @pytest.mark.asyncio
    async def test_loop_pause_and_resume(self, qyuan_core):
        """测试循环暂停和恢复"""
        # 设置目标
        await qyuan_core.set_goal("测试暂停恢复", priority=3)
        
        # 启动循环
        loop_task = asyncio.create_task(qyuan_core.perception_action_cycle())
        
        # 短暂等待后暂停
        await asyncio.sleep(0.1)
        await qyuan_core.pause_perception_action_loop()
        
        # 验证暂停状态
        loop_status = qyuan_core.get_perception_action_loop_status()
        assert loop_status["status"] == "paused"
        
        # 恢复循环
        await qyuan_core.resume_perception_action_loop()
        
        # 验证恢复状态
        await asyncio.sleep(0.1)
        loop_status = qyuan_core.get_perception_action_loop_status()
        assert loop_status["status"] == "running"
        
        # 清理
        try:
            await asyncio.wait_for(loop_task, timeout=1.0)
        except asyncio.TimeoutError:
            loop_task.cancel()
    
    @pytest.mark.asyncio
    async def test_stability_under_load(self, qyuan_core):
        """测试高负载下的稳定性"""
        # 设置目标
        await qyuan_core.set_goal("稳定性测试", priority=3)
        
        # 高频率执行循环
        start_time = time.time()
        cycle_count = 20
        errors = 0
        
        for i in range(cycle_count):
            try:
                await qyuan_core.perception_action_cycle()
                
                # 随机添加一些负载
                if i % 5 == 0:
                    await asyncio.sleep(0.01)
                    
            except Exception as e:
                errors += 1
                print(f"循环 {i} 异常: {e}")
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 验证稳定性指标
        error_rate = errors / cycle_count
        avg_cycle_time = total_time / cycle_count
        
        # 错误率应该较低
        assert error_rate < 0.3  # 允许30%的错误率
        
        # 平均循环时间应该合理
        assert avg_cycle_time < 0.5  # 平均循环时间应小于0.5秒
        
        # 系统应该仍在运行
        assert qyuan_core.is_running
        
        print(f"稳定性测试结果: 错误率={error_rate:.2%}, 平均循环时间={avg_cycle_time:.3f}秒")
    
    @pytest.mark.asyncio
    async def test_memory_usage_stability(self, qyuan_core):
        """测试内存使用稳定性"""
        import psutil
        import os
        
        # 获取当前进程
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 设置目标
        await qyuan_core.set_goal("内存稳定性测试", priority=3)
        
        # 执行大量循环
        for i in range(50):
            await qyuan_core.perception_action_cycle()
            
            # 每10次循环检查一次内存
            if i % 10 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_growth = current_memory - initial_memory
                
                # 内存增长不应该过大
                assert memory_growth < 100  # 不超过100MB增长
                
                print(f"循环 {i}: 内存使用 {current_memory:.1f}MB (增长 {memory_growth:.1f}MB)")
        
        # 最终内存检查
        final_memory = process.memory_info().rss / 1024 / 1024
        total_growth = final_memory - initial_memory
        
        print(f"内存稳定性测试完成: 初始={initial_memory:.1f}MB, 最终={final_memory:.1f}MB, 增长={total_growth:.1f}MB")
        
        # 总内存增长应该在合理范围内
        assert total_growth < 50  # 总增长不超过50MB


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-s"])

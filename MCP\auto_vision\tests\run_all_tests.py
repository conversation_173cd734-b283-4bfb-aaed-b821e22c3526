#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Auto Vision 功能测试脚本

本脚本测试 Auto Vision 项目的所有功能，包括：
1. 屏幕截图功能
2. OCR文本识别功能
3. UI元素识别功能
4. 视觉LLM分析功能
5. MCP服务器功能
"""

import os
import sys
import unittest
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入测试模块
from test_screen_capture import ScreenCaptureTest
from test_ocr_recognition import OCRRecognitionTest
from test_ui_automation import UIAutomationTest
from test_vision_llm import VisionLLMTest
from test_server import ServerTest

def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(ScreenCaptureTest))
    test_suite.addTest(unittest.makeSuite(OCRRecognitionTest))
    test_suite.addTest(unittest.makeSuite(UIAutomationTest))
    test_suite.addTest(unittest.makeSuite(VisionLLMTest))
    test_suite.addTest(unittest.makeSuite(ServerTest))
    
    # 创建测试运行器
    test_runner = unittest.TextTestRunner(verbosity=2)
    
    # 运行测试
    test_result = test_runner.run(test_suite)
    
    # 返回测试结果
    return test_result

def generate_report(test_result):
    """生成测试报告"""
    # 创建报告目录
    report_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "reports")
    os.makedirs(report_dir, exist_ok=True)
    
    # 创建报告文件
    report_file = os.path.join(report_dir, f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")
    
    # 写入报告
    with open(report_file, "w", encoding="utf-8") as f:
        f.write("# Auto Vision 功能测试报告\n\n")
        f.write(f"测试时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## 测试结果摘要\n\n")
        f.write(f"- 运行测试：{test_result.testsRun}\n")
        f.write(f"- 成功：{test_result.testsRun - len(test_result.errors) - len(test_result.failures)}\n")
        f.write(f"- 失败：{len(test_result.failures)}\n")
        f.write(f"- 错误：{len(test_result.errors)}\n\n")
        
        if test_result.failures:
            f.write("## 失败的测试\n\n")
            for test, error in test_result.failures:
                f.write(f"### {test}\n\n")
                f.write("```\n")
                f.write(error)
                f.write("\n```\n\n")
        
        if test_result.errors:
            f.write("## 错误的测试\n\n")
            for test, error in test_result.errors:
                f.write(f"### {test}\n\n")
                f.write("```\n")
                f.write(error)
                f.write("\n```\n\n")
    
    print(f"测试报告已生成：{report_file}")
    return report_file

if __name__ == "__main__":
    # 运行测试
    test_result = run_tests()
    
    # 生成报告
    report_file = generate_report(test_result)
    
    # 打印结果
    print("\n测试完成！")
    print(f"运行测试：{test_result.testsRun}")
    print(f"成功：{test_result.testsRun - len(test_result.errors) - len(test_result.failures)}")
    print(f"失败：{len(test_result.failures)}")
    print(f"错误：{len(test_result.errors)}")

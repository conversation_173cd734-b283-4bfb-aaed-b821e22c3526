#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
区域截图模块

提供区域截图功能。
"""

import os
import base64
from io import BytesIO
from datetime import datetime
from PIL import Image
import pyautogui

from .screen_capture_base import ScreenCaptureBase


class RegionScreenCapture(ScreenCaptureBase):
    """区域截图类，提供区域截图功能"""

    def __init__(self, screenshots_dir="screenshots"):
        """
        初始化区域截图类

        Args:
            screenshots_dir: 截图保存目录
        """
        self.screenshots_dir = screenshots_dir
        # 确保截图目录存在
        if not os.path.exists(screenshots_dir):
            os.makedirs(screenshots_dir)

    def capture_screen(self, region=None, save_path=None, suffix=None):
        """
        捕获屏幕区域

        Args:
            region: 截图区域 [x, y, width, height]，为None则截取全屏
            save_path: 保存路径，为None则自动生成路径
            suffix: 文件名后缀，用于区分不同的截图

        Returns:
            PIL.Image: 截图图像对象
            str: 保存路径（如果有保存）
        """
        # 截图
        if region is None:
            # 如果未指定区域，则委托给全屏截图类处理
            from .screen_capture_full import FullScreenCapture
            full_capture = FullScreenCapture(self.screenshots_dir)
            return full_capture.capture_screen(region=None, save_path=save_path, suffix=suffix)
        else:
            # 区域截图
            screenshot = pyautogui.screenshot(region=region)

        # 保存截图
        if save_path:
            screenshot.save(save_path)
            return screenshot, save_path
        elif self.screenshots_dir:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # 添加毫秒级时间戳和可选后缀，确保文件名唯一
            ms = datetime.now().microsecond // 1000
            if suffix:
                filename = f"screenshot_{timestamp}_{ms:03d}_{suffix}.png"
            else:
                filename = f"screenshot_{timestamp}_{ms:03d}.png"
            save_path = os.path.join(self.screenshots_dir, filename)
            screenshot.save(save_path)
            return screenshot, save_path

        return screenshot, None

    def capture_region(self, x, y, width, height, save_path=None, suffix=None):
        """
        捕获指定区域的屏幕

        Args:
            x: 区域左上角X坐标
            y: 区域左上角Y坐标
            width: 区域宽度
            height: 区域高度
            save_path: 保存路径，为None则自动生成路径
            suffix: 文件名后缀，用于区分不同的截图

        Returns:
            PIL.Image: 截图图像对象
            str: 保存路径（如果有保存）
        """
        return self.capture_screen(region=(x, y, width, height), save_path=save_path, suffix=suffix)

    def get_screen_size(self):
        """
        获取屏幕尺寸

        Returns:
            tuple: (宽度, 高度)
        """
        return pyautogui.size()

    def capture_region_with_coordinates(self, x, y, width, height, suffix="region_with_coords"):
        """
        带坐标的区域截图

        Args:
            x: 区域左上角X坐标
            y: 区域左上角Y坐标
            width: 区域宽度
            height: 区域高度
            suffix: 文件名后缀，用于区分不同的截图

        Returns:
            dict: 包含base64编码的带坐标图片数据和相关信息
        """
        try:
            # 捕获区域
            screenshot, screenshot_path = self.capture_region(x, y, width, height, suffix=suffix)

            # 导入图像增强器（避免循环导入）
            from .vision_llm.image_enhancer import ImageEnhancer
            enhancer = ImageEnhancer()

            # 增强图像（添加坐标）
            region_coords = (x, y, width, height)
            enhanced_path = enhancer.enhance_image(screenshot_path, region_coords=region_coords)

            # 读取增强后的图像
            enhanced_image = Image.open(enhanced_path)

            # 将图片转换为base64编码
            buffered = BytesIO()
            enhanced_image.save(buffered, format="PNG")
            img_str = base64.b64encode(buffered.getvalue()).decode()

            return {
                "success": True,
                "image_base64": img_str,
                "path": enhanced_path,
                "original_path": screenshot_path,
                "x": x,
                "y": y,
                "width": width,
                "height": height
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

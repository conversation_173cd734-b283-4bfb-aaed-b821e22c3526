#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
屏幕截图包

提供屏幕截图功能，支持全屏和区域截图，以及视觉LLM分析。
"""

# 导入基础截图功能（但不直接导出）
from .screen_capture_base import ScreenCaptureBase
from .screen_capture_factory import Screen<PERSON>aptureFactory
from .screen_capture_full import FullScreenCapture
from .screen_capture_region import RegionScreenCapture

# 导入视觉LLM功能
from .vision_llm.image_enhancer import ImageEnhancer
from .vision_llm.vision_llm import VisionLLM
from .vision_llm.screen_analyzer import ScreenAnalyzer

# 创建默认的屏幕分析对象（这是主要的对外接口）
ScreenCapture = ScreenAnalyzer

# 明确指定导出的符号
__all__ = [
    'ScreenCapture',
    'ScreenAnalyzer',
    'ImageEnhancer',
    'VisionLLM',
    'FullScreenCapture',
    'RegionScreenCapture'
]

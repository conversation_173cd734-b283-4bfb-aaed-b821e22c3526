# -*- coding: utf-8 -*-
"""
通信引擎 - 负责QYuan的对外交流和用户交互
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from ..import BaseEngine, EngineStatus
from ...interfaces.llm_manager import LLMManager
from ...memory.memory_manager import MemoryManager
from ...core.events import EventType
from ...core.exceptions import CommunicationError, LLMError

class CommunicationEngine(BaseEngine):
    """通信引擎 - 处理用户交互和智能对话"""
    
    def __init__(self, name: str, qyuan_core=None):
        super().__init__(name, qyuan_core)
        self.llm_manager: Optional[LLMManager] = None
        self.memory_manager: Optional[MemoryManager] = None

        # 对话状态
        self.active_conversations: Dict[str, Dict[str, Any]] = {}
        self.total_messages_processed = 0
        self.total_responses_sent = 0
    
    async def _initialize(self):
        """初始化通信引擎"""
        try:
            self.logger.info("初始化通信引擎...")

            # 初始化LLM管理器
            self.llm_manager = LLMManager(self.config)
            await self.llm_manager.initialize()

            # 初始化记忆管理器
            self.memory_manager = MemoryManager()
            memory_success = await self.memory_manager.initialize()
            if memory_success:
                self.logger.info("✅ 记忆管理器初始化成功")
            else:
                self.logger.warning("⚠️ 记忆管理器初始化失败，将使用临时记忆")

            # 注册事件监听器
            if self.event_bus:
                self.event_bus.on(EventType.USER_MESSAGE, self._handle_user_message)

            self.logger.info("✅ 通信引擎初始化完成")

        except Exception as e:
            self.logger.error(f"❌ 通信引擎初始化失败: {e}")
            raise CommunicationError(f"通信引擎初始化失败: {e}")
    
    async def _start(self):
        """启动通信引擎"""
        self.logger.info("通信引擎启动完成")
    
    async def _stop(self):
        """停止通信引擎"""
        if self.llm_manager:
            await self.llm_manager.shutdown()

        if self.memory_manager:
            self.memory_manager.close()

        self.logger.info("通信引擎停止完成")
    
    async def _health_check(self) -> bool:
        """健康检查"""
        if not self.llm_manager:
            return False
        
        return await self.llm_manager.health_check()
    
    async def _handle_user_message(self, event):
        """处理用户消息事件"""
        try:
            message = event.data.get("message", "")
            user_id = event.data.get("user_id", "default")
            
            self.logger.info(f"处理用户消息: {message[:50]}...")
            
            # 处理消息并生成回复
            response = await self.process_user_message(message, user_id)
            
            # 发射响应事件
            if self.event_bus:
                await self.event_bus.emit(
                    EventType.USER_RESPONSE,
                    {
                        "response": response,
                        "user_id": user_id,
                        "original_message": message
                    },
                    source=self.name,
                    correlation_id=event.correlation_id
                )
            
        except Exception as e:
            self.logger.error(f"处理用户消息失败: {e}")
    
    async def process_user_message(self, message: str, user_id: str = "default") -> str:
        """处理用户消息并生成智能回复"""
        try:
            if not self.llm_manager:
                raise CommunicationError("LLM管理器未初始化")

            self.total_messages_processed += 1

            # 获取用户上下文和历史记忆
            context = await self._get_enhanced_user_context(user_id, message)

            # 分析用户意图
            intent_result = await self.llm_manager.analyze_intent(message)
            self.logger.debug(f"用户意图: {intent_result.intent} (置信度: {intent_result.confidence})")

            # 更新上下文
            context["last_intent"] = intent_result
            context["last_message_time"] = datetime.now()

            # 根据意图类型处理消息
            if intent_result.intent == "task_request":
                response = await self._handle_task_request(message, intent_result, context)
            elif intent_result.intent == "question":
                response = await self._handle_question(message, context)
            elif intent_result.intent == "command":
                response = await self._handle_command(message, intent_result, context)
            else:
                # 默认智能对话
                response = await self.llm_manager.chat(message, context)

            # 存储对话记忆
            await self._store_conversation_memory(user_id, message, response, intent_result)

            # 更新对话状态
            self._update_conversation_state(user_id, message, response)

            self.total_responses_sent += 1
            return response

        except LLMError as e:
            self.logger.error(f"LLM处理失败: {e}")
            return "抱歉，我的思考模块遇到了问题，请稍后再试。"

        except Exception as e:
            self.logger.error(f"消息处理异常: {e}")
            return f"处理您的消息时出现了问题：{e}"
    
    async def _handle_task_request(self, message: str, intent_result, context: Dict[str, Any]) -> str:
        """处理任务请求"""
        try:
            # 提取任务目标
            goal = intent_result.entities.get("action", message)
            
            # 创建行动计划
            action_plan = await self.llm_manager.create_action_plan(goal, context)
            
            # 发射目标设置事件
            if self.event_bus:
                await self.event_bus.emit(
                    EventType.GOAL_SET,
                    {
                        "goal": goal,
                        "action_plan": action_plan.__dict__,
                        "user_request": message
                    },
                    source=self.name
                )
            
            # 生成确认回复
            response = f"我理解您要{goal}。我已经制定了行动计划：\n\n"
            for i, step in enumerate(action_plan.steps[:3], 1):  # 只显示前3步
                response += f"{i}. {step.get('action', '执行步骤')}\n"
            
            if len(action_plan.steps) > 3:
                response += f"...等共{len(action_plan.steps)}个步骤\n"
            
            response += f"\n预计需要{action_plan.estimated_time or '未知'}分钟。是否开始执行？"
            
            return response
            
        except Exception as e:
            self.logger.error(f"任务请求处理失败: {e}")
            return f"我理解您的任务请求，但制定计划时遇到问题：{e}。请提供更多细节。"
    
    async def _handle_question(self, message: str, context: Dict[str, Any]) -> str:
        """处理问题咨询"""
        # 使用智能对话处理问题
        return await self.llm_manager.chat(message, context)
    
    async def _handle_command(self, message: str, intent_result, context: Dict[str, Any]) -> str:
        """处理系统命令"""
        command = intent_result.entities.get("command", "").lower()
        
        if command in ["status", "状态"]:
            return await self._get_system_status()
        elif command in ["clear", "清空", "重置"]:
            self.llm_manager.clear_conversation_history()
            return "好的，我已经清空了对话历史。"
        elif command in ["help", "帮助"]:
            return self._get_help_message()
        else:
            return await self.llm_manager.chat(message, context)
    
    async def _get_system_status(self) -> str:
        """获取系统状态"""
        if not self.qyuan:
            return "系统状态获取失败：QYuan核心未连接"
        
        try:
            status = await self.qyuan.get_status()
            
            status_msg = f"""📊 QYuan系统状态报告

🤖 **运行状态**: {'运行中' if status.is_running else '已停止'}
⏱️ **运行时间**: {status.uptime or '未知'}
🎯 **当前目标**: {status.current_goal or '无'}
📋 **当前任务**: {status.current_task or '无'}

🔧 **引擎状态**:"""
            
            for engine_name, engine_status in status.engines_status.items():
                status_icon = "✅" if engine_status["status"] == "running" else "❌"
                status_msg += f"\n  {status_icon} {engine_name}: {engine_status['status']}"
            
            # 添加LLM统计
            if self.llm_manager:
                llm_stats = self.llm_manager.get_statistics()
                status_msg += f"\n\n💬 **对话统计**:"
                status_msg += f"\n  📨 处理消息: {self.total_messages_processed}"
                status_msg += f"\n  📤 发送回复: {self.total_responses_sent}"
                status_msg += f"\n  🧠 LLM请求: {llm_stats.get('total_requests', 0)}"
                status_msg += f"\n  ❌ 错误次数: {llm_stats.get('total_errors', 0)}"
            
            return status_msg
            
        except Exception as e:
            return f"获取系统状态时出错：{e}"
    
    def _get_help_message(self) -> str:
        """获取帮助信息"""
        return """🤖 QYuan使用帮助

我是您的智能CEO助手，可以帮助您：

📋 **任务执行**
- "帮我打开浏览器"
- "发送一封邮件给张三"
- "整理桌面文件"

❓ **问题咨询**
- "如何提高工作效率？"
- "今天的日程安排是什么？"
- "这个文档的要点是什么？"

🔧 **系统命令**
- "状态" - 查看系统状态
- "清空" - 清空对话历史
- "帮助" - 显示此帮助信息

💬 **日常对话**
- 随时与我聊天，我会尽力帮助您！

如有问题，请随时告诉我！"""
    
    async def _get_enhanced_user_context(self, user_id: str, current_message: str) -> Dict[str, Any]:
        """获取增强的用户上下文（包含记忆）"""
        # 获取基础上下文
        context = self._get_user_context(user_id)

        # 如果记忆管理器可用，添加相关记忆
        if self.memory_manager:
            try:
                # 搜索相关的历史对话
                recent_memories = self.memory_manager.get_recent_memories(
                    session_id=user_id,
                    hours=24,
                    memory_types=["raw"],
                    limit=5
                )

                # 搜索与当前消息相关的记忆
                relevant_memories = self.memory_manager.search_memories(
                    query=current_message,
                    memory_types=["raw"],
                    session_id=user_id,
                    limit=3
                )

                # 添加记忆到上下文
                context["recent_memories"] = recent_memories
                context["relevant_memories"] = relevant_memories

                # 获取会话上下文
                session_contexts = self.memory_manager.get_session_contexts(user_id)
                if session_contexts:
                    context["session_context"] = session_contexts[0]  # 最新的上下文

            except Exception as e:
                self.logger.warning(f"获取记忆上下文失败: {e}")

        return context

    def _get_user_context(self, user_id: str) -> Dict[str, Any]:
        """获取基础用户上下文"""
        if user_id not in self.active_conversations:
            self.active_conversations[user_id] = {
                "start_time": datetime.now(),
                "message_count": 0,
                "last_intent": None,
                "preferences": {}
            }

        return self.active_conversations[user_id]
    
    async def _store_conversation_memory(self, user_id: str, message: str, response: str, intent_result):
        """存储对话记忆"""
        if not self.memory_manager:
            return

        try:
            memory_id = self.memory_manager.store_conversation(
                session_id=user_id,
                user_message=message,
                assistant_response=response,
                user_intent=intent_result.intent if intent_result else None,
                intent_confidence=intent_result.confidence if intent_result else 0.0,
                context={
                    "timestamp": datetime.now().isoformat(),
                    "message_length": len(message),
                    "response_length": len(response),
                    "engine": self.name
                }
            )

            if memory_id:
                self.logger.debug(f"存储对话记忆成功: {memory_id}")
            else:
                self.logger.warning("存储对话记忆失败")

        except Exception as e:
            self.logger.error(f"存储对话记忆异常: {e}")

    def _update_conversation_state(self, user_id: str, message: str, response: str):
        """更新对话状态"""
        if user_id in self.active_conversations:
            self.active_conversations[user_id]["message_count"] += 1
            self.active_conversations[user_id]["last_message"] = message
            self.active_conversations[user_id]["last_response"] = response
            self.active_conversations[user_id]["last_update"] = datetime.now()
    
    def get_engine_statistics(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        base_stats = {
            "total_messages_processed": self.total_messages_processed,
            "total_responses_sent": self.total_responses_sent,
            "active_conversations": len(self.active_conversations),
            "llm_manager_initialized": self.llm_manager is not None,
            "memory_manager_initialized": self.memory_manager is not None
        }

        if self.llm_manager:
            base_stats.update(self.llm_manager.get_statistics())

        if self.memory_manager:
            try:
                memory_stats = self.memory_manager.get_memory_stats()
                base_stats["memory_stats"] = memory_stats
            except Exception as e:
                self.logger.warning(f"获取记忆统计失败: {e}")

        return base_stats

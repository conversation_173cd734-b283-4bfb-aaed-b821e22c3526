# Auto Vision

Auto Vision 是一个让大型语言模型（LLM）能够"看见"屏幕内容的项目。它是让LLM像人一样操作电脑的重要组成部分，与已实现的键盘和鼠标控制功能相结合，使LLM能够更全面地与计算机交互。

## 项目背景

项目目标是让LLM像人一样操作电脑，已实现键盘鼠标操作，现在正在开发视觉功能，让LLM能够"看见"屏幕内容。

## 功能特点

Auto Vision 已实现以下功能：

1. **屏幕截图**：
   - 全屏截图和区域截图
   - 带坐标的截图（添加经纬线和坐标标注）
   - 截图保存和base64编码返回
   - 支持通过视觉LLM分析截图内容

2. **OCR文本识别**：
   - 支持多种OCR引擎（Windows OCR、EasyOCR、百度OCR）
   - 自动选择最佳引擎和备用引擎机制
   - 支持结构化返回格式，包含文本、位置和置信度信息
   - 支持指定区域的文本识别

3. **UI元素识别**：
   - 窗口级操作（激活、最大化、最小化等）
   - 窗口内元素识别和坐标获取
   - 支持通过文本查找元素
   - 支持获取窗口Z序和可见性信息

4. **视觉LLM分析**：
   - 使用Gemini API分析截图
   - 支持全屏分析和区域分析
   - 支持针对特定问题的分析
   - 支持组合分析（同时获取OCR、UI元素和视觉LLM分析结果）

5. **MCP服务器**：
   - 提供屏幕截图、OCR文本识别和UI元素识别功能的MCP服务器
   - 支持stdio和tcp传输方式
   - 提供多种工具函数，方便LLM调用

## 技术选择

### 屏幕截图
- 使用`PIL`和`pyautogui`进行屏幕截图
- 支持全屏和区域截图
- 添加了图像增强功能，包括经纬线和坐标标注
- 集成了Gemini视觉LLM进行屏幕分析

### OCR文本识别
- 支持多种OCR引擎：
  - Windows OCR API（通过comtypes和pythonnet两种方式实现）
  - EasyOCR
  - 百度OCR
- 实现了备用引擎机制，当主引擎失败时自动切换到备用引擎
- 支持结构化返回格式，包含文本、位置和置信度信息

### UI元素识别（Windows平台）
- 使用Win32GUI实现窗口级操作
- 使用PyWinAuto实现窗口内元素识别
- 支持获取UI元素的坐标信息
- 支持通过文本查找元素

### MCP服务器
- 提供屏幕截图、OCR文本识别和UI元素识别功能的MCP服务器
- 支持stdio和tcp传输方式
- 集成了视觉LLM分析功能

## 项目结构

```
auto_vision/
├── docs/                  # 文档
│   ├── mcp_server_guide.md                # MCP Server开发指南
│   ├── development_experience.md          # 开发经验总结
│   ├── keyboard_vscode_integration_guide.md  # 键盘控制器VSCode集成指南
│   ├── vscode_integration_guide.md        # 鼠标控制器VSCode集成指南
│   └── auto_vision_discussion.md          # 视觉功能讨论文档
├── src/                   # 源代码
│   ├── screen_capture.py  # 屏幕截图模块（向后兼容）
│   ├── screen_capture/    # 屏幕截图模块
│   │   ├── __init__.py    # 包初始化文件
│   │   ├── screen_capture_base.py      # 基础接口类
│   │   ├── screen_capture_factory.py   # 工厂类
│   │   ├── screen_capture_full.py      # 全屏截图实现
│   │   ├── screen_capture_region.py    # 区域截图实现
│   │   ├── vision_llm/                 # 视觉LLM相关功能
│   │   │   ├── image_enhancer.py       # 图像增强功能
│   │   │   ├── vision_llm.py           # 视觉LLM连接功能
│   │   │   ├── screen_analyzer.py      # 屏幕分析服务
│   │   │   └── README.md               # 视觉LLM模块说明
│   │   └── README.md                   # 屏幕截图模块说明
│   ├── ocr_recognition.py # OCR文本识别模块（向后兼容）
│   ├── ocr_recognition/   # OCR文本识别模块
│   │   ├── __init__.py    # 包初始化文件
│   │   ├── ocr_recognition_base.py     # 基础接口类
│   │   ├── ocr_recognition_factory.py  # 工厂类
│   │   ├── ocr_recognition_windows.py  # Windows OCR实现
│   │   ├── ocr_recognition_easyocr.py  # EasyOCR实现
│   │   ├── ocr_recognition_baidu.py    # 百度OCR实现
│   │   ├── ocr_recognition_windows_net.py # Windows OCR .NET实现
│   │   ├── ocr_recognition.py          # 主OCR接口类
│   │   └── README.md                   # OCR文本识别模块说明
│   ├── ui_automation.py   # UI元素识别主模块（向后兼容）
│   ├── ui_automation/     # UI元素识别子模块
│   │   ├── __init__.py    # 包初始化文件
│   │   ├── ui_automation_base.py      # 基础接口类
│   │   ├── ui_automation_factory.py   # 工厂类
│   │   ├── ui_automation_pywinauto.py # PyWinAuto实现
│   │   ├── ui_automation_win32.py     # Win32GUI实现
│   │   └── README.md                  # UI自动化模块说明
│   └── server.py          # MCP服务器
├── tests/                 # 测试代码
│   ├── test_screen_capture.py         # 屏幕截图测试
│   └── ...                            # 其他测试文件
├── screenshots/           # 截图保存目录
├── start_server.py        # 启动MCP服务器的脚本
└── README.md              # 项目说明
```

## 使用方法

### 屏幕截图和分析

```python
from src.screen_capture import ScreenCapture

# 创建屏幕分析对象
screen_capture = ScreenCapture()  # ScreenCapture 现在是 ScreenAnalyzer 的别名

# 捕获并分析全屏
result = screen_capture.capture_and_analyze_screen(
    question="这个屏幕上有什么内容？请描述主要元素及其位置。"
)
print(f"分析结果:\n{result['analysis']}")

# 捕获并分析区域
region = (100, 100, 400, 300)  # x, y, width, height
result = screen_capture.capture_and_analyze_screen(
    question="这个区域中有什么内容？请描述主要元素及其位置。",
    region=region
)
print(f"分析结果:\n{result['analysis']}")

# 分析最新的屏幕截图
result = screen_capture.analyze_latest_screenshot(
    question="屏幕上有什么颜色？"
)
print(f"分析结果:\n{result['analysis']}")
```

### OCR文本识别

```python
from src.ocr_recognition import OCRRecognition

# 创建OCR对象（自动选择引擎）
ocr = OCRRecognition(engine="auto", fallback=True)

# 或者指定引擎
# ocr = OCRRecognition(engine="windows", lang="en-US")  # Windows OCR
# ocr = OCRRecognition(engine="easyocr", lang=["en", "ch_sim"])  # EasyOCR
# ocr = OCRRecognition(engine="baidu")  # 百度OCR

# 从图像文件识别文本
results = ocr.recognize_text("screenshot.png", return_format="structured")

# 或者从PIL图像对象识别文本
from PIL import Image
image = Image.open("screenshot.png")
results = ocr.recognize_text(image, return_format="structured")

# 识别指定区域的文本
region = (100, 100, 400, 300)  # x, y, width, height
results = ocr.recognize_text(image, region=region, return_format="structured")

# 获取纯文本结果
text = ocr.recognize_text(image, return_format="text")

# 打印识别结果
print("\n识别结果:")
for item in results:
    print(f"文本: {item['text']}")
    print(f"位置: {item['box']}")
    print(f"置信度: {item['confidence']}")
```

### UI元素识别

```python
from src.ui_automation import UIAutomation

# 创建UI自动化对象，默认使用win32gui后端
ui = UIAutomation()

# 或者指定后端
# ui = UIAutomation(backend="win32gui")  # 窗口级操作
# ui = UIAutomation(backend="pywinauto")  # 窗口内元素识别

# 获取前台窗口
window = ui.get_foreground_window()

# 通过标题获取窗口
window = ui.get_window_by_title("记事本")

# 获取UI元素（必须指定元素类型）
elements = ui.get_ui_elements(window, element_type="Button", depth=3, visible_only=True)

# 通过文本查找元素（必须指定元素类型）
properties = ui.find_element_by_text("文件", element_type="Button")

# 获取元素的坐标信息
print(f"元素中心点坐标: {properties['center']['x']}, {properties['center']['y']}")
print(f"元素矩形区域: {properties['rectangle']}")

# 窗口操作
ui.activate_window(window)  # 激活窗口
ui.maximize_window(window)  # 最大化窗口
ui.minimize_window(window)  # 最小化窗口
ui.restore_window(window)   # 还原窗口
ui.resize_window(window, 800, 600)  # 调整窗口大小
ui.move_window(window, 100, 100)    # 移动窗口
```

### 启动MCP服务器

```python
# 使用命令行启动MCP服务器
python start_server.py --transport stdio --ocr-engine auto --ui-backend win32gui

# 或者在代码中启动
from src.server import AutoVisionServer

# 创建并运行服务器
server = AutoVisionServer(ocr_engine="auto", ui_backend="win32gui", vision_llm=True)
server.run(transport="stdio")
```

## 开发计划

1. **第一阶段**：基础屏幕截图和OCR（已完成）
   - 实现屏幕截图功能
   - 实现基本OCR文本识别
   - 提供简单的图像保存和加载

2. **第二阶段**：UI元素识别（已完成）
   - 实现基于辅助技术API的UI元素识别
   - 提供元素查找和属性获取功能
   - 实现基于图像的UI元素检测

3. **第三阶段**：优化与完善（进行中）
   - 优化性能和用户体验
   - 完善文档和示例
   - 提高各模块的稳定性和可靠性

## 贡献指南

欢迎贡献代码、报告问题或提出改进建议。

## 许可证

MIT

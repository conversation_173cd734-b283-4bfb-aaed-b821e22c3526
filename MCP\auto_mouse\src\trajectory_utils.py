"""
轨迹处理工具模块，提供轨迹处理和路径生成的功能。
"""

import math
import random
import numpy as np
from typing import List, Dict, Tuple, Any, Optional


def calculate_linear_path(start_x: int, start_y: int, end_x: int, end_y: int, num_points: int = 50) -> List[Dict[str, Any]]:
    """
    计算两点之间的直线路径。
    
    Args:
        start_x: 起始X坐标
        start_y: 起始Y坐标
        end_x: 结束X坐标
        end_y: 结束Y坐标
        num_points: 路径点数量
    
    Returns:
        list: 路径点列表
    """
    path = []
    
    # 确保至少有2个点
    num_points = max(2, num_points)
    
    for i in range(num_points):
        t = i / (num_points - 1)
        x = int(start_x + (end_x - start_x) * t)
        y = int(start_y + (end_y - start_y) * t)
        
        path.append({
            "x": x,
            "y": y,
            "time": t,  # 归一化时间
            "dwell_time": 0
        })
    
    return path


def find_nearest_trajectory_points(path_points: List[Dict[str, Any]], 
                                  trajectory_points: List[Dict[str, Any]], 
                                  max_distance: int = 20) -> List[Dict[str, Any]]:
    """
    为路径上的每个点找到轨迹库中最近的点。
    
    Args:
        path_points: 路径点列表
        trajectory_points: 轨迹库中的点列表
        max_distance: 最大搜索距离
    
    Returns:
        list: 匹配的轨迹点列表
    """
    matched_points = []
    
    for path_point in path_points:
        px, py = path_point["x"], path_point["y"]
        
        # 找到距离当前路径点最近的轨迹点
        nearest_points = []
        for traj_point in trajectory_points:
            tx, ty = traj_point["x"], traj_point["y"]
            distance = math.sqrt((px - tx) ** 2 + (py - ty) ** 2)
            
            if distance <= max_distance:
                nearest_points.append({
                    "point": traj_point,
                    "distance": distance
                })
        
        # 如果找到了匹配的点，添加到结果中
        if nearest_points:
            # 按距离排序
            nearest_points.sort(key=lambda p: p["distance"])
            
            # 取最近的几个点（最多5个）
            top_points = nearest_points[:min(5, len(nearest_points))]
            
            # 添加到匹配结果
            matched_points.append({
                "path_point": path_point,
                "matched_points": top_points
            })
        else:
            # 如果没有找到匹配的点，使用原始路径点
            matched_points.append({
                "path_point": path_point,
                "matched_points": []
            })
    
    return matched_points


def blend_trajectory_points(matched_points: List[Dict[str, Any]], screen_width: int, screen_height: int) -> List[Dict[str, Any]]:
    """
    将路径点和匹配的轨迹点混合，生成新的轨迹。
    
    Args:
        matched_points: 匹配的轨迹点列表
        screen_width: 屏幕宽度
        screen_height: 屏幕高度
    
    Returns:
        list: 混合后的轨迹点列表
    """
    blended_path = []
    
    for match in matched_points:
        path_point = match["path_point"]
        matched_traj_points = match["matched_points"]
        
        if not matched_traj_points:
            # 如果没有匹配的轨迹点，使用原始路径点
            blended_path.append(path_point.copy())
        else:
            # 计算加权平均值
            total_weight = 0
            weighted_x = 0
            weighted_y = 0
            total_dwell_time = 0
            
            # 路径点的权重
            path_weight = 1.0
            weighted_x += path_point["x"] * path_weight
            weighted_y += path_point["y"] * path_weight
            total_weight += path_weight
            
            # 轨迹点的权重（距离越近权重越大）
            for mp in matched_traj_points:
                traj_point = mp["point"]
                distance = mp["distance"]
                
                # 距离越近权重越大
                weight = 1.0 / (distance + 1)
                
                weighted_x += traj_point["x"] * weight
                weighted_y += traj_point["y"] * weight
                total_dwell_time += traj_point.get("dwell_time", 0)
                total_weight += weight
            
            # 计算加权平均值
            avg_x = int(weighted_x / total_weight)
            avg_y = int(weighted_y / total_weight)
            avg_dwell_time = total_dwell_time / len(matched_traj_points) if matched_traj_points else 0
            
            # 确保坐标在屏幕范围内
            avg_x = max(0, min(avg_x, screen_width - 1))
            avg_y = max(0, min(avg_y, screen_height - 1))
            
            # 添加到混合路径
            blended_path.append({
                "x": avg_x,
                "y": avg_y,
                "time": path_point["time"],
                "dwell_time": avg_dwell_time
            })
    
    return blended_path


def distribute_time(blended_path: List[Dict[str, Any]], total_time: float) -> List[Dict[str, Any]]:
    """
    为混合路径分配时间。
    
    Args:
        blended_path: 混合后的轨迹点列表
        total_time: 总时间（秒）
    
    Returns:
        list: 添加了绝对时间的轨迹点列表
    """
    # 如果路径为空，返回空列表
    if not blended_path:
        return []
    
    # 计算总距离
    total_distance = 0
    for i in range(1, len(blended_path)):
        x1, y1 = blended_path[i-1]["x"], blended_path[i-1]["y"]
        x2, y2 = blended_path[i]["x"], blended_path[i]["y"]
        distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
        total_distance += distance
    
    # 分配时间
    timed_path = []
    current_time = 0
    
    for i, point in enumerate(blended_path):
        if i > 0:
            # 计算当前段的距离
            x1, y1 = blended_path[i-1]["x"], blended_path[i-1]["y"]
            x2, y2 = point["x"], point["y"]
            distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
            
            # 根据距离分配时间（加入一些随机性）
            segment_time = (distance / total_distance) * total_time
            segment_time *= random.uniform(0.8, 1.2)  # 添加20%的随机性
            
            # 更新当前时间
            current_time += segment_time
        
        # 添加到时间路径
        timed_path.append({
            "x": point["x"],
            "y": point["y"],
            "time": current_time,
            "dwell_time": point["dwell_time"]
        })
    
    # 确保最后一个点的时间等于总时间
    if timed_path:
        timed_path[-1]["time"] = total_time
    
    return timed_path


def smooth_path(path: List[Dict[str, Any]], smoothing_factor: float = 0.2) -> List[Dict[str, Any]]:
    """
    平滑路径，使其更加自然。
    
    Args:
        path: 路径点列表
        smoothing_factor: 平滑因子（0-1之间）
    
    Returns:
        list: 平滑后的路径点列表
    """
    if len(path) <= 2:
        return path
    
    smoothed = [path[0]]  # 保持第一个点不变
    
    for i in range(1, len(path) - 1):
        prev = path[i - 1]
        curr = path[i]
        next_point = path[i + 1]
        
        # 计算平滑后的坐标
        smooth_x = (1 - smoothing_factor) * curr["x"] + smoothing_factor * (prev["x"] + next_point["x"]) / 2
        smooth_y = (1 - smoothing_factor) * curr["y"] + smoothing_factor * (prev["y"] + next_point["y"]) / 2
        
        # 创建平滑后的点
        smoothed.append({
            "x": int(smooth_x),
            "y": int(smooth_y),
            "time": curr["time"],
            "dwell_time": curr["dwell_time"]
        })
    
    smoothed.append(path[-1])  # 保持最后一个点不变
    
    return smoothed

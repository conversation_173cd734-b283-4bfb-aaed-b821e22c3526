#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI元素识别模块 (Win32GUI实现)

提供UI元素识别功能，基于Win32GUI API。
专注于窗口级别的操作，不深入到应用程序内部UI元素。
"""

import time
import random
import os
import sys
import win32gui
import win32con
import win32api
import win32process
import ctypes
from ctypes import wintypes

# 添加当前目录到路径，确保可以导入同级模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from .ui_automation_base import UIAutomationBase

# 定义一些常量
SW_HIDE = 0
SW_NORMAL = 1
SW_MINIMIZE = 6
SW_MAXIMIZE = 3
SW_RESTORE = 9

class UIAutomationWin32(UIAutomationBase):
    """UI元素识别类，提供基于Win32GUI的窗口级操作功能"""

    def __init__(self):
        """初始化UI元素识别类"""
        self._last_found_windows = []  # 缓存上次查找的窗口列表
        print("Win32GUI UI自动化引擎初始化成功")

    def get_foreground_window(self):
        """
        获取前台窗口

        Returns:
            窗口句柄
        """
        hwnd = win32gui.GetForegroundWindow()
        return hwnd

    def get_window_by_title(self, title, partial_match=True):
        """
        通过标题获取窗口

        Args:
            title: 窗口标题
            partial_match: 是否部分匹配

        Returns:
            窗口句柄列表
        """
        result = []

        def enum_windows_callback(hwnd, result_list):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if window_text:
                    if partial_match and title in window_text:
                        result_list.append(hwnd)
                    elif not partial_match and title == window_text:
                        result_list.append(hwnd)

        win32gui.EnumWindows(enum_windows_callback, result)
        self._last_found_windows = result
        return result

    def get_window_info(self, hwnd):
        """
        获取窗口信息

        Args:
            hwnd: 窗口句柄

        Returns:
            窗口信息字典
        """
        if not hwnd:
            return None

        try:
            # 获取窗口标题
            title = win32gui.GetWindowText(hwnd)

            # 获取窗口类名
            class_name = win32gui.GetClassName(hwnd)

            # 获取窗口矩形
            rect = win32gui.GetWindowRect(hwnd)
            left, top, right, bottom = rect
            width = right - left
            height = bottom - top

            # 获取窗口状态
            placement = win32gui.GetWindowPlacement(hwnd)
            is_minimized = placement[1] == win32con.SW_SHOWMINIMIZED
            is_maximized = placement[1] == win32con.SW_SHOWMAXIMIZED

            # 获取窗口样式
            style = win32api.GetWindowLong(hwnd, win32con.GWL_STYLE)
            ex_style = win32api.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)

            # 检查窗口样式
            is_popup = (style & win32con.WS_POPUP) != 0
            is_child = (style & win32con.WS_CHILD) != 0
            is_visible = (style & win32con.WS_VISIBLE) != 0
            is_disabled = (style & win32con.WS_DISABLED) != 0
            is_topmost = (ex_style & win32con.WS_EX_TOPMOST) != 0

            # 获取进程ID和线程ID
            thread_id, process_id = win32process.GetWindowThreadProcessId(hwnd)

            # 计算中心点
            center_x = (left + right) // 2
            center_y = (top + bottom) // 2

            return {
                "hwnd": hwnd,
                "title": title,
                "class_name": class_name,
                "rectangle": {
                    "left": left,
                    "top": top,
                    "right": right,
                    "bottom": bottom,
                    "width": width,
                    "height": height
                },
                "center": {
                    "x": center_x,
                    "y": center_y
                },
                "state": {
                    "minimized": is_minimized,
                    "maximized": is_maximized,
                    "visible": is_visible,
                    "enabled": not is_disabled,
                    "topmost": is_topmost,
                    "popup": is_popup,
                    "child": is_child
                },
                "process_id": process_id,
                "thread_id": thread_id
            }
        except Exception as e:
            print(f"获取窗口信息时出错: {e}")
            return None

    def get_all_windows(self, visible_only=True, min_size=50):
        """
        获取所有窗口

        Args:
            visible_only: 是否只返回可见窗口
            min_size: 最小窗口尺寸，过滤掉太小的窗口

        Returns:
            窗口信息列表
        """
        windows = []

        def enum_windows_callback(hwnd, result_list):
            if not visible_only or win32gui.IsWindowVisible(hwnd):
                try:
                    # 获取窗口标题
                    title = win32gui.GetWindowText(hwnd)

                    # 获取窗口类名
                    class_name = win32gui.GetClassName(hwnd)

                    # 过滤掉一些系统窗口
                    if class_name in ['Shell_TrayWnd', 'Shell_SecondaryTrayWnd', 'Progman', 'WorkerW']:
                        return

                    # 获取窗口矩形
                    rect = win32gui.GetWindowRect(hwnd)
                    left, top, right, bottom = rect
                    width = right - left
                    height = bottom - top

                    # 过滤掉太小的窗口
                    if width < min_size or height < min_size:
                        return

                    # 获取窗口信息
                    window_info = self.get_window_info(hwnd)
                    if window_info:
                        result_list.append(window_info)
                except Exception as e:
                    print(f"枚举窗口时出错: {e}")

        win32gui.EnumWindows(enum_windows_callback, windows)
        self._last_found_windows = [w["hwnd"] for w in windows]
        return windows

    def get_window_z_order(self):
        """
        获取窗口Z序（从前到后）

        Returns:
            窗口信息列表（按Z序排序）和前台窗口信息
        """
        # 获取所有窗口
        windows = self.get_all_windows()
        window_dict = {w["hwnd"]: w for w in windows}

        # 按Z序排序（从前到后）
        z_ordered_windows = []

        # 获取前台窗口
        foreground_hwnd = win32gui.GetForegroundWindow()
        foreground_window = window_dict.get(foreground_hwnd)

        # 遍历所有窗口，按Z序排列
        hwnd = foreground_hwnd
        while hwnd and hwnd in window_dict:
            z_ordered_windows.append(window_dict[hwnd])
            # 获取下一个窗口（Z序中的下一个）
            hwnd = win32gui.GetWindow(hwnd, win32con.GW_HWNDNEXT)

        # 添加未通过Z序找到的窗口（可能是因为它们不在同一个层级）
        for window in windows:
            if window not in z_ordered_windows:
                z_ordered_windows.append(window)

        return z_ordered_windows, foreground_window

    def get_ui_elements(self, window=None, element_type=None, depth=3, visible_only=True):
        """
        获取UI元素及其属性

        Args:
            window: 窗口对象，为None则获取前台窗口
            element_type: 元素类型，必须指定，例如："Window"
            depth: 树的深度
            visible_only: 是否只返回可见元素

        Returns:
            元素列表，每个元素包含其属性（包括坐标信息）
        """
        # 对于win32gui，我们只能获取窗口级别的信息，不能获取窗口内部的UI元素
        # 因此，我们只支持"Window"类型
        elements = []

        if element_type is None:
            raise ValueError("必须指定element_type参数，以避免返回过多数据")

        if element_type != "Window":
            # Win32GUI只支持窗口级别操作，不支持其他类型的元素
            print(f"Win32GUI后端只支持'Window'类型，不支持'{element_type}'类型")
            return []

        if window is None:
            window = self.get_foreground_window()

        if isinstance(window, int):  # 如果是窗口句柄
            window_info = self.get_window_info(window)
            if window_info:
                elements.append({
                    "name": window_info["title"],
                    "control_type": "Window",
                    "automation_id": "",
                    "class_name": window_info["class_name"],
                    "rectangle": window_info["rectangle"],
                    "center": window_info["center"],
                    "value": None,
                    "enabled": window_info["state"]["enabled"],
                    "visible": window_info["state"]["visible"]
                })

        # 如果需要获取所有窗口，可以使用get_all_windows方法
        if depth > 1:
            # 获取所有窗口
            all_windows = self.get_all_windows(visible_only=visible_only)
            for window_info in all_windows:
                elements.append({
                    "name": window_info["title"],
                    "control_type": "Window",
                    "automation_id": "",
                    "class_name": window_info["class_name"],
                    "rectangle": window_info["rectangle"],
                    "center": window_info["center"],
                    "value": None,
                    "enabled": window_info["state"]["enabled"],
                    "visible": window_info["state"]["visible"]
                })

        return elements

    def find_element_by_text(self, text, element_type=None, window=None, match_type="contains", visible_only=True, timeout=5):
        """
        通过文本查找UI元素

        Args:
            text: 要查找的文本
            element_type: 元素类型，必须指定，例如："Window"
            window: 窗口对象，为None则在前台窗口中查找
            match_type: 匹配类型，可选值：exact, contains, starts_with, ends_with
            visible_only: 是否只查找可见元素
            timeout: 超时时间（秒）

        Returns:
            元素属性字典，包含元素的坐标信息
        """
        if element_type is None:
            raise ValueError("必须指定element_type参数，以避免返回过多数据")

        if element_type != "Window":
            # Win32GUI只支持窗口级别操作，不支持其他类型的元素
            print(f"Win32GUI后端只支持'Window'类型，不支持'{element_type}'类型")
            return None

        start_time = time.time()
        while time.time() - start_time < timeout:
            # 对于win32gui，我们只能查找窗口标题，不能查找窗口内部的UI元素
            try:
                # 根据匹配类型查找窗口
                if match_type == "exact":
                    windows = self.get_window_by_title(text, partial_match=False)
                elif match_type == "contains":
                    windows = self.get_window_by_title(text, partial_match=True)
                elif match_type == "starts_with":
                    # 手动实现starts_with匹配
                    all_windows = self.get_all_windows()
                    windows = [w["hwnd"] for w in all_windows if w["title"].startswith(text)]
                elif match_type == "ends_with":
                    # 手动实现ends_with匹配
                    all_windows = self.get_all_windows()
                    windows = [w["hwnd"] for w in all_windows if w["title"].endswith(text)]
                else:
                    raise ValueError(f"不支持的匹配类型: {match_type}")

                if windows:
                    # 返回第一个匹配的窗口
                    hwnd = windows[0]
                    window_info = self.get_window_info(hwnd)

                    if window_info:
                        return {
                            "name": window_info["title"],
                            "control_type": "Window",
                            "automation_id": "",
                            "class_name": window_info["class_name"],
                            "rectangle": window_info["rectangle"],
                            "center": window_info["center"],
                            "value": None,
                            "enabled": window_info["state"]["enabled"],
                            "visible": window_info["state"]["visible"]
                        }
            except Exception as e:
                print(f"查找窗口时出错: {e}")

            time.sleep(0.5)

        return None



    def check_window_visibility(self, windows):
        """
        检查窗口可见性（是否被其他窗口遮挡）

        Args:
            windows: 窗口信息列表（按Z序排序）

        Returns:
            更新了可见性信息的窗口列表
        """
        # 按Z序从前到后遍历窗口
        for i, window in enumerate(windows):
            hwnd = window["hwnd"]
            rect = window["rectangle"]

            # 如果窗口最小化，则跳过
            if window["state"]["minimized"]:
                window["visible_area"] = 0
                window["visible_ratio"] = 0
                window["is_fully_visible"] = False
                window["is_partially_visible"] = False
                window["is_hidden"] = True
                continue

            # 计算窗口面积
            window_area = rect["width"] * rect["height"]

            # 初始可见区域为整个窗口
            visible_area = window_area

            # 检查是否被前面的窗口遮挡
            for j in range(i):
                front_window = windows[j]

                # 如果前面的窗口最小化，则跳过
                if front_window["state"]["minimized"]:
                    continue

                # 计算重叠区域
                front_rect = front_window["rectangle"]

                # 检查是否有重叠
                if (rect["left"] < front_rect["right"] and rect["right"] > front_rect["left"] and
                    rect["top"] < front_rect["bottom"] and rect["bottom"] > front_rect["top"]):

                    # 计算重叠区域
                    overlap_left = max(rect["left"], front_rect["left"])
                    overlap_top = max(rect["top"], front_rect["top"])
                    overlap_right = min(rect["right"], front_rect["right"])
                    overlap_bottom = min(rect["bottom"], front_rect["bottom"])

                    overlap_width = overlap_right - overlap_left
                    overlap_height = overlap_bottom - overlap_top
                    overlap_area = overlap_width * overlap_height

                    # 减去重叠区域
                    visible_area -= overlap_area

            # 存储可见区域比例
            visible_ratio = visible_area / window_area if window_area > 0 else 0

            # 更新窗口信息
            window["visible_area"] = visible_area
            window["visible_ratio"] = visible_ratio
            window["is_fully_visible"] = visible_ratio >= 0.99  # 考虑浮点误差
            window["is_partially_visible"] = 0 < visible_ratio < 0.99
            window["is_hidden"] = visible_ratio <= 0

        return windows

    def activate_window(self, hwnd):
        """
        激活窗口

        Args:
            hwnd: 窗口句柄

        Returns:
            是否成功
        """
        try:
            # 检查窗口是否最小化
            if win32gui.IsIconic(hwnd):
                # 恢复窗口
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(0.1)

            # 激活窗口
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.1)

            return True
        except Exception as e:
            print(f"激活窗口时出错: {e}")
            return False

    def maximize_window(self, hwnd):
        """
        最大化窗口

        Args:
            hwnd: 窗口句柄

        Returns:
            是否成功
        """
        try:
            win32gui.ShowWindow(hwnd, win32con.SW_MAXIMIZE)
            time.sleep(0.1)
            return True
        except Exception as e:
            print(f"最大化窗口时出错: {e}")
            return False

    def minimize_window(self, hwnd):
        """
        最小化窗口

        Args:
            hwnd: 窗口句柄

        Returns:
            是否成功
        """
        try:
            win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)
            time.sleep(0.1)
            return True
        except Exception as e:
            print(f"最小化窗口时出错: {e}")
            return False

    def restore_window(self, hwnd):
        """
        还原窗口

        Args:
            hwnd: 窗口句柄

        Returns:
            是否成功
        """
        try:
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            time.sleep(0.1)
            return True
        except Exception as e:
            print(f"还原窗口时出错: {e}")
            return False

    def resize_window(self, hwnd, width, height):
        """
        调整窗口大小

        Args:
            hwnd: 窗口句柄
            width: 宽度
            height: 高度

        Returns:
            是否成功
        """
        try:
            # 获取当前窗口位置
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)

            # 使用MoveWindow函数调整窗口大小
            win32gui.MoveWindow(
                hwnd,       # 窗口句柄
                left,       # X坐标
                top,        # Y坐标
                width,      # 宽度
                height,     # 高度
                True        # 重绘
            )

            time.sleep(0.1)
            return True
        except Exception as e:
            print(f"调整窗口大小时出错: {e}")
            return False

    def move_window(self, hwnd, x, y):
        """
        移动窗口

        Args:
            hwnd: 窗口句柄
            x: X坐标
            y: Y坐标

        Returns:
            是否成功
        """
        try:
            # 获取当前窗口大小
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)
            width = right - left
            height = bottom - top

            # 使用MoveWindow函数移动窗口
            win32gui.MoveWindow(
                hwnd,       # 窗口句柄
                x,          # X坐标
                y,          # Y坐标
                width,      # 宽度
                height,     # 高度
                True        # 重绘
            )

            time.sleep(0.1)
            return True
        except Exception as e:
            print(f"移动窗口时出错: {e}")
            return False


# 测试代码
if __name__ == "__main__":
    # 创建UI自动化对象
    ui = UIAutomationWin32()

    # 获取前台窗口
    hwnd = ui.get_foreground_window()
    window_info = ui.get_window_info(hwnd)
    print(f"前台窗口: {window_info['title']} ({window_info['class_name']})")
    print(f"位置: {window_info['rectangle']}")
    print(f"状态: {window_info['state']}")

    # 获取所有窗口
    print("\n所有窗口:")
    windows = ui.get_all_windows()
    for i, window in enumerate(windows[:5]):  # 只显示前5个
        print(f"{i+1}. {window['title']} ({window['class_name']})")
        print(f"   位置: {window['rectangle']}")
        print(f"   状态: {window['state']}")
        print("-" * 50)

    # 获取窗口Z序
    print("\n窗口Z序:")
    z_windows, foreground = ui.get_window_z_order()
    z_windows = ui.check_window_visibility(z_windows)
    for i, window in enumerate(z_windows[:5]):  # 只显示前5个
        visibility = "完全可见" if window.get("is_fully_visible") else \
                    "部分可见" if window.get("is_partially_visible") else \
                    "被遮挡"
        print(f"{i+1}. {window['title']} ({window['class_name']})")
        print(f"   可见性: {visibility} ({window.get('visible_ratio', 0)*100:.1f}%)")
        print("-" * 50)

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
屏幕分析服务模块

提供屏幕分析服务，结合屏幕截图、图像增强和视觉LLM功能。
"""

import os
import sys
import time
from datetime import datetime

# 导入自定义模块
from ..screen_capture_full import FullScreenCapture
from ..screen_capture_region import RegionScreenCapture
from .image_enhancer import ImageEnhancer
from .vision_llm import VisionLLM


class ScreenAnalyzer:
    """
    屏幕分析服务类，提供屏幕分析服务

    这是屏幕截图模块的主要对外接口。由于MCP服务器无法直接将截图返回给主LLM，
    所以所有截图操作都会通过辅助LLM进行分析，然后将分析结果作为文本返回给主LLM。
    """

    def __init__(self, screenshots_dir="screenshots", api_key=None, model=None):
        """
        初始化屏幕分析服务类

        Args:
            screenshots_dir: 截图保存目录
            api_key: Gemini API密钥，为None则使用默认值或环境变量
            model: Gemini模型名称，为None则使用默认值
        """
        self.screenshots_dir = screenshots_dir

        # 确保截图目录存在
        if not os.path.exists(screenshots_dir):
            os.makedirs(screenshots_dir)

        # 创建屏幕截图对象（内部使用，不对外暴露）
        self.full_capture = FullScreenCapture(screenshots_dir)
        self.region_capture = RegionScreenCapture(screenshots_dir)

        # 创建图像增强对象
        self.enhancer = ImageEnhancer()

        # 创建视觉LLM对象
        self.vision_llm = VisionLLM(api_key=api_key, model=model)

        # 保存最新截图的路径
        self.latest_screenshot = None
        self.latest_enhanced = None

    def capture_and_analyze_screen(self, question, region=None, suffix="latest"):
        """
        捕获并分析屏幕

        Args:
            question: 问题
            region: 截图区域 [x, y, width, height]，为None则截取全屏
            suffix: 文件名后缀

        Returns:
            dict: 分析结果
        """
        try:
            # 捕获屏幕
            if region is None:
                screenshot, screenshot_path = self.full_capture.capture_screen(suffix=suffix)
            else:
                x, y, width, height = region
                screenshot, screenshot_path = self.region_capture.capture_region(
                    x, y, width, height, suffix=suffix
                )

            # 保存最新截图路径
            self.latest_screenshot = screenshot_path

            # 增强图像
            if region is None:
                enhanced_path = self.enhancer.enhance_image(screenshot_path)
            else:
                enhanced_path = self.enhancer.enhance_image(screenshot_path, region_coords=region)

            # 保存最新增强图像路径
            self.latest_enhanced = enhanced_path

            # 分析屏幕截图
            analysis = self.vision_llm.analyze_screenshot(
                screenshot_path, enhanced_path, question
            )

            return {
                "success": True,
                "screenshot_path": screenshot_path,
                "enhanced_path": enhanced_path,
                "analysis": analysis
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def analyze_latest_screenshot(self, question):
        """
        分析最新的屏幕截图

        Args:
            question: 问题

        Returns:
            dict: 分析结果
        """
        if self.latest_screenshot is None or self.latest_enhanced is None:
            return {
                "success": False,
                "error": "没有可用的屏幕截图，请先捕获屏幕"
            }

        try:
            # 分析屏幕截图
            analysis = self.vision_llm.analyze_screenshot(
                self.latest_screenshot, self.latest_enhanced, question
            )

            return {
                "success": True,
                "screenshot_path": self.latest_screenshot,
                "enhanced_path": self.latest_enhanced,
                "analysis": analysis
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def get_screen_size(self):
        """
        获取屏幕尺寸

        Returns:
            tuple: (宽度, 高度)
        """
        return self.full_capture.get_screen_size()


# 测试代码
if __name__ == "__main__":
    # 创建屏幕分析服务对象
    analyzer = ScreenAnalyzer()

    # 捕获并分析全屏
    print("捕获并分析全屏...")
    result = analyzer.capture_and_analyze_screen(
        question="这个屏幕上有什么内容？请描述主要元素及其位置。"
    )

    if result["success"]:
        print(f"屏幕截图已保存至: {result['screenshot_path']}")
        print(f"增强后的图像已保存至: {result['enhanced_path']}")
        print(f"分析结果:\n{result['analysis']}")
    else:
        print(f"分析失败: {result['error']}")

    # 捕获并分析区域
    print("\n捕获并分析区域...")
    # 获取屏幕尺寸
    screen_width, screen_height = analyzer.full_capture.get_screen_size()

    # 定义区域（屏幕中央区域）
    region = (
        screen_width // 4,
        screen_height // 4,
        screen_width // 2,
        screen_height // 2
    )

    result = analyzer.capture_and_analyze_screen(
        question="这个区域中有什么内容？请描述主要元素及其位置。",
        region=region,
        suffix="region"
    )

    if result["success"]:
        print(f"区域截图已保存至: {result['screenshot_path']}")
        print(f"增强后的图像已保存至: {result['enhanced_path']}")
        print(f"分析结果:\n{result['analysis']}")
    else:
        print(f"分析失败: {result['error']}")

    # 分析最新的屏幕截图
    print("\n分析最新的屏幕截图...")
    result = analyzer.analyze_latest_screenshot(
        question="屏幕上有什么颜色？"
    )

    if result["success"]:
        print(f"分析结果:\n{result['analysis']}")
    else:
        print(f"分析失败: {result['error']}")

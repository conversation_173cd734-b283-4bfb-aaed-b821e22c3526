// -*- coding: utf-8 -*-

import { useState, useEffect, useRef, useCallback } from 'react';
import { WebSocketService } from '../services/websocket';
import { WebSocketMessage } from '../types';

/**
 * WebSocket Hook
 * 专门负责WebSocket连接状态管理
 * 严格按照代码规范的单一职责原则
 */
export function useWebSocket(url: string) {
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const wsServiceRef = useRef<WebSocketService | null>(null);
  const messageHandlersRef = useRef<Map<string, (message: WebSocketMessage) => void>>(new Map());

  /**
   * 初始化WebSocket服务
   */
  useEffect(() => {
    wsServiceRef.current = new WebSocketService(url);
    
    // 注册连接状态变化处理器
    wsServiceRef.current.onConnectionStateChange((connected) => {
      setIsConnected(connected);
      if (connected) {
        setConnectionError(null);
      }
    });

    return () => {
      if (wsServiceRef.current) {
        wsServiceRef.current.disconnect();
      }
    };
  }, [url]);

  /**
   * 连接WebSocket
   */
  const connect = useCallback(async () => {
    if (wsServiceRef.current) {
      try {
        await wsServiceRef.current.connect();
        setConnectionError(null);
      } catch (error) {
        setConnectionError(error instanceof Error ? error.message : '连接失败');
      }
    }
  }, []);

  /**
   * 断开WebSocket连接
   */
  const disconnect = useCallback(() => {
    if (wsServiceRef.current) {
      wsServiceRef.current.disconnect();
    }
  }, []);

  /**
   * 发送消息
   */
  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (wsServiceRef.current) {
      wsServiceRef.current.sendMessage(message);
    }
  }, []);

  /**
   * 注册消息处理器
   */
  const onMessage = useCallback((
    type: string,
    handler: (message: WebSocketMessage) => void
  ) => {
    messageHandlersRef.current.set(type, handler);
    
    if (wsServiceRef.current) {
      wsServiceRef.current.onMessage(type, (message) => {
        setLastMessage(message);
        handler(message);
      });
    }
  }, []);

  /**
   * 移除消息处理器
   */
  const offMessage = useCallback((type: string) => {
    messageHandlersRef.current.delete(type);
    
    if (wsServiceRef.current) {
      wsServiceRef.current.offMessage(type);
    }
  }, []);

  /**
   * 发送聊天消息
   */
  const sendChatMessage = useCallback((content: string, sessionId: string) => {
    const message: WebSocketMessage = {
      type: 'command',
      data: {
        action: 'chat',
        content,
      },
      timestamp: new Date().toISOString(),
      sessionId,
    };
    sendMessage(message);
  }, [sendMessage]);

  /**
   * 发送系统命令
   */
  const sendSystemCommand = useCallback((command: string, parameters?: Record<string, any>) => {
    const message: WebSocketMessage = {
      type: 'command',
      data: {
        action: 'system',
        command,
        parameters,
      },
      timestamp: new Date().toISOString(),
      sessionId: 'system',
    };
    sendMessage(message);
  }, [sendMessage]);

  return {
    isConnected,
    connectionError,
    lastMessage,
    connect,
    disconnect,
    sendMessage,
    sendChatMessage,
    sendSystemCommand,
    onMessage,
    offMessage,
  };
}

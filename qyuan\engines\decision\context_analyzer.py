# -*- coding: utf-8 -*-
"""
上下文分析服务实现
专门负责分析和管理决策上下文信息
严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from .base import (
    ContextAnalyzerBase,
    DecisionContext
)

class IntelligentContextAnalyzer(ContextAnalyzerBase):
    """智能上下文分析器"""
    
    def __init__(self):
        super().__init__("IntelligentContextAnalyzer")
        self.logger = logging.getLogger(f"QYuan.Decision.{self.name}")
        
        # 上下文历史记录
        self.context_history: List[DecisionContext] = []
        self.max_history_size = 50
        
        # 状态变化检测器
        self.state_change_detectors = self._build_state_change_detectors()
        
        # 约束规则
        self.constraint_rules = self._build_constraint_rules()
    
    async def analyze_context(self, perception_data: Dict[str, Any]) -> DecisionContext:
        """分析决策上下文"""
        start_time = time.time()
        
        try:
            # 提取屏幕状态
            screen_state = self._extract_screen_state(perception_data)
            
            # 提取系统状态
            system_state = self._extract_system_state(perception_data)
            
            # 分析可用行动
            available_actions = self._analyze_available_actions(screen_state, system_state)
            
            # 识别约束条件
            constraints = self._identify_constraints(screen_state, system_state)
            
            # 提取目标信息
            goals = self._extract_goals(perception_data)
            
            # 获取用户历史
            user_history = self._get_relevant_user_history()
            
            # 构建上下文
            context = DecisionContext(
                current_screen_state=screen_state,
                system_state=system_state,
                user_history=user_history,
                available_actions=available_actions,
                constraints=constraints,
                goals=goals
            )
            
            # 缓存上下文
            self._cache_context(context)
            
            processing_time = time.time() - start_time
            self.logger.debug(f"上下文分析完成，耗时: {processing_time:.2f}秒")
            
            return context
            
        except Exception as e:
            self.logger.error(f"上下文分析失败: {e}")
            return DecisionContext()
    
    async def update_context(self, context: DecisionContext, new_data: Dict[str, Any]) -> DecisionContext:
        """更新上下文"""
        try:
            # 检测状态变化
            changes = self._detect_state_changes(context, new_data)
            
            # 更新屏幕状态
            if 'screen_analysis' in new_data:
                context.current_screen_state = self._extract_screen_state(new_data)
            
            # 更新系统状态
            if 'system_state' in new_data:
                context.system_state = self._extract_system_state(new_data)
            
            # 重新分析可用行动
            if changes.get('ui_changed', False):
                context.available_actions = self._analyze_available_actions(
                    context.current_screen_state, 
                    context.system_state
                )
            
            # 更新约束条件
            context.constraints.update(self._identify_constraints(
                context.current_screen_state, 
                context.system_state
            ))
            
            # 更新时间戳
            context.timestamp = datetime.now()
            
            self.logger.debug("上下文更新完成")
            return context
            
        except Exception as e:
            self.logger.error(f"上下文更新失败: {e}")
            return context
    
    def _extract_screen_state(self, perception_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取屏幕状态"""
        screen_state = {}
        
        # 从感知数据中提取屏幕分析结果
        if 'screen_analysis' in perception_data:
            analysis = perception_data['screen_analysis']
            
            screen_state.update({
                'screenshot_info': {
                    'width': getattr(analysis.screenshot, 'width', 0),
                    'height': getattr(analysis.screenshot, 'height', 0),
                    'timestamp': getattr(analysis.screenshot, 'timestamp', datetime.now().isoformat())
                },
                'ui_elements': self._process_ui_elements(analysis.ui_elements),
                'text_content': analysis.text_content,
                'visual_description': analysis.visual_description,
                'context_info': analysis.context_info or {}
            })
        
        return screen_state
    
    def _extract_system_state(self, perception_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取系统状态"""
        system_state = {}
        
        # 从感知数据中提取系统状态
        if 'system_state' in perception_data:
            state = perception_data['system_state']
            
            system_state.update({
                'active_window': state.get('active_window'),
                'system_resources': state.get('system_resources', {}),
                'running_processes': state.get('running_processes', [])[:10],  # 限制数量
                'window_list': state.get('window_list', [])[:20],  # 限制数量
                'capabilities': state.get('capabilities', {})
            })
        
        return system_state
    
    def _process_ui_elements(self, ui_elements: List) -> List[Dict[str, Any]]:
        """处理UI元素信息"""
        processed_elements = []
        
        for element in ui_elements[:50]:  # 限制元素数量
            try:
                element_info = {
                    'type': element.element_type.value if hasattr(element, 'element_type') else 'unknown',
                    'bounds': {
                        'x': element.bounds.x if hasattr(element, 'bounds') else 0,
                        'y': element.bounds.y if hasattr(element, 'bounds') else 0,
                        'width': element.bounds.width if hasattr(element, 'bounds') else 0,
                        'height': element.bounds.height if hasattr(element, 'bounds') else 0
                    },
                    'text': getattr(element, 'text', None),
                    'clickable': getattr(element, 'clickable', False),
                    'visible': getattr(element, 'visible', True),
                    'confidence': getattr(element, 'confidence', 0.0)
                }
                processed_elements.append(element_info)
            except Exception as e:
                self.logger.warning(f"处理UI元素失败: {e}")
                continue
        
        return processed_elements
    
    def _analyze_available_actions(self, screen_state: Dict[str, Any], system_state: Dict[str, Any]) -> List[str]:
        """分析可用行动"""
        available_actions = []
        
        # 基础行动总是可用
        available_actions.extend(['screenshot', 'wait', 'system_info'])
        
        # 根据UI元素分析可用行动
        ui_elements = screen_state.get('ui_elements', [])
        
        # 检查是否有可点击元素
        clickable_elements = [elem for elem in ui_elements if elem.get('clickable', False)]
        if clickable_elements:
            available_actions.extend(['click', 'double_click', 'right_click'])
        
        # 检查是否有输入框
        input_elements = [elem for elem in ui_elements if elem.get('type') == 'input']
        if input_elements:
            available_actions.extend(['type', 'clear', 'select_all'])
        
        # 检查是否有文本内容（可复制）
        text_content = screen_state.get('text_content', [])
        if text_content:
            available_actions.extend(['copy', 'select_text'])
        
        # 检查是否可以滚动
        if len(ui_elements) > 10:  # 简单判断：元素多可能需要滚动
            available_actions.extend(['scroll_up', 'scroll_down', 'scroll_left', 'scroll_right'])
        
        # 根据活动窗口分析导航行动
        active_window = system_state.get('active_window')
        if active_window:
            window_title = active_window.get('title', '').lower()
            if any(browser in window_title for browser in ['chrome', 'firefox', 'edge', 'safari']):
                available_actions.extend(['navigate_back', 'navigate_forward', 'refresh', 'navigate_url'])
        
        # 系统级行动
        available_actions.extend(['open_application', 'close_application', 'switch_window'])
        
        return list(set(available_actions))  # 去重
    
    def _identify_constraints(self, screen_state: Dict[str, Any], system_state: Dict[str, Any]) -> Dict[str, Any]:
        """识别约束条件"""
        constraints = {}
        
        # 屏幕尺寸约束
        screenshot_info = screen_state.get('screenshot_info', {})
        if screenshot_info:
            constraints['screen_bounds'] = {
                'max_x': screenshot_info.get('width', 1920),
                'max_y': screenshot_info.get('height', 1080)
            }
        
        # 系统资源约束
        system_resources = system_state.get('system_resources', {})
        if system_resources:
            memory_info = system_resources.get('memory', {})
            cpu_info = system_resources.get('cpu', {})
            
            # 如果系统资源紧张，限制某些操作
            if memory_info.get('percent', 0) > 90:
                constraints['memory_limited'] = True
            
            if cpu_info.get('percent', 0) > 90:
                constraints['cpu_limited'] = True
        
        # 应用程序约束
        active_window = system_state.get('active_window')
        if active_window:
            constraints['active_application'] = active_window.get('title', '')
            constraints['window_bounds'] = active_window.get('rect', {})
        
        # UI状态约束
        ui_elements = screen_state.get('ui_elements', [])
        enabled_elements = [elem for elem in ui_elements if elem.get('clickable', False)]
        constraints['interactive_elements_count'] = len(enabled_elements)
        
        # 时间约束
        constraints['analysis_time'] = datetime.now().isoformat()
        
        return constraints
    
    def _extract_goals(self, perception_data: Dict[str, Any]) -> List[str]:
        """提取目标信息"""
        goals = []
        
        # 从上下文信息中提取可能的目标
        context_info = perception_data.get('screen_analysis', {}).get('context_info', {})
        
        if context_info:
            # 根据应用类型推断目标
            app_type = context_info.get('application_type', '')
            if 'browser' in app_type.lower():
                goals.append('web_browsing')
            elif 'editor' in app_type.lower():
                goals.append('text_editing')
            elif 'settings' in app_type.lower():
                goals.append('system_configuration')
            
            # 根据当前任务推断目标
            current_task = context_info.get('current_task', '')
            if current_task:
                goals.append(f"task_{current_task.lower().replace(' ', '_')}")
        
        return goals
    
    def _get_relevant_user_history(self) -> List[Dict[str, Any]]:
        """获取相关的用户历史"""
        # 返回最近的上下文历史
        recent_contexts = self.context_history[-5:] if self.context_history else []
        
        history = []
        for ctx in recent_contexts:
            history.append({
                'timestamp': ctx.timestamp.isoformat(),
                'available_actions': ctx.available_actions,
                'constraints': ctx.constraints,
                'goals': ctx.goals
            })
        
        return history
    
    def _detect_state_changes(self, old_context: DecisionContext, new_data: Dict[str, Any]) -> Dict[str, bool]:
        """检测状态变化"""
        changes = {
            'ui_changed': False,
            'system_changed': False,
            'window_changed': False
        }
        
        try:
            # 检测UI变化
            if 'screen_analysis' in new_data:
                new_screen_state = self._extract_screen_state(new_data)
                if self._compare_screen_states(old_context.current_screen_state, new_screen_state):
                    changes['ui_changed'] = True
            
            # 检测系统变化
            if 'system_state' in new_data:
                new_system_state = self._extract_system_state(new_data)
                if self._compare_system_states(old_context.system_state, new_system_state):
                    changes['system_changed'] = True
            
            # 检测窗口变化
            old_window = old_context.system_state.get('active_window') if old_context.system_state else None
            new_window = new_data.get('system_state', {}).get('active_window')
            if old_window and new_window:
                if old_window.get('handle') != new_window.get('handle'):
                    changes['window_changed'] = True
        
        except Exception as e:
            self.logger.warning(f"状态变化检测失败: {e}")
        
        return changes
    
    def _compare_screen_states(self, old_state: Optional[Dict], new_state: Dict) -> bool:
        """比较屏幕状态"""
        if not old_state:
            return True
        
        # 简单比较：UI元素数量变化
        old_elements_count = len(old_state.get('ui_elements', []))
        new_elements_count = len(new_state.get('ui_elements', []))
        
        return abs(old_elements_count - new_elements_count) > 5
    
    def _compare_system_states(self, old_state: Optional[Dict], new_state: Dict) -> bool:
        """比较系统状态"""
        if not old_state:
            return True
        
        # 简单比较：活动窗口变化
        old_window = old_state.get('active_window', {}).get('handle')
        new_window = new_state.get('active_window', {}).get('handle')
        
        return old_window != new_window
    
    def _cache_context(self, context: DecisionContext):
        """缓存上下文"""
        self.context_history.append(context)
        
        # 限制历史记录大小
        if len(self.context_history) > self.max_history_size:
            self.context_history = self.context_history[-self.max_history_size:]
    
    def _build_state_change_detectors(self) -> Dict[str, callable]:
        """构建状态变化检测器"""
        return {
            'ui_change': self._detect_ui_change,
            'window_change': self._detect_window_change,
            'content_change': self._detect_content_change
        }
    
    def _build_constraint_rules(self) -> Dict[str, callable]:
        """构建约束规则"""
        return {
            'resource_constraints': self._check_resource_constraints,
            'ui_constraints': self._check_ui_constraints,
            'time_constraints': self._check_time_constraints
        }
    
    def _detect_ui_change(self, old_data: Dict, new_data: Dict) -> bool:
        """检测UI变化"""
        return True  # 简化实现
    
    def _detect_window_change(self, old_data: Dict, new_data: Dict) -> bool:
        """检测窗口变化"""
        return True  # 简化实现
    
    def _detect_content_change(self, old_data: Dict, new_data: Dict) -> bool:
        """检测内容变化"""
        return True  # 简化实现
    
    def _check_resource_constraints(self, context: DecisionContext) -> Dict[str, Any]:
        """检查资源约束"""
        return {}  # 简化实现
    
    def _check_ui_constraints(self, context: DecisionContext) -> Dict[str, Any]:
        """检查UI约束"""
        return {}  # 简化实现
    
    def _check_time_constraints(self, context: DecisionContext) -> Dict[str, Any]:
        """检查时间约束"""
        return {}  # 简化实现

# -*- coding: utf-8 -*-
"""
决策引擎主类实现
整合所有决策服务，提供统一的智能决策接口
严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Union

from .. import BaseEngine
from .base import (
    UserIntent,
    Task,
    ActionPlan,
    DecisionContext,
    DecisionResult,
    IntentType
)
from .intent_parser import NLPIntentParser
from .task_decomposer import IntelligentTaskDecomposer
from .action_planner import IntelligentActionPlanner
from .context_analyzer import IntelligentContextAnalyzer
from .memory_integrator import IntelligentMemoryIntegrator

class DecisionEngine(BaseEngine):
    """决策引擎主类"""
    
    def __init__(self, qyuan_core=None):
        super().__init__("Decision", qyuan_core)
        
        # 获取LLM客户端和记忆管理器
        llm_client = None
        memory_manager = None
        
        if qyuan_core:
            if hasattr(qyuan_core, 'llm_manager'):
                llm_client = qyuan_core.llm_manager.get_client()
            if hasattr(qyuan_core, 'memory_manager'):
                memory_manager = qyuan_core.memory_manager
        
        # 初始化各个决策服务
        self.intent_parser = NLPIntentParser(llm_client)
        self.task_decomposer = IntelligentTaskDecomposer(llm_client)
        self.action_planner = IntelligentActionPlanner(llm_client)
        self.context_analyzer = IntelligentContextAnalyzer()
        self.memory_integrator = IntelligentMemoryIntegrator(memory_manager)
        
        # 决策历史记录
        self.decision_history: List[DecisionResult] = []
        self.max_history_size = 100
        
        # 性能统计
        self.performance_stats = {
            "total_decisions": 0,
            "successful_decisions": 0,
            "average_processing_time": 0.0,
            "intent_parsing_time": 0.0,
            "planning_time": 0.0,
            "last_decision_time": None
        }
        
        # 当前决策上下文
        self.current_context: Optional[DecisionContext] = None
    
    async def _initialize(self):
        """初始化决策引擎"""
        self.logger.info("初始化决策引擎...")
        
        # 检查各个服务的可用性
        services_status = await self._check_services_availability()
        
        self.logger.info("决策引擎服务状态:")
        for service_name, status in services_status.items():
            status_text = "可用" if status else "不可用"
            self.logger.info(f"  {service_name}: {status_text}")
        
        # 如果没有任何服务可用，记录警告
        if not any(services_status.values()):
            self.logger.warning("所有决策服务都不可用，决策功能将受限")
        
        self.logger.info("决策引擎初始化完成")
    
    async def _start(self):
        """启动决策引擎"""
        self.logger.info("决策引擎启动完成")
    
    async def _stop(self):
        """停止决策引擎"""
        self.logger.info("决策引擎停止完成")
    
    async def _health_check(self) -> bool:
        """健康检查"""
        try:
            # 检查至少有一个服务可用
            services_status = await self._check_services_availability()
            return any(services_status.values())
        except Exception as e:
            self.logger.error(f"决策引擎健康检查失败: {e}")
            return False
    
    async def make_decision(self, user_input: str, perception_data: Optional[Dict[str, Any]] = None) -> DecisionResult:
        """做出决策"""
        start_time = time.time()
        
        try:
            self.logger.debug(f"开始决策过程: {user_input}")
            
            # 1. 分析上下文
            if perception_data:
                context = await self.context_analyzer.analyze_context(perception_data)
                self.current_context = context
            else:
                context = self.current_context or DecisionContext()
            
            # 2. 解析用户意图
            intent_start = time.time()
            intent = await self.intent_parser.parse_intent(user_input, context)
            intent_time = time.time() - intent_start
            
            # 3. 分解任务
            tasks = await self.task_decomposer.decompose_task(intent, context)
            
            # 4. 创建行动计划
            planning_start = time.time()
            action_plan = None
            
            if tasks:
                # 选择主要任务（通常是第一个或优先级最高的）
                main_task = self._select_main_task(tasks)
                action_plan = await self.action_planner.create_plan(main_task, context)
                
                # 优化计划
                action_plan = await self.action_planner.optimize_plan(action_plan, context)
            
            planning_time = time.time() - planning_start
            
            # 5. 检索相似案例进行参考
            similar_cases = await self.memory_integrator.retrieve_similar_cases(intent, limit=3)
            
            # 6. 计算决策置信度
            confidence = self._calculate_decision_confidence(intent, action_plan, similar_cases)
            
            # 7. 生成决策推理
            reasoning = self._generate_decision_reasoning(intent, action_plan, similar_cases, context)
            
            processing_time = time.time() - start_time
            
            # 8. 构建决策结果
            decision_result = DecisionResult(
                success=action_plan is not None,
                action_plan=action_plan,
                confidence=confidence,
                reasoning=reasoning,
                processing_time=processing_time
            )
            
            # 9. 存储决策记录
            if action_plan:
                await self._store_decision_record(intent, action_plan, decision_result)
            
            # 10. 更新统计信息
            self._update_performance_stats(True, processing_time, intent_time, planning_time)
            
            # 11. 缓存决策历史
            self._cache_decision_history(decision_result)
            
            self.logger.debug(f"决策完成，耗时: {processing_time:.2f}秒，置信度: {confidence:.2f}")
            
            return decision_result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"决策过程失败: {e}")
            
            # 更新统计信息
            self._update_performance_stats(False, processing_time, 0.0, 0.0)
            
            # 返回失败结果
            decision_result = DecisionResult(
                success=False,
                confidence=0.0,
                reasoning=f"决策失败: {str(e)}",
                error_message=str(e),
                processing_time=processing_time
            )
            
            self._cache_decision_history(decision_result)
            return decision_result
    
    async def update_context(self, perception_data: Dict[str, Any]):
        """更新决策上下文"""
        try:
            if self.current_context:
                self.current_context = await self.context_analyzer.update_context(
                    self.current_context, perception_data
                )
            else:
                self.current_context = await self.context_analyzer.analyze_context(perception_data)
        except Exception as e:
            self.logger.error(f"更新决策上下文失败: {e}")
    
    async def provide_feedback(self, decision_id: str, feedback: Dict[str, Any]):
        """提供决策反馈"""
        try:
            await self.memory_integrator.learn_from_feedback(decision_id, feedback)
            self.logger.debug(f"决策反馈已处理: {decision_id}")
        except Exception as e:
            self.logger.error(f"处理决策反馈失败: {e}")
    
    def _select_main_task(self, tasks: List[Task]) -> Task:
        """选择主要任务"""
        if not tasks:
            return None
        
        # 按优先级排序，选择优先级最高的任务
        sorted_tasks = sorted(tasks, key=lambda t: t.priority.value, reverse=True)
        return sorted_tasks[0]
    
    def _calculate_decision_confidence(self, intent: UserIntent, action_plan: Optional[ActionPlan], similar_cases: List[Dict[str, Any]]) -> float:
        """计算决策置信度"""
        if not action_plan:
            return 0.0
        
        # 基础置信度来自意图解析
        base_confidence = intent.confidence
        
        # 根据相似案例调整
        if similar_cases:
            successful_cases = [case for case in similar_cases if case.get('success', False)]
            success_rate = len(successful_cases) / len(similar_cases)
            base_confidence = (base_confidence + success_rate) / 2
        
        # 根据行动计划复杂度调整
        if action_plan.actions:
            complexity_factor = min(1.0, 1.0 - (len(action_plan.actions) - 1) * 0.1)
            base_confidence *= complexity_factor
        
        return min(1.0, max(0.0, base_confidence))
    
    def _generate_decision_reasoning(self, intent: UserIntent, action_plan: Optional[ActionPlan], similar_cases: List[Dict[str, Any]], context: DecisionContext) -> str:
        """生成决策推理"""
        reasoning_parts = []
        
        # 意图分析
        reasoning_parts.append(f"用户意图: {intent.description} (类型: {intent.intent_type.value}, 置信度: {intent.confidence:.2f})")
        
        # 行动计划
        if action_plan and action_plan.actions:
            reasoning_parts.append(f"生成了包含 {len(action_plan.actions)} 个行动的执行计划")
            reasoning_parts.append(f"预估执行时间: {action_plan.estimated_duration:.1f}秒")
        else:
            reasoning_parts.append("未能生成有效的行动计划")
        
        # 相似案例参考
        if similar_cases:
            successful_cases = [case for case in similar_cases if case.get('success', False)]
            reasoning_parts.append(f"参考了 {len(similar_cases)} 个相似案例，其中 {len(successful_cases)} 个成功")
        
        # 上下文信息
        if context.available_actions:
            reasoning_parts.append(f"当前环境支持 {len(context.available_actions)} 种操作")
        
        if context.constraints:
            reasoning_parts.append(f"识别到 {len(context.constraints)} 个约束条件")
        
        return "; ".join(reasoning_parts)
    
    async def _store_decision_record(self, intent: UserIntent, action_plan: ActionPlan, result: DecisionResult):
        """存储决策记录"""
        try:
            result_data = {
                'success': result.success,
                'confidence': result.confidence,
                'processing_time': result.processing_time,
                'error_message': result.error_message
            }
            
            await self.memory_integrator.store_decision(intent, action_plan, result_data)
        except Exception as e:
            self.logger.error(f"存储决策记录失败: {e}")
    
    async def _check_services_availability(self) -> Dict[str, bool]:
        """检查各个服务的可用性"""
        services = {
            "intent_parser": self.intent_parser,
            "task_decomposer": self.task_decomposer,
            "action_planner": self.action_planner,
            "context_analyzer": self.context_analyzer,
            "memory_integrator": self.memory_integrator
        }
        
        availability = {}
        
        for service_name, service in services.items():
            try:
                availability[service_name] = await service.is_available()
            except Exception as e:
                self.logger.warning(f"检查服务 {service_name} 可用性失败: {e}")
                availability[service_name] = False
        
        return availability
    
    def _update_performance_stats(self, success: bool, processing_time: float, intent_time: float, planning_time: float):
        """更新性能统计"""
        self.performance_stats["total_decisions"] += 1
        
        if success:
            self.performance_stats["successful_decisions"] += 1
        
        # 更新平均处理时间
        total = self.performance_stats["total_decisions"]
        current_avg = self.performance_stats["average_processing_time"]
        self.performance_stats["average_processing_time"] = (
            (current_avg * (total - 1) + processing_time) / total
        )
        
        # 更新各阶段时间
        current_intent_avg = self.performance_stats["intent_parsing_time"]
        self.performance_stats["intent_parsing_time"] = (
            (current_intent_avg * (total - 1) + intent_time) / total
        )
        
        current_planning_avg = self.performance_stats["planning_time"]
        self.performance_stats["planning_time"] = (
            (current_planning_avg * (total - 1) + planning_time) / total
        )
        
        self.performance_stats["last_decision_time"] = time.time()
    
    def _cache_decision_history(self, decision_result: DecisionResult):
        """缓存决策历史"""
        self.decision_history.append(decision_result)
        
        # 限制历史记录大小
        if len(self.decision_history) > self.max_history_size:
            self.decision_history = self.decision_history[-self.max_history_size:]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self.performance_stats.copy()
        
        # 计算成功率
        total = stats["total_decisions"]
        successful = stats["successful_decisions"]
        stats["success_rate"] = (successful / total) if total > 0 else 0.0
        
        # 添加记忆统计
        stats["memory_stats"] = self.memory_integrator.get_learning_stats()
        
        return stats
    
    def get_decision_history(self, limit: int = 10) -> List[DecisionResult]:
        """获取决策历史记录"""
        return self.decision_history[-limit:] if self.decision_history else []
    
    def get_current_context(self) -> Optional[DecisionContext]:
        """获取当前决策上下文"""
        return self.current_context

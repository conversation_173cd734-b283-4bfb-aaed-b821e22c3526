#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试区域坐标功能

测试区域截图时坐标是否正确显示。
"""

import os
import sys
import time
from PIL import Image

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入屏幕截图模块
from src.screen_capture import ScreenCapture, RegionScreenCapture, ImageEnhancer


def test_region_coordinates():
    """测试区域坐标功能"""
    print("\n测试区域坐标功能...")
    
    # 创建区域截图对象
    region_capture = RegionScreenCapture()
    
    # 获取屏幕尺寸
    screen_width, screen_height = region_capture.get_screen_size()
    print(f"屏幕尺寸: {screen_width} x {screen_height}")
    
    # 定义测试区域（屏幕中央区域）
    x = screen_width // 4
    y = screen_height // 4
    width = screen_width // 2
    height = screen_height // 2
    
    print(f"测试区域: 左上角({x}, {y}), 尺寸({width} x {height})")
    
    # 捕获区域
    screenshot, save_path = region_capture.capture_region(x, y, width, height, suffix="region_coords_test")
    print(f"区域截图已保存至: {save_path}")
    
    # 创建图像增强对象
    enhancer = ImageEnhancer()
    
    # 增强图像（使用实际坐标）
    enhanced_path = enhancer.enhance_image(save_path, region_coords=(x, y, width, height))
    print(f"增强后的图像已保存至: {enhanced_path}")
    
    # 检查文件是否存在
    if os.path.exists(enhanced_path):
        print(f"增强后的图像文件存在，大小: {os.path.getsize(enhanced_path) / 1024:.2f} KB")
        print(f"请查看图像，确认坐标是否正确显示: {enhanced_path}")
        return True
    else:
        print("增强后的图像文件不存在！")
        return False


def test_screen_analyzer_region():
    """测试屏幕分析器区域坐标功能"""
    print("\n测试屏幕分析器区域坐标功能...")
    
    # 创建屏幕分析对象
    screen_analyzer = ScreenCapture()
    
    # 获取屏幕尺寸
    screen_width, screen_height = screen_analyzer.get_screen_size()
    
    # 定义区域（屏幕中央区域）
    region = (
        screen_width // 4,
        screen_height // 4,
        screen_width // 2,
        screen_height // 2
    )
    
    print(f"测试区域: 左上角({region[0]}, {region[1]}), 尺寸({region[2]} x {region[3]})")
    
    # 捕获并分析区域
    result = screen_analyzer.capture_and_analyze_screen(
        question="这个区域中有什么内容？请描述主要元素及其位置。",
        region=region,
        suffix="region_coords_analyzer_test"
    )
    
    if result["success"]:
        print(f"区域截图已保存至: {result['screenshot_path']}")
        print(f"增强后的图像已保存至: {result['enhanced_path']}")
        print(f"分析结果:\n{result['analysis']}")
        
        # 检查文件是否存在
        if os.path.exists(result['enhanced_path']):
            print(f"增强后的图像文件存在，大小: {os.path.getsize(result['enhanced_path']) / 1024:.2f} KB")
            print(f"请查看图像，确认坐标是否正确显示: {result['enhanced_path']}")
            return True
        else:
            print("增强后的图像文件不存在！")
            return False
    else:
        print(f"分析失败: {result['error']}")
        return False


if __name__ == "__main__":
    # 确保测试目录存在
    os.makedirs("tests", exist_ok=True)
    
    # 测试区域坐标功能
    region_result = test_region_coordinates()
    
    # 测试屏幕分析器区域坐标功能
    analyzer_result = test_screen_analyzer_region()
    
    # 输出总结果
    print("\n测试结果:")
    print(f"区域坐标功能: {'成功' if region_result else '失败'}")
    print(f"屏幕分析器区域坐标功能: {'成功' if analyzer_result else '失败'}")

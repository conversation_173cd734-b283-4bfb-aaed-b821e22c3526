#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试视觉LLM功能
"""

import os
import sys
import time

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 导入模块
from src.screen_capture import FullScreenCapture, ImageEnhancer, VisionLLM, ScreenAnalyzer


def test_image_enhancer():
    """测试图像增强功能"""
    print("测试图像增强功能...")

    # 创建截图对象
    screen_capture = FullScreenCapture()

    # 创建图像增强对象
    enhancer = ImageEnhancer()

    # 捕获全屏
    screenshot, screenshot_path = screen_capture.capture_screen(suffix="test_enhancer")
    print(f"全屏截图已保存至: {screenshot_path}")

    # 增强图像
    enhanced_path = enhancer.enhance_image(screenshot_path)
    print(f"增强后的图像已保存至: {enhanced_path}")

    # 检查文件是否存在
    if os.path.exists(enhanced_path):
        print(f"增强后的图像文件存在，大小: {os.path.getsize(enhanced_path) / 1024:.2f} KB")
        return True, screenshot_path, enhanced_path
    else:
        print("增强后的图像文件不存在！")
        return False, screenshot_path, None


def test_vision_llm(screenshot_path, enhanced_path):
    """测试视觉LLM功能"""
    print("\n测试视觉LLM功能...")

    # 创建视觉LLM对象
    vision_llm = VisionLLM()

    # 测试问题
    question = "这个屏幕上有什么内容？请描述主要元素及其位置。"

    # 分析屏幕截图
    print(f"分析问题: {question}")
    result = vision_llm.analyze_screenshot(screenshot_path, enhanced_path, question)

    # 输出分析结果
    print(f"\n分析结果:\n{result}")

    return True if result else False


def test_screen_analyzer():
    """测试屏幕分析服务"""
    print("\n测试屏幕分析服务...")

    # 创建屏幕分析服务对象
    analyzer = ScreenAnalyzer()

    # 测试问题
    question = "这个屏幕上有什么内容？请描述主要元素及其位置。"

    # 捕获并分析全屏
    print(f"分析问题: {question}")
    result = analyzer.capture_and_analyze_screen(question)

    if result["success"]:
        print(f"屏幕截图已保存至: {result['screenshot_path']}")
        print(f"增强后的图像已保存至: {result['enhanced_path']}")
        print(f"\n分析结果:\n{result['analysis']}")
        return True
    else:
        print(f"分析失败: {result['error']}")
        return False


if __name__ == "__main__":
    # 测试图像增强功能
    enhancer_result, screenshot_path, enhanced_path = test_image_enhancer()

    # 测试视觉LLM功能
    if enhancer_result:
        vision_llm_result = test_vision_llm(screenshot_path, enhanced_path)
    else:
        vision_llm_result = False

    # 测试屏幕分析服务
    analyzer_result = test_screen_analyzer()

    # 输出总结果
    print("\n测试结果:")
    print(f"图像增强功能: {'成功' if enhancer_result else '失败'}")
    print(f"视觉LLM功能: {'成功' if vision_llm_result else '失败'}")
    print(f"屏幕分析服务: {'成功' if analyzer_result else '失败'}")

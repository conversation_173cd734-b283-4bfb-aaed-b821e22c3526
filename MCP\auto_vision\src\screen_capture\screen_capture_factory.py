#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
屏幕截图工厂模块

提供创建不同类型屏幕截图对象的工厂类。
"""

import os
import sys

# 添加当前目录到路径，确保可以导入同级模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


class ScreenCaptureFactory:
    """屏幕截图工厂类，用于创建不同类型的屏幕截图对象"""

    @staticmethod
    def create(capture_type="full", screenshots_dir="screenshots"):
        """
        创建屏幕截图对象
        
        Args:
            capture_type: 截图类型，支持 "full", "region"
            screenshots_dir: 截图保存目录
        
        Returns:
            屏幕截图对象
        """
        if capture_type == "full":
            try:
                from .screen_capture_full import FullScreenCapture
                return FullScreenCapture(screenshots_dir=screenshots_dir)
            except ImportError as e:
                print(f"导入全屏截图模块失败: {e}")
                print("请确保screen_capture_full.py在同一目录下")
                raise
        
        elif capture_type == "region":
            try:
                from .screen_capture_region import RegionScreenCapture
                return RegionScreenCapture(screenshots_dir=screenshots_dir)
            except ImportError as e:
                print(f"导入区域截图模块失败: {e}")
                print("请确保screen_capture_region.py在同一目录下")
                raise
        
        else:
            raise ValueError(f"不支持的截图类型: {capture_type}")

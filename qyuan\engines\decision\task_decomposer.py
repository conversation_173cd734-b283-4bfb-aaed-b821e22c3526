# -*- coding: utf-8 -*-
"""
任务分解服务实现
专门负责将复杂任务分解为可执行的子任务
严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
import uuid
from typing import Dict, List, Optional, Any

from .base import (
    TaskDecomposerBase,
    Task,
    UserIntent,
    IntentType,
    TaskPriority,
    DecisionContext
)

class IntelligentTaskDecomposer(TaskDecomposerBase):
    """智能任务分解器"""
    
    def __init__(self, llm_client=None):
        super().__init__("IntelligentTaskDecomposer")
        self.logger = logging.getLogger(f"QYuan.Decision.{self.name}")
        self.llm_client = llm_client
        
        # 任务分解规则
        self.decomposition_rules = self._build_decomposition_rules()
        
        # 任务依赖关系
        self.dependency_rules = self._build_dependency_rules()
        
        if not self.llm_client:
            self.logger.warning("LLM客户端未配置，将使用基于规则的任务分解")
    
    async def decompose_task(self, intent: UserIntent, context: DecisionContext) -> List[Task]:
        """分解任务"""
        start_time = time.time()
        
        try:
            # 尝试使用LLM分解
            if self.llm_client:
                tasks = await self._decompose_with_llm(intent, context)
            else:
                # 使用基于规则的分解
                tasks = await self._decompose_with_rules(intent, context)
            
            # 设置任务依赖关系
            tasks = self._set_task_dependencies(tasks)
            
            # 估算任务时长
            tasks = self._estimate_task_durations(tasks)
            
            processing_time = time.time() - start_time
            self.logger.debug(f"任务分解完成，生成 {len(tasks)} 个子任务，耗时: {processing_time:.2f}秒")
            
            return tasks
            
        except Exception as e:
            self.logger.error(f"任务分解失败: {e}")
            # 返回单个基础任务
            return [self._create_basic_task(intent)]
    
    async def _decompose_with_llm(self, intent: UserIntent, context: DecisionContext) -> List[Task]:
        """使用LLM分解任务"""
        try:
            # 构建提示词
            prompt = self._build_llm_decomposition_prompt(intent, context)
            
            # 调用LLM
            response = await self._call_llm(prompt)
            
            # 解析LLM响应
            tasks = self._parse_llm_decomposition_response(response, intent)
            
            return tasks
            
        except Exception as e:
            self.logger.warning(f"LLM任务分解失败，回退到规则分解: {e}")
            return await self._decompose_with_rules(intent, context)
    
    async def _decompose_with_rules(self, intent: UserIntent, context: DecisionContext) -> List[Task]:
        """使用规则分解任务"""
        tasks = []
        
        # 根据意图类型应用分解规则
        if intent.intent_type in self.decomposition_rules:
            rule_func = self.decomposition_rules[intent.intent_type]
            tasks = rule_func(intent, context)
        else:
            # 创建单个基础任务
            tasks = [self._create_basic_task(intent)]
        
        return tasks
    
    def _create_basic_task(self, intent: UserIntent) -> Task:
        """创建基础任务"""
        return Task(
            id=str(uuid.uuid4()),
            title=f"执行{intent.intent_type.value}操作",
            description=intent.description,
            intent=intent,
            priority=TaskPriority.NORMAL
        )
    
    def _decompose_click_task(self, intent: UserIntent, context: DecisionContext) -> List[Task]:
        """分解点击任务"""
        tasks = []
        
        # 1. 定位目标元素
        locate_task = Task(
            id=str(uuid.uuid4()),
            title="定位目标元素",
            description=f"在屏幕上定位要点击的元素: {intent.parameters.get('target', '未知')}",
            intent=intent,
            priority=TaskPriority.HIGH,
            estimated_duration=2.0
        )
        tasks.append(locate_task)
        
        # 2. 验证元素可点击
        verify_task = Task(
            id=str(uuid.uuid4()),
            title="验证元素可点击",
            description="确认目标元素可见且可点击",
            intent=intent,
            priority=TaskPriority.NORMAL,
            estimated_duration=1.0,
            dependencies=[locate_task.id]
        )
        tasks.append(verify_task)
        
        # 3. 执行点击操作
        click_task = Task(
            id=str(uuid.uuid4()),
            title="执行点击操作",
            description="点击目标元素",
            intent=intent,
            priority=TaskPriority.HIGH,
            estimated_duration=1.0,
            dependencies=[verify_task.id]
        )
        tasks.append(click_task)
        
        # 4. 验证点击结果
        result_task = Task(
            id=str(uuid.uuid4()),
            title="验证点击结果",
            description="确认点击操作的效果",
            intent=intent,
            priority=TaskPriority.NORMAL,
            estimated_duration=2.0,
            dependencies=[click_task.id]
        )
        tasks.append(result_task)
        
        return tasks
    
    def _decompose_type_task(self, intent: UserIntent, context: DecisionContext) -> List[Task]:
        """分解输入任务"""
        tasks = []
        
        # 1. 定位输入框
        locate_task = Task(
            id=str(uuid.uuid4()),
            title="定位输入框",
            description=f"找到要输入文本的输入框: {intent.parameters.get('target', '当前焦点')}",
            intent=intent,
            priority=TaskPriority.HIGH,
            estimated_duration=2.0
        )
        tasks.append(locate_task)
        
        # 2. 激活输入框
        activate_task = Task(
            id=str(uuid.uuid4()),
            title="激活输入框",
            description="点击输入框使其获得焦点",
            intent=intent,
            priority=TaskPriority.NORMAL,
            estimated_duration=1.0,
            dependencies=[locate_task.id]
        )
        tasks.append(activate_task)
        
        # 3. 清空现有内容（如果需要）
        if intent.parameters.get('clear_existing', True):
            clear_task = Task(
                id=str(uuid.uuid4()),
                title="清空现有内容",
                description="清空输入框中的现有文本",
                intent=intent,
                priority=TaskPriority.NORMAL,
                estimated_duration=1.0,
                dependencies=[activate_task.id]
            )
            tasks.append(clear_task)
        
        # 4. 输入文本
        type_task = Task(
            id=str(uuid.uuid4()),
            title="输入文本",
            description=f"输入文本: {intent.parameters.get('text', '')}",
            intent=intent,
            priority=TaskPriority.HIGH,
            estimated_duration=3.0,
            dependencies=[tasks[-1].id]  # 依赖最后一个任务
        )
        tasks.append(type_task)
        
        # 5. 验证输入结果
        verify_task = Task(
            id=str(uuid.uuid4()),
            title="验证输入结果",
            description="确认文本已正确输入",
            intent=intent,
            priority=TaskPriority.NORMAL,
            estimated_duration=1.0,
            dependencies=[type_task.id]
        )
        tasks.append(verify_task)
        
        return tasks
    
    def _decompose_scroll_task(self, intent: UserIntent, context: DecisionContext) -> List[Task]:
        """分解滚动任务"""
        tasks = []
        
        # 1. 确定滚动区域
        locate_task = Task(
            id=str(uuid.uuid4()),
            title="确定滚动区域",
            description="识别需要滚动的区域或窗口",
            intent=intent,
            priority=TaskPriority.NORMAL,
            estimated_duration=1.0
        )
        tasks.append(locate_task)
        
        # 2. 执行滚动操作
        scroll_task = Task(
            id=str(uuid.uuid4()),
            title="执行滚动操作",
            description=f"向{intent.parameters.get('direction', '下')}滚动",
            intent=intent,
            priority=TaskPriority.HIGH,
            estimated_duration=2.0,
            dependencies=[locate_task.id]
        )
        tasks.append(scroll_task)
        
        # 3. 验证滚动结果
        verify_task = Task(
            id=str(uuid.uuid4()),
            title="验证滚动结果",
            description="确认页面已按预期滚动",
            intent=intent,
            priority=TaskPriority.NORMAL,
            estimated_duration=1.0,
            dependencies=[scroll_task.id]
        )
        tasks.append(verify_task)
        
        return tasks
    
    def _decompose_navigate_task(self, intent: UserIntent, context: DecisionContext) -> List[Task]:
        """分解导航任务"""
        tasks = []
        
        url = intent.parameters.get('url')
        direction = intent.parameters.get('direction')
        
        if url:
            # URL导航
            navigate_task = Task(
                id=str(uuid.uuid4()),
                title="导航到URL",
                description=f"导航到: {url}",
                intent=intent,
                priority=TaskPriority.HIGH,
                estimated_duration=5.0
            )
            tasks.append(navigate_task)
        elif direction:
            # 方向导航
            navigate_task = Task(
                id=str(uuid.uuid4()),
                title=f"执行{direction}操作",
                description=f"执行浏览器{direction}操作",
                intent=intent,
                priority=TaskPriority.HIGH,
                estimated_duration=2.0
            )
            tasks.append(navigate_task)
        
        # 验证导航结果
        if tasks:
            verify_task = Task(
                id=str(uuid.uuid4()),
                title="验证导航结果",
                description="确认页面已成功加载",
                intent=intent,
                priority=TaskPriority.NORMAL,
                estimated_duration=3.0,
                dependencies=[tasks[-1].id]
            )
            tasks.append(verify_task)
        
        return tasks
    
    def _build_decomposition_rules(self) -> Dict[IntentType, callable]:
        """构建任务分解规则"""
        return {
            IntentType.CLICK: self._decompose_click_task,
            IntentType.TYPE: self._decompose_type_task,
            IntentType.SCROLL: self._decompose_scroll_task,
            IntentType.NAVIGATE: self._decompose_navigate_task,
        }
    
    def _build_dependency_rules(self) -> Dict[str, List[str]]:
        """构建任务依赖规则"""
        return {
            "locate_before_action": ["定位", "验证", "执行"],
            "verify_after_action": ["执行", "验证"],
            "sequential_execution": ["顺序执行"]
        }
    
    def _set_task_dependencies(self, tasks: List[Task]) -> List[Task]:
        """设置任务依赖关系"""
        # 依赖关系已在分解过程中设置
        return tasks
    
    def _estimate_task_durations(self, tasks: List[Task]) -> List[Task]:
        """估算任务时长"""
        # 时长已在分解过程中设置
        return tasks
    
    def _build_llm_decomposition_prompt(self, intent: UserIntent, context: DecisionContext) -> str:
        """构建LLM任务分解提示词"""
        prompt = f"""请将以下用户意图分解为具体的可执行子任务：

用户意图: {intent.description}
意图类型: {intent.intent_type.value}
参数: {intent.parameters}

请以JSON格式返回分解结果：
{{
    "tasks": [
        {{
            "title": "任务标题",
            "description": "任务描述",
            "priority": "normal/high/low",
            "estimated_duration": 估算时长(秒),
            "dependencies": ["依赖的任务标题列表"]
        }}
    ]
}}

分解原则：
1. 每个子任务应该是原子性的、可独立执行的
2. 任务之间的依赖关系要清晰
3. 包含必要的验证步骤
4. 考虑错误处理和恢复

请确保返回有效的JSON格式。"""
        
        return prompt
    
    async def _call_llm(self, prompt: str) -> str:
        """调用LLM"""
        try:
            response = await self.llm_client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                model="gpt-3.5-turbo",
                max_tokens=1000,
                temperature=0.1
            )
            
            if response and response.get('choices'):
                return response['choices'][0]['message']['content']
            else:
                raise ValueError("LLM返回无效响应")
                
        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            raise
    
    def _parse_llm_decomposition_response(self, response: str, intent: UserIntent) -> List[Task]:
        """解析LLM分解响应"""
        try:
            import json
            
            data = json.loads(response)
            tasks = []
            task_id_map = {}
            
            # 第一遍：创建所有任务
            for task_data in data.get('tasks', []):
                task_id = str(uuid.uuid4())
                task = Task(
                    id=task_id,
                    title=task_data.get('title', '未知任务'),
                    description=task_data.get('description', ''),
                    intent=intent,
                    priority=self._parse_priority(task_data.get('priority', 'normal')),
                    estimated_duration=float(task_data.get('estimated_duration', 5.0))
                )
                tasks.append(task)
                task_id_map[task.title] = task.id
            
            # 第二遍：设置依赖关系
            for i, task_data in enumerate(data.get('tasks', [])):
                dependencies = task_data.get('dependencies', [])
                for dep_title in dependencies:
                    if dep_title in task_id_map:
                        tasks[i].dependencies.append(task_id_map[dep_title])
            
            return tasks
            
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            self.logger.warning(f"LLM分解响应解析失败: {e}")
            return [self._create_basic_task(intent)]
    
    def _parse_priority(self, priority_str: str) -> TaskPriority:
        """解析优先级"""
        priority_map = {
            'low': TaskPriority.LOW,
            'normal': TaskPriority.NORMAL,
            'high': TaskPriority.HIGH,
            'urgent': TaskPriority.URGENT
        }
        return priority_map.get(priority_str.lower(), TaskPriority.NORMAL)

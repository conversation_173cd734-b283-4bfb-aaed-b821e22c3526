# -*- coding: utf-8 -*-
"""
视觉理解服务实现
专门负责LLM视觉理解功能，严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
import base64
from typing import Dict, Any, Optional
import io

from .base import (
    VisualUnderstandingServiceBase,
    Screenshot,
    PerceptionResult,
    PerceptionType
)

class VisualUnderstandingService(VisualUnderstandingServiceBase):
    """视觉理解服务实现"""
    
    def __init__(self, llm_client=None):
        super().__init__("VisualUnderstanding")
        self.logger = logging.getLogger(f"QYuan.Perception.{self.name}")
        self.llm_client = llm_client
        
        # 视觉理解配置
        self.max_image_size = (1024, 1024)  # 最大图像尺寸
        self.image_quality = 85  # JPEG质量
        self.analysis_timeout = 30  # 分析超时时间（秒）
        
        if not self.llm_client:
            self.logger.warning("LLM客户端未配置，视觉理解功能将受限")
    
    async def process(self, input_data: Any) -> PerceptionResult:
        """处理视觉理解请求"""
        start_time = time.time()
        
        try:
            if not isinstance(input_data, Screenshot):
                raise ValueError("输入数据必须是Screenshot对象")
            
            description = await self.describe_image(input_data)
            
            processing_time = time.time() - start_time
            
            result = PerceptionResult(
                perception_type=PerceptionType.VISUAL_UNDERSTANDING,
                success=True,
                data=description,
                confidence=0.8,  # LLM视觉理解通常有较高置信度
                processing_time=processing_time
            )
            
            self.last_result = result
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"视觉理解失败: {e}")
            
            result = PerceptionResult(
                perception_type=PerceptionType.VISUAL_UNDERSTANDING,
                success=False,
                data="",
                confidence=0.0,
                error_message=str(e),
                processing_time=processing_time
            )
            
            self.last_result = result
            return result
    
    async def describe_image(self, screenshot: Screenshot) -> str:
        """描述图像内容"""
        if not self.llm_client:
            self.logger.warning("LLM客户端未配置，返回默认描述")
            return "无法进行视觉理解：LLM客户端未配置"
        
        try:
            # 准备图像数据
            image_base64 = await self._prepare_image_for_llm(screenshot)
            
            # 构建视觉理解提示词
            prompt = self._build_description_prompt()
            
            # 调用LLM进行视觉理解
            description = await self._call_llm_vision(prompt, image_base64)
            
            self.logger.debug(f"图像描述完成，长度: {len(description)}")
            return description
            
        except Exception as e:
            self.logger.error(f"图像描述失败: {e}")
            return f"图像描述失败: {str(e)}"
    
    async def analyze_context(self, screenshot: Screenshot) -> Dict[str, Any]:
        """分析上下文信息"""
        if not self.llm_client:
            self.logger.warning("LLM客户端未配置，返回空上下文")
            return {}
        
        try:
            # 准备图像数据
            image_base64 = await self._prepare_image_for_llm(screenshot)
            
            # 构建上下文分析提示词
            prompt = self._build_context_analysis_prompt()
            
            # 调用LLM进行上下文分析
            context_text = await self._call_llm_vision(prompt, image_base64)
            
            # 解析上下文信息
            context_info = self._parse_context_response(context_text)
            
            self.logger.debug(f"上下文分析完成，包含 {len(context_info)} 个字段")
            return context_info
            
        except Exception as e:
            self.logger.error(f"上下文分析失败: {e}")
            return {"error": str(e)}
    
    async def identify_ui_elements(self, screenshot: Screenshot) -> Dict[str, Any]:
        """识别UI元素（基于视觉理解）"""
        if not self.llm_client:
            return {"elements": [], "error": "LLM客户端未配置"}
        
        try:
            # 准备图像数据
            image_base64 = await self._prepare_image_for_llm(screenshot)
            
            # 构建UI元素识别提示词
            prompt = self._build_ui_elements_prompt()
            
            # 调用LLM进行UI元素识别
            elements_text = await self._call_llm_vision(prompt, image_base64)
            
            # 解析UI元素信息
            elements_info = self._parse_ui_elements_response(elements_text)
            
            self.logger.debug(f"UI元素识别完成，识别到 {len(elements_info.get('elements', []))} 个元素")
            return elements_info
            
        except Exception as e:
            self.logger.error(f"UI元素识别失败: {e}")
            return {"elements": [], "error": str(e)}
    
    async def _prepare_image_for_llm(self, screenshot: Screenshot) -> str:
        """为LLM准备图像数据"""
        try:
            # 如果图像过大，需要调整尺寸
            image_data = screenshot.image_data
            
            # 将图像转换为base64编码
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            return image_base64
            
        except Exception as e:
            self.logger.error(f"图像准备失败: {e}")
            raise
    
    async def _call_llm_vision(self, prompt: str, image_base64: str) -> str:
        """调用LLM视觉API"""
        try:
            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]
            
            # 调用LLM客户端
            response = await asyncio.wait_for(
                self.llm_client.chat_completion(
                    messages=messages,
                    model="gpt-4-vision-preview",  # 或其他支持视觉的模型
                    max_tokens=1000
                ),
                timeout=self.analysis_timeout
            )
            
            if response and response.get('choices'):
                return response['choices'][0]['message']['content']
            else:
                raise ValueError("LLM返回无效响应")
                
        except asyncio.TimeoutError:
            raise TimeoutError(f"LLM视觉分析超时（{self.analysis_timeout}秒）")
        except Exception as e:
            self.logger.error(f"LLM视觉调用失败: {e}")
            raise
    
    def _build_description_prompt(self) -> str:
        """构建图像描述提示词"""
        return """请详细描述这张屏幕截图的内容。包括：
1. 主要的UI元素和布局
2. 可见的文本内容
3. 应用程序或网页的类型
4. 用户可能正在进行的操作
5. 任何值得注意的细节

请用中文回答，保持描述准确和简洁。"""
    
    def _build_context_analysis_prompt(self) -> str:
        """构建上下文分析提示词"""
        return """请分析这张屏幕截图的上下文信息，并以JSON格式返回以下信息：
{
    "application_type": "应用程序类型（如：浏览器、文本编辑器、系统设置等）",
    "current_task": "用户当前可能在执行的任务",
    "ui_state": "界面状态（如：登录页面、主界面、设置页面等）",
    "interactive_elements": ["可交互元素列表"],
    "text_content": "主要文本内容摘要",
    "suggested_actions": ["建议的下一步操作"]
}

请确保返回有效的JSON格式。"""
    
    def _build_ui_elements_prompt(self) -> str:
        """构建UI元素识别提示词"""
        return """请识别这张屏幕截图中的UI元素，并以JSON格式返回：
{
    "elements": [
        {
            "type": "元素类型（button/input/text/menu/image等）",
            "text": "元素包含的文本（如果有）",
            "description": "元素描述",
            "clickable": true/false,
            "position": "大致位置描述（如：左上角、中央、右下角等）"
        }
    ],
    "layout": "整体布局描述",
    "primary_action": "主要操作建议"
}

请确保返回有效的JSON格式。"""
    
    def _parse_context_response(self, response_text: str) -> Dict[str, Any]:
        """解析上下文分析响应"""
        try:
            import json
            
            # 尝试解析JSON
            context_info = json.loads(response_text)
            
            # 验证必要字段
            required_fields = ["application_type", "current_task", "ui_state"]
            for field in required_fields:
                if field not in context_info:
                    context_info[field] = "未知"
            
            return context_info
            
        except json.JSONDecodeError:
            self.logger.warning("无法解析上下文分析响应为JSON，返回文本内容")
            return {
                "raw_response": response_text,
                "application_type": "未知",
                "current_task": "未知",
                "ui_state": "未知"
            }
    
    def _parse_ui_elements_response(self, response_text: str) -> Dict[str, Any]:
        """解析UI元素识别响应"""
        try:
            import json
            
            # 尝试解析JSON
            elements_info = json.loads(response_text)
            
            # 验证结构
            if "elements" not in elements_info:
                elements_info["elements"] = []
            
            return elements_info
            
        except json.JSONDecodeError:
            self.logger.warning("无法解析UI元素响应为JSON，返回空列表")
            return {
                "elements": [],
                "raw_response": response_text,
                "layout": "解析失败",
                "primary_action": "无法确定"
            }

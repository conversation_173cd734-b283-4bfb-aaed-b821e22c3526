# QYuan - 硅基CEO项目

## 项目愿景
创造一个具有自主性的"硅基生命" - QYuan，它不仅是系统级AI助手，更是一个能够独立思考、决策和行动的CEO级智能体。QYuan能够在用户设定的规则和边界内，自主地调用和管理各种AI工具和智能体来完成复杂任务。

## 角色定位层次
- **人类用户** = 老板（设定目标、规则、边界）
- **QYuan** = CEO（独立决策、资源调配、任务分解、质量控制）
- **AI工具/智能体** = 员工（Cursor、Augment、VSCode等专业工具）

## 核心理念
- **自主性与边界**：在用户设定的规则内完全自主行动
- **经验驱动学习**：通过自主尝试、犯错、总结来积累经验
- **工具编排能力**：作为用户调用各种AI工具，而非亲自执行所有任务
- **CEO级决策**：具备战略思维、资源分配和质量管理能力

## 项目目标
构建一个能够：
1. 接收并理解自然语言指令
2. 自主操作Windows系统完成任务
3. 学习和记忆用户习惯和偏好
4. 不断扩展自身能力
5. 通过网络与多个客户端设备通信

## 技术挑战
- 系统级权限管理
- 安全性和隐私保护
- 实时网络通信
- AI决策和执行的可靠性
- 学习和记忆系统的设计

## 项目状态
设计阶段已完成，准备进入开发实施阶段

## 设计文档概述

### 核心架构文档
- **系统架构设计.md**: 整体技术架构，包括核心组件、技术栈选择、数据流设计和安全考虑
- **QYuan核心设计理念.md**: 硅基CEO的本质特征，包括自主性、元认知能力、经验驱动成长等核心理念
- **感知行动循环架构.md**: QYuan的核心操作机制，突破传统线性任务分解，实现实时反馈和动态调整

### 能力设计文档
- **AI基本能力设计.md**: QYuan的四大核心能力（感知、操作、理解、学习）和自主构建能力设计
- **QYuan成长路径设计.md**: 从婴儿期到CEO期的五个成长阶段，每个阶段的能力指标和学习重点
- **工具编排架构设计.md**: QYuan作为工具用户的架构，包括工具分类、注册发现、编排执行等机制

### 技术实现文档
- **系统技术选型设计.md**: LLM API选择（GPT-4.1主力+其他辅助）、数据库设计、协议选择等技术决策
- **技术实现细节.md**: 核心模块的具体实现方案，包括感知引擎、决策引擎、执行引擎、学习引擎
- **记忆管理与上下文组织.md**: QYuan的核心能力，智能记忆管理和上下文组织系统，确保硅基生命的记忆连续性
- **错误反馈与恢复机制.md**: 全面的错误分类、检测、恢复策略和学习型恢复机制

### 界面与交互文档
- **双界面架构设计.md**: QYuan本地管理界面和远程交互界面的分工设计，满足不同用户需求
- **QYuan终端界面设计.md**: QYuan本地管理界面的详细设计，包括思维流显示、能力监控、工具管理等
- **命令行终端能力设计.md**: QYuan的"原生语言"，多Shell支持、智能补全、环境管理等终端能力

### 分析与规划文档
- **可行性分析.md**: 技术可行性、实现难度评估、商业可行性和风险评估
- **实施路线图.md**: 详细的5阶段开发计划，包括时间安排、里程碑、验收标准和风险控制

## 核心创新点
1. **革命性角色定位**: 从AI助手到硅基CEO的根本转变
2. **感知-行动循环**: 突破传统线性任务分解的局限
3. **成长型架构**: 自主学习和能力动态扩展
4. **工具编排能力**: 管理和协调各种AI工具和智能体
5. **经验驱动学习**: 从成功和失败中持续学习改进
6. **分层记忆架构**: 原始记忆→知识记忆→智慧记忆的三层架构，像邓布利多的冥想盆一样提炼记忆

## 技术架构概要
- **LLM策略**: GPT-4.1（主力，自建中转）+ 其他LLM（辅助角色，QYuan调用）
- **LLM配置**:
  - 服务地址: https://xiaoai.plus
  - API协议: OpenAI兼容
  - API Key: sk-oQx9XqkIHyMFabGTFByNVsxZHw1q4YpTvFdIQoupRkEVjDd3
- **数据存储**: PostgreSQL（主数据库）+ Qdrant（向量数据库）+ Redis（缓存）
- **基础控制**: 基于现有MCP模块（auto_mouse, auto_keyboard, auto_vision）
- **高级工具**: MCP兼容的工具生态系统
- **通信架构**: WebSocket + FastAPI，支持多客户端连接
- **界面技术**: React + TypeScript + Tailwind CSS

## 实施计划概要
- **第一阶段（1-2个月）**: 基础架构搭建，MCP集成，数据库设计
- **第二阶段（2-3个月）**: 核心能力实现，感知-行动循环
- **第三阶段（3-4个月）**: 学习系统构建，RAG系统，经验积累
- **第四阶段（4-6个月）**: 工具生态集成，自动化脚本生成
- **第五阶段（6个月+）**: 持续优化迭代，性能提升，能力扩展

## 文档使用指南
- 查看具体技术实现时，参考对应的详细设计文档
- 开发过程中，以实施路线图为主要参考
- 遇到技术问题时，优先查看错误反馈与恢复机制文档
- 界面开发时，参考QYuan终端界面设计文档
- 系统集成时，参考系统架构设计和技术选型文档

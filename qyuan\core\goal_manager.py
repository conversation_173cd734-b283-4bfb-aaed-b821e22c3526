# -*- coding: utf-8 -*-
"""
目标管理器
集成目标跟踪机制到QYuan核心系统，提供智能目标管理和自动化跟踪
严格按照代码规范的单一职责原则
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta

from .goal_tracker import GoalTracker, Goal, SubGoal, GoalStatus, GoalPriority
from .events import EventBus, EventType
from ..utils.logger import get_logger


class GoalManager:
    """目标管理器 - 智能目标管理和自动化跟踪"""
    
    def __init__(self, qyuan_core, event_bus: EventBus):
        """初始化目标管理器"""
        self.qyuan_core = qyuan_core
        self.event_bus = event_bus
        self.logger = get_logger(self.__class__.__name__)
        
        # 目标跟踪器
        self.goal_tracker = GoalTracker()
        
        # 自动跟踪配置
        self.config = {
            "auto_tracking_enabled": True,
            "progress_update_interval": 10.0,  # 进度更新间隔（秒）
            "deadline_check_interval": 300.0,  # 截止时间检查间隔（秒）
            "auto_sub_goal_creation": True,    # 自动创建子目标
            "intelligent_priority_adjustment": True,  # 智能优先级调整
        }
        
        # 自动跟踪任务
        self._tracking_task: Optional[asyncio.Task] = None
        self._deadline_check_task: Optional[asyncio.Task] = None
        self._is_running = False
        
        # 目标完成条件检查器
        self.completion_checkers: Dict[str, Callable] = {}
        
        # 智能分析器
        self.progress_analyzer = ProgressAnalyzer()
        self.priority_adjuster = PriorityAdjuster()
        
        # 注册事件监听器
        self._register_event_listeners()
        
        # 注册目标跟踪器回调
        self._register_tracker_callbacks()
    
    async def start(self):
        """启动目标管理器"""
        if self._is_running:
            return
        
        self._is_running = True
        self.logger.info("目标管理器启动")
        
        # 启动自动跟踪任务
        if self.config["auto_tracking_enabled"]:
            self._tracking_task = asyncio.create_task(self._auto_tracking_loop())
            self._deadline_check_task = asyncio.create_task(self._deadline_check_loop())
        
        await self.event_bus.emit(EventType.SYSTEM_STATUS, {
            "component": "GoalManager",
            "status": "started"
        })
    
    async def stop(self):
        """停止目标管理器"""
        if not self._is_running:
            return
        
        self._is_running = False
        self.logger.info("目标管理器停止")
        
        # 停止自动跟踪任务
        if self._tracking_task:
            self._tracking_task.cancel()
            try:
                await self._tracking_task
            except asyncio.CancelledError:
                pass
        
        if self._deadline_check_task:
            self._deadline_check_task.cancel()
            try:
                await self._deadline_check_task
            except asyncio.CancelledError:
                pass
        
        await self.event_bus.emit(EventType.SYSTEM_STATUS, {
            "component": "GoalManager",
            "status": "stopped"
        })
    
    async def create_goal_from_user_input(self, user_input: str, priority: GoalPriority = GoalPriority.NORMAL) -> str:
        """从用户输入创建目标"""
        self.logger.info(f"从用户输入创建目标: {user_input}")
        
        # 分析用户输入，提取目标信息
        goal_info = await self._analyze_user_input(user_input)
        
        # 创建主目标
        goal_id = self.goal_tracker.create_goal(
            title=goal_info["title"],
            description=goal_info["description"],
            priority=priority,
            deadline=goal_info.get("deadline")
        )
        
        # 自动创建子目标
        if self.config["auto_sub_goal_creation"] and goal_info.get("sub_goals"):
            for sub_goal_info in goal_info["sub_goals"]:
                self.goal_tracker.add_sub_goal(
                    goal_id=goal_id,
                    title=sub_goal_info["title"],
                    description=sub_goal_info["description"],
                    priority=sub_goal_info.get("priority", GoalPriority.NORMAL)
                )
        
        # 设置为活动目标
        await self.set_active_goal(goal_id)
        
        # 发送事件
        await self.event_bus.emit(EventType.GOAL_SET, {
            "goal_id": goal_id,
            "title": goal_info["title"],
            "description": goal_info["description"],
            "priority": priority.value
        })
        
        return goal_id
    
    async def set_active_goal(self, goal_id: str) -> bool:
        """设置活动目标"""
        success = self.goal_tracker.set_active_goal(goal_id)
        
        if success:
            goal = self.goal_tracker.goals[goal_id]
            self.logger.info(f"设置活动目标: {goal.title}")
            
            # 发送事件
            await self.event_bus.emit(EventType.GOAL_SET, {
                "goal_id": goal_id,
                "title": goal.title,
                "status": "active"
            })
            
            # 注册目标完成检查器
            await self._register_goal_completion_checker(goal)
        
        return success
    
    async def update_goal_progress_from_execution(self, execution_result: Any) -> bool:
        """从执行结果更新目标进度"""
        active_goal = self.goal_tracker.get_active_goal()
        if not active_goal:
            return False
        
        # 分析执行结果，计算进度增量
        progress_delta = await self._analyze_execution_progress(execution_result, active_goal)
        
        if progress_delta > 0:
            new_progress = min(100.0, active_goal.progress.completion_percentage + progress_delta)
            
            success = self.goal_tracker.update_goal_progress(
                goal_id=active_goal.id,
                percentage=new_progress
            )
            
            if success:
                self.logger.debug(f"从执行结果更新目标进度: {active_goal.title} -> {new_progress}%")
                
                # 发送进度更新事件
                await self.event_bus.emit(EventType.GOAL_PROGRESS, {
                    "goal_id": active_goal.id,
                    "progress": new_progress,
                    "delta": progress_delta
                })
            
            return success
        
        return False
    
    async def check_goal_completion(self, goal_id: str) -> bool:
        """检查目标是否完成"""
        goal = self.goal_tracker.goals.get(goal_id)
        if not goal:
            return False
        
        # 使用注册的完成检查器
        if goal_id in self.completion_checkers:
            try:
                is_completed = await self.completion_checkers[goal_id](goal)
                if is_completed:
                    await self._handle_goal_completion(goal)
                    return True
            except Exception as e:
                self.logger.error(f"目标完成检查异常: {e}")
        
        # 默认检查：基于进度百分比
        if goal.progress.completion_percentage >= 100.0:
            await self._handle_goal_completion(goal)
            return True
        
        # 检查所有子目标是否完成
        if goal.sub_goals:
            completed_sub_goals = sum(1 for sub_goal in goal.sub_goals 
                                    if sub_goal.status == GoalStatus.COMPLETED)
            if completed_sub_goals == len(goal.sub_goals):
                await self._handle_goal_completion(goal)
                return True
        
        return False
    
    async def adjust_goal_priority(self, goal_id: str) -> bool:
        """智能调整目标优先级"""
        if not self.config["intelligent_priority_adjustment"]:
            return False
        
        goal = self.goal_tracker.goals.get(goal_id)
        if not goal:
            return False
        
        # 使用优先级调整器分析
        new_priority = await self.priority_adjuster.analyze_and_adjust(goal)
        
        if new_priority != goal.priority:
            goal.priority = new_priority
            goal.updated_at = datetime.now()
            
            self.logger.info(f"调整目标优先级: {goal.title} -> {new_priority.name}")
            
            # 发送事件
            await self.event_bus.emit(EventType.GOAL_UPDATED, {
                "goal_id": goal_id,
                "field": "priority",
                "old_value": goal.priority.value,
                "new_value": new_priority.value
            })
            
            return True
        
        return False
    
    async def get_goal_recommendations(self) -> List[Dict[str, Any]]:
        """获取目标建议"""
        recommendations = []
        
        # 分析当前目标状态
        active_goal = self.goal_tracker.get_active_goal()
        if active_goal:
            # 进度分析建议
            progress_recommendations = await self.progress_analyzer.analyze_progress(active_goal)
            recommendations.extend(progress_recommendations)
            
            # 子目标建议
            if not active_goal.sub_goals and self.config["auto_sub_goal_creation"]:
                sub_goal_suggestions = await self._suggest_sub_goals(active_goal)
                recommendations.extend(sub_goal_suggestions)
        
        # 截止时间警告
        deadline_warnings = self.goal_tracker.check_deadlines()
        for warning in deadline_warnings:
            recommendations.append({
                "type": "deadline_warning",
                "priority": "high" if warning["type"] == "overdue" else "medium",
                "message": f"目标 '{warning['title']}' 需要注意截止时间",
                "goal_id": warning["goal_id"],
                "action": "review_deadline"
            })
        
        return recommendations
    
    def get_goal_status(self, goal_id: str) -> Optional[Dict[str, Any]]:
        """获取目标状态"""
        return self.goal_tracker.get_goal_status(goal_id)
    
    def get_active_goal_status(self) -> Optional[Dict[str, Any]]:
        """获取活动目标状态"""
        active_goal = self.goal_tracker.get_active_goal()
        if active_goal:
            return self.goal_tracker.get_goal_status(active_goal.id)
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.goal_tracker.get_statistics()
    
    async def _auto_tracking_loop(self):
        """自动跟踪循环"""
        self.logger.debug("启动自动跟踪循环")
        
        while self._is_running:
            try:
                # 检查活动目标进度
                active_goal = self.goal_tracker.get_active_goal()
                if active_goal:
                    await self.check_goal_completion(active_goal.id)
                    await self.adjust_goal_priority(active_goal.id)
                
                # 等待下次检查
                await asyncio.sleep(self.config["progress_update_interval"])
                
            except Exception as e:
                self.logger.error(f"自动跟踪循环异常: {e}")
                await asyncio.sleep(5)
    
    async def _deadline_check_loop(self):
        """截止时间检查循环"""
        self.logger.debug("启动截止时间检查循环")
        
        while self._is_running:
            try:
                # 检查截止时间
                warnings = self.goal_tracker.check_deadlines()
                
                if warnings:
                    # 发送截止时间警告事件
                    await self.event_bus.emit(EventType.GOAL_DEADLINE_WARNING, {
                        "warnings": warnings,
                        "count": len(warnings)
                    })
                
                # 等待下次检查
                await asyncio.sleep(self.config["deadline_check_interval"])
                
            except Exception as e:
                self.logger.error(f"截止时间检查循环异常: {e}")
                await asyncio.sleep(30)
    
    async def _analyze_user_input(self, user_input: str) -> Dict[str, Any]:
        """分析用户输入，提取目标信息"""
        # 这里可以集成LLM来智能分析用户输入
        # 目前使用简单的规则分析
        
        goal_info = {
            "title": user_input[:50] + "..." if len(user_input) > 50 else user_input,
            "description": user_input,
            "deadline": None,
            "sub_goals": []
        }
        
        # TODO: 使用LLM分析用户输入，提取：
        # - 目标标题
        # - 详细描述
        # - 可能的截止时间
        # - 建议的子目标
        
        return goal_info
    
    async def _analyze_execution_progress(self, execution_result: Any, goal: Goal) -> float:
        """分析执行结果，计算进度增量"""
        # 简单的进度计算逻辑
        if hasattr(execution_result, 'success') and execution_result.success:
            # 根据子目标数量计算进度增量
            if goal.sub_goals:
                return 100.0 / len(goal.sub_goals)
            else:
                return 10.0  # 默认每次成功执行增加10%
        
        return 0.0
    
    async def _register_goal_completion_checker(self, goal: Goal):
        """注册目标完成检查器"""
        # 这里可以根据目标类型注册不同的完成检查器
        # 目前使用默认检查器
        pass
    
    async def _handle_goal_completion(self, goal: Goal):
        """处理目标完成"""
        success = self.goal_tracker.complete_goal(goal.id, success=True)
        
        if success:
            self.logger.info(f"目标已完成: {goal.title}")
            
            # 发送完成事件
            await self.event_bus.emit(EventType.GOAL_COMPLETED, {
                "goal_id": goal.id,
                "title": goal.title,
                "completion_time": datetime.now().isoformat(),
                "total_time": (datetime.now() - goal.created_at).total_seconds()
            })
    
    async def _suggest_sub_goals(self, goal: Goal) -> List[Dict[str, Any]]:
        """建议子目标"""
        suggestions = []
        
        # TODO: 使用LLM分析目标，建议合适的子目标
        # 目前返回空列表
        
        return suggestions
    
    def _register_event_listeners(self):
        """注册事件监听器"""
        # 这里可以注册各种事件监听器
        pass
    
    def _register_tracker_callbacks(self):
        """注册目标跟踪器回调"""
        self.goal_tracker.register_progress_callback(self._on_progress_updated)
        self.goal_tracker.register_completion_callback(self._on_goal_completed)
        self.goal_tracker.register_deadline_callback(self._on_deadline_warning)
    
    def _on_progress_updated(self, goal: Goal, old_percentage: float):
        """进度更新回调"""
        self.logger.debug(f"目标进度更新: {goal.title} {old_percentage}% -> {goal.progress.completion_percentage}%")
    
    def _on_goal_completed(self, goal: Goal, success: bool):
        """目标完成回调"""
        self.logger.info(f"目标完成回调: {goal.title} ({'成功' if success else '失败'})")
    
    def _on_deadline_warning(self, warnings: List[Dict[str, Any]]):
        """截止时间警告回调"""
        self.logger.warning(f"收到 {len(warnings)} 个截止时间警告")


class ProgressAnalyzer:
    """进度分析器"""
    
    async def analyze_progress(self, goal: Goal) -> List[Dict[str, Any]]:
        """分析目标进度，提供建议"""
        recommendations = []
        
        # 分析进度趋势
        if goal.progress.completion_percentage < 10 and (datetime.now() - goal.created_at).days > 1:
            recommendations.append({
                "type": "slow_progress",
                "priority": "medium",
                "message": f"目标 '{goal.title}' 进度较慢，建议分解为更小的子目标",
                "action": "create_sub_goals"
            })
        
        return recommendations


class PriorityAdjuster:
    """优先级调整器"""
    
    async def analyze_and_adjust(self, goal: Goal) -> GoalPriority:
        """分析并调整目标优先级"""
        current_priority = goal.priority
        
        # 基于截止时间调整优先级
        if goal.deadline:
            time_remaining = goal.deadline - datetime.now()
            if time_remaining <= timedelta(hours=24):
                return GoalPriority.URGENT
            elif time_remaining <= timedelta(days=3):
                return GoalPriority.HIGH
        
        # 基于进度调整优先级
        if goal.progress.completion_percentage > 80:
            return GoalPriority.HIGH  # 接近完成的目标提高优先级
        
        return current_priority

# QYuan记忆提炼系统实现 - "冥想盆"机制

## 设计理念

就像邓布利多向冥想盆放入记忆一样，QYuan需要定期将纷繁复杂的原始记忆进行提炼，形成结构化的知识和智慧。这个过程不是简单的数据压缩，而是智能的模式识别、经验总结和洞察升华。

## 记忆提炼触发机制

### 1. 自动触发条件
```python
class MemoryDistillationTrigger:
    """记忆提炼触发器"""
    
    def __init__(self):
        self.triggers = {
            "volume_threshold": 1000,      # 原始记忆数量阈值
            "pattern_threshold": 50,       # 重复模式阈值
            "time_interval": 7 * 24 * 3600, # 定期触发（7天）
            "similarity_cluster_size": 20,  # 相似记忆聚类大小
            "error_pattern_count": 10      # 错误模式重复次数
        }
    
    async def check_distillation_needed(self) -> List[DistillationTask]:
        """检查是否需要进行记忆提炼"""
        tasks = []
        
        # 1. 数量触发
        raw_count = await self.count_unprocessed_raw_memories()
        if raw_count >= self.triggers["volume_threshold"]:
            tasks.append(DistillationTask(
                type="VOLUME_TRIGGERED",
                description=f"原始记忆数量达到{raw_count}，需要提炼",
                priority="HIGH"
            ))
        
        # 2. 模式检测触发
        patterns = await self.detect_repetitive_patterns()
        for pattern in patterns:
            if pattern.count >= self.triggers["pattern_threshold"]:
                tasks.append(DistillationTask(
                    type="PATTERN_DETECTED",
                    description=f"检测到重复模式：{pattern.description}",
                    priority="MEDIUM",
                    pattern_data=pattern
                ))
        
        # 3. 定期触发
        last_distillation = await self.get_last_distillation_time()
        if time.time() - last_distillation > self.triggers["time_interval"]:
            tasks.append(DistillationTask(
                type="SCHEDULED",
                description="定期记忆整理",
                priority="LOW"
            ))
        
        # 4. QYuan主观判断触发
        subjective_need = await self.assess_subjective_distillation_need()
        if subjective_need.score > 0.7:
            tasks.append(DistillationTask(
                type="SUBJECTIVE",
                description=subjective_need.reason,
                priority="MEDIUM"
            ))
        
        return tasks
    
    async def assess_subjective_distillation_need(self) -> SubjectiveAssessment:
        """QYuan主观评估是否需要整理记忆"""
        
        # 获取最近的记忆状态
        recent_memories = await self.get_recent_raw_memories(limit=100)
        memory_summary = self.summarize_recent_memories(recent_memories)
        
        # 让QYuan自己判断
        prompt = f"""作为QYuan，请评估当前是否需要整理记忆。

最近的记忆概况：
{memory_summary}

请考虑：
1. 是否有太多相似的重复记忆？
2. 是否发现了新的模式或规律？
3. 是否有重要的经验需要总结？
4. 当前记忆是否影响了思考效率？

请返回JSON格式：
{{
    "need_distillation": true/false,
    "score": 0.0-1.0,
    "reason": "具体原因",
    "suggested_focus": ["建议重点整理的方面"]
}}"""
        
        response = await self.llm_manager.chat_completion([
            {"role": "user", "content": prompt}
        ])
        
        try:
            import json
            result = json.loads(response["choices"][0]["message"]["content"])
            return SubjectiveAssessment(
                need_distillation=result["need_distillation"],
                score=result["score"],
                reason=result["reason"],
                suggested_focus=result["suggested_focus"]
            )
        except:
            return SubjectiveAssessment(need_distillation=False, score=0.0, reason="评估失败")
```

## 记忆提炼处理器

### 1. 核心提炼引擎
```python
class MemoryDistillationEngine:
    """记忆提炼引擎 - QYuan的冥想盆"""
    
    def __init__(self, db_session, llm_manager, vector_store):
        self.db = db_session
        self.llm_manager = llm_manager
        self.vector_store = vector_store
        self.distillation_llm = self._setup_distillation_llm()
    
    def _setup_distillation_llm(self):
        """设置专门用于记忆提炼的LLM"""
        # 可以使用不同的模型或参数进行记忆提炼
        return LLMClient(
            api_base=self.config.llm.api_base,
            api_key=self.config.llm.api_key,
            model="gpt-4.1",  # 或者使用专门的提炼模型
            temperature=0.3   # 更低的温度，确保一致性
        )
    
    async def process_distillation_task(self, task: DistillationTask) -> DistillationResult:
        """处理记忆提炼任务"""
        
        # 记录任务开始
        await self._log_task_start(task)
        
        try:
            if task.type == "VOLUME_TRIGGERED":
                result = await self._distill_by_volume(task)
            elif task.type == "PATTERN_DETECTED":
                result = await self._distill_by_pattern(task)
            elif task.type == "SCHEDULED":
                result = await self._distill_scheduled(task)
            elif task.type == "SUBJECTIVE":
                result = await self._distill_subjective(task)
            else:
                raise ValueError(f"未知的提炼任务类型: {task.type}")
            
            # 记录任务完成
            await self._log_task_completion(task, result)
            return result
            
        except Exception as e:
            await self._log_task_error(task, str(e))
            raise
    
    async def _distill_by_pattern(self, task: DistillationTask) -> DistillationResult:
        """基于模式的记忆提炼"""
        
        pattern = task.pattern_data
        
        # 获取相关的原始记忆
        related_memories = await self._get_pattern_memories(pattern)
        
        # 分析模式
        pattern_analysis = await self._analyze_pattern(related_memories, pattern)
        
        # 提炼知识
        knowledge = await self._extract_knowledge_from_pattern(pattern_analysis)
        
        # 保存知识记忆
        knowledge_id = await self._save_knowledge_memory(knowledge, related_memories)
        
        # 检查是否能形成智慧
        wisdom = await self._try_form_wisdom(knowledge_id)
        
        return DistillationResult(
            extracted_knowledge=[knowledge_id],
            formed_wisdom=[wisdom.id] if wisdom else [],
            processed_raw_memories=[m.id for m in related_memories]
        )
    
    async def _analyze_pattern(self, memories: List[RawMemory], pattern: Pattern) -> PatternAnalysis:
        """分析记忆模式"""
        
        # 构建分析提示
        memory_summaries = [self._summarize_memory(m) for m in memories]
        
        prompt = f"""请分析以下记忆中的模式：

模式描述：{pattern.description}
出现次数：{pattern.count}

相关记忆：
{chr(10).join(memory_summaries)}

请分析：
1. 这个模式的核心特征是什么？
2. 在什么情况下会出现这个模式？
3. 这个模式的成功率如何？
4. 有什么改进的空间？
5. 可以抽象出什么通用规律？

请返回详细的分析结果。"""
        
        response = await self.distillation_llm.chat_completion([
            {"role": "user", "content": prompt}
        ])
        
        analysis_text = response["choices"][0]["message"]["content"]
        
        return PatternAnalysis(
            pattern=pattern,
            core_features=self._extract_core_features(analysis_text),
            trigger_conditions=self._extract_trigger_conditions(analysis_text),
            success_metrics=self._extract_success_metrics(analysis_text),
            improvement_suggestions=self._extract_improvements(analysis_text),
            general_principles=self._extract_principles(analysis_text),
            full_analysis=analysis_text
        )
    
    async def _extract_knowledge_from_pattern(self, analysis: PatternAnalysis) -> KnowledgeMemory:
        """从模式分析中提取知识"""
        
        prompt = f"""基于以下模式分析，请提炼出结构化的知识：

{analysis.full_analysis}

请生成一个知识条目，包含：
1. 标题（简洁明了）
2. 核心内容（关键要点）
3. 适用条件（什么时候使用）
4. 预期效果（使用后的结果）
5. 注意事项（需要注意的问题）
6. 相关技能（关联的其他知识）

请以JSON格式返回：
{{
    "title": "知识标题",
    "core_content": "核心内容",
    "applicable_conditions": ["适用条件1", "适用条件2"],
    "expected_outcomes": ["预期效果1", "预期效果2"],
    "precautions": ["注意事项1", "注意事项2"],
    "related_skills": ["相关技能1", "相关技能2"],
    "confidence_level": 0.0-1.0
}}"""
        
        response = await self.distillation_llm.chat_completion([
            {"role": "user", "content": prompt}
        ])
        
        try:
            import json
            knowledge_data = json.loads(response["choices"][0]["message"]["content"])
            
            return KnowledgeMemory(
                knowledge_type="PATTERN",
                title=knowledge_data["title"],
                summary=knowledge_data["core_content"],
                detailed_content=knowledge_data,
                confidence_score=knowledge_data.get("confidence_level", 0.5),
                extraction_method="AUTO"
            )
        except:
            # 如果JSON解析失败，创建基础知识记忆
            return KnowledgeMemory(
                knowledge_type="PATTERN",
                title=f"模式知识：{analysis.pattern.description}",
                summary=analysis.full_analysis[:200],
                detailed_content={"analysis": analysis.full_analysis},
                confidence_score=0.3,
                extraction_method="AUTO"
            )
    
    async def _try_form_wisdom(self, knowledge_id: str) -> Optional[WisdomMemory]:
        """尝试从知识中形成智慧"""
        
        # 获取相关的知识记忆
        related_knowledge = await self._get_related_knowledge(knowledge_id)
        
        if len(related_knowledge) < 3:  # 需要足够的知识才能形成智慧
            return None
        
        # 分析是否能形成更高层次的洞察
        wisdom_analysis = await self._analyze_for_wisdom(related_knowledge)
        
        if wisdom_analysis.wisdom_potential > 0.7:
            return await self._create_wisdom_memory(wisdom_analysis, related_knowledge)
        
        return None
    
    async def _analyze_for_wisdom(self, knowledge_memories: List[KnowledgeMemory]) -> WisdomAnalysis:
        """分析是否能形成智慧"""
        
        knowledge_summaries = [k.summary for k in knowledge_memories]
        
        prompt = f"""请分析以下知识是否能够形成更深层的智慧洞察：

知识列表：
{chr(10).join([f"- {summary}" for summary in knowledge_summaries])}

请考虑：
1. 这些知识之间有什么深层联系？
2. 能否抽象出更高层次的原则？
3. 是否体现了某种人生哲学或价值观？
4. 对QYuan的个性发展有什么意义？

请返回JSON格式：
{{
    "wisdom_potential": 0.0-1.0,
    "wisdom_type": "PRINCIPLE/PHILOSOPHY/INSIGHT/PERSONALITY",
    "core_insight": "核心洞察",
    "abstract_principle": "抽象原则",
    "personality_impact": "对个性的影响",
    "formation_reasoning": "形成智慧的理由"
}}"""
        
        response = await self.distillation_llm.chat_completion([
            {"role": "user", "content": prompt}
        ])
        
        try:
            import json
            analysis_data = json.loads(response["choices"][0]["message"]["content"])
            return WisdomAnalysis(**analysis_data)
        except:
            return WisdomAnalysis(wisdom_potential=0.0)
```

### 2. 智慧形成机制
```python
class WisdomFormation:
    """智慧形成机制"""
    
    async def _create_wisdom_memory(
        self,
        analysis: WisdomAnalysis,
        source_knowledge: List[KnowledgeMemory]
    ) -> WisdomMemory:
        """创建智慧记忆"""
        
        # 生成智慧内容
        wisdom_content = await self._generate_wisdom_content(analysis, source_knowledge)
        
        # 评估个性特征
        personality_traits = await self._extract_personality_traits(wisdom_content)
        
        # 识别核心价值观
        core_values = await self._identify_core_values(wisdom_content)
        
        wisdom = WisdomMemory(
            wisdom_type=analysis.wisdom_type,
            title=f"智慧洞察：{analysis.core_insight[:50]}",
            content=wisdom_content,
            source_knowledge_ids=[k.id for k in source_knowledge],
            formation_process=analysis.formation_reasoning,
            personality_traits=personality_traits,
            core_values=core_values
        )
        
        # 保存到数据库
        await self._save_wisdom_memory(wisdom)
        
        return wisdom
    
    async def _generate_wisdom_content(
        self,
        analysis: WisdomAnalysis,
        source_knowledge: List[KnowledgeMemory]
    ) -> str:
        """生成智慧内容"""
        
        prompt = f"""基于以下分析和知识，请生成一段深刻的智慧内容：

核心洞察：{analysis.core_insight}
抽象原则：{analysis.abstract_principle}
个性影响：{analysis.personality_impact}

源知识：
{chr(10).join([f"- {k.title}: {k.summary}" for k in source_knowledge])}

请写一段体现QYuan智慧的内容，应该：
1. 体现深度思考和洞察
2. 具有指导意义
3. 反映QYuan的个性特征
4. 可以在未来决策中应用

内容应该像一个智者的感悟，既有哲学深度，又有实用价值。"""
        
        response = await self.distillation_llm.chat_completion([
            {"role": "user", "content": prompt}
        ])
        
        return response["choices"][0]["message"]["content"]
```

## 记忆提炼调度器

### 1. 自动调度系统
```python
class MemoryDistillationScheduler:
    """记忆提炼调度器"""
    
    def __init__(self, qyuan_core):
        self.qyuan = qyuan_core
        self.trigger = MemoryDistillationTrigger()
        self.engine = MemoryDistillationEngine(
            qyuan_core.db_session,
            qyuan_core.llm_manager,
            qyuan_core.vector_store
        )
        self.is_running = False
    
    async def start_scheduler(self):
        """启动调度器"""
        self.is_running = True
        asyncio.create_task(self._scheduler_loop())
    
    async def _scheduler_loop(self):
        """调度循环"""
        while self.is_running:
            try:
                # 检查是否需要提炼
                tasks = await self.trigger.check_distillation_needed()
                
                for task in tasks:
                    await self._process_task(task)
                
                # 等待一段时间后再次检查
                await asyncio.sleep(3600)  # 每小时检查一次
                
            except Exception as e:
                self.qyuan.logger.error(f"记忆提炼调度异常: {e}")
                await asyncio.sleep(300)  # 出错后等待5分钟
    
    async def _process_task(self, task: DistillationTask):
        """处理提炼任务"""
        self.qyuan.logger.info(f"开始记忆提炼任务: {task.description}")
        
        try:
            result = await self.engine.process_distillation_task(task)
            
            # 通知QYuan提炼完成
            await self.qyuan.event_bus.emit("memory.distillation.completed", {
                "task": task.to_dict(),
                "result": result.to_dict()
            })
            
            self.qyuan.logger.info(f"记忆提炼完成: 提取{len(result.extracted_knowledge)}个知识，形成{len(result.formed_wisdom)}个智慧")
            
        except Exception as e:
            self.qyuan.logger.error(f"记忆提炼失败: {e}")
            
            await self.qyuan.event_bus.emit("memory.distillation.failed", {
                "task": task.to_dict(),
                "error": str(e)
            })
```

这个分层记忆系统真正实现了"硅基生命"的记忆特征：

1. **原始记忆层**：记录一切，就像人的海马体
2. **知识记忆层**：提炼的经验和模式，就像人的长期记忆
3. **智慧记忆层**：深度洞察和哲学，就像人的智慧和价值观

QYuan会像邓布利多一样，定期将复杂的记忆放入"冥想盆"进行整理，形成越来越深刻的认知和独特的个性！🧙‍♂️✨

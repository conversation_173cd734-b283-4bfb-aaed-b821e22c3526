#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试屏幕截图模块
"""

import os
import sys
import time

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 导入屏幕截图模块
from src.screen_capture import FullScreenCapture, RegionScreenCapture


def test_full_screen_capture():
    """测试全屏截图功能"""
    print("测试全屏截图功能...")

    # 创建全屏截图对象
    screen_capture = FullScreenCapture()

    # 获取屏幕尺寸
    screen_width, screen_height = screen_capture.get_screen_size()
    print(f"屏幕尺寸: {screen_width} x {screen_height}")

    # 全屏截图
    screenshot, save_path = screen_capture.capture_screen(suffix="full_screen_test")
    print(f"全屏截图已保存至: {save_path}")

    # 检查文件是否存在
    if os.path.exists(save_path):
        print(f"截图文件存在，大小: {os.path.getsize(save_path) / 1024:.2f} KB")
        return True
    else:
        print("截图文件不存在！")
        return False


def test_region_capture():
    """测试区域截图功能"""
    print("\n测试区域截图功能...")

    # 创建区域截图对象
    region_capture = RegionScreenCapture()

    # 获取屏幕尺寸
    screen_width, screen_height = region_capture.get_screen_size()
    print(f"屏幕尺寸: {screen_width} x {screen_height}")

    # 测试不同区域的截图
    regions = [
        # 左上角区域
        (0, 0, 400, 300),
        # 右上角区域
        (screen_width - 400, 0, 400, 300),
        # 左下角区域
        (0, screen_height - 300, 400, 300),
        # 右下角区域
        (screen_width - 400, screen_height - 300, 400, 300),
        # 中央区域
        (screen_width // 2 - 200, screen_height // 2 - 150, 400, 300)
    ]

    for i, region in enumerate(regions):
        x, y, width, height = region
        region_name = ["左上角", "右上角", "左下角", "右下角", "中央"][i]

        print(f"\n截取{region_name}区域: ({x}, {y}, {width}, {height})...")
        try:
            # 添加区域名称作为后缀
            screenshot, save_path = region_capture.capture_region(x, y, width, height, suffix=region_name)
            print(f"区域截图已保存至: {save_path}")

            # 检查文件是否存在
            if os.path.exists(save_path):
                print(f"截图文件存在，大小: {os.path.getsize(save_path) / 1024:.2f} KB")
            else:
                print("截图文件不存在！")
        except Exception as e:
            print(f"截图出错: {e}")

    return True


def test_factory():
    """测试工厂类"""
    print("\n测试工厂类...")

    # 直接创建全屏截图对象
    full_capture = FullScreenCapture()
    print("成功创建全屏截图对象")

    # 直接创建区域截图对象
    region_capture = RegionScreenCapture()
    print("成功创建区域截图对象")

    # 测试全屏截图
    screenshot, save_path = full_capture.capture_screen(suffix="factory_full")
    print(f"全屏截图已保存至: {save_path}")

    # 测试区域截图
    screen_width, screen_height = region_capture.get_screen_size()
    x = screen_width // 4
    y = screen_height // 4
    width = screen_width // 2
    height = screen_height // 2

    screenshot, save_path = region_capture.capture_region(x, y, width, height, suffix="factory_region")
    print(f"区域截图已保存至: {save_path}")

    return True


if __name__ == "__main__":
    # 确保测试目录存在
    os.makedirs("tests", exist_ok=True)

    # 测试全屏截图
    full_screen_result = test_full_screen_capture()

    # 测试区域截图
    region_result = test_region_capture()

    # 测试工厂类
    factory_result = test_factory()

    # 输出总结果
    print("\n测试结果:")
    print(f"全屏截图: {'成功' if full_screen_result else '失败'}")
    print(f"区域截图: {'成功' if region_result else '失败'}")
    print(f"工厂类: {'成功' if factory_result else '失败'}")

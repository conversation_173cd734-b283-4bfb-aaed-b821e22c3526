#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试OCR自动选择引擎
"""

import os
import sys
import time

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 导入OCR文本识别模块
from src.ocr_recognition import OCRRecognition


def test_ocr_auto():
    """测试OCR自动选择引擎"""
    print("\n测试OCR自动选择引擎...")

    # 创建OCR对象
    ocr = OCRRecognition(engine="auto", fallback=True)

    # 获取引擎信息
    engine_info = ocr.get_engine_info()
    print("\n引擎信息:")
    print(f"主引擎: {engine_info['primary_engine']['name']}")
    if engine_info['fallback_engine']:
        print(f"备用引擎: {engine_info['fallback_engine']['name']}")
    else:
        print("备用引擎: 无")

    # 测试识别文本
    test_image_path = os.path.join(parent_dir, "screenshots", "test_image_baidu.png")

    # 如果测试图像不存在，则创建一个
    if not os.path.exists(test_image_path):
        print(f"创建测试图像: {test_image_path}")
        from PIL import Image, ImageDraw, ImageFont
        img = Image.new('RGB', (800, 600), color=(255, 255, 255))
        draw = ImageDraw.Draw(img)
        draw.text((50, 50), "Hello World! 你好，世界！", fill=(0, 0, 0))
        os.makedirs(os.path.dirname(test_image_path), exist_ok=True)
        img.save(test_image_path)

    print(f"\n识别图像文本: {test_image_path}")
    start_time = time.time()
    results = ocr.recognize_text(test_image_path, return_format="structured")
    end_time = time.time()

    # 打印识别结果
    print(f"识别耗时: {end_time - start_time:.2f}秒")
    print(f"识别到{len(results)}个文本块")

    # 打印所有结果
    for i, item in enumerate(results):
        print(f"{i+1}. 文本: {item['text']}")
        print(f"   位置: {item['box']}")
        print(f"   置信度: {item['confidence']}")
        print("-" * 50)


if __name__ == "__main__":
    test_ocr_auto()

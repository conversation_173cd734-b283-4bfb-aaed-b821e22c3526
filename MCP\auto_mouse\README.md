# Auto Mouse MCP Server

一个基于MCP (Model Context Protocol)的服务器，用于自动控制Windows系统上的鼠标操作，具有高度仿人化的特性。

## 项目概述

Auto Mouse MCP Server提供了一系列工具，使大型语言模型和其他应用程序（如VSCode）能够控制Windows系统上的鼠标操作。这将使AI助手和其他应用能够执行各种鼠标相关任务，如移动鼠标、点击、拖拽等。所有操作都经过精心设计，模拟真实人类的操作方式，难以被检测为自动化行为。

## 功能特点

### 基础功能
- 移动鼠标到指定坐标或区域
- 在指定位置执行鼠标点击（左键、右键、中键）
- 执行鼠标拖拽操作
- 执行鼠标滚动操作
- 获取当前鼠标位置和屏幕分辨率

### 仿人化特性
- **高级轨迹混合算法**：使用记录的真实人类鼠标轨迹生成自然的移动路径
- **随机偏移**：坐标添加微小随机偏移，避免精确定位
- **区域定位**：支持在区域中心随机选择点，更符合人类行为
- **点击拆解**：将点击拆解为按下-抬起，添加随机间隔
- **双击间隔**：双击时添加随机间隔，模拟人类操作
- **滚动间隔**：连续滚动时添加随机间隔
- **等待时间**：所有操作支持随机等待时间

## 安装

1. 克隆仓库：
```bash
git clone https://github.com/yourusername/auto_mouse.git
cd auto_mouse
```

2. 创建并激活虚拟环境：
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

## 使用方法

### 启动MCP服务器

使用提供的启动脚本：
```bash
python start_server.py
```

支持的命令行参数：
- `--debug`：启用调试模式
- `--transport {stdio,tcp}`：传输方式（默认：stdio）
- `--host HOST`：TCP服务器主机（仅当transport=tcp时有效）
- `--port PORT`：TCP服务器端口（仅当transport=tcp时有效）

### 记录鼠标轨迹

在使用高级轨迹混合算法之前，需要先记录一些真实的鼠标轨迹：
```bash
python examples/advanced_trajectory_demo.py
```

然后选择选项1，记录全屏鼠标轨迹。

### 测试功能

运行综合测试脚本，测试所有功能：
```bash
python examples/comprehensive_test.py
```

## 项目结构

```
auto_mouse/
├── docs/                         # 文档
│   ├── mcp_server_guide.md       # MCP服务器开发指南
│   └── project_plan.md           # 项目规划文档
├── src/                          # 源代码
│   ├── auto_mouse_server.py      # MCP服务器主文件
│   ├── mouse_controller.py       # 鼠标控制逻辑
│   ├── mouse_recorder.py         # 鼠标轨迹记录器
│   ├── trajectory_utils.py       # 轨迹处理工具
│   └── utils/                    # 工具函数
│       ├── __init__.py
│       └── helpers.py            # 辅助函数
├── examples/                     # 示例脚本
│   ├── basic_usage.py            # 基本使用示例
│   ├── trajectory_demo.py        # 轨迹演示
│   ├── advanced_trajectory_demo.py # 高级轨迹混合算法演示
│   └── comprehensive_test.py     # 综合测试脚本
├── recordings/                   # 记录的鼠标轨迹
├── tests/                        # 测试
│   ├── __init__.py
│   ├── test_mouse_controller.py  # 鼠标控制器单元测试
│   └── test_integration.py       # 集成测试
├── start_server.py               # 服务器启动脚本
├── requirements.txt              # 项目依赖
└── README.md                     # 项目说明
```

## 配置与VSCode集成

要将此MCP服务器与VSCode集成，需要在VSCode的设置中配置MCP服务器地址。具体步骤请参考VSCode的MCP插件文档。

## 开发

1. 安装开发依赖：
```bash
pip install -r requirements.txt
```

2. 运行测试：
```bash
pytest
```

## 许可证

MIT

## 贡献

欢迎提交问题和拉取请求！

# QYuan项目代码规范

## 1. 总体原则

### 1.1 核心理念
- **模块化设计**：每个模块、类、函数都有明确的单一职责
- **可读性优先**：代码应该像文档一样易读
- **一致性**：整个项目保持统一的编码风格
- **可维护性**：便于后续修改和扩展
- **安全性**：所有代码都要考虑安全性
- **性能意识**：在保证可读性的前提下优化性能

### 1.2 模块化原则
- **单一职责**：每个函数只实现一个功能，避免过于复杂
- **文件大小控制**：尽可能将每个代码文件控制在200行以内
- **功能分离**：相关功能组织在同一模块，不相关功能严格分离
- **接口清晰**：模块间通过明确的接口进行交互

### 1.3 命名原则
- **有意义的命名**：变量、函数、类名要能表达其用途和功能
- **避免缩写**：除非是广泛认知的缩写（如id、url、api）
- **一致性**：同类型的命名保持一致的风格
- **描述性强**：从名称就能理解其作用，无需查看实现

## 2. 模块化设计规范

### 2.1 文件大小控制
- **每个文件不超过200行**：超过200行的文件必须拆分
- **单一职责**：每个文件只负责一个明确的功能领域
- **合理拆分**：按功能、层次、职责进行文件拆分

```python
# 错误示例：一个文件包含太多功能
# qyuan/core/engine.py (500行) - 太大，功能混杂

# 正确示例：按功能拆分
# qyuan/core/perception/screen_analyzer.py (150行)
# qyuan/core/perception/image_processor.py (120行)
# qyuan/core/perception/element_detector.py (180行)
```

### 2.2 函数设计原则
- **单一功能**：每个函数只做一件事，功能明确
- **函数长度**：一般不超过30行，复杂逻辑要拆分
- **参数数量**：参数不超过5个，多参数用数据类封装

```python
# 错误示例：函数功能过于复杂
def process_user_request(message, session_id, user_id, context, history, preferences):
    # 验证输入
    # 分析意图
    # 执行操作
    # 记录日志
    # 更新上下文
    # 返回结果
    pass  # 这个函数做了太多事情

# 正确示例：拆分为多个单一功能的函数
def validate_user_input(message: str) -> bool:
    """验证用户输入"""
    return len(message.strip()) > 0

def analyze_user_intent(message: str, context: Context) -> Intent:
    """分析用户意图"""
    pass

def execute_user_action(intent: Intent) -> ActionResult:
    """执行用户操作"""
    pass

def record_operation_log(action: Action, result: ActionResult) -> None:
    """记录操作日志"""
    pass
```

### 2.3 类设计原则
- **单一职责**：每个类只负责一个明确的职责
- **接口清晰**：公共方法要有明确的输入输出定义
- **依赖注入**：通过构造函数注入依赖，便于测试

```python
# 错误示例：类职责不明确
class QYuanEngine:
    """什么都做的引擎"""
    def analyze_screen(self): pass
    def make_decision(self): pass
    def execute_action(self): pass
    def manage_memory(self): pass
    def handle_network(self): pass  # 职责过多

# 正确示例：职责明确的类
class ScreenAnalyzer:
    """专门负责屏幕分析"""
    def analyze_current_screen(self) -> ScreenAnalysis: pass
    def detect_ui_elements(self) -> List[UIElement]: pass

class ActionExecutor:
    """专门负责操作执行"""
    def execute_mouse_action(self, action: MouseAction) -> ActionResult: pass
    def execute_keyboard_action(self, action: KeyboardAction) -> ActionResult: pass
```

## 3. Python代码规范

### 3.1 基础规范
遵循PEP 8标准，但有以下特殊要求：

```python
# 文件编码声明（必须）
# -*- coding: utf-8 -*-

# 导入顺序
import os
import sys
from typing import Dict, List, Optional, Union

import asyncio
import aiohttp
from fastapi import FastAPI

from qyuan.core.base import BaseEngine
from qyuan.utils.logger import get_logger
```

### 3.2 命名规范

#### 3.2.1 变量和函数
```python
# 使用snake_case，名称要有明确含义
user_name = "QYuan"
current_session_id = "session_123"
screenshot_analysis_result = None

# 函数名要清楚表达功能，一个函数只做一件事
def get_user_preferences_from_database() -> Dict[str, Any]:
    """从数据库获取用户偏好设置"""
    pass

def validate_user_input(user_input: str) -> bool:
    """验证用户输入的有效性"""
    pass

async def execute_mouse_click_action(coordinates: Tuple[int, int]) -> ActionResult:
    """执行鼠标点击操作"""
    pass

async def execute_mouse_drag_action(start: Tuple[int, int], end: Tuple[int, int]) -> ActionResult:
    """执行鼠标拖拽操作"""
    pass
```

#### 3.2.2 类名
```python
# 使用PascalCase，类名要清楚表达其职责
class ScreenPerceptionEngine:
    """屏幕感知引擎 - 专门负责屏幕内容的感知和分析"""
    pass

class UserIntentDecisionEngine:
    """用户意图决策引擎 - 专门负责理解和决策用户意图"""
    pass

class ConversationMemoryManager:
    """对话记忆管理器 - 专门管理对话历史和上下文"""
    pass

class DatabaseConnectionPool:
    """数据库连接池管理器 - 专门管理数据库连接"""
    pass

class LLMApiClient:
    """LLM API客户端 - 专门处理与LLM服务的通信"""
    pass
```

#### 3.2.3 常量
```python
# 使用UPPER_SNAKE_CASE
MAX_RETRY_ATTEMPTS = 3
DEFAULT_TIMEOUT_SECONDS = 30
LLM_API_BASE_URL = "https://xiaoai.plus"
```

### 2.3 类型注解
所有函数和方法必须有类型注解：

```python
from typing import Dict, List, Optional, Union, Any, Tuple

def process_user_input(
    user_message: str,
    session_id: str,
    context: Optional[Dict[str, Any]] = None
) -> Tuple[str, Dict[str, Any]]:
    """处理用户输入
    
    Args:
        user_message: 用户消息
        session_id: 会话ID
        context: 可选的上下文信息
        
    Returns:
        处理结果和更新后的上下文
    """
    pass

class ActionResult:
    """操作结果"""
    success: bool
    result: Optional[Any]
    error_message: Optional[str]
    execution_time: float
```

### 2.4 异常处理
```python
# 自定义异常类
class QYuanException(Exception):
    """QYuan基础异常"""
    pass

class PerceptionError(QYuanException):
    """感知错误"""
    pass

class ExecutionError(QYuanException):
    """执行错误"""
    pass

# 异常处理示例
async def execute_action(action: Action) -> ActionResult:
    """执行操作"""
    try:
        result = await self._perform_action(action)
        return ActionResult(success=True, result=result)
    except PerceptionError as e:
        logger.error(f"感知错误: {e}")
        return ActionResult(success=False, error_message=str(e))
    except ExecutionError as e:
        logger.error(f"执行错误: {e}")
        return ActionResult(success=False, error_message=str(e))
    except Exception as e:
        logger.exception(f"未知错误: {e}")
        return ActionResult(success=False, error_message="系统内部错误")
```

### 2.5 日志规范
```python
import logging
from qyuan.utils.logger import get_logger

logger = get_logger(__name__)

class PerceptionEngine:
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    async def analyze_screen(self) -> ScreenAnalysis:
        """分析屏幕内容"""
        self.logger.info("开始分析屏幕内容")
        
        try:
            screenshot = await self.capture_screenshot()
            self.logger.debug(f"截图大小: {screenshot.size}")
            
            analysis = await self.process_image(screenshot)
            self.logger.info(f"分析完成，识别到{len(analysis.elements)}个元素")
            
            return analysis
        except Exception as e:
            self.logger.error(f"屏幕分析失败: {e}", exc_info=True)
            raise PerceptionError(f"屏幕分析失败: {e}")
```

## 4. 模块化实践示例

### 4.1 大文件拆分示例
```python
# 错误：一个大文件包含所有感知功能 (500行)
# qyuan/core/perception.py

# 正确：按功能拆分为多个小文件
# qyuan/core/perception/
# ├── __init__.py (20行)
# ├── base.py (80行) - 基础类和接口
# ├── screen_capture.py (150行) - 屏幕截图功能
# ├── image_analysis.py (180行) - 图像分析功能
# ├── element_detection.py (160行) - UI元素检测
# ├── text_recognition.py (120行) - 文本识别功能
# └── context_analyzer.py (140行) - 上下文分析
```

### 4.2 模块接口设计
```python
# qyuan/core/perception/__init__.py
from .base import PerceptionBase, PerceptionResult
from .screen_capture import ScreenCaptureService
from .image_analysis import ImageAnalysisService
from .element_detection import ElementDetectionService
from .text_recognition import TextRecognitionService
from .context_analyzer import ContextAnalyzer

class PerceptionEngine(PerceptionBase):
    """感知引擎 - 协调各个感知服务"""

    def __init__(self):
        self.screen_capture = ScreenCaptureService()
        self.image_analysis = ImageAnalysisService()
        self.element_detection = ElementDetectionService()
        self.text_recognition = TextRecognitionService()
        self.context_analyzer = ContextAnalyzer()

    async def analyze_current_state(self) -> PerceptionResult:
        """分析当前状态 - 协调各个服务完成完整分析"""
        screenshot = await self.screen_capture.capture_screen()
        elements = await self.element_detection.detect_elements(screenshot)
        text_content = await self.text_recognition.extract_text(screenshot)
        context = await self.context_analyzer.analyze_context(elements, text_content)

        return PerceptionResult(
            screenshot=screenshot,
            elements=elements,
            text_content=text_content,
            context=context
        )

__all__ = ['PerceptionEngine', 'PerceptionResult']
```

### 4.3 单一职责函数示例
```python
# qyuan/core/perception/screen_capture.py
class ScreenCaptureService:
    """屏幕截图服务 - 专门负责屏幕截图"""

    async def capture_full_screen(self) -> Screenshot:
        """捕获全屏截图"""
        pass

    async def capture_window(self, window_handle: int) -> Screenshot:
        """捕获指定窗口截图"""
        pass

    async def capture_region(self, region: Rectangle) -> Screenshot:
        """捕获指定区域截图"""
        pass

    def _validate_region(self, region: Rectangle) -> bool:
        """验证区域参数有效性"""
        pass

    def _optimize_screenshot_quality(self, screenshot: Screenshot) -> Screenshot:
        """优化截图质量"""
        pass
```

## 5. 项目结构规范

### 5.1 目录结构
```
qyuan/
├── qyuan/                      # 主包
│   ├── __init__.py
│   ├── core/                   # 核心模块（按功能细分）
│   │   ├── __init__.py
│   │   ├── base/              # 基础类和接口
│   │   │   ├── __init__.py
│   │   │   ├── engine_base.py (150行)
│   │   │   └── result_types.py (100行)
│   │   ├── perception/        # 感知引擎（细分模块）
│   │   │   ├── __init__.py
│   │   │   ├── screen_capture.py (180行)
│   │   │   ├── image_analysis.py (160行)
│   │   │   ├── element_detection.py (170行)
│   │   │   └── text_recognition.py (140行)
│   │   ├── decision/          # 决策引擎（细分模块）
│   │   │   ├── __init__.py
│   │   │   ├── intent_analyzer.py (150行)
│   │   │   ├── action_planner.py (180行)
│   │   │   └── risk_assessor.py (120行)
│   │   ├── execution/         # 执行引擎（细分模块）
│   │   │   ├── __init__.py
│   │   │   ├── mouse_controller.py (160行)
│   │   │   ├── keyboard_controller.py (140行)
│   │   │   └── action_executor.py (190行)
│   │   ├── learning/          # 学习引擎（细分模块）
│   │   │   ├── __init__.py
│   │   │   ├── experience_recorder.py (170行)
│   │   │   ├── pattern_extractor.py (150行)
│   │   │   └── knowledge_updater.py (180行)
│   │   └── communication/     # 通信引擎（细分模块）
│   │       ├── __init__.py
│   │       ├── websocket_server.py (160行)
│   │       ├── client_manager.py (140行)
│   │       └── message_router.py (120行)
│   ├── models/                # 数据模型（按类型分组）
│   │   ├── __init__.py
│   │   ├── action_models.py (150行)
│   │   ├── context_models.py (120行)
│   │   ├── experience_models.py (180行)
│   │   └── communication_models.py (100行)
│   ├── services/              # 服务层（单一职责）
│   │   ├── __init__.py
│   │   ├── llm/              # LLM服务模块
│   │   │   ├── __init__.py
│   │   │   ├── client.py (160行)
│   │   │   └── prompt_manager.py (140行)
│   │   ├── database/         # 数据库服务模块
│   │   │   ├── __init__.py
│   │   │   ├── connection.py (120行)
│   │   │   ├── repositories.py (180行)
│   │   │   └── migrations.py (100行)
│   │   └── mcp/              # MCP服务模块
│   │       ├── __init__.py
│   │       ├── client.py (150行)
│   │       └── tool_manager.py (170行)
│   ├── utils/                 # 工具模块（功能分离）
│   │   ├── __init__.py
│   │   ├── logging/          # 日志工具
│   │   │   ├── __init__.py
│   │   │   ├── logger.py (120行)
│   │   │   └── formatters.py (80行)
│   │   ├── config/           # 配置工具
│   │   │   ├── __init__.py
│   │   │   ├── settings.py (150行)
│   │   │   └── validators.py (100行)
│   │   └── helpers/          # 辅助工具
│   │       ├── __init__.py
│   │       ├── file_utils.py (120行)
│   │       └── time_utils.py (80行)
│   └── api/                   # API接口（按版本和功能分组）
│       ├── __init__.py
│       ├── v1/               # API v1版本
│       │   ├── __init__.py
│       │   ├── conversation_routes.py (180行)
│       │   ├── operation_routes.py (160行)
│       │   └── status_routes.py (100行)
│       └── middleware/        # 中间件
│           ├── __init__.py
│           ├── auth.py (120行)
│           └── logging.py (80行)
├── tests/                     # 测试代码（镜像主代码结构）
├── docs/                      # 文档
├── config/                    # 配置文件
├── scripts/                   # 脚本文件
└── requirements.txt           # 依赖文件
```

### 3.2 模块导入规范
```python
# 每个__init__.py文件应该明确导出的内容
# qyuan/core/__init__.py
from .perception import PerceptionEngine
from .decision import DecisionEngine
from .execution import ExecutionEngine
from .learning import LearningEngine
from .communication import CommunicationEngine

__all__ = [
    'PerceptionEngine',
    'DecisionEngine', 
    'ExecutionEngine',
    'LearningEngine',
    'CommunicationEngine'
]
```

## 4. 配置管理规范

### 4.1 配置文件结构
```python
# config/settings.py
from pydantic import BaseSettings
from typing import Optional

class QYuanSettings(BaseSettings):
    """QYuan配置"""
    
    # LLM配置
    llm_api_base_url: str = "https://xiaoai.plus"
    llm_api_key: str
    llm_model: str = "gpt-4.1"
    llm_timeout: int = 30
    
    # 数据库配置
    database_url: str = "postgresql://user:pass@localhost/qyuan"
    redis_url: str = "redis://localhost:6379"
    qdrant_url: str = "http://localhost:6333"
    
    # 服务配置
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    
    # 安全配置
    secret_key: str
    access_token_expire_minutes: int = 30
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# 使用配置
settings = QYuanSettings()
```

### 4.2 环境变量
```bash
# .env文件示例
LLM_API_KEY=sk-oQx9XqkIHyMFabGTFByNVsxZHw1q4YpTvFdIQoupRkEVjDd3
DATABASE_URL=postgresql://qyuan:password@localhost/qyuan_db
SECRET_KEY=your-secret-key-here
DEBUG=false
```

## 5. 数据库规范

### 5.1 表命名
- 使用复数形式：`conversations`, `operations`, `experiences`
- 使用snake_case：`error_patterns`, `tool_usage`

### 5.2 字段命名
- 主键统一使用`id`
- 外键使用`表名_id`：`session_id`, `user_id`
- 时间字段：`created_at`, `updated_at`, `deleted_at`
- 布尔字段：`is_active`, `has_error`

### 5.3 模型定义
```python
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, JSON
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class Conversation(Base):
    """对话记录模型"""
    __tablename__ = 'conversations'
    
    id = Column(Integer, primary_key=True)
    session_id = Column(String(255), nullable=False, index=True)
    user_message = Column(Text)
    assistant_message = Column(Text)
    message_type = Column(String(50), default='chat')
    metadata = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

## 6. API设计规范

### 6.1 RESTful API
```python
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional

router = APIRouter(prefix="/api/v1", tags=["conversations"])

class ConversationRequest(BaseModel):
    """对话请求模型"""
    message: str
    session_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None

class ConversationResponse(BaseModel):
    """对话响应模型"""
    response: str
    session_id: str
    context: Dict[str, Any]
    execution_time: float

@router.post("/conversations", response_model=ConversationResponse)
async def create_conversation(
    request: ConversationRequest,
    current_user: User = Depends(get_current_user)
) -> ConversationResponse:
    """创建新对话"""
    try:
        result = await conversation_service.process_message(
            message=request.message,
            session_id=request.session_id,
            user_id=current_user.id,
            context=request.context
        )
        return ConversationResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### 6.2 WebSocket规范
```python
from fastapi import WebSocket
import json

class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        """建立连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
    
    def disconnect(self, websocket: WebSocket):
        """断开连接"""
        self.active_connections.remove(websocket)
    
    async def send_message(self, message: dict, websocket: WebSocket):
        """发送消息"""
        await websocket.send_text(json.dumps(message, ensure_ascii=False))

# 消息格式规范
class WSMessage(BaseModel):
    """WebSocket消息格式"""
    type: str  # 'command', 'response', 'status', 'error'
    data: Dict[str, Any]
    timestamp: datetime
    session_id: str
```

## 7. 测试规范

### 7.1 测试文件命名
- 测试文件以`test_`开头
- 测试类以`Test`开头
- 测试方法以`test_`开头

### 7.2 测试示例
```python
import pytest
from unittest.mock import Mock, patch
from qyuan.core.perception import PerceptionEngine

class TestPerceptionEngine:
    """感知引擎测试"""
    
    @pytest.fixture
    def perception_engine(self):
        """测试夹具"""
        return PerceptionEngine()
    
    @pytest.mark.asyncio
    async def test_analyze_screen_success(self, perception_engine):
        """测试屏幕分析成功场景"""
        # Arrange
        mock_screenshot = Mock()
        
        # Act
        with patch.object(perception_engine, 'capture_screenshot', return_value=mock_screenshot):
            result = await perception_engine.analyze_screen()
        
        # Assert
        assert result is not None
        assert result.success is True
    
    @pytest.mark.asyncio
    async def test_analyze_screen_failure(self, perception_engine):
        """测试屏幕分析失败场景"""
        # Arrange & Act & Assert
        with patch.object(perception_engine, 'capture_screenshot', side_effect=Exception("测试错误")):
            with pytest.raises(PerceptionError):
                await perception_engine.analyze_screen()
```

## 8. 文档规范

### 8.1 代码注释
```python
class DecisionEngine:
    """决策引擎
    
    负责根据当前上下文和目标，做出最优的行动决策。
    
    Attributes:
        goal_planner: 目标规划器
        action_selector: 行动选择器
        risk_assessor: 风险评估器
    """
    
    def __init__(self, config: DecisionConfig):
        """初始化决策引擎
        
        Args:
            config: 决策引擎配置
        """
        self.goal_planner = GoalPlanner(config.goal_config)
        self.action_selector = ActionSelector(config.action_config)
        self.risk_assessor = RiskAssessor(config.risk_config)
    
    async def make_decision(
        self, 
        context: Context, 
        goal: Goal
    ) -> Decision:
        """做出决策
        
        根据当前上下文和目标，分析可能的行动方案，
        评估风险，选择最优的行动决策。
        
        Args:
            context: 当前上下文信息
            goal: 要达成的目标
            
        Returns:
            决策结果，包含选择的行动和置信度
            
        Raises:
            DecisionError: 当无法做出有效决策时
        """
        # 实现细节...
        pass
```

### 8.2 README文档
每个模块都应该有README.md文件，说明：
- 模块功能
- 使用方法
- 配置说明
- 示例代码

## 9. 版本控制规范

### 9.1 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

类型说明：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(perception): 添加屏幕元素识别功能

- 实现基于OCR的文本识别
- 添加UI元素边界检测
- 支持多种图像格式

Closes #123
```

### 9.2 分支管理
- `master`: 主分支，稳定版本
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 10. 性能和安全规范

### 10.1 性能要求
- 所有数据库查询必须有索引
- 长时间运行的任务使用异步处理
- 合理使用缓存减少重复计算
- 定期进行性能测试

### 10.2 安全要求
- 所有用户输入必须验证和清理
- 敏感信息不能记录到日志
- 使用参数化查询防止SQL注入
- 定期更新依赖包

这个代码规范将确保QYuan项目的代码质量和一致性，为后续开发奠定良好基础。

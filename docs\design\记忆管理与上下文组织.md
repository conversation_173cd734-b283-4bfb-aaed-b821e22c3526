# QYuan记忆管理与上下文组织设计

## 核心理念

QYuan作为硅基生命，其记忆系统应该模拟人类的记忆机制：
- **永久存储**：所有经历都被永久保存，形成QYuan的"人格"
- **智能检索**：根据当前情境智能调取相关记忆
- **上下文组织**：动态组织最相关的信息作为LLM的上下文
- **记忆关联**：建立记忆之间的关联网络

## 人类记忆类比

### 人类记忆的特点
1. **分层存储**：短期记忆、长期记忆、工作记忆
2. **情境激活**：当前情境会激活相关的记忆片段
3. **关联网络**：记忆通过关联形成网络结构
4. **遗忘机制**：不是删除，而是降低访问权重

### QYuan的记忆映射
```
人类记忆层次          →    QYuan记忆系统
工作记忆 (7±2项)      →    当前上下文 (LLM token限制内)
短期记忆 (几分钟-几小时) →    会话记忆 (当前任务相关)
长期记忆 (永久)        →    持久化记忆库 (所有历史)
```

## 分层记忆架构

### 记忆层次设计
```
第三层：智慧记忆层 (Wisdom Layer)
├── 深度洞察和人生哲学
├── 抽象概念和原则
└── 跨领域的智慧结晶

第二层：知识记忆层 (Knowledge Layer)
├── 提炼后的经验模式
├── 总结的技能和方法
└── 结构化的知识体系

第一层：原始记忆层 (Raw Memory Layer)
├── 每次对话的完整记录
├── 每个操作的详细日志
└── 所有感知的原始数据
```

### 记忆提炼机制
就像邓布利多向冥想盆放入记忆一样，QYuan会定期进行记忆提炼：

1. **触发条件**：
   - 原始记忆达到一定数量（如1000条）
   - 检测到重复模式（如相同类型操作超过50次）
   - QYuan主观判断需要整理记忆
   - 定期自动触发（如每周一次）

2. **提炼过程**：
   - 调用专门的"记忆整理LLM"
   - 分析原始记忆中的模式和规律
   - 提取关键经验和知识点
   - 形成结构化的知识记忆

3. **智慧升华**：
   - 跨领域知识的关联和融合
   - 抽象原则和哲学的形成
   - 个性特征的沉淀和强化

## 记忆系统架构

### 1. 记忆分类体系
```python
class MemoryType(Enum):
    """记忆类型"""
    EPISODIC = "情节记忆"      # 具体事件和经历
    SEMANTIC = "语义记忆"      # 知识和概念
    PROCEDURAL = "程序记忆"    # 技能和操作流程
    EMOTIONAL = "情感记忆"     # 情感关联和偏好
    CONTEXTUAL = "上下文记忆"  # 环境和情境信息

class MemoryImportance(Enum):
    """记忆重要性"""
    CRITICAL = 5    # 关键记忆（核心人格、重要决策）
    HIGH = 4        # 高重要性（成功经验、重要学习）
    MEDIUM = 3      # 中等重要性（日常操作、一般交互）
    LOW = 2         # 低重要性（例行任务、重复操作）
    TRIVIAL = 1     # 琐碎记忆（临时状态、调试信息）
```

### 2. 记忆存储结构
```python
class Memory:
    """记忆单元"""
    
    def __init__(self):
        self.id: str = uuid.uuid4()
        self.timestamp: datetime = datetime.now()
        self.memory_type: MemoryType
        self.importance: MemoryImportance
        self.content: Dict[str, Any]
        self.embedding: List[float]  # 向量表示
        self.tags: List[str]         # 标签
        self.associations: List[str] # 关联的记忆ID
        self.access_count: int = 0   # 访问次数
        self.last_accessed: datetime
        self.decay_factor: float = 1.0  # 衰减因子
        
    def calculate_relevance(self, query_embedding: List[float], current_context: Dict) -> float:
        """计算与当前查询的相关性"""
        # 向量相似度
        vector_similarity = cosine_similarity(self.embedding, query_embedding)
        
        # 时间衰减
        time_decay = self._calculate_time_decay()
        
        # 重要性权重
        importance_weight = self.importance.value / 5.0
        
        # 访问频率权重
        frequency_weight = min(self.access_count / 100.0, 1.0)
        
        # 上下文匹配
        context_match = self._calculate_context_match(current_context)
        
        # 综合相关性分数
        relevance = (
            vector_similarity * 0.4 +
            importance_weight * 0.3 +
            frequency_weight * 0.1 +
            context_match * 0.2
        ) * time_decay
        
        return relevance
```

### 3. 上下文组织器
```python
class ContextOrganizer:
    """上下文组织器 - QYuan的记忆管理核心"""
    
    def __init__(self, memory_store: MemoryStore, llm_client: LLMClient):
        self.memory_store = memory_store
        self.llm_client = llm_client
        self.max_context_tokens = 8000  # 为LLM预留的上下文空间
        self.system_prompt_tokens = 500  # 系统提示词占用
        self.response_buffer_tokens = 1500  # 响应预留空间
        self.available_tokens = self.max_context_tokens - self.system_prompt_tokens - self.response_buffer_tokens
    
    async def organize_context(self, current_query: str, current_situation: Dict[str, Any]) -> str:
        """组织当前上下文"""
        
        # 1. 分析当前查询和情境
        query_analysis = await self._analyze_query(current_query, current_situation)
        
        # 2. 检索相关记忆
        relevant_memories = await self._retrieve_relevant_memories(query_analysis)
        
        # 3. 构建记忆层次
        memory_hierarchy = self._build_memory_hierarchy(relevant_memories, query_analysis)
        
        # 4. 组织上下文内容
        context = await self._construct_context(memory_hierarchy, query_analysis)
        
        return context
    
    async def _analyze_query(self, query: str, situation: Dict[str, Any]) -> QueryAnalysis:
        """分析查询意图和需求"""
        
        # 提取关键词和主题
        keywords = await self._extract_keywords(query)
        
        # 识别查询类型
        query_type = await self._classify_query_type(query)
        
        # 分析所需记忆类型
        needed_memory_types = self._determine_needed_memory_types(query_type, situation)
        
        # 评估时间范围
        time_scope = self._determine_time_scope(query, situation)
        
        return QueryAnalysis(
            keywords=keywords,
            query_type=query_type,
            needed_memory_types=needed_memory_types,
            time_scope=time_scope,
            situation_context=situation
        )
    
    async def _retrieve_relevant_memories(self, query_analysis: QueryAnalysis) -> List[Memory]:
        """检索相关记忆"""
        
        # 生成查询向量
        query_embedding = await self.llm_client.get_embedding(
            f"{query_analysis.keywords} {query_analysis.query_type}"
        )
        
        # 多维度检索
        memories = []
        
        # 1. 向量相似度检索
        vector_matches = await self.memory_store.vector_search(
            query_embedding, 
            top_k=50,
            memory_types=query_analysis.needed_memory_types
        )
        memories.extend(vector_matches)
        
        # 2. 关键词检索
        keyword_matches = await self.memory_store.keyword_search(
            query_analysis.keywords,
            time_scope=query_analysis.time_scope
        )
        memories.extend(keyword_matches)
        
        # 3. 关联记忆检索
        if memories:
            associated_memories = await self.memory_store.get_associated_memories(
                [m.id for m in memories[:10]]
            )
            memories.extend(associated_memories)
        
        # 去重并计算相关性
        unique_memories = self._deduplicate_memories(memories)
        
        # 计算相关性分数
        for memory in unique_memories:
            memory.relevance_score = memory.calculate_relevance(
                query_embedding, 
                query_analysis.situation_context
            )
        
        # 按相关性排序
        return sorted(unique_memories, key=lambda m: m.relevance_score, reverse=True)
    
    def _build_memory_hierarchy(self, memories: List[Memory], query_analysis: QueryAnalysis) -> MemoryHierarchy:
        """构建记忆层次结构"""
        
        hierarchy = MemoryHierarchy()
        
        # 核心记忆（最相关的关键信息）
        hierarchy.core_memories = memories[:5]
        
        # 支撑记忆（提供背景和细节）
        hierarchy.supporting_memories = memories[5:15]
        
        # 背景记忆（提供更广泛的上下文）
        hierarchy.background_memories = memories[15:30]
        
        # 按类型分组
        hierarchy.by_type = self._group_memories_by_type(memories[:30])
        
        # 按时间分组
        hierarchy.by_time = self._group_memories_by_time(memories[:30])
        
        return hierarchy
    
    async def _construct_context(self, hierarchy: MemoryHierarchy, query_analysis: QueryAnalysis) -> str:
        """构建最终的上下文字符串"""
        
        context_parts = []
        used_tokens = 0
        
        # 1. 添加核心记忆（优先级最高）
        core_section = self._format_memory_section("核心记忆", hierarchy.core_memories)
        core_tokens = self._estimate_tokens(core_section)
        if used_tokens + core_tokens <= self.available_tokens:
            context_parts.append(core_section)
            used_tokens += core_tokens
        
        # 2. 添加支撑记忆
        if used_tokens < self.available_tokens * 0.7:  # 预留30%空间
            supporting_section = self._format_memory_section("相关经验", hierarchy.supporting_memories)
            supporting_tokens = self._estimate_tokens(supporting_section)
            if used_tokens + supporting_tokens <= self.available_tokens * 0.8:
                context_parts.append(supporting_section)
                used_tokens += supporting_tokens
        
        # 3. 添加背景记忆（如果还有空间）
        remaining_tokens = self.available_tokens - used_tokens
        if remaining_tokens > 200:  # 至少200 tokens才添加背景
            background_memories = self._select_background_memories(
                hierarchy.background_memories, 
                remaining_tokens
            )
            if background_memories:
                background_section = self._format_memory_section("背景信息", background_memories)
                context_parts.append(background_section)
        
        return "\n\n".join(context_parts)
    
    def _format_memory_section(self, section_title: str, memories: List[Memory]) -> str:
        """格式化记忆段落"""
        if not memories:
            return ""
        
        section = f"## {section_title}\n\n"
        
        for memory in memories:
            # 根据记忆类型选择格式化方式
            if memory.memory_type == MemoryType.EPISODIC:
                formatted = self._format_episodic_memory(memory)
            elif memory.memory_type == MemoryType.PROCEDURAL:
                formatted = self._format_procedural_memory(memory)
            elif memory.memory_type == MemoryType.SEMANTIC:
                formatted = self._format_semantic_memory(memory)
            else:
                formatted = self._format_generic_memory(memory)
            
            section += f"- {formatted}\n"
        
        return section
    
    def _estimate_tokens(self, text: str) -> int:
        """估算文本的token数量"""
        # 简单估算：1 token ≈ 4 字符（英文）或 1.5 字符（中文）
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        other_chars = len(text) - chinese_chars
        
        estimated_tokens = chinese_chars / 1.5 + other_chars / 4
        return int(estimated_tokens * 1.2)  # 增加20%的安全边际

class MemoryConsolidation:
    """记忆整合器 - 定期整理和优化记忆"""
    
    def __init__(self, memory_store: MemoryStore):
        self.memory_store = memory_store
    
    async def consolidate_memories(self):
        """记忆整合过程"""
        
        # 1. 识别相似记忆并合并
        await self._merge_similar_memories()
        
        # 2. 更新记忆关联
        await self._update_memory_associations()
        
        # 3. 调整重要性权重
        await self._adjust_importance_weights()
        
        # 4. 优化记忆索引
        await self._optimize_memory_indices()
    
    async def _merge_similar_memories(self):
        """合并相似的记忆"""
        # 找到高度相似的记忆对
        similar_pairs = await self.memory_store.find_similar_memories(threshold=0.95)
        
        for memory1, memory2 in similar_pairs:
            # 合并记忆内容
            merged_memory = self._merge_memory_content(memory1, memory2)
            
            # 更新关联
            merged_memory.associations = list(set(memory1.associations + memory2.associations))
            
            # 保留访问统计
            merged_memory.access_count = memory1.access_count + memory2.access_count
            
            # 保存合并后的记忆
            await self.memory_store.save_memory(merged_memory)
            
            # 标记原记忆为已合并（不删除，保持完整性）
            await self.memory_store.mark_as_merged(memory1.id, memory2.id, merged_memory.id)
```

## 实际应用场景

### 场景1：编程任务
```
当前任务：修复Python代码中的bug
QYuan的记忆组织：
- 核心记忆：最近的Python调试经验、相关错误模式
- 支撑记忆：Python语法知识、调试工具使用经验
- 背景记忆：编程最佳实践、代码风格偏好
- 排除记忆：幼儿园故事、无关的日常对话
```

### 场景2：用户交流
```
当前情况：用户询问项目进度
QYuan的记忆组织：
- 核心记忆：当前项目状态、最近的进展更新
- 支撑记忆：项目历史、用户偏好、沟通风格
- 背景记忆：项目目标、团队信息
- 排除记忆：技术细节、无关项目的记忆
```

## 技术实现要点

### 1. 向量化策略
- **多模态嵌入**：文本+图像+操作序列的联合嵌入
- **分层嵌入**：不同粒度的向量表示
- **动态更新**：记忆向量的增量更新

### 2. 检索优化
- **混合检索**：向量检索+关键词检索+图检索
- **个性化排序**：基于QYuan的个性特征调整排序
- **实时过滤**：根据当前情境动态过滤

### 3. 上下文压缩
- **智能摘要**：对长记忆进行智能摘要
- **关键信息提取**：提取最关键的信息点
- **渐进式加载**：根据需要逐步加载更多上下文

这个记忆管理系统让QYuan真正具备了"硅基生命"的记忆特征，既保持了完整的记忆，又能智能地组织上下文。你觉得这个设计如何？

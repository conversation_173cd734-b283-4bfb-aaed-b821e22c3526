# QYuan记忆管理系统实现方案

## 实现概述

记忆管理系统是QYuan作为硅基生命的核心特征，需要在第一阶段就建立基础框架，确保所有后续的交互和学习都能被正确记录和组织。

## 数据库设计

### 1. 分层记忆存储表结构

```sql
-- 原始记忆层 (Raw Memory Layer) - 记录一切
CREATE TABLE raw_memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    memory_type VARCHAR(50) NOT NULL, -- CONVERSATION, ACTION, PERCEPTION, SYSTEM
    content JSONB NOT NULL, -- 完整的原始数据
    session_id VARCHAR(255),
    user_id VARCHAR(255),
    context_data JSONB, -- 当时的环境上下文
    embedding VECTOR(1536),

    -- 提炼状态
    is_processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP WITH TIME ZONE,
    extracted_to_knowledge BOOLEAN DEFAULT FALSE
);

-- 知识记忆层 (Knowledge Layer) - 提炼后的经验和模式
CREATE TABLE knowledge_memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    knowledge_type VARCHAR(50) NOT NULL, -- PATTERN, SKILL, EXPERIENCE, FACT
    title VARCHAR(255) NOT NULL,
    summary TEXT NOT NULL,
    detailed_content JSONB NOT NULL,

    -- 来源追踪
    source_raw_memory_ids UUID[] NOT NULL, -- 来源的原始记忆ID数组
    extraction_method VARCHAR(50), -- AUTO, MANUAL, TRIGGERED

    -- 质量评估
    confidence_score REAL DEFAULT 0.5,
    usage_count INTEGER DEFAULT 0,
    success_rate REAL DEFAULT 0.0,

    embedding VECTOR(1536),
    tags TEXT[],

    -- 关联信息
    related_knowledge_ids UUID[],
    importance_level INTEGER CHECK (importance_level BETWEEN 1 AND 5)
);

-- 智慧记忆层 (Wisdom Layer) - 深度洞察和哲学
CREATE TABLE wisdom_memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    wisdom_type VARCHAR(50) NOT NULL, -- PRINCIPLE, PHILOSOPHY, INSIGHT, PERSONALITY
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,

    -- 来源追踪
    source_knowledge_ids UUID[] NOT NULL,
    formation_process TEXT, -- 形成过程的描述

    -- 影响力评估
    influence_score REAL DEFAULT 0.0, -- 对决策的影响程度
    application_count INTEGER DEFAULT 0,

    embedding VECTOR(1536),

    -- 个性特征
    personality_traits TEXT[], -- 体现的个性特征
    core_values TEXT[] -- 体现的核心价值观
);

-- 记忆提炼任务表
CREATE TABLE memory_distillation_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,

    task_type VARCHAR(50) NOT NULL, -- AUTO_SCHEDULED, PATTERN_DETECTED, MANUAL_TRIGGERED
    trigger_condition TEXT,

    -- 处理范围
    raw_memory_count INTEGER,
    time_range_start TIMESTAMP WITH TIME ZONE,
    time_range_end TIMESTAMP WITH TIME ZONE,

    -- 结果
    status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, PROCESSING, COMPLETED, FAILED
    extracted_knowledge_count INTEGER DEFAULT 0,
    formed_wisdom_count INTEGER DEFAULT 0,

    processing_log TEXT,
    error_message TEXT
);

-- 传统记忆表（保持兼容性）
CREATE TABLE memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    memory_type VARCHAR(50) NOT NULL, -- EPISODIC, SEMANTIC, PROCEDURAL, EMOTIONAL, CONTEXTUAL
    importance INTEGER NOT NULL CHECK (importance BETWEEN 1 AND 5),
    content JSONB NOT NULL,
    summary TEXT,
    tags TEXT[],
    embedding VECTOR(1536), -- OpenAI embedding维度
    access_count INTEGER DEFAULT 0,
    last_accessed TIMESTAMP WITH TIME ZONE,
    decay_factor REAL DEFAULT 1.0,
    source_session_id VARCHAR(255),
    parent_memory_id UUID REFERENCES memories(id),
    
    -- 索引
    CONSTRAINT valid_memory_type CHECK (memory_type IN ('EPISODIC', 'SEMANTIC', 'PROCEDURAL', 'EMOTIONAL', 'CONTEXTUAL'))
);

-- 记忆关联表
CREATE TABLE memory_associations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id_1 UUID NOT NULL REFERENCES memories(id),
    memory_id_2 UUID NOT NULL REFERENCES memories(id),
    association_type VARCHAR(50) NOT NULL, -- CAUSAL, TEMPORAL, SEMANTIC, EMOTIONAL
    strength REAL NOT NULL DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(memory_id_1, memory_id_2, association_type)
);

-- 上下文会话表
CREATE TABLE context_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    session_type VARCHAR(50) NOT NULL, -- CONVERSATION, TASK, LEARNING
    context_summary TEXT,
    total_memories INTEGER DEFAULT 0,
    importance_score REAL DEFAULT 0.0
);

-- 记忆访问日志
CREATE TABLE memory_access_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id UUID NOT NULL REFERENCES memories(id),
    accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    access_context JSONB,
    relevance_score REAL,
    session_id VARCHAR(255)
);

-- 创建索引
CREATE INDEX idx_memories_type ON memories(memory_type);
CREATE INDEX idx_memories_importance ON memories(importance);
CREATE INDEX idx_memories_created_at ON memories(created_at);
CREATE INDEX idx_memories_tags ON memories USING GIN(tags);
CREATE INDEX idx_memories_embedding ON memories USING ivfflat(embedding vector_cosine_ops);
CREATE INDEX idx_memory_associations_memory1 ON memory_associations(memory_id_1);
CREATE INDEX idx_memory_associations_memory2 ON memory_associations(memory_id_2);
```

### 2. 向量数据库配置（Qdrant）
```python
# qyuan/database/vector_store.py
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
from typing import List, Dict, Any, Optional
import uuid

class QYuanVectorStore:
    """QYuan向量存储"""
    
    def __init__(self, url: str = "http://localhost:6333"):
        self.client = QdrantClient(url=url)
        self.collection_name = "qyuan_memories"
        self.vector_size = 1536  # OpenAI embedding size
    
    async def initialize(self):
        """初始化向量数据库"""
        try:
            # 创建集合
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(
                    size=self.vector_size,
                    distance=Distance.COSINE
                )
            )
        except Exception as e:
            # 集合可能已存在
            pass
    
    async def store_memory_vector(
        self,
        memory_id: str,
        embedding: List[float],
        metadata: Dict[str, Any]
    ):
        """存储记忆向量"""
        point = PointStruct(
            id=memory_id,
            vector=embedding,
            payload=metadata
        )
        
        self.client.upsert(
            collection_name=self.collection_name,
            points=[point]
        )
    
    async def search_similar_memories(
        self,
        query_vector: List[float],
        limit: int = 10,
        score_threshold: float = 0.7,
        filter_conditions: Optional[Dict] = None
    ) -> List[Dict[str, Any]]:
        """搜索相似记忆"""
        
        search_result = self.client.search(
            collection_name=self.collection_name,
            query_vector=query_vector,
            limit=limit,
            score_threshold=score_threshold,
            query_filter=filter_conditions
        )
        
        return [
            {
                "memory_id": hit.id,
                "score": hit.score,
                "metadata": hit.payload
            }
            for hit in search_result
        ]
```

## 核心实现

### 1. 记忆管理器
```python
# qyuan/engines/learning/memory_manager.py
import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from enum import Enum

from ...database.models import Memory, MemoryAssociation, ContextSession
from ...database.vector_store import QYuanVectorStore
from ...interfaces.llm_client import LLMManager

class MemoryType(Enum):
    EPISODIC = "EPISODIC"      # 情节记忆
    SEMANTIC = "SEMANTIC"      # 语义记忆
    PROCEDURAL = "PROCEDURAL"  # 程序记忆
    EMOTIONAL = "EMOTIONAL"    # 情感记忆
    CONTEXTUAL = "CONTEXTUAL"  # 上下文记忆

class MemoryImportance(Enum):
    CRITICAL = 5
    HIGH = 4
    MEDIUM = 3
    LOW = 2
    TRIVIAL = 1

class MemoryManager:
    """QYuan记忆管理器"""
    
    def __init__(self, db_session, vector_store: QYuanVectorStore, llm_manager: LLMManager):
        self.db = db_session
        self.vector_store = vector_store
        self.llm_manager = llm_manager
        self.current_session_id: Optional[str] = None
        
    async def start_session(self, session_type: str = "CONVERSATION") -> str:
        """开始新的上下文会话"""
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        
        session = ContextSession(
            session_id=session_id,
            session_type=session_type,
            started_at=datetime.now()
        )
        
        self.db.add(session)
        await self.db.commit()
        
        self.current_session_id = session_id
        return session_id
    
    async def end_session(self):
        """结束当前会话"""
        if self.current_session_id:
            session = await self.db.query(ContextSession).filter(
                ContextSession.session_id == self.current_session_id
            ).first()
            
            if session:
                session.ended_at = datetime.now()
                # 生成会话摘要
                session.context_summary = await self._generate_session_summary(session)
                await self.db.commit()
        
        self.current_session_id = None
    
    async def store_memory(
        self,
        content: Dict[str, Any],
        memory_type: MemoryType,
        importance: MemoryImportance,
        tags: List[str] = None,
        parent_memory_id: str = None
    ) -> str:
        """存储新记忆"""
        
        # 生成记忆摘要
        summary = await self._generate_memory_summary(content, memory_type)
        
        # 生成嵌入向量
        embedding_text = f"{summary} {' '.join(tags or [])}"
        embedding = await self.llm_manager.get_embedding(embedding_text)
        
        # 创建记忆对象
        memory = Memory(
            memory_type=memory_type.value,
            importance=importance.value,
            content=content,
            summary=summary,
            tags=tags or [],
            embedding=embedding,
            source_session_id=self.current_session_id,
            parent_memory_id=parent_memory_id
        )
        
        self.db.add(memory)
        await self.db.commit()
        
        # 存储到向量数据库
        await self.vector_store.store_memory_vector(
            memory_id=str(memory.id),
            embedding=embedding,
            metadata={
                "memory_type": memory_type.value,
                "importance": importance.value,
                "tags": tags or [],
                "created_at": memory.created_at.isoformat(),
                "session_id": self.current_session_id
            }
        )
        
        # 建立关联
        await self._create_memory_associations(memory)
        
        return str(memory.id)
    
    async def retrieve_relevant_memories(
        self,
        query: str,
        context: Dict[str, Any],
        max_memories: int = 20
    ) -> List[Dict[str, Any]]:
        """检索相关记忆"""
        
        # 生成查询向量
        query_embedding = await self.llm_manager.get_embedding(query)
        
        # 向量搜索
        vector_results = await self.vector_store.search_similar_memories(
            query_vector=query_embedding,
            limit=max_memories * 2,  # 获取更多候选
            score_threshold=0.6
        )
        
        # 从数据库获取完整记忆信息
        memory_ids = [result["memory_id"] for result in vector_results]
        memories = await self.db.query(Memory).filter(
            Memory.id.in_(memory_ids)
        ).all()
        
        # 计算综合相关性分数
        scored_memories = []
        for memory in memories:
            relevance_score = await self._calculate_relevance_score(
                memory, query_embedding, context
            )
            scored_memories.append({
                "memory": memory,
                "relevance_score": relevance_score
            })
        
        # 排序并返回
        scored_memories.sort(key=lambda x: x["relevance_score"], reverse=True)
        
        # 更新访问记录
        for item in scored_memories[:max_memories]:
            await self._record_memory_access(
                item["memory"], 
                context, 
                item["relevance_score"]
            )
        
        return scored_memories[:max_memories]
    
    async def _generate_memory_summary(self, content: Dict[str, Any], memory_type: MemoryType) -> str:
        """生成记忆摘要"""
        
        prompt = f"""请为以下{memory_type.value}记忆生成简洁的摘要（不超过100字）：

记忆内容：
{json.dumps(content, ensure_ascii=False, indent=2)}

摘要应该：
1. 提取关键信息
2. 保持原意
3. 便于后续检索
4. 突出重要细节"""
        
        messages = [
            {"role": "user", "content": prompt}
        ]
        
        response = await self.llm_manager.chat_completion(messages, max_tokens=200)
        return response["choices"][0]["message"]["content"].strip()
    
    async def _calculate_relevance_score(
        self,
        memory: Memory,
        query_embedding: List[float],
        context: Dict[str, Any]
    ) -> float:
        """计算记忆相关性分数"""
        
        # 向量相似度 (40%)
        vector_similarity = self._cosine_similarity(memory.embedding, query_embedding)
        
        # 重要性权重 (25%)
        importance_weight = memory.importance / 5.0
        
        # 时间衰减 (15%)
        time_decay = self._calculate_time_decay(memory.created_at)
        
        # 访问频率 (10%)
        frequency_weight = min(memory.access_count / 100.0, 1.0)
        
        # 上下文匹配 (10%)
        context_match = await self._calculate_context_match(memory, context)
        
        # 综合分数
        relevance_score = (
            vector_similarity * 0.4 +
            importance_weight * 0.25 +
            time_decay * 0.15 +
            frequency_weight * 0.1 +
            context_match * 0.1
        )
        
        return relevance_score
    
    async def _create_memory_associations(self, new_memory: Memory):
        """为新记忆创建关联"""
        
        # 查找最近的相关记忆
        recent_memories = await self.db.query(Memory).filter(
            Memory.created_at > datetime.now() - timedelta(hours=24),
            Memory.id != new_memory.id
        ).order_by(Memory.created_at.desc()).limit(10).all()
        
        for memory in recent_memories:
            # 计算相似度
            similarity = self._cosine_similarity(new_memory.embedding, memory.embedding)
            
            if similarity > 0.8:  # 高相似度
                association = MemoryAssociation(
                    memory_id_1=new_memory.id,
                    memory_id_2=memory.id,
                    association_type="SEMANTIC",
                    strength=similarity
                )
                self.db.add(association)
        
        await self.db.commit()
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        import numpy as np
        
        vec1 = np.array(vec1)
        vec2 = np.array(vec2)
        
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def _calculate_time_decay(self, created_at: datetime) -> float:
        """计算时间衰减因子"""
        age_hours = (datetime.now() - created_at).total_seconds() / 3600
        
        # 使用指数衰减，但保持最小值
        decay = max(0.1, np.exp(-age_hours / (24 * 7)))  # 一周半衰期
        return decay
```

### 2. 上下文组织器
```python
# qyuan/engines/learning/context_organizer.py
from typing import List, Dict, Any, Optional
from .memory_manager import MemoryManager

class ContextOrganizer:
    """上下文组织器"""
    
    def __init__(self, memory_manager: MemoryManager, llm_manager):
        self.memory_manager = memory_manager
        self.llm_manager = llm_manager
        self.max_context_tokens = 6000  # 为GPT-4.1预留的上下文空间
    
    async def organize_context_for_query(
        self,
        query: str,
        current_situation: Dict[str, Any]
    ) -> str:
        """为查询组织上下文"""
        
        # 检索相关记忆
        relevant_memories = await self.memory_manager.retrieve_relevant_memories(
            query=query,
            context=current_situation,
            max_memories=30
        )
        
        # 构建分层上下文
        context_sections = []
        used_tokens = 0
        
        # 核心记忆（最重要的5个）
        core_memories = relevant_memories[:5]
        core_section = self._format_memory_section("核心记忆", core_memories)
        core_tokens = self._estimate_tokens(core_section)
        
        if used_tokens + core_tokens <= self.max_context_tokens:
            context_sections.append(core_section)
            used_tokens += core_tokens
        
        # 支撑记忆
        if used_tokens < self.max_context_tokens * 0.7:
            supporting_memories = relevant_memories[5:15]
            supporting_section = self._format_memory_section("相关经验", supporting_memories)
            supporting_tokens = self._estimate_tokens(supporting_section)
            
            if used_tokens + supporting_tokens <= self.max_context_tokens * 0.85:
                context_sections.append(supporting_section)
                used_tokens += supporting_tokens
        
        # 背景记忆
        remaining_tokens = self.max_context_tokens - used_tokens
        if remaining_tokens > 300:
            background_memories = relevant_memories[15:25]
            background_section = self._format_memory_section("背景信息", background_memories)
            background_tokens = min(remaining_tokens - 100, self._estimate_tokens(background_section))
            
            if background_tokens > 100:
                # 截断背景信息以适应token限制
                truncated_section = self._truncate_section(background_section, background_tokens)
                context_sections.append(truncated_section)
        
        return "\n\n".join(context_sections)
    
    def _format_memory_section(self, title: str, memories: List[Dict[str, Any]]) -> str:
        """格式化记忆段落"""
        if not memories:
            return ""
        
        section = f"## {title}\n\n"
        
        for item in memories:
            memory = item["memory"]
            relevance = item["relevance_score"]
            
            # 根据记忆类型格式化
            if memory.memory_type == "EPISODIC":
                formatted = f"[{memory.created_at.strftime('%Y-%m-%d %H:%M')}] {memory.summary}"
            elif memory.memory_type == "PROCEDURAL":
                formatted = f"[操作经验] {memory.summary}"
            elif memory.memory_type == "SEMANTIC":
                formatted = f"[知识] {memory.summary}"
            else:
                formatted = memory.summary
            
            section += f"- {formatted} (相关性: {relevance:.2f})\n"
        
        return section
    
    def _estimate_tokens(self, text: str) -> int:
        """估算token数量"""
        # 简化的token估算
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        other_chars = len(text) - chinese_chars
        
        estimated_tokens = chinese_chars / 1.5 + other_chars / 4
        return int(estimated_tokens * 1.2)
    
    def _truncate_section(self, section: str, max_tokens: int) -> str:
        """截断段落以适应token限制"""
        lines = section.split('\n')
        truncated_lines = []
        current_tokens = 0
        
        for line in lines:
            line_tokens = self._estimate_tokens(line)
            if current_tokens + line_tokens <= max_tokens:
                truncated_lines.append(line)
                current_tokens += line_tokens
            else:
                truncated_lines.append("... (更多内容因长度限制省略)")
                break
        
        return '\n'.join(truncated_lines)
```

## 集成到QYuan核心

### 在QYuan核心中集成记忆管理
```python
# 在 qyuan/core/qyuan_core.py 中添加
from ..engines.learning.memory_manager import MemoryManager, MemoryType, MemoryImportance
from ..engines.learning.context_organizer import ContextOrganizer

class QYuanCore:
    def __init__(self, config: QYuanConfig):
        # ... 现有初始化代码 ...
        
        # 初始化记忆管理
        self.memory_manager = MemoryManager(
            db_session=self.db_session,
            vector_store=self.vector_store,
            llm_manager=self.llm_manager
        )
        
        self.context_organizer = ContextOrganizer(
            memory_manager=self.memory_manager,
            llm_manager=self.llm_manager
        )
    
    async def handle_user_message(self, message: str, user_id: str = None) -> str:
        """处理用户消息（增强版）"""
        
        # 开始新的记忆会话
        session_id = await self.memory_manager.start_session("CONVERSATION")
        
        try:
            # 组织相关上下文
            context = await self.context_organizer.organize_context_for_query(
                query=message,
                current_situation={
                    "user_id": user_id,
                    "timestamp": datetime.now().isoformat(),
                    "session_id": session_id
                }
            )
            
            # 构建完整的LLM输入
            full_prompt = f"""
{context}

## 当前对话
用户: {message}

请基于以上记忆和上下文，以QYuan的身份回应用户。
"""
            
            # 调用LLM
            response = await self.llm_manager.chat_completion([
                {"role": "user", "content": full_prompt}
            ])
            
            assistant_response = response["choices"][0]["message"]["content"]
            
            # 存储对话记忆
            await self.memory_manager.store_memory(
                content={
                    "user_message": message,
                    "assistant_response": assistant_response,
                    "user_id": user_id,
                    "context_used": len(context) > 0
                },
                memory_type=MemoryType.EPISODIC,
                importance=MemoryImportance.MEDIUM,
                tags=["conversation", "user_interaction"]
            )
            
            return assistant_response
            
        finally:
            # 结束记忆会话
            await self.memory_manager.end_session()
```

这个记忆管理系统确保了QYuan的每一次交互都被永久保存，同时能够智能地组织上下文，让QYuan真正具备了"硅基生命"的记忆特征。你觉得这个实现方案如何？

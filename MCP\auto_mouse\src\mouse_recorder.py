"""
鼠标轨迹记录器，记录鼠标移动轨迹并保存为文件。
"""

import time
import json
import os
import threading
import pyautogui
import random
from datetime import datetime

class MouseRecorder:
    """鼠标轨迹记录器类，用于记录鼠标移动轨迹。"""

    def __init__(self, save_dir="recordings"):
        """
        初始化鼠标轨迹记录器。

        Args:
            save_dir: 轨迹保存目录
        """
        self.save_dir = save_dir
        self.recording = False
        self.trajectory = []
        self.start_time = None
        self.record_thread = None

        # 确保保存目录存在
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

    def start_recording(self, duration=30):
        """
        开始记录鼠标轨迹。

        Args:
            duration: 记录持续时间（秒）

        Returns:
            bool: 是否成功开始记录
        """
        if self.recording:
            print("已经在记录中...")
            return False

        self.recording = True
        self.trajectory = []
        self.start_time = time.time()

        # 创建并启动记录线程
        self.record_thread = threading.Thread(target=self._record_loop, args=(duration,))
        self.record_thread.daemon = True
        self.record_thread.start()

        print(f"开始记录鼠标轨迹，持续 {duration} 秒...")
        return True

    def stop_recording(self):
        """
        停止记录鼠标轨迹。

        Returns:
            bool: 是否成功停止记录
        """
        if not self.recording:
            print("没有正在进行的记录...")
            return False

        self.recording = False
        if self.record_thread:
            self.record_thread.join(timeout=1)

        print("停止记录鼠标轨迹")
        return True

    def _record_loop(self, duration):
        """
        记录循环，持续记录鼠标位置和停留时间。

        Args:
            duration: 记录持续时间（秒）
        """
        end_time = self.start_time + duration
        last_x, last_y = None, None
        last_move_time = None

        try:
            while self.recording and time.time() < end_time:
                # 获取当前鼠标位置
                x, y = pyautogui.position()
                current_time = time.time()
                elapsed_time = current_time - self.start_time

                # 检查鼠标是否移动
                if last_x is None or last_y is None:
                    # 第一个点
                    self.trajectory.append({
                        "x": x,
                        "y": y,
                        "time": elapsed_time,
                        "dwell_time": 0  # 第一个点没有停留时间
                    })
                    last_move_time = current_time
                elif x != last_x or y != last_y:
                    # 鼠标移动了
                    dwell_time = current_time - last_move_time if last_move_time else 0
                    self.trajectory.append({
                        "x": x,
                        "y": y,
                        "time": elapsed_time,
                        "dwell_time": dwell_time  # 记录在上一个位置的停留时间
                    })
                    last_move_time = current_time

                # 更新上一个位置
                last_x, last_y = x, y

                # 短暂休眠，避免记录过于密集
                # 使用更高的采样率，每5毫秒记录一次
                time.sleep(0.005)

            # 记录完成后自动停止
            self.recording = False

            # 保存轨迹
            self.save_trajectory()

            print(f"记录完成，共记录 {len(self.trajectory)} 个点")

            # 计算并显示一些统计信息
            if self.trajectory:
                total_distance = 0
                for i in range(1, len(self.trajectory)):
                    x1, y1 = self.trajectory[i-1]["x"], self.trajectory[i-1]["y"]
                    x2, y2 = self.trajectory[i]["x"], self.trajectory[i]["y"]
                    distance = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
                    total_distance += distance

                avg_speed = total_distance / self.trajectory[-1]["time"] if self.trajectory[-1]["time"] > 0 else 0
                print(f"总移动距离: {total_distance:.2f} 像素")
                print(f"平均速度: {avg_speed:.2f} 像素/秒")

        except Exception as e:
            print(f"记录过程中发生错误: {e}")
            self.recording = False

    def save_trajectory(self, filename=None):
        """
        保存记录的轨迹到文件。

        Args:
            filename: 文件名（可选，如果不提供则自动生成）

        Returns:
            str: 保存的文件路径
        """
        if not self.trajectory:
            print("没有轨迹可保存...")
            return None

        # 如果未提供文件名，则自动生成
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mouse_trajectory_{timestamp}.json"

        # 确保文件名有.json后缀
        if not filename.endswith(".json"):
            filename += ".json"

        # 完整文件路径
        filepath = os.path.join(self.save_dir, filename)

        # 保存轨迹数据
        with open(filepath, "w") as f:
            json.dump({
                "trajectory": self.trajectory,
                "total_points": len(self.trajectory),
                "duration": self.trajectory[-1]["time"] if self.trajectory else 0,
                "recorded_at": datetime.now().isoformat()
            }, f, indent=2)

        print(f"轨迹已保存到: {filepath}")
        return filepath

    def load_trajectory(self, filepath):
        """
        从文件加载轨迹。

        Args:
            filepath: 轨迹文件路径

        Returns:
            list: 轨迹数据
        """
        try:
            with open(filepath, "r") as f:
                data = json.load(f)
                return data["trajectory"]
        except Exception as e:
            print(f"加载轨迹文件时发生错误: {e}")
            return []

    def get_random_trajectory(self):
        """
        从recordings目录随机选择一个轨迹文件。

        Returns:
            list: 轨迹数据
        """
        if not os.path.exists(self.save_dir):
            print(f"目录不存在: {self.save_dir}")
            return []

        # 获取所有JSON文件
        json_files = [f for f in os.listdir(self.save_dir) if f.endswith(".json")]

        if not json_files:
            print(f"在 {self.save_dir} 中没有找到轨迹文件")
            return []

        # 随机选择一个文件
        random_file = random.choice(json_files)
        filepath = os.path.join(self.save_dir, random_file)

        print(f"随机选择轨迹文件: {random_file}")
        return self.load_trajectory(filepath)


def main():
    """主函数，演示鼠标轨迹记录器的使用。"""
    recorder = MouseRecorder()

    print("鼠标轨迹记录器")
    print("-" * 30)
    print("将记录接下来30秒的鼠标移动轨迹")
    print("请在这段时间内自然地移动鼠标")
    print("按Enter键开始记录...")
    input()

    # 开始记录
    recorder.start_recording(duration=30)

    # 等待记录完成
    while recorder.recording:
        time.sleep(1)

    print("记录完成！轨迹已保存")


if __name__ == "__main__":
    main()

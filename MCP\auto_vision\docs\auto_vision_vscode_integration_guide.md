# Auto Vision VSCode集成指南

本文档详细说明如何在VSCode中配置和使用Auto Vision MCP服务器。

## 1. 前提条件

在开始之前，请确保：

1. 已完成Auto Vision项目的安装和配置
2. 已创建并激活Python虚拟环境
3. 已安装所有必要的依赖，特别是`fastmcp`、`pyautogui`、`PIL`和OCR相关库
4. VSCode已安装并配置好

## 2. 在VSCode中添加MCP服务器

### 2.1 打开MCP服务器设置

1. 在VSCode中，点击左侧活动栏中的扩展图标（或按`Ctrl+Shift+X`）
2. 搜索并安装"Augment"扩展（如果尚未安装）
3. 安装完成后，点击VSCode左侧活动栏中的Augment图标
4. 在Augment面板中，点击"Add MCP Server"按钮

### 2.2 配置MCP服务器

在弹出的"New MCP Server"对话框中，填写以下信息：

1. **Name**：输入一个描述性名称，例如：
   ```
   Auto Vision
   ```

2. **Command**：输入启动服务器的命令，使用虚拟环境中的Python解释器：
   ```
   D:\Project\auto_vision\venv\Scripts\python.exe D:\Project\auto_vision\start_server.py
   ```
   
   > 注意：请将路径替换为您实际的项目路径。

3. **Environment Variables**：通常不需要添加特殊的环境变量，除非您有特定需求。

4. 点击"Add"按钮完成添加。

### 2.3 启动MCP服务器

1. 在Augment面板中，找到刚刚添加的"Auto Vision"服务器
2. 点击服务器名称旁边的启动按钮（播放图标）
3. 如果一切正常，服务器状态应该变为"Running"
4. 如果出现错误，请查看错误信息并参考下面的"常见问题"部分

## 3. 使用Auto Vision

一旦MCP服务器成功启动，您就可以在VSCode中使用Auto Vision的功能了。

### 3.1 可用的工具

Auto Vision提供以下主要工具：

1. **屏幕截图和分析工具**：
   - `analyze_screen`：分析全屏
   - `analyze_screen_region`：分析屏幕区域
   - `analyze_latest_screenshot`：分析最新的屏幕截图
   - `capture_fullscreen`：全屏截图
   - `capture_fullscreen_with_coordinates`：带坐标的全屏截图
   - `capture_region`：局部截图
   - `capture_region_with_coordinates`：带坐标的局部截图

2. **OCR文本识别工具**：
   - `recognize_text`：识别图像中的文本

3. **UI元素识别工具**：
   - `get_ui_elements`：获取UI元素及其属性
   - `find_element_by_text`：通过文本查找元素

4. **组合工具**：
   - `capture_and_analyze_screen`：捕获并分析屏幕

### 3.2 工具参数说明

#### 屏幕分析工具

```python
# 分析全屏
analyze_screen(question)
# 参数:
# - question: 问题，例如"这个屏幕上有什么内容？"

# 分析屏幕区域
analyze_screen_region(question, x, y, width, height)
# 参数:
# - question: 问题
# - x, y, width, height: 区域坐标和尺寸

# 分析最新的屏幕截图
analyze_latest_screenshot(question)
# 参数:
# - question: 问题
```

#### 截图工具

```python
# 全屏截图
capture_fullscreen()
# 无参数

# 带坐标的全屏截图
capture_fullscreen_with_coordinates()
# 无参数

# 局部截图
capture_region(x, y, width, height)
# 参数:
# - x, y, width, height: 区域坐标和尺寸

# 带坐标的局部截图
capture_region_with_coordinates(x, y, width, height)
# 参数:
# - x, y, width, height: 区域坐标和尺寸
```

#### OCR文本识别工具

```python
# 识别文本
recognize_text(image_path, region=None, return_format="structured")
# 参数:
# - image_path: 图像路径
# - region: 识别区域 [x, y, width, height]，为None则识别整个图像
# - return_format: 返回格式，支持 "text", "structured"
```

#### UI元素识别工具

```python
# 获取UI元素
get_ui_elements(window_title=None, element_type=None, depth=3, visible_only=True)
# 参数:
# - window_title: 窗口标题，为None则获取前台窗口
# - element_type: 元素类型，必须指定，例如："Button", "Edit", "ComboBox"等
# - depth: 树的深度
# - visible_only: 是否只返回可见元素

# 通过文本查找元素
find_element_by_text(text, element_type=None, window=None, match_type="contains", visible_only=True, timeout=5)
# 参数:
# - text: 要查找的文本
# - element_type: 元素类型，必须指定
# - window: 窗口标题，为None则在前台窗口中查找
# - match_type: 匹配类型，可选值：exact, contains, starts_with, ends_with
# - visible_only: 是否只查找可见元素
# - timeout: 超时时间（秒）
```

### 3.3 使用示例

以下是一些使用示例：

1. **分析全屏**
   ```python
   result = await analyze_screen(question="这个屏幕上有什么内容？请描述主要元素及其位置。")
   print(result["vision_analysis"])
   ```

2. **分析区域**
   ```python
   result = await analyze_screen_region(
       question="这个区域中有什么内容？请描述主要元素及其位置。",
       x=100, y=100, width=400, height=300
   )
   print(result["vision_analysis"])
   ```

3. **带坐标的全屏截图**
   ```python
   result = await capture_fullscreen_with_coordinates()
   print(f"截图路径: {result['path']}")
   ```

4. **识别文本**
   ```python
   result = await recognize_text("screenshots/screenshot_latest.png", return_format="structured")
   for item in result:
       print(f"文本: {item['text']}")
       print(f"位置: {item['box']}")
       print(f"置信度: {item['confidence']}")
   ```

5. **获取UI元素**
   ```python
   result = await get_ui_elements(element_type="Button", depth=3)
   for element in result["elements"]:
       print(f"按钮: {element['name']}")
       print(f"位置: {element['rectangle']}")
   ```

## 4. 常见问题

### 4.1 服务器启动失败

**问题**：MCP服务器启动失败，显示"Connection closed"错误。

**解决方案**：
1. 确保已安装`fastmcp`库：
   ```
   venv\Scripts\pip.exe install fastmcp
   ```
2. 检查路径是否正确，确保使用绝对路径
3. 尝试在命令行中手动运行服务器，查看详细错误信息

### 4.2 OCR识别不准确

**问题**：OCR文本识别结果不准确或为空。

**解决方案**：
1. 确保已安装相应的OCR引擎：
   ```
   # Windows OCR
   venv\Scripts\pip.exe install pywin32 comtypes

   # EasyOCR
   venv\Scripts\pip.exe install easyocr

   # 百度OCR
   venv\Scripts\pip.exe install baidu-aip
   ```
2. 对于Windows OCR，确保系统已安装相应的语言包
3. 尝试使用不同的OCR引擎，例如：
   ```python
   # 在start_server.py中指定OCR引擎
   python start_server.py --ocr-engine easyocr
   ```

### 4.3 UI元素识别问题

**问题**：无法识别UI元素或返回的元素不完整。

**解决方案**：
1. 确保已安装相应的UI自动化库：
   ```
   # PyWinAuto
   venv\Scripts\pip.exe install pywinauto

   # Win32GUI
   venv\Scripts\pip.exe install pywin32
   ```
2. 尝试使用不同的UI自动化后端，例如：
   ```python
   # 在start_server.py中指定UI自动化后端
   python start_server.py --ui-backend pywinauto
   ```
3. 确保指定了元素类型，例如：
   ```python
   result = await get_ui_elements(element_type="Button")
   ```

### 4.4 视觉LLM分析问题

**问题**：视觉LLM分析结果为空或不准确。

**解决方案**：
1. 确保已设置正确的API密钥：
   ```
   # 在环境变量中设置
   set GOOGLE_API_KEY=your_api_key
   ```
2. 尝试使用更具体的问题，例如：
   ```python
   result = await analyze_screen(question="这个屏幕上有哪些按钮和文本框？它们的位置在哪里？")
   ```
3. 检查网络连接，确保可以访问Gemini API

## 5. 高级配置

### 5.1 使用TCP传输

如果您需要使用TCP传输而不是默认的stdio传输，可以在命令中添加相应参数：

```
D:\Project\auto_vision\venv\Scripts\python.exe D:\Project\auto_vision\start_server.py --transport tcp --host localhost --port 8000
```

### 5.2 启用调试模式

如果需要更详细的日志信息，可以启用调试模式：

```
D:\Project\auto_vision\venv\Scripts\python.exe D:\Project\auto_vision\start_server.py --debug
```

### 5.3 自定义OCR引擎和UI自动化后端

可以在启动服务器时指定OCR引擎和UI自动化后端：

```
D:\Project\auto_vision\venv\Scripts\python.exe D:\Project\auto_vision\start_server.py --ocr-engine windows --ui-backend win32gui
```

## 6. 总结

通过本指南，您应该能够成功地在VSCode中配置和使用Auto Vision MCP服务器。如果遇到任何问题，请参考"常见问题"部分或查阅项目文档。

祝您使用愉快！

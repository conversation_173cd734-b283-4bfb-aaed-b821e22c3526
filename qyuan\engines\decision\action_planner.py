# -*- coding: utf-8 -*-
"""
行动规划服务实现
专门负责为任务创建详细的行动计划
严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
import uuid
from typing import Dict, List, Optional, Any

from .base import (
    ActionPlannerBase,
    Task,
    Action,
    ActionPlan,
    ActionType,
    IntentType,
    DecisionContext
)

class IntelligentActionPlanner(ActionPlannerBase):
    """智能行动规划器"""
    
    def __init__(self, llm_client=None):
        super().__init__("IntelligentActionPlanner")
        self.logger = logging.getLogger(f"QYuan.Decision.{self.name}")
        self.llm_client = llm_client
        
        # 行动模板
        self.action_templates = self._build_action_templates()
        
        # 优化策略
        self.optimization_strategies = self._build_optimization_strategies()
        
        if not self.llm_client:
            self.logger.warning("LLM客户端未配置，将使用基于模板的行动规划")
    
    async def create_plan(self, task: Task, context: DecisionContext) -> ActionPlan:
        """创建行动计划"""
        start_time = time.time()
        
        try:
            # 创建基础计划
            plan = ActionPlan(
                id=str(uuid.uuid4()),
                task_id=task.id
            )
            
            # 根据任务意图类型生成行动
            if self.llm_client:
                actions = await self._generate_actions_with_llm(task, context)
            else:
                actions = await self._generate_actions_with_templates(task, context)
            
            # 添加行动到计划
            for action in actions:
                plan.add_action(action)
            
            # 设置成功标准
            plan.success_criteria = self._define_success_criteria(task, context)
            
            processing_time = time.time() - start_time
            self.logger.debug(f"行动计划创建完成，包含 {len(actions)} 个行动，耗时: {processing_time:.2f}秒")
            
            return plan
            
        except Exception as e:
            self.logger.error(f"行动计划创建失败: {e}")
            # 返回空计划
            return ActionPlan(
                id=str(uuid.uuid4()),
                task_id=task.id
            )
    
    async def optimize_plan(self, plan: ActionPlan, context: DecisionContext) -> ActionPlan:
        """优化行动计划"""
        try:
            # 应用各种优化策略
            optimized_plan = plan
            
            for strategy_name, strategy_func in self.optimization_strategies.items():
                optimized_plan = strategy_func(optimized_plan, context)
                self.logger.debug(f"应用优化策略: {strategy_name}")
            
            return optimized_plan
            
        except Exception as e:
            self.logger.error(f"行动计划优化失败: {e}")
            return plan
    
    async def _generate_actions_with_llm(self, task: Task, context: DecisionContext) -> List[Action]:
        """使用LLM生成行动"""
        try:
            # 构建提示词
            prompt = self._build_llm_planning_prompt(task, context)
            
            # 调用LLM
            response = await self._call_llm(prompt)
            
            # 解析LLM响应
            actions = self._parse_llm_planning_response(response)
            
            return actions
            
        except Exception as e:
            self.logger.warning(f"LLM行动规划失败，回退到模板规划: {e}")
            return await self._generate_actions_with_templates(task, context)
    
    async def _generate_actions_with_templates(self, task: Task, context: DecisionContext) -> List[Action]:
        """使用模板生成行动"""
        actions = []
        intent_type = task.intent.intent_type
        
        if intent_type in self.action_templates:
            template_func = self.action_templates[intent_type]
            actions = template_func(task, context)
        else:
            # 创建通用行动
            actions = self._create_generic_actions(task, context)
        
        return actions
    
    def _create_click_actions(self, task: Task, context: DecisionContext) -> List[Action]:
        """创建点击行动"""
        actions = []
        intent = task.intent
        
        # 1. 屏幕截图行动
        screenshot_action = Action(
            id=str(uuid.uuid4()),
            action_type=ActionType.UI_INTERACTION,
            description="捕获当前屏幕状态",
            parameters={
                "action": "screenshot",
                "save_path": f"/tmp/screenshot_{task.id}.png"
            },
            timeout=5.0,
            validation_criteria={"screenshot_exists": True}
        )
        actions.append(screenshot_action)
        
        # 2. 元素定位行动
        locate_action = Action(
            id=str(uuid.uuid4()),
            action_type=ActionType.UI_INTERACTION,
            description=f"定位目标元素: {intent.parameters.get('target', '未知')}",
            parameters={
                "action": "locate_element",
                "target": intent.parameters.get('target'),
                "method": "text" if intent.parameters.get('target') else "position",
                "position": intent.parameters.get('position')
            },
            timeout=10.0,
            validation_criteria={"element_found": True, "element_visible": True}
        )
        actions.append(locate_action)
        
        # 3. 点击行动
        click_action = Action(
            id=str(uuid.uuid4()),
            action_type=ActionType.UI_INTERACTION,
            description="执行点击操作",
            parameters={
                "action": "click",
                "element_id": "located_element",
                "click_type": "left",
                "wait_after": 1.0
            },
            timeout=5.0,
            validation_criteria={"click_executed": True}
        )
        actions.append(click_action)
        
        # 4. 结果验证行动
        verify_action = Action(
            id=str(uuid.uuid4()),
            action_type=ActionType.VERIFICATION,
            description="验证点击操作结果",
            parameters={
                "action": "verify_click_result",
                "expected_changes": ["page_change", "dialog_open", "state_change"]
            },
            timeout=5.0,
            validation_criteria={"verification_passed": True}
        )
        actions.append(verify_action)
        
        return actions
    
    def _create_type_actions(self, task: Task, context: DecisionContext) -> List[Action]:
        """创建输入行动"""
        actions = []
        intent = task.intent
        
        # 1. 定位输入框
        locate_action = Action(
            id=str(uuid.uuid4()),
            action_type=ActionType.UI_INTERACTION,
            description="定位输入框",
            parameters={
                "action": "locate_input",
                "target": intent.parameters.get('target', 'active_input'),
                "input_type": "text"
            },
            timeout=10.0,
            validation_criteria={"input_found": True, "input_enabled": True}
        )
        actions.append(locate_action)
        
        # 2. 激活输入框
        activate_action = Action(
            id=str(uuid.uuid4()),
            action_type=ActionType.UI_INTERACTION,
            description="激活输入框",
            parameters={
                "action": "click",
                "element_id": "located_input",
                "focus": True
            },
            timeout=5.0,
            validation_criteria={"input_focused": True}
        )
        actions.append(activate_action)
        
        # 3. 清空现有内容（如果需要）
        if intent.parameters.get('clear_existing', True):
            clear_action = Action(
                id=str(uuid.uuid4()),
                action_type=ActionType.UI_INTERACTION,
                description="清空输入框内容",
                parameters={
                    "action": "clear_input",
                    "method": "select_all_delete"
                },
                timeout=3.0,
                validation_criteria={"input_cleared": True}
            )
            actions.append(clear_action)
        
        # 4. 输入文本
        type_action = Action(
            id=str(uuid.uuid4()),
            action_type=ActionType.UI_INTERACTION,
            description=f"输入文本: {intent.parameters.get('text', '')}",
            parameters={
                "action": "type_text",
                "text": intent.parameters.get('text', ''),
                "typing_speed": "normal"
            },
            timeout=15.0,
            validation_criteria={"text_entered": True}
        )
        actions.append(type_action)
        
        # 5. 验证输入结果
        verify_action = Action(
            id=str(uuid.uuid4()),
            action_type=ActionType.VERIFICATION,
            description="验证文本输入结果",
            parameters={
                "action": "verify_input",
                "expected_text": intent.parameters.get('text', '')
            },
            timeout=5.0,
            validation_criteria={"input_verified": True}
        )
        actions.append(verify_action)
        
        return actions
    
    def _create_scroll_actions(self, task: Task, context: DecisionContext) -> List[Action]:
        """创建滚动行动"""
        actions = []
        intent = task.intent
        
        # 1. 确定滚动区域
        locate_action = Action(
            id=str(uuid.uuid4()),
            action_type=ActionType.UI_INTERACTION,
            description="确定滚动区域",
            parameters={
                "action": "locate_scroll_area",
                "area_type": "main_content"
            },
            timeout=5.0,
            validation_criteria={"scroll_area_found": True}
        )
        actions.append(locate_action)
        
        # 2. 执行滚动
        scroll_action = Action(
            id=str(uuid.uuid4()),
            action_type=ActionType.UI_INTERACTION,
            description=f"滚动{intent.parameters.get('direction', '下')}",
            parameters={
                "action": "scroll",
                "direction": intent.parameters.get('direction', 'down'),
                "amount": intent.parameters.get('amount', 3),
                "smooth": True
            },
            timeout=5.0,
            validation_criteria={"scroll_executed": True}
        )
        actions.append(scroll_action)
        
        # 3. 验证滚动结果
        verify_action = Action(
            id=str(uuid.uuid4()),
            action_type=ActionType.VERIFICATION,
            description="验证滚动结果",
            parameters={
                "action": "verify_scroll",
                "direction": intent.parameters.get('direction', 'down')
            },
            timeout=3.0,
            validation_criteria={"scroll_verified": True}
        )
        actions.append(verify_action)
        
        return actions
    
    def _create_navigate_actions(self, task: Task, context: DecisionContext) -> List[Action]:
        """创建导航行动"""
        actions = []
        intent = task.intent
        
        url = intent.parameters.get('url')
        direction = intent.parameters.get('direction')
        
        if url:
            # URL导航
            navigate_action = Action(
                id=str(uuid.uuid4()),
                action_type=ActionType.NAVIGATION,
                description=f"导航到URL: {url}",
                parameters={
                    "action": "navigate_url",
                    "url": url,
                    "wait_for_load": True
                },
                timeout=30.0,
                validation_criteria={"page_loaded": True, "url_correct": True}
            )
            actions.append(navigate_action)
        elif direction:
            # 方向导航
            navigate_action = Action(
                id=str(uuid.uuid4()),
                action_type=ActionType.NAVIGATION,
                description=f"执行{direction}操作",
                parameters={
                    "action": f"browser_{direction}",
                    "wait_for_load": True
                },
                timeout=15.0,
                validation_criteria={"navigation_completed": True}
            )
            actions.append(navigate_action)
        
        return actions
    
    def _create_generic_actions(self, task: Task, context: DecisionContext) -> List[Action]:
        """创建通用行动"""
        return [
            Action(
                id=str(uuid.uuid4()),
                action_type=ActionType.UI_INTERACTION,
                description=f"执行{task.intent.intent_type.value}操作",
                parameters={
                    "action": task.intent.intent_type.value,
                    "parameters": task.intent.parameters
                },
                timeout=30.0
            )
        ]
    
    def _build_action_templates(self) -> Dict[IntentType, callable]:
        """构建行动模板"""
        return {
            IntentType.CLICK: self._create_click_actions,
            IntentType.TYPE: self._create_type_actions,
            IntentType.SCROLL: self._create_scroll_actions,
            IntentType.NAVIGATE: self._create_navigate_actions,
        }
    
    def _build_optimization_strategies(self) -> Dict[str, callable]:
        """构建优化策略"""
        return {
            "remove_redundant": self._remove_redundant_actions,
            "merge_similar": self._merge_similar_actions,
            "optimize_timeouts": self._optimize_timeouts,
            "add_error_handling": self._add_error_handling
        }
    
    def _remove_redundant_actions(self, plan: ActionPlan, context: DecisionContext) -> ActionPlan:
        """移除冗余行动"""
        # 简单实现：移除重复的截图行动
        seen_actions = set()
        filtered_actions = []
        
        for action in plan.actions:
            action_key = (action.action_type, action.parameters.get('action'))
            if action_key not in seen_actions or action.action_type != ActionType.UI_INTERACTION:
                filtered_actions.append(action)
                seen_actions.add(action_key)
        
        plan.actions = filtered_actions
        return plan
    
    def _merge_similar_actions(self, plan: ActionPlan, context: DecisionContext) -> ActionPlan:
        """合并相似行动"""
        # 简单实现：保持原样
        return plan
    
    def _optimize_timeouts(self, plan: ActionPlan, context: DecisionContext) -> ActionPlan:
        """优化超时时间"""
        for action in plan.actions:
            # 根据行动类型调整超时时间
            if action.action_type == ActionType.NAVIGATION:
                action.timeout = max(action.timeout, 30.0)
            elif action.action_type == ActionType.VERIFICATION:
                action.timeout = min(action.timeout, 10.0)
        
        return plan
    
    def _add_error_handling(self, plan: ActionPlan, context: DecisionContext) -> ActionPlan:
        """添加错误处理"""
        # 为关键行动增加重试次数
        for action in plan.actions:
            if action.action_type in [ActionType.UI_INTERACTION, ActionType.NAVIGATION]:
                action.retry_count = max(action.retry_count, 2)
        
        return plan
    
    def _define_success_criteria(self, task: Task, context: DecisionContext) -> Dict[str, Any]:
        """定义成功标准"""
        return {
            "task_completed": True,
            "no_errors": True,
            "expected_state_reached": True,
            "user_intent_fulfilled": True
        }
    
    def _build_llm_planning_prompt(self, task: Task, context: DecisionContext) -> str:
        """构建LLM规划提示词"""
        prompt = f"""请为以下任务创建详细的行动计划：

任务: {task.title}
描述: {task.description}
意图类型: {task.intent.intent_type.value}
参数: {task.intent.parameters}

请以JSON格式返回行动计划：
{{
    "actions": [
        {{
            "action_type": "ui_interaction/system_operation/navigation/verification",
            "description": "行动描述",
            "parameters": {{
                "action": "具体行动",
                "参数名": "参数值"
            }},
            "timeout": 超时时间(秒),
            "validation_criteria": {{
                "验证条件": true/false
            }}
        }}
    ]
}}

可用的行动类型：
- ui_interaction: UI交互操作
- system_operation: 系统操作
- navigation: 导航操作
- verification: 验证操作

请确保返回有效的JSON格式。"""
        
        return prompt
    
    async def _call_llm(self, prompt: str) -> str:
        """调用LLM"""
        try:
            response = await self.llm_client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                model="gpt-3.5-turbo",
                max_tokens=1500,
                temperature=0.1
            )
            
            if response and response.get('choices'):
                return response['choices'][0]['message']['content']
            else:
                raise ValueError("LLM返回无效响应")
                
        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            raise
    
    def _parse_llm_planning_response(self, response: str) -> List[Action]:
        """解析LLM规划响应"""
        try:
            import json
            
            data = json.loads(response)
            actions = []
            
            for action_data in data.get('actions', []):
                action = Action(
                    id=str(uuid.uuid4()),
                    action_type=self._parse_action_type(action_data.get('action_type', 'ui_interaction')),
                    description=action_data.get('description', ''),
                    parameters=action_data.get('parameters', {}),
                    timeout=float(action_data.get('timeout', 30.0)),
                    validation_criteria=action_data.get('validation_criteria', {})
                )
                actions.append(action)
            
            return actions
            
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            self.logger.warning(f"LLM规划响应解析失败: {e}")
            return []
    
    def _parse_action_type(self, action_type_str: str) -> ActionType:
        """解析行动类型"""
        type_map = {
            'ui_interaction': ActionType.UI_INTERACTION,
            'system_operation': ActionType.SYSTEM_OPERATION,
            'navigation': ActionType.NAVIGATION,
            'verification': ActionType.VERIFICATION,
            'data_processing': ActionType.DATA_PROCESSING
        }
        return type_map.get(action_type_str, ActionType.UI_INTERACTION)

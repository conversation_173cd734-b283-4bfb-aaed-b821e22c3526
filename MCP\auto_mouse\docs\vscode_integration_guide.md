# VSCode集成指南

本文档详细说明如何在VSCode中配置和使用Auto Mouse Controller MCP服务器。

## 1. 前提条件

在开始之前，请确保：

1. 已完成Auto Mouse Controller项目的安装和配置
2. 已创建并激活Python虚拟环境
3. 已安装所有必要的依赖，特别是`fastmcp`和`pyautogui`
4. VSCode已安装并配置好

## 2. 在VSCode中添加MCP服务器

### 2.1 打开MCP服务器设置

1. 在VSCode中，点击左侧活动栏中的扩展图标（或按`Ctrl+Shift+X`）
2. 搜索并安装"Augment"扩展（如果尚未安装）
3. 安装完成后，点击VSCode左侧活动栏中的Augment图标
4. 在Augment面板中，点击"Add MCP Server"按钮

### 2.2 配置MCP服务器

在弹出的"New MCP Server"对话框中，填写以下信息：

1. **Name**：输入一个描述性名称，例如：
   ```
   Auto Mouse Controller
   ```

2. **Command**：输入启动服务器的命令，使用虚拟环境中的Python解释器：
   ```
   D:\Project\auto_mouse\venv\Scripts\python.exe D:\Project\auto_mouse\start_server.py
   ```
   
   > 注意：请将路径替换为您实际的项目路径。

3. **Environment Variables**：通常不需要添加特殊的环境变量，除非您有特定需求。

4. 点击"Add"按钮完成添加。

### 2.3 启动MCP服务器

1. 在Augment面板中，找到刚刚添加的"Auto Mouse Controller"服务器
2. 点击服务器名称旁边的启动按钮（播放图标）
3. 如果一切正常，服务器状态应该变为"Running"
4. 如果出现错误，请查看错误信息并参考下面的"常见问题"部分

## 3. 使用Auto Mouse Controller

一旦MCP服务器成功启动，您就可以在VSCode中使用Auto Mouse Controller的功能了。

### 3.1 可用的工具

Auto Mouse Controller提供以下工具：

1. **move_mouse**：移动鼠标到指定坐标或区域
2. **mouse_click**：执行鼠标点击操作
3. **mouse_drag**：执行鼠标拖拽操作
4. **mouse_scroll**：执行鼠标滚动操作
5. **get_mouse_position**：获取当前鼠标位置
6. **get_screen_size**：获取屏幕尺寸

### 3.2 工具参数说明

#### move_mouse
```
move_mouse(
    x=None,                # 目标X坐标
    y=None,                # 目标Y坐标
    left=None,             # 区域左边界
    top=None,              # 区域上边界
    right=None,            # 区域右边界
    bottom=None,           # 区域下边界
    duration=0.5,          # 移动持续时间（秒）
    random_offset=True,    # 是否添加随机偏移
    wait_before_ms=50,     # 执行前等待时间（毫秒）
    wait_after_ms=50       # 执行后等待时间（毫秒）
)
```

#### mouse_click
```
mouse_click(
    x=None,                # 点击的X坐标
    y=None,                # 点击的Y坐标
    button="left",         # 鼠标按钮（"left", "right", "middle"）
    clicks=1,              # 点击次数
    interval=0.1,          # 点击之间的间隔（秒）
    wait_before_ms=50,     # 执行前等待时间（毫秒）
    wait_after_ms=50       # 执行后等待时间（毫秒）
)
```

#### mouse_drag
```
mouse_drag(
    end_x,                 # 结束X坐标
    end_y,                 # 结束Y坐标
    start_x=None,          # 起始X坐标
    start_y=None,          # 起始Y坐标
    duration=0.5,          # 拖拽持续时间（秒）
    button="left",         # 鼠标按钮
    wait_before_ms=50,     # 执行前等待时间（毫秒）
    wait_after_ms=50       # 执行后等待时间（毫秒）
)
```

#### mouse_scroll
```
mouse_scroll(
    amount=3,              # 滚动的数量
    x=None,                # 滚动位置的X坐标
    y=None,                # 滚动位置的Y坐标
    direction="up",        # 滚动方向（"up", "down"）
    wait_before_ms=50,     # 执行前等待时间（毫秒）
    wait_after_ms=50       # 执行后等待时间（毫秒）
)
```

#### get_mouse_position
```
get_mouse_position()       # 不需要参数
```

#### get_screen_size
```
get_screen_size()          # 不需要参数
```

### 3.3 使用示例

以下是一些使用示例：

1. **获取当前鼠标位置**
   ```python
   position = get_mouse_position()
   print(f"当前鼠标位置: {position}")
   ```

2. **移动鼠标到指定坐标**
   ```python
   move_mouse(x=500, y=500, duration=0.8)
   ```

3. **移动鼠标到区域中心**
   ```python
   move_mouse(left=100, top=100, right=300, bottom=300)
   ```

4. **执行鼠标点击**
   ```python
   mouse_click(button="left", clicks=2)
   ```

5. **执行鼠标拖拽**
   ```python
   mouse_drag(end_x=800, end_y=600, start_x=400, start_y=300)
   ```

6. **执行鼠标滚动**
   ```python
   mouse_scroll(amount=5, direction="down")
   ```

## 4. 常见问题

### 4.1 服务器启动失败

**问题**：MCP服务器启动失败，显示"Connection closed"错误。

**解决方案**：
1. 确保已安装`fastmcp`库：
   ```
   venv\Scripts\pip.exe install fastmcp
   ```
2. 检查路径是否正确，确保使用绝对路径
3. 尝试在命令行中手动运行服务器，查看详细错误信息

### 4.2 鼠标操作不响应

**问题**：调用鼠标操作函数，但鼠标没有响应。

**解决方案**：
1. 确保MCP服务器正在运行
2. 检查是否有权限问题，尝试以管理员身份运行VSCode
3. 确保没有其他程序阻止鼠标操作

### 4.3 操作精度问题

**问题**：鼠标移动不够精确，无法点击小目标。

**解决方案**：
1. 调整`random_offset`参数为`False`，禁用随机偏移
2. 使用更小的`duration`值，加快移动速度
3. 对于精确操作，先移动到目标附近，再进行微调

### 4.4 性能问题

**问题**：鼠标操作响应缓慢。

**解决方案**：
1. 减少`wait_before_ms`和`wait_after_ms`的值
2. 使用更小的`duration`值
3. 减少不必要的操作，合并连续操作

## 5. 高级配置

### 5.1 使用TCP传输

如果您需要使用TCP传输而不是默认的stdio传输，可以在命令中添加相应参数：

```
D:\Project\auto_mouse\venv\Scripts\python.exe D:\Project\auto_mouse\start_server.py --transport tcp --host localhost --port 8000
```

### 5.2 启用调试模式

如果需要更详细的日志信息，可以启用调试模式：

```
D:\Project\auto_mouse\venv\Scripts\python.exe D:\Project\auto_mouse\start_server.py --debug
```

### 5.3 自定义配置

如果需要更多自定义配置，可以修改`start_server.py`文件，添加更多命令行参数和配置选项。

## 6. 总结

通过本指南，您应该能够成功地在VSCode中配置和使用Auto Mouse Controller MCP服务器。如果遇到任何问题，请参考"常见问题"部分或查阅项目文档。

祝您使用愉快！

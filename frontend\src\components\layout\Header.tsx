// -*- coding: utf-8 -*-

import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Settings, User } from 'lucide-react';
import { cn } from '../../utils';
import { BaseComponentProps } from '../../types';
import { Button } from '../common/Button';

/**
 * Header组件属性接口
 */
interface HeaderProps extends BaseComponentProps {
  title: string;
  showMenuButton?: boolean;
  onMenuClick?: () => void;
  sidebarOpen?: boolean;
}

/**
 * Header组件
 * 顶部导航栏组件，包含标题、菜单按钮和用户操作
 * 严格按照代码规范的单一职责原则
 */
export function Header({
  className,
  title,
  showMenuButton = true,
  onMenuClick,
  sidebarOpen = true,
  ...props
}: HeaderProps) {
  /**
   * 获取头部样式类名
   */
  const getHeaderClasses = () => {
    return cn(
      'flex',
      'items-center',
      'justify-between',
      'h-16',
      'px-6',
      'bg-white',
      'dark:bg-gray-800',
      'border-b',
      'border-gray-200',
      'dark:border-gray-700',
      'shadow-sm',
      className
    );
  };

  /**
   * 获取左侧区域样式类名
   */
  const getLeftSectionClasses = () => {
    return cn('flex', 'items-center', 'space-x-4');
  };

  /**
   * 获取右侧区域样式类名
   */
  const getRightSectionClasses = () => {
    return cn('flex', 'items-center', 'space-x-3');
  };

  /**
   * 获取标题样式类名
   */
  const getTitleClasses = () => {
    return cn(
      'text-xl',
      'font-semibold',
      'text-gray-900',
      'dark:text-gray-100'
    );
  };

  /**
   * 处理菜单按钮点击
   */
  const handleMenuClick = () => {
    if (onMenuClick) {
      onMenuClick();
    }
  };

  return (
    <header className={getHeaderClasses()} {...props}>
      {/* 左侧区域 */}
      <div className={getLeftSectionClasses()}>
        {/* 菜单按钮 */}
        {showMenuButton && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleMenuClick}
            className="p-2"
          >
            <Menu className="h-5 w-5" />
          </Button>
        )}

        {/* 标题 */}
        <h1 className={getTitleClasses()}>
          {title}
        </h1>

        {/* 连接状态指示器 */}
        <div className="flex items-center space-x-2">
          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-sm text-gray-600 dark:text-gray-400">
            已连接
          </span>
        </div>
      </div>

      {/* 右侧区域 */}
      <div className={getRightSectionClasses()}>
        {/* 通知按钮 */}
        <Button
          variant="ghost"
          size="sm"
          className="p-2 relative"
        >
          <Bell className="h-5 w-5" />
          {/* 通知徽章 */}
          <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
            3
          </span>
        </Button>

        {/* 设置按钮 */}
        <Button
          variant="ghost"
          size="sm"
          className="p-2"
        >
          <Settings className="h-5 w-5" />
        </Button>

        {/* 用户菜单 */}
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            className="p-2"
          >
            <User className="h-5 w-5" />
          </Button>
          <div className="hidden md:block">
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
              管理员
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              <EMAIL>
            </p>
          </div>
        </div>
      </div>
    </header>
  );
}

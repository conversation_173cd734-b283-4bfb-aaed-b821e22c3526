# -*- coding: utf-8 -*-
"""
精度控制器实现
专门负责操作精度控制和优化，严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
import math
from typing import Dict, List, Optional, Any, Tuple

try:
    import pyautogui
    PRECISION_AVAILABLE = True
except ImportError:
    PRECISION_AVAILABLE = False

from .base import (
    PrecisionControllerBase,
    OperationParameters,
    ExecutionContext,
    OperationType
)

class AdvancedPrecisionController(PrecisionControllerBase):
    """高级精度控制器"""
    
    def __init__(self):
        super().__init__("AdvancedPrecisionController")
        self.logger = logging.getLogger(f"QYuan.Execution.{self.name}")
        
        # 检查依赖可用性
        self.precision_available = PRECISION_AVAILABLE
        if not self.precision_available:
            self.logger.warning("精度控制库不可用，精度控制功能将受限")
        
        # 精度配置
        self.precision_config = {
            'coordinate_calibration': True,
            'timing_optimization': True,
            'adaptive_precision': True,
            'dpi_awareness': True,
            'sub_pixel_precision': False,
            'movement_smoothing': True
        }
        
        # 校准数据
        self.calibration_data = {
            'screen_dpi': 96,
            'scaling_factor': 1.0,
            'coordinate_offset': {'x': 0, 'y': 0},
            'timing_adjustments': {},
            'last_calibration': None
        }
        
        # 性能优化参数
        self.optimization_params = {
            'mouse_move_curve': 'bezier',  # linear, bezier, smooth
            'acceleration_profile': 'adaptive',  # constant, adaptive, custom
            'precision_mode': 'balanced',  # speed, balanced, precision
            'error_correction': True,
            'predictive_adjustment': True
        }
        
        # 统计数据
        self.precision_stats = {
            'total_calibrations': 0,
            'successful_calibrations': 0,
            'average_precision_error': 0.0,
            'timing_optimizations': 0,
            'coordinate_adjustments': 0
        }
    
    async def calibrate_coordinates(self, coordinates: Dict[str, int], context: ExecutionContext) -> Dict[str, int]:
        """校准坐标"""
        try:
            if not self.precision_config['coordinate_calibration']:
                return coordinates
            
            calibrated_coords = coordinates.copy()
            
            # DPI感知校准
            if self.precision_config['dpi_awareness']:
                calibrated_coords = await self._apply_dpi_calibration(calibrated_coords)
            
            # 坐标偏移校准
            offset = self.calibration_data['coordinate_offset']
            calibrated_coords['x'] += offset['x']
            calibrated_coords['y'] += offset['y']
            
            # 缩放因子校准
            scaling_factor = self.calibration_data['scaling_factor']
            calibrated_coords['x'] = int(calibrated_coords['x'] * scaling_factor)
            calibrated_coords['y'] = int(calibrated_coords['y'] * scaling_factor)
            
            # 边界检查
            calibrated_coords = await self._apply_boundary_constraints(calibrated_coords)
            
            # 自适应精度调整
            if self.precision_config['adaptive_precision']:
                calibrated_coords = await self._apply_adaptive_adjustment(calibrated_coords, context)
            
            # 更新统计
            self.precision_stats['coordinate_adjustments'] += 1
            
            self.logger.debug(f"坐标校准: {coordinates} -> {calibrated_coords}")
            return calibrated_coords
            
        except Exception as e:
            self.logger.error(f"坐标校准失败: {e}")
            return coordinates
    
    async def optimize_timing(self, operation_type: OperationType, context: ExecutionContext) -> Dict[str, float]:
        """优化时序"""
        try:
            if not self.precision_config['timing_optimization']:
                return {}
            
            timing_params = {}
            
            # 根据操作类型优化时序
            if operation_type == OperationType.MOUSE_CLICK:
                timing_params = await self._optimize_click_timing(context)
            elif operation_type == OperationType.MOUSE_MOVE:
                timing_params = await self._optimize_move_timing(context)
            elif operation_type == OperationType.MOUSE_DRAG:
                timing_params = await self._optimize_drag_timing(context)
            elif operation_type == OperationType.KEYBOARD_TYPE:
                timing_params = await self._optimize_type_timing(context)
            elif operation_type == OperationType.KEYBOARD_PRESS:
                timing_params = await self._optimize_key_timing(context)
            
            # 应用系统性能调整
            timing_params = await self._apply_performance_adjustment(timing_params, context)
            
            # 更新统计
            self.precision_stats['timing_optimizations'] += 1
            
            self.logger.debug(f"时序优化 {operation_type.value}: {timing_params}")
            return timing_params
            
        except Exception as e:
            self.logger.error(f"时序优化失败: {e}")
            return {}
    
    async def adjust_parameters(self, params: OperationParameters, context: ExecutionContext) -> OperationParameters:
        """调整参数"""
        try:
            adjusted_params = params
            
            # 校准坐标
            if params.coordinates:
                adjusted_coords = await self.calibrate_coordinates(params.coordinates, context)
                adjusted_params.coordinates = adjusted_coords
            
            # 优化时序
            timing_params = await self.optimize_timing(params.operation_type, context)
            if timing_params:
                adjusted_params.custom_params.update(timing_params)
            
            # 调整超时时间
            adjusted_params.timeout = await self._adjust_timeout(params, context)
            
            # 调整重试次数
            adjusted_params.retry_count = await self._adjust_retry_count(params, context)
            
            return adjusted_params
            
        except Exception as e:
            self.logger.error(f"参数调整失败: {e}")
            return params
    
    async def _apply_dpi_calibration(self, coordinates: Dict[str, int]) -> Dict[str, int]:
        """应用DPI校准"""
        try:
            if not self.precision_available:
                return coordinates
            
            # 获取屏幕DPI信息
            screen_dpi = self.calibration_data['screen_dpi']
            
            # 如果DPI不是标准96，进行调整
            if screen_dpi != 96:
                dpi_factor = screen_dpi / 96.0
                coordinates['x'] = int(coordinates['x'] * dpi_factor)
                coordinates['y'] = int(coordinates['y'] * dpi_factor)
            
            return coordinates
            
        except Exception as e:
            self.logger.warning(f"DPI校准失败: {e}")
            return coordinates
    
    async def _apply_boundary_constraints(self, coordinates: Dict[str, int]) -> Dict[str, int]:
        """应用边界约束"""
        try:
            if not self.precision_available:
                return coordinates
            
            # 获取屏幕尺寸
            screen_size = pyautogui.size()
            
            # 确保坐标在屏幕范围内
            coordinates['x'] = max(0, min(coordinates['x'], screen_size.width - 1))
            coordinates['y'] = max(0, min(coordinates['y'], screen_size.height - 1))
            
            return coordinates
            
        except Exception as e:
            self.logger.warning(f"边界约束失败: {e}")
            return coordinates
    
    async def _apply_adaptive_adjustment(self, coordinates: Dict[str, int], context: ExecutionContext) -> Dict[str, int]:
        """应用自适应调整"""
        try:
            # 根据历史精度数据进行调整
            precision_error = self.precision_stats['average_precision_error']
            
            if precision_error > 2.0:  # 如果平均误差大于2像素
                # 应用微调
                adjustment_x = int(precision_error * 0.5)
                adjustment_y = int(precision_error * 0.5)
                
                coordinates['x'] += adjustment_x
                coordinates['y'] += adjustment_y
            
            return coordinates
            
        except Exception as e:
            self.logger.warning(f"自适应调整失败: {e}")
            return coordinates
    
    async def _optimize_click_timing(self, context: ExecutionContext) -> Dict[str, float]:
        """优化点击时序"""
        base_timing = {
            'pre_click_delay': 0.1,
            'click_duration': 0.05,
            'post_click_delay': 0.1
        }
        
        # 根据精度模式调整
        if self.optimization_params['precision_mode'] == 'precision':
            base_timing['pre_click_delay'] *= 1.5
            base_timing['click_duration'] *= 1.2
            base_timing['post_click_delay'] *= 1.5
        elif self.optimization_params['precision_mode'] == 'speed':
            base_timing['pre_click_delay'] *= 0.7
            base_timing['click_duration'] *= 0.8
            base_timing['post_click_delay'] *= 0.7
        
        return base_timing
    
    async def _optimize_move_timing(self, context: ExecutionContext) -> Dict[str, float]:
        """优化移动时序"""
        base_timing = {
            'move_duration': 0.5,
            'acceleration': 1.0,
            'smoothing_factor': 0.8
        }
        
        # 根据移动曲线调整
        if self.optimization_params['mouse_move_curve'] == 'bezier':
            base_timing['move_duration'] *= 1.2
            base_timing['smoothing_factor'] = 0.9
        elif self.optimization_params['mouse_move_curve'] == 'linear':
            base_timing['move_duration'] *= 0.8
            base_timing['smoothing_factor'] = 0.5
        
        return base_timing
    
    async def _optimize_drag_timing(self, context: ExecutionContext) -> Dict[str, float]:
        """优化拖拽时序"""
        base_timing = {
            'drag_duration': 1.0,
            'start_delay': 0.2,
            'end_delay': 0.2
        }
        
        # 根据精度模式调整
        if self.optimization_params['precision_mode'] == 'precision':
            base_timing['drag_duration'] *= 1.5
            base_timing['start_delay'] *= 1.3
            base_timing['end_delay'] *= 1.3
        
        return base_timing
    
    async def _optimize_type_timing(self, context: ExecutionContext) -> Dict[str, float]:
        """优化输入时序"""
        base_timing = {
            'type_interval': 0.05,
            'word_pause': 0.1,
            'sentence_pause': 0.3
        }
        
        # 根据精度模式调整
        if self.optimization_params['precision_mode'] == 'precision':
            base_timing['type_interval'] *= 1.2
        elif self.optimization_params['precision_mode'] == 'speed':
            base_timing['type_interval'] *= 0.8
        
        return base_timing
    
    async def _optimize_key_timing(self, context: ExecutionContext) -> Dict[str, float]:
        """优化按键时序"""
        base_timing = {
            'key_press_duration': 0.05,
            'key_interval': 0.1
        }
        
        return base_timing
    
    async def _apply_performance_adjustment(self, timing_params: Dict[str, float], context: ExecutionContext) -> Dict[str, float]:
        """应用性能调整"""
        try:
            # 获取系统性能信息
            import psutil
            
            cpu_percent = psutil.cpu_percent(interval=None)
            memory_percent = psutil.virtual_memory().percent
            
            # 如果系统负载高，增加延迟
            if cpu_percent > 80 or memory_percent > 80:
                performance_factor = 1.5
                for key, value in timing_params.items():
                    if 'delay' in key or 'duration' in key or 'interval' in key:
                        timing_params[key] = value * performance_factor
            
            return timing_params
            
        except Exception as e:
            self.logger.warning(f"性能调整失败: {e}")
            return timing_params
    
    async def _adjust_timeout(self, params: OperationParameters, context: ExecutionContext) -> float:
        """调整超时时间"""
        base_timeout = params.timeout
        
        # 根据操作类型调整
        if params.operation_type in [OperationType.MOUSE_CLICK, OperationType.MOUSE_MOVE]:
            return base_timeout * 0.8  # 鼠标操作通常较快
        elif params.operation_type == OperationType.KEYBOARD_TYPE:
            text_length = len(params.text) if params.text else 10
            return max(base_timeout, text_length * 0.1)  # 根据文本长度调整
        elif params.operation_type == OperationType.WAIT:
            return base_timeout  # 等待操作不调整超时
        
        return base_timeout
    
    async def _adjust_retry_count(self, params: OperationParameters, context: ExecutionContext) -> int:
        """调整重试次数"""
        base_retry = params.retry_count
        
        # 根据操作复杂度调整
        if params.operation_type in [OperationType.MOUSE_CLICK, OperationType.KEYBOARD_PRESS]:
            return max(base_retry, 2)  # 简单操作至少重试2次
        elif params.operation_type in [OperationType.MOUSE_DRAG, OperationType.KEYBOARD_TYPE]:
            return max(base_retry, 1)  # 复杂操作至少重试1次
        
        return base_retry
    
    async def calibrate_system(self) -> Dict[str, Any]:
        """校准系统"""
        calibration_result = {
            'success': False,
            'calibration_data': {},
            'recommendations': []
        }
        
        try:
            self.logger.info("开始系统校准...")
            
            # 校准屏幕DPI
            dpi_result = await self._calibrate_screen_dpi()
            calibration_result['calibration_data']['dpi'] = dpi_result
            
            # 校准坐标偏移
            offset_result = await self._calibrate_coordinate_offset()
            calibration_result['calibration_data']['offset'] = offset_result
            
            # 校准缩放因子
            scaling_result = await self._calibrate_scaling_factor()
            calibration_result['calibration_data']['scaling'] = scaling_result
            
            # 更新校准数据
            self.calibration_data.update({
                'screen_dpi': dpi_result.get('dpi', 96),
                'coordinate_offset': offset_result.get('offset', {'x': 0, 'y': 0}),
                'scaling_factor': scaling_result.get('factor', 1.0),
                'last_calibration': time.time()
            })
            
            calibration_result['success'] = True
            calibration_result['recommendations'].append("校准完成，建议定期重新校准")
            
            # 更新统计
            self.precision_stats['total_calibrations'] += 1
            self.precision_stats['successful_calibrations'] += 1
            
            self.logger.info("系统校准完成")
            
        except Exception as e:
            calibration_result['recommendations'].append(f"校准失败: {str(e)}")
            self.logger.error(f"系统校准失败: {e}")
        
        return calibration_result
    
    async def _calibrate_screen_dpi(self) -> Dict[str, Any]:
        """校准屏幕DPI"""
        try:
            if self.precision_available:
                # 简化实现：使用默认DPI
                return {'dpi': 96, 'method': 'default'}
            else:
                return {'dpi': 96, 'method': 'fallback'}
        except Exception as e:
            return {'dpi': 96, 'error': str(e)}
    
    async def _calibrate_coordinate_offset(self) -> Dict[str, Any]:
        """校准坐标偏移"""
        try:
            # 简化实现：无偏移
            return {'offset': {'x': 0, 'y': 0}, 'method': 'default'}
        except Exception as e:
            return {'offset': {'x': 0, 'y': 0}, 'error': str(e)}
    
    async def _calibrate_scaling_factor(self) -> Dict[str, Any]:
        """校准缩放因子"""
        try:
            # 简化实现：无缩放
            return {'factor': 1.0, 'method': 'default'}
        except Exception as e:
            return {'factor': 1.0, 'error': str(e)}
    
    def get_precision_stats(self) -> Dict[str, Any]:
        """获取精度统计"""
        stats = self.precision_stats.copy()
        total = stats['total_calibrations']
        stats['calibration_success_rate'] = (stats['successful_calibrations'] / total) if total > 0 else 0.0
        stats['calibration_data'] = self.calibration_data.copy()
        stats['optimization_params'] = self.optimization_params.copy()
        return stats
    
    def update_precision_config(self, config: Dict[str, Any]):
        """更新精度配置"""
        self.precision_config.update(config)
        self.logger.info(f"精度配置已更新: {config}")
    
    def update_optimization_params(self, params: Dict[str, Any]):
        """更新优化参数"""
        self.optimization_params.update(params)
        self.logger.info(f"优化参数已更新: {params}")

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
键盘控制器测试脚本

该脚本用于测试键盘控制器的各项功能，包括按键、释放键、组合键等。
"""

import time
import logging
from keyboard_controller import KeyboardController

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("KeyboardTest")

def test_single_key():
    """测试单个按键操作"""
    logger.info("开始测试单个按键操作")
    
    controller = KeyboardController()
    
    # 等待用户准备
    print("请在5秒内将光标放在文本编辑器中...")
    time.sleep(5)
    
    # 测试按下并释放单个字符
    print("测试: 输入字符 'a'")
    controller.type_key('a')
    time.sleep(1)
    
    # 测试按下并释放特殊键
    print("测试: 按下回车键")
    controller.type_key('enter')
    time.sleep(1)
    
    # 测试按下并释放功能键
    print("测试: 按下F1键")
    controller.type_key('f1')
    time.sleep(1)
    
    logger.info("单个按键操作测试完成")

def test_text_input():
    """测试文本输入"""
    logger.info("开始测试文本输入")
    
    controller = KeyboardController()
    
    # 等待用户准备
    print("请在5秒内将光标放在文本编辑器中...")
    time.sleep(5)
    
    # 测试输入文本
    test_text = "Hello, this is a test of the keyboard controller!"
    print(f"测试: 输入文本 '{test_text}'")
    controller.type_text(test_text)
    time.sleep(1)
    
    # 测试输入带特殊字符的文本
    controller.type_key('enter')
    special_text = "Special chars: !@#$%^&*()_+-=[]{}|;':\",./<>?"
    print(f"测试: 输入带特殊字符的文本 '{special_text}'")
    controller.type_text(special_text)
    time.sleep(1)
    
    logger.info("文本输入测试完成")

def test_hotkeys():
    """测试组合键操作"""
    logger.info("开始测试组合键操作")
    
    controller = KeyboardController()
    
    # 等待用户准备
    print("请在5秒内将光标放在文本编辑器中...")
    time.sleep(5)
    
    # 测试常见组合键
    print("测试: Ctrl+A (全选)")
    controller.hotkey('ctrl', 'a')
    time.sleep(2)
    
    print("测试: Ctrl+C (复制)")
    controller.hotkey('ctrl', 'c')
    time.sleep(1)
    
    print("测试: Ctrl+V (粘贴)")
    controller.hotkey('ctrl', 'v')
    time.sleep(1)
    
    # 测试三键组合
    print("测试: Ctrl+Shift+Esc (打开任务管理器)")
    controller.hotkey('ctrl', 'shift', 'esc')
    time.sleep(3)
    
    # 测试Alt+Tab (切换窗口)
    print("测试: Alt+Tab (切换窗口)")
    controller.hotkey('alt', 'tab')
    time.sleep(1)
    
    logger.info("组合键操作测试完成")

def test_press_release():
    """测试单独的按下和释放操作"""
    logger.info("开始测试单独的按下和释放操作")
    
    controller = KeyboardController()
    
    # 等待用户准备
    print("请在5秒内将光标放在文本编辑器中...")
    time.sleep(5)
    
    # 测试按下和释放
    print("测试: 按下Shift键2秒后释放")
    controller.press_key('shift')
    time.sleep(2)
    controller.release_key('shift')
    time.sleep(1)
    
    # 测试在Shift按下时输入文本
    print("测试: 按下Shift键并输入文本")
    controller.press_key('shift')
    time.sleep(0.5)
    controller.type_text("this should be uppercase")
    time.sleep(0.5)
    controller.release_key('shift')
    time.sleep(1)
    
    logger.info("单独的按下和释放操作测试完成")

def main():
    """主函数，运行所有测试"""
    logger.info("开始键盘控制器测试")
    
    print("键盘控制器测试程序")
    print("=================")
    print("该程序将测试键盘控制器的各项功能。")
    print("请确保您已准备好一个文本编辑器窗口用于测试。")
    print()
    
    # 询问用户是否准备好
    input("准备好开始测试了吗？按回车键继续...")
    
    # 运行测试
    try:
        test_single_key()
        print("\n按回车键继续下一个测试...")
        input()
        
        test_text_input()
        print("\n按回车键继续下一个测试...")
        input()
        
        test_press_release()
        print("\n按回车键继续下一个测试...")
        input()
        
        test_hotkeys()
        
        print("\n所有测试已完成！")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")
        print(f"\n测试过程中出错: {str(e)}")
    
    logger.info("键盘控制器测试结束")

if __name__ == "__main__":
    main()

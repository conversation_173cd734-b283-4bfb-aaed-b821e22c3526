#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OCR文本识别模块 (EasyOCR实现)

提供基于EasyOCR的文本识别功能。
"""

import os
import sys
import time
import numpy as np
from PIL import Image

# 导入基础接口类
from .ocr_recognition_base import OCRRecognitionBase


class OCRRecognitionEasyOCR(OCRRecognitionBase):
    """OCR文本识别类，提供基于EasyOCR的文本识别功能"""

    def __init__(self, lang=["en", "ch_sim"]):
        """
        初始化EasyOCR引擎

        Args:
            lang: 识别语言列表，默认为英语和简体中文
        """
        self.lang = lang
        self.ocr_engine = None

        # 初始化EasyOCR引擎
        self._init_engine()

    def _init_engine(self):
        """初始化EasyOCR引擎"""
        try:
            import easyocr
            self.ocr_engine = easyocr.Reader(self.lang)
            print("EasyOCR引擎初始化成功")
        except ImportError:
            print("请安装EasyOCR: pip install easyocr")
            raise
        except Exception as e:
            print(f"EasyOCR引擎初始化失败: {e}")
            raise

    def recognize_text(self, image, region=None, return_format="structured"):
        """
        识别图像中的文本

        Args:
            image: 图像数据或路径
            region: 识别区域 [x, y, width, height]，为None则识别整个图像
            return_format: 返回格式，支持 "text", "structured"

        Returns:
            根据return_format返回不同格式的结果:
            - "text": 返回纯文本字符串
            - "structured": 返回结构化数据，包含文本、位置和置信度
        """
        # 处理图像输入
        if isinstance(image, str):
            # 如果是路径，则加载图像
            image = Image.open(image)

        # 处理区域
        if region:
            x, y, width, height = region
            image = image.crop((x, y, x + width, y + height))

        # 转换为numpy数组
        if not isinstance(image, np.ndarray):
            image_np = np.array(image)
        else:
            image_np = image

        try:
            # 使用EasyOCR进行识别
            results = self.ocr_engine.readtext(image_np)

            if return_format == "text":
                # 返回纯文本
                return "\n".join([text for _, text, _ in results])
            else:
                # 返回结构化数据
                structured_result = []

                # 按行组织结果
                lines = self._group_results_by_line(results)

                for line in lines:
                    line_text = " ".join([item[1] for item in line])

                    # 计算行的边界框
                    line_boxes = [item[0] for item in line]
                    line_box = self._merge_boxes(line_boxes)

                    # 计算行的平均置信度
                    line_confidence = sum([item[2] for item in line]) / len(line)

                    # 构建单词列表
                    words = []
                    for box, text, conf in line:
                        words.append({
                            "text": text,
                            "box": self._convert_box_format(box),
                            "confidence": conf
                        })

                    structured_result.append({
                        "text": line_text,
                        "box": self._convert_box_format(line_box),
                        "confidence": line_confidence,
                        "words": words
                    })

                return structured_result

        except Exception as e:
            print(f"EasyOCR识别失败: {e}")
            return [] if return_format == "structured" else ""

    def _group_results_by_line(self, results):
        """
        将识别结果按行分组

        Args:
            results: EasyOCR识别结果

        Returns:
            list: 按行分组的结果
        """
        if not results:
            return []

        # 按y坐标排序
        sorted_results = sorted(results, key=lambda x: sum([p[1] for p in x[0]]) / len(x[0]))

        # 分组
        lines = []
        current_line = [sorted_results[0]]

        for i in range(1, len(sorted_results)):
            current_item = sorted_results[i]
            prev_item = sorted_results[i-1]

            # 计算当前项和前一项的y坐标中心
            current_y_center = sum([p[1] for p in current_item[0]]) / len(current_item[0])
            prev_y_center = sum([p[1] for p in prev_item[0]]) / len(prev_item[0])

            # 计算当前项的高度
            current_height = max([p[1] for p in current_item[0]]) - min([p[1] for p in current_item[0]])

            # 如果y坐标差异小于当前项高度的一半，认为是同一行
            if abs(current_y_center - prev_y_center) < current_height * 0.5:
                current_line.append(current_item)
            else:
                # 按x坐标排序当前行
                current_line.sort(key=lambda x: min([p[0] for p in x[0]]))
                lines.append(current_line)
                current_line = [current_item]

        # 添加最后一行
        if current_line:
            # 按x坐标排序当前行
            current_line.sort(key=lambda x: min([p[0] for p in x[0]]))
            lines.append(current_line)

        return lines

    def _merge_boxes(self, boxes):
        """
        合并多个边界框

        Args:
            boxes: 边界框列表

        Returns:
            list: 合并后的边界框
        """
        if not boxes:
            return []

        # 找出所有点
        all_points = []
        for box in boxes:
            all_points.extend(box)

        # 找出最小和最大坐标
        min_x = min([p[0] for p in all_points])
        min_y = min([p[1] for p in all_points])
        max_x = max([p[0] for p in all_points])
        max_y = max([p[1] for p in all_points])

        # 返回合并后的边界框
        return [[min_x, min_y], [max_x, min_y], [max_x, max_y], [min_x, max_y]]

    def _convert_box_format(self, box):
        """
        转换边界框格式为[x1, y1, x2, y2]

        Args:
            box: EasyOCR边界框格式 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]

        Returns:
            list: [x1, y1, x2, y2]格式的边界框，使用普通整数而非numpy.int32
        """
        # 找出最小和最大坐标
        min_x = min([p[0] for p in box])
        min_y = min([p[1] for p in box])
        max_x = max([p[0] for p in box])
        max_y = max([p[1] for p in box])

        # 确保返回普通整数而非numpy.int32
        return [int(min_x), int(min_y), int(max_x), int(max_y)]

    def get_engine_info(self):
        """
        获取OCR引擎信息

        Returns:
            dict: 包含引擎名称、版本、支持的语言等信息
        """
        return {
            "name": "EasyOCR",
            "version": self._get_easyocr_version(),
            "supported_languages": self.get_supported_languages(),
            "current_language": self.lang
        }

    def _get_easyocr_version(self):
        """
        获取EasyOCR版本

        Returns:
            str: EasyOCR版本
        """
        try:
            import easyocr
            return easyocr.__version__
        except:
            return "未知"

    def get_supported_languages(self):
        """
        获取支持的语言列表

        Returns:
            list: 支持的语言代码列表
        """
        # EasyOCR支持的语言列表
        return [
            'en', 'ch_sim', 'ch_tra', 'ja', 'ko', 'th', 'ta', 'te', 'kn', 'ar', 'hi', 'vi', 'mr', 'ne',
            'bn', 'ru', 'rs_cyrillic', 'bg', 'uk', 'be', 'mn', 'abq', 'ady', 'kbd', 'ava', 'dar', 'inh',
            'che', 'lbe', 'lez', 'tab', 'fa', 'ur', 'ug', 'he', 'ml', 'fr', 'de', 'it', 'es', 'pt', 'nl',
            'pl', 'cs', 'ro', 'sk', 'sl', 'hr', 'hu', 'fi', 'sv', 'da', 'no', 'lv', 'lt', 'et', 'is', 'tr',
            'az', 'id', 'ms', 'tl', 'la', 'bs', 'af', 'sq', 'mt', 'ga', 'eu', 'ca', 'gl', 'cy', 'gd', 'as',
            'brx', 'doi', 'gu', 'ks', 'mai', 'mni', 'or', 'pa', 'sa', 'sd', 'si', 'am', 'ti', 'km', 'lo',
            'my', 'dv', 'bo', 'el', 'hy', 'ka', 'iu', 'chr', 'piqd', 'iu', 'ak', 'bm', 'ee', 'ff', 'gaa',
            'ha', 'ig', 'ln', 'pcm', 'rw', 'st', 'sw', 'wo', 'yo', 'zu'
        ]


# 测试代码
if __name__ == "__main__":
    # 创建OCR对象
    ocr = OCRRecognitionEasyOCR(lang=["en", "ch_sim"])

    # 测试图像路径
    test_image_path = "screenshots/screenshot_latest.png"

    # 如果测试图像不存在，则退出
    if not os.path.exists(test_image_path):
        print(f"测试图像不存在: {test_image_path}")
        sys.exit(1)

    # 识别文本
    results = ocr.recognize_text(test_image_path, return_format="structured")

    # 打印识别结果
    print("\n识别结果:")
    for line in results:
        print(f"文本: {line['text']}")
        print(f"位置: {line['box']}")
        print(f"置信度: {line['confidence']}")
        print("-" * 50)

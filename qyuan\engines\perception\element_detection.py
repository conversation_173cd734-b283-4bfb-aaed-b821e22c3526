# -*- coding: utf-8 -*-
"""
UI元素检测服务实现
专门负责UI元素识别和检测，严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
from typing import List, Optional, Dict, Any, Tuple
import io

try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

try:
    import PIL.Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from .base import (
    UIElementDetectionServiceBase,
    UIElement,
    ElementType,
    Rectangle,
    Screenshot,
    PerceptionResult,
    PerceptionType
)

class UIElementDetectionService(UIElementDetectionServiceBase):
    """UI元素检测服务实现"""
    
    def __init__(self):
        super().__init__("UIElementDetection")
        self.logger = logging.getLogger(f"QYuan.Perception.{self.name}")
        
        # 检查依赖可用性
        self.cv2_available = CV2_AVAILABLE
        self.pil_available = PIL_AVAILABLE
        
        if not self.cv2_available:
            self.logger.warning("OpenCV不可用，UI元素检测功能将受限")
        
        if not self.pil_available:
            self.logger.warning("PIL不可用，图像处理功能将受限")
        
        # 元素检测配置
        self.button_templates = []
        self.input_templates = []
        self.confidence_threshold = 0.7
    
    async def process(self, input_data: Any) -> PerceptionResult:
        """处理UI元素检测请求"""
        start_time = time.time()
        
        try:
            if not isinstance(input_data, Screenshot):
                raise ValueError("输入数据必须是Screenshot对象")
            
            elements = await self.detect_elements(input_data)
            
            processing_time = time.time() - start_time
            
            result = PerceptionResult(
                perception_type=PerceptionType.UI_ELEMENT_DETECTION,
                success=True,
                data=elements,
                confidence=self._calculate_overall_confidence(elements),
                processing_time=processing_time
            )
            
            self.last_result = result
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"UI元素检测失败: {e}")
            
            result = PerceptionResult(
                perception_type=PerceptionType.UI_ELEMENT_DETECTION,
                success=False,
                data=[],
                confidence=0.0,
                error_message=str(e),
                processing_time=processing_time
            )
            
            self.last_result = result
            return result
    
    async def detect_elements(self, screenshot: Screenshot) -> List[UIElement]:
        """检测UI元素"""
        if not self.cv2_available or not self.pil_available:
            self.logger.warning("缺少必要依赖，返回空元素列表")
            return []
        
        try:
            # 转换截图为OpenCV格式
            cv_image = self._screenshot_to_cv2(screenshot)
            
            elements = []
            
            # 检测按钮
            buttons = await self._detect_buttons(cv_image)
            elements.extend(buttons)
            
            # 检测输入框
            inputs = await self._detect_inputs(cv_image)
            elements.extend(inputs)
            
            # 检测文本区域
            texts = await self._detect_text_areas(cv_image)
            elements.extend(texts)
            
            # 检测窗口和对话框
            windows = await self._detect_windows(cv_image)
            elements.extend(windows)
            
            self.logger.debug(f"检测到 {len(elements)} 个UI元素")
            return elements
            
        except Exception as e:
            self.logger.error(f"UI元素检测失败: {e}")
            return []
    
    async def find_element_by_text(self, screenshot: Screenshot, text: str) -> Optional[UIElement]:
        """根据文本查找元素"""
        elements = await self.detect_elements(screenshot)
        
        for element in elements:
            if element.text and text.lower() in element.text.lower():
                return element
        
        return None
    
    async def find_elements_by_type(self, screenshot: Screenshot, element_type: ElementType) -> List[UIElement]:
        """根据类型查找元素"""
        elements = await self.detect_elements(screenshot)
        
        return [element for element in elements if element.element_type == element_type]
    
    async def _detect_buttons(self, cv_image: np.ndarray) -> List[UIElement]:
        """检测按钮元素"""
        buttons = []
        
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # 使用边缘检测
            edges = cv2.Canny(gray, 50, 150)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                # 计算轮廓的边界矩形
                x, y, w, h = cv2.boundingRect(contour)
                
                # 过滤太小或太大的区域
                if w < 20 or h < 10 or w > 300 or h > 100:
                    continue
                
                # 计算长宽比，按钮通常是矩形
                aspect_ratio = w / h
                if aspect_ratio < 0.5 or aspect_ratio > 10:
                    continue
                
                # 创建按钮元素
                button = UIElement(
                    element_type=ElementType.BUTTON,
                    bounds=Rectangle(x, y, w, h),
                    confidence=0.6,  # 基础置信度
                    clickable=True,
                    attributes={
                        'aspect_ratio': aspect_ratio,
                        'area': w * h
                    }
                )
                
                buttons.append(button)
            
            self.logger.debug(f"检测到 {len(buttons)} 个按钮")
            
        except Exception as e:
            self.logger.error(f"按钮检测失败: {e}")
        
        return buttons
    
    async def _detect_inputs(self, cv_image: np.ndarray) -> List[UIElement]:
        """检测输入框元素"""
        inputs = []
        
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # 使用形态学操作检测矩形区域
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            morph = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
            
            # 边缘检测
            edges = cv2.Canny(morph, 30, 100)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                # 计算轮廓的边界矩形
                x, y, w, h = cv2.boundingRect(contour)
                
                # 输入框通常是长矩形
                if w < 50 or h < 15 or w > 500 or h > 50:
                    continue
                
                aspect_ratio = w / h
                if aspect_ratio < 2 or aspect_ratio > 20:
                    continue
                
                # 创建输入框元素
                input_element = UIElement(
                    element_type=ElementType.INPUT,
                    bounds=Rectangle(x, y, w, h),
                    confidence=0.5,
                    clickable=True,
                    attributes={
                        'aspect_ratio': aspect_ratio,
                        'area': w * h
                    }
                )
                
                inputs.append(input_element)
            
            self.logger.debug(f"检测到 {len(inputs)} 个输入框")
            
        except Exception as e:
            self.logger.error(f"输入框检测失败: {e}")
        
        return inputs
    
    async def _detect_text_areas(self, cv_image: np.ndarray) -> List[UIElement]:
        """检测文本区域"""
        texts = []
        
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # 使用MSER检测文本区域
            mser = cv2.MSER_create()
            regions, _ = mser.detectRegions(gray)
            
            for region in regions:
                # 计算区域的边界矩形
                x, y, w, h = cv2.boundingRect(region)
                
                # 过滤太小的文本区域
                if w < 10 or h < 8 or w * h < 100:
                    continue
                
                # 创建文本元素
                text_element = UIElement(
                    element_type=ElementType.TEXT,
                    bounds=Rectangle(x, y, w, h),
                    confidence=0.4,
                    clickable=False,
                    attributes={
                        'area': w * h,
                        'region_points': len(region)
                    }
                )
                
                texts.append(text_element)
            
            self.logger.debug(f"检测到 {len(texts)} 个文本区域")
            
        except Exception as e:
            self.logger.error(f"文本区域检测失败: {e}")
        
        return texts
    
    async def _detect_windows(self, cv_image: np.ndarray) -> List[UIElement]:
        """检测窗口和对话框"""
        windows = []
        
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # 使用霍夫变换检测矩形
            edges = cv2.Canny(gray, 50, 150)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, minLineLength=100, maxLineGap=10)
            
            if lines is not None:
                # 简单的窗口检测逻辑
                # 这里可以根据需要实现更复杂的窗口检测算法
                pass
            
            self.logger.debug(f"检测到 {len(windows)} 个窗口")
            
        except Exception as e:
            self.logger.error(f"窗口检测失败: {e}")
        
        return windows
    
    def _screenshot_to_cv2(self, screenshot: Screenshot) -> np.ndarray:
        """将Screenshot转换为OpenCV格式"""
        # 将字节数据转换为PIL图像
        pil_image = PIL.Image.open(io.BytesIO(screenshot.image_data))
        
        # 转换为RGB格式
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        
        # 转换为numpy数组
        np_array = np.array(pil_image)
        
        # OpenCV使用BGR格式
        cv_image = cv2.cvtColor(np_array, cv2.COLOR_RGB2BGR)
        
        return cv_image
    
    def _calculate_overall_confidence(self, elements: List[UIElement]) -> float:
        """计算整体置信度"""
        if not elements:
            return 0.0
        
        total_confidence = sum(element.confidence for element in elements)
        return total_confidence / len(elements)

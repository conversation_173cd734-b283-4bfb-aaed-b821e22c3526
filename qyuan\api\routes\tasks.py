#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务API路由

提供QYuan任务管理的API接口
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status

from ..models import TaskRequest, TaskResponse, APIResponse
from ..app import get_qyuan_core, verify_api_key
from ...core.qyuan_core import QYuanCore

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/create",
    response_model=TaskResponse,
    summary="创建任务",
    description="创建新的任务并生成执行计划"
)
async def create_task(
    request: TaskRequest,
    qyuan: QYuanCore = Depends(get_qyuan_core),
    _: bool = Depends(verify_api_key)
):
    """创建任务"""
    try:
        logger.info(f"创建任务: {request.goal}")
        
        # 获取通信引擎
        communication_engine = qyuan.engines.get("communication")
        if not communication_engine:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="通信引擎不可用"
            )
        
        # 生成任务计划
        if hasattr(communication_engine, 'llm_manager') and communication_engine.llm_manager:
            try:
                action_plan = await communication_engine.llm_manager.create_action_plan(
                    goal=request.goal,
                    context=request.context or {}
                )
                
                return TaskResponse(
                    task_id=f"task_{hash(request.goal)}",
                    status="planned",
                    message=f"任务计划已生成: {request.goal}",
                    action_plan=action_plan.__dict__ if action_plan else None,
                    estimated_time=getattr(action_plan, 'estimated_time', None) if action_plan else None
                )
                
            except Exception as e:
                logger.error(f"生成任务计划失败: {e}")
                return TaskResponse(
                    task_id=f"task_{hash(request.goal)}",
                    status="error",
                    message=f"生成任务计划失败: {e}"
                )
        else:
            return TaskResponse(
                task_id=f"task_{hash(request.goal)}",
                status="created",
                message=f"任务已创建: {request.goal}（LLM管理器不可用，无法生成详细计划）"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建任务时发生错误: {str(e)}"
        )

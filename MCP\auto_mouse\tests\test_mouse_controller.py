"""
鼠标控制器模块的单元测试。
"""

import unittest
from unittest.mock import patch, MagicMock

import sys
import os
import random

# 添加src目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 设置随机种子，使测试结果可重现
random.seed(42)

from src.mouse_controller import MouseController


class TestMouseController(unittest.TestCase):
    """鼠标控制器测试类。"""

    @patch('src.mouse_controller.pyautogui')
    def setUp(self, mock_pyautogui):
        """测试前的设置。"""
        # 模拟屏幕尺寸
        mock_pyautogui.size.return_value = (1920, 1080)
        self.controller = MouseController()

    @patch('src.mouse_controller.pyautogui')
    def test_get_position(self, mock_pyautogui):
        """测试获取鼠标位置。"""
        # 设置模拟返回值
        mock_pyautogui.position.return_value = (100, 200)

        # 调用方法
        position = self.controller.get_position()

        # 验证结果
        self.assertEqual(position, (100, 200))
        mock_pyautogui.position.assert_called_once()

    @patch('src.mouse_controller.pyautogui')
    def test_move_mouse_with_coordinates(self, mock_pyautogui):
        """测试移动鼠标到指定坐标。"""
        # 调用方法，禁用随机偏移
        result = self.controller.move_mouse(500, 600, duration=0.2, random_offset=False)

        # 验证结果
        self.assertTrue(result["success"])
        self.assertEqual(result["position"]["x"], 500)
        self.assertEqual(result["position"]["y"], 600)
        mock_pyautogui.moveTo.assert_called_once_with(500, 600, duration=0.2)

    @patch('src.mouse_controller.pyautogui')
    def test_move_mouse_with_random_offset(self, mock_pyautogui):
        """测试移动鼠标到指定坐标并添加随机偏移。"""
        # 调用方法，启用随机偏移
        result = self.controller.move_mouse(500, 600, duration=0.2, random_offset=True)

        # 验证结果
        self.assertTrue(result["success"])
        # 坐标应该在原始坐标附近（±3像素）
        self.assertGreaterEqual(result["position"]["x"], 497)
        self.assertLessEqual(result["position"]["x"], 503)
        self.assertGreaterEqual(result["position"]["y"], 597)
        self.assertLessEqual(result["position"]["y"], 603)
        # 确保调用了moveTo
        mock_pyautogui.moveTo.assert_called_once()

    @patch('src.mouse_controller.pyautogui')
    def test_move_mouse_with_region(self, mock_pyautogui):
        """测试使用区域坐标移动鼠标。"""
        # 调用方法
        result = self.controller.move_mouse(
            left=100, top=200, right=300, bottom=400,
            duration=0.2, random_offset=False
        )

        # 验证结果
        self.assertTrue(result["success"])
        # 坐标应该在区域的中心30%范围内
        self.assertGreaterEqual(result["position"]["x"], 170)  # 100 + (300-100)*0.35
        self.assertLessEqual(result["position"]["x"], 230)     # 100 + (300-100)*0.65
        self.assertGreaterEqual(result["position"]["y"], 270)  # 200 + (400-200)*0.35
        self.assertLessEqual(result["position"]["y"], 330)     # 200 + (400-200)*0.65
        # 确保调用了moveTo
        mock_pyautogui.moveTo.assert_called_once()

    @patch('src.mouse_controller.pyautogui')
    def test_move_mouse_without_coordinates(self, mock_pyautogui):
        """测试不提供坐标时获取当前位置。"""
        # 设置模拟返回值
        mock_pyautogui.position.return_value = (300, 400)

        # 调用方法
        result = self.controller.move_mouse()

        # 验证结果
        self.assertTrue(result["success"])
        self.assertEqual(result["position"]["x"], 300)
        self.assertEqual(result["position"]["y"], 400)
        mock_pyautogui.moveTo.assert_not_called()

    @patch('src.mouse_controller.pyautogui')
    def test_mouse_click(self, mock_pyautogui):
        """测试鼠标点击。"""
        # 调用方法
        result = self.controller.mouse_click(700, 800, "right", 2, 0.1)

        # 验证结果
        self.assertTrue(result["success"])
        self.assertEqual(result["position"]["x"], 700)
        self.assertEqual(result["position"]["y"], 800)
        self.assertEqual(result["button"], "right")
        self.assertEqual(result["clicks"], 2)
        mock_pyautogui.moveTo.assert_called_once_with(700, 800)
        mock_pyautogui.click.assert_called_once_with(x=700, y=800, button="right", clicks=2, interval=0.1)

    @patch('src.mouse_controller.pyautogui')
    def test_mouse_drag(self, mock_pyautogui):
        """测试鼠标拖拽。"""
        # 调用方法
        result = self.controller.mouse_drag(900, 1000, 700, 800, 0.3, "left")

        # 验证结果
        self.assertTrue(result["success"])
        self.assertEqual(result["start_position"]["x"], 700)
        self.assertEqual(result["start_position"]["y"], 800)
        self.assertEqual(result["end_position"]["x"], 900)
        self.assertEqual(result["end_position"]["y"], 1000)
        self.assertEqual(result["button"], "left")
        mock_pyautogui.moveTo.assert_called_once_with(700, 800)
        mock_pyautogui.dragTo.assert_called_once_with(900, 1000, duration=0.3, button="left")

    @patch('src.mouse_controller.pyautogui')
    def test_mouse_scroll(self, mock_pyautogui):
        """测试鼠标滚动。"""
        # 调用方法
        result = self.controller.mouse_scroll(5, 500, 600, "down")

        # 验证结果
        self.assertTrue(result["success"])
        self.assertEqual(result["position"]["x"], 500)
        self.assertEqual(result["position"]["y"], 600)
        self.assertEqual(result["direction"], "down")
        self.assertEqual(result["amount"], 5)
        mock_pyautogui.moveTo.assert_called_once_with(500, 600)
        mock_pyautogui.scroll.assert_called_once_with(-5)  # 向下滚动是负值

    def test_get_screen_size(self):
        """测试获取屏幕尺寸。"""
        # 调用方法
        result = self.controller.get_screen_size()

        # 验证结果
        self.assertEqual(result["width"], 1920)
        self.assertEqual(result["height"], 1080)


if __name__ == '__main__':
    unittest.main()

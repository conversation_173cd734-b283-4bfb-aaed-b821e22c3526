# QYuan核心框架实现方案

## 项目结构设计

### 1. 整体目录结构
```
QYuan/
├── qyuan/                      # 核心代码包
│   ├── __init__.py
│   ├── core/                   # 核心模块
│   │   ├── __init__.py
│   │   ├── qyuan_core.py       # QYuan主类
│   │   ├── config.py           # 配置管理
│   │   ├── exceptions.py       # 异常定义
│   │   └── events.py           # 事件系统
│   ├── engines/                # 五大引擎
│   │   ├── __init__.py
│   │   ├── perception/         # 感知引擎
│   │   ├── decision/           # 决策引擎
│   │   ├── execution/          # 执行引擎
│   │   ├── learning/           # 学习引擎
│   │   └── communication/      # 通信引擎
│   ├── interfaces/             # 接口层
│   │   ├── __init__.py
│   │   ├── mcp_adapter.py      # MCP适配器
│   │   ├── llm_client.py       # LLM客户端
│   │   ├── terminal_interface.py # 终端接口
│   │   └── web_interface.py    # Web接口
│   ├── database/               # 数据层
│   │   ├── __init__.py
│   │   ├── models.py           # 数据模型
│   │   ├── repositories.py     # 数据访问
│   │   └── migrations/         # 数据库迁移
│   ├── utils/                  # 工具模块
│   │   ├── __init__.py
│   │   ├── logging.py          # 日志工具
│   │   ├── security.py         # 安全工具
│   │   ├── helpers.py          # 辅助函数
│   │   └── decorators.py       # 装饰器
│   └── api/                    # API层
│       ├── __init__.py
│       ├── main.py             # FastAPI应用
│       ├── routes/             # 路由定义
│       ├── middleware/         # 中间件
│       └── schemas/            # 数据模式
├── frontend/                   # 前端代码
├── tests/                      # 测试代码
├── docs/                       # 文档
├── scripts/                    # 脚本工具
├── requirements.txt            # Python依赖
├── .env.example               # 环境变量示例
├── docker-compose.yml         # Docker配置
└── main.py                    # 主入口
```

## 核心类设计

### 1. QYuan主类
```python
# qyuan/core/qyuan_core.py
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime

from .config import QYuanConfig
from .events import EventBus
from ..engines.perception import PerceptionEngine
from ..engines.decision import DecisionEngine
from ..engines.execution import ExecutionEngine
from ..engines.learning import LearningEngine
from ..engines.communication import CommunicationEngine

class QYuanCore:
    """QYuan核心类 - 硅基CEO的大脑"""
    
    def __init__(self, config: QYuanConfig):
        self.config = config
        self.event_bus = EventBus()
        self.status = QYuanStatus()
        
        # 初始化五大引擎
        self.perception = PerceptionEngine(self)
        self.decision = DecisionEngine(self)
        self.execution = ExecutionEngine(self)
        self.learning = LearningEngine(self)
        self.communication = CommunicationEngine(self)
        
        # 当前状态
        self.current_goal: Optional[Goal] = None
        self.current_task: Optional[Task] = None
        self.is_running = False
        self.start_time: Optional[datetime] = None
        
    async def start(self):
        """启动QYuan"""
        self.logger.info("QYuan正在启动...")
        
        try:
            # 初始化各个引擎
            await self.perception.initialize()
            await self.decision.initialize()
            await self.execution.initialize()
            await self.learning.initialize()
            await self.communication.initialize()
            
            # 启动主循环
            self.is_running = True
            self.start_time = datetime.now()
            
            # 启动后台任务
            asyncio.create_task(self.main_loop())
            asyncio.create_task(self.health_monitor())
            
            self.logger.info("QYuan启动完成")
            await self.event_bus.emit("qyuan.started", {"timestamp": self.start_time})
            
        except Exception as e:
            self.logger.error(f"QYuan启动失败: {e}")
            raise
    
    async def stop(self):
        """停止QYuan"""
        self.logger.info("QYuan正在停止...")
        
        self.is_running = False
        
        # 停止各个引擎
        await self.communication.shutdown()
        await self.learning.shutdown()
        await self.execution.shutdown()
        await self.decision.shutdown()
        await self.perception.shutdown()
        
        self.logger.info("QYuan已停止")
        await self.event_bus.emit("qyuan.stopped", {"timestamp": datetime.now()})
    
    async def main_loop(self):
        """主循环 - QYuan的心跳"""
        while self.is_running:
            try:
                # 检查是否有新的目标或任务
                if self.current_goal is None:
                    await self.wait_for_goal()
                    continue
                
                # 执行感知-行动循环
                await self.perception_action_cycle()
                
                # 短暂休息
                await asyncio.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"主循环异常: {e}")
                await self.handle_main_loop_error(e)
    
    async def perception_action_cycle(self):
        """感知-行动循环"""
        # 感知当前状态
        current_state = await self.perception.capture_current_state()
        
        # 决策下一步行动
        action = await self.decision.plan_next_action(
            goal=self.current_goal,
            current_state=current_state
        )
        
        if action is None:
            # 目标已完成或无法继续
            await self.handle_goal_completion()
            return
        
        # 执行行动
        execution_result = await self.execution.execute_action(action)
        
        # 验证结果
        verification = await self.execution.verify_result(action, execution_result)
        
        # 学习和记录
        await self.learning.record_experience(
            context=current_state,
            action=action,
            result=execution_result,
            verification=verification
        )
        
        # 更新目标进度
        if verification.success:
            await self.update_goal_progress(verification)
        else:
            await self.handle_action_failure(action, execution_result, verification)
    
    async def set_goal(self, goal: Goal):
        """设置新目标"""
        self.current_goal = goal
        self.logger.info(f"设置新目标: {goal.description}")
        await self.event_bus.emit("goal.set", {"goal": goal.to_dict()})
    
    async def handle_user_message(self, message: str, user_id: str = None) -> str:
        """处理用户消息"""
        try:
            # 解析用户意图
            intent = await self.decision.parse_user_intent(message)
            
            # 根据意图类型处理
            if intent.type == "GOAL_SETTING":
                goal = await self.decision.create_goal_from_intent(intent)
                await self.set_goal(goal)
                return f"好的，我将帮您{goal.description}"
            
            elif intent.type == "STATUS_QUERY":
                status = await self.get_status()
                return self.format_status_response(status)
            
            elif intent.type == "CONVERSATION":
                response = await self.decision.generate_response(intent, message)
                return response
            
            else:
                return "我不太理解您的意思，能否详细说明一下？"
                
        except Exception as e:
            self.logger.error(f"处理用户消息失败: {e}")
            return "抱歉，处理您的请求时出现了问题。"
    
    async def get_status(self) -> QYuanStatus:
        """获取当前状态"""
        return QYuanStatus(
            is_running=self.is_running,
            current_goal=self.current_goal.description if self.current_goal else None,
            current_task=self.current_task.description if self.current_task else None,
            uptime=datetime.now() - self.start_time if self.start_time else None,
            engines_status={
                "perception": await self.perception.get_status(),
                "decision": await self.decision.get_status(),
                "execution": await self.execution.get_status(),
                "learning": await self.learning.get_status(),
                "communication": await self.communication.get_status()
            }
        )
    
    @property
    def logger(self):
        """获取日志器"""
        return self.config.logger

class QYuanStatus:
    """QYuan状态类"""
    
    def __init__(self, **kwargs):
        self.is_running = kwargs.get('is_running', False)
        self.current_goal = kwargs.get('current_goal')
        self.current_task = kwargs.get('current_task')
        self.uptime = kwargs.get('uptime')
        self.engines_status = kwargs.get('engines_status', {})
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "is_running": self.is_running,
            "current_goal": self.current_goal,
            "current_task": self.current_task,
            "uptime": str(self.uptime) if self.uptime else None,
            "engines_status": self.engines_status,
            "timestamp": self.timestamp.isoformat()
        }
```

### 2. 配置管理
```python
# qyuan/core/config.py
import os
import logging
from typing import Dict, Any
from dataclasses import dataclass
from pathlib import Path

@dataclass
class DatabaseConfig:
    """数据库配置"""
    url: str
    redis_url: str
    qdrant_url: str
    pool_size: int = 10
    max_overflow: int = 20

@dataclass
class LLMConfig:
    """LLM配置"""
    api_base: str
    api_key: str
    model: str
    timeout: int = 30
    max_retries: int = 3

@dataclass
class MCPConfig:
    """MCP服务配置"""
    mouse_port: int
    keyboard_port: int
    vision_port: int
    host: str = "localhost"

class QYuanConfig:
    """QYuan配置管理"""
    
    def __init__(self, config_file: str = None):
        self.config_file = config_file or ".env"
        self.load_config()
        self.setup_logging()
    
    def load_config(self):
        """加载配置"""
        # 数据库配置
        self.database = DatabaseConfig(
            url=os.getenv("DATABASE_URL", "postgresql://qyuan_user:qyuan_password@localhost:5432/qyuan_db"),
            redis_url=os.getenv("REDIS_URL", "redis://localhost:6379"),
            qdrant_url=os.getenv("QDRANT_URL", "http://localhost:6333")
        )
        
        # LLM配置
        self.llm = LLMConfig(
            api_base=os.getenv("LLM_API_BASE", "https://xiaoai.plus"),
            api_key=os.getenv("LLM_API_KEY", "sk-oQx9XqkIHyMFabGTFByNVsxZHw1q4YpTvFdIQoupRkEVjDd3"),
            model=os.getenv("LLM_MODEL", "gpt-4.1")
        )
        
        # MCP配置
        self.mcp = MCPConfig(
            mouse_port=int(os.getenv("MCP_MOUSE_PORT", "8001")),
            keyboard_port=int(os.getenv("MCP_KEYBOARD_PORT", "8002")),
            vision_port=int(os.getenv("MCP_VISION_PORT", "8003"))
        )
        
        # 应用配置
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.secret_key = os.getenv("SECRET_KEY", "your-secret-key-here")
    
    def setup_logging(self):
        """设置日志"""
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        
        logging.basicConfig(
            level=getattr(logging, self.log_level),
            format=log_format,
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler("qyuan.log")
            ]
        )
        
        self.logger = logging.getLogger("QYuan")
```

### 3. 事件系统
```python
# qyuan/core/events.py
import asyncio
from typing import Dict, List, Callable, Any
from datetime import datetime

class EventBus:
    """事件总线"""
    
    def __init__(self):
        self.listeners: Dict[str, List[Callable]] = {}
        self.event_history: List[Dict[str, Any]] = []
    
    def on(self, event_name: str, callback: Callable):
        """注册事件监听器"""
        if event_name not in self.listeners:
            self.listeners[event_name] = []
        self.listeners[event_name].append(callback)
    
    def off(self, event_name: str, callback: Callable):
        """移除事件监听器"""
        if event_name in self.listeners:
            self.listeners[event_name].remove(callback)
    
    async def emit(self, event_name: str, data: Any = None):
        """发射事件"""
        event = {
            "name": event_name,
            "data": data,
            "timestamp": datetime.now()
        }
        
        # 记录事件历史
        self.event_history.append(event)
        
        # 通知监听器
        if event_name in self.listeners:
            tasks = []
            for callback in self.listeners[event_name]:
                if asyncio.iscoroutinefunction(callback):
                    tasks.append(callback(event))
                else:
                    callback(event)
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
```

## 引擎基类设计

### 1. 引擎基类
```python
# qyuan/engines/__init__.py
from abc import ABC, abstractmethod
from typing import Dict, Any
from ..core.qyuan_core import QYuanCore

class BaseEngine(ABC):
    """引擎基类"""
    
    def __init__(self, qyuan_core: QYuanCore):
        self.qyuan = qyuan_core
        self.config = qyuan_core.config
        self.logger = qyuan_core.logger
        self.event_bus = qyuan_core.event_bus
        self.is_initialized = False
    
    @abstractmethod
    async def initialize(self):
        """初始化引擎"""
        pass
    
    @abstractmethod
    async def shutdown(self):
        """关闭引擎"""
        pass
    
    @abstractmethod
    async def get_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        pass
    
    async def health_check(self) -> bool:
        """健康检查"""
        return self.is_initialized
```

## 主入口设计

### 1. 主程序入口
```python
# main.py
import asyncio
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from qyuan.core.qyuan_core import QYuanCore
from qyuan.core.config import QYuanConfig

class QYuanApplication:
    """QYuan应用程序"""
    
    def __init__(self):
        self.config = QYuanConfig()
        self.qyuan = QYuanCore(self.config)
        self.is_running = False
    
    async def start(self):
        """启动应用"""
        print("🤖 QYuan - 硅基CEO 正在启动...")
        
        try:
            # 启动QYuan核心
            await self.qyuan.start()
            
            # 设置信号处理
            self.setup_signal_handlers()
            
            self.is_running = True
            print("✅ QYuan启动成功！")
            
            # 保持运行
            await self.keep_alive()
            
        except KeyboardInterrupt:
            print("\n⏹️  收到停止信号...")
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            sys.exit(1)
        finally:
            await self.stop()
    
    async def stop(self):
        """停止应用"""
        if self.is_running:
            print("🛑 QYuan正在停止...")
            self.is_running = False
            await self.qyuan.stop()
            print("✅ QYuan已停止")
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            print(f"\n收到信号 {signum}")
            self.is_running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def keep_alive(self):
        """保持应用运行"""
        while self.is_running:
            await asyncio.sleep(1)

async def main():
    """主函数"""
    app = QYuanApplication()
    await app.start()

if __name__ == "__main__":
    asyncio.run(main())
```

## 下一步实现

1. **实现各个引擎的基础框架**
2. **完善数据库模型和迁移**
3. **实现LLM客户端和MCP适配器**
4. **添加基础的API接口**
5. **编写单元测试**

这个核心框架为QYuan提供了坚实的基础，后续的所有功能都将基于这个架构进行开发。

# -*- coding: utf-8 -*-
"""
感知引擎基础类和接口定义
严格按照代码规范，每个文件不超过200行，单一职责原则
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

class PerceptionType(Enum):
    """感知类型枚举"""
    SCREEN_CAPTURE = "screen_capture"
    UI_ELEMENT_DETECTION = "ui_element_detection"
    TEXT_RECOGNITION = "text_recognition"
    VISUAL_UNDERSTANDING = "visual_understanding"
    SYSTEM_STATE = "system_state"

class ElementType(Enum):
    """UI元素类型枚举"""
    BUTTON = "button"
    INPUT = "input"
    TEXT = "text"
    IMAGE = "image"
    MENU = "menu"
    WINDOW = "window"
    DIALOG = "dialog"
    UNKNOWN = "unknown"

@dataclass
class Rectangle:
    """矩形区域"""
    x: int
    y: int
    width: int
    height: int
    
    @property
    def center(self) -> Tuple[int, int]:
        """获取中心点坐标"""
        return (self.x + self.width // 2, self.y + self.height // 2)
    
    @property
    def area(self) -> int:
        """获取面积"""
        return self.width * self.height

@dataclass
class UIElement:
    """UI元素"""
    element_type: ElementType
    bounds: Rectangle
    text: Optional[str] = None
    confidence: float = 0.0
    attributes: Optional[Dict[str, Any]] = None
    clickable: bool = False
    visible: bool = True
    
    def __post_init__(self):
        if self.attributes is None:
            self.attributes = {}

@dataclass
class Screenshot:
    """屏幕截图"""
    image_data: bytes
    width: int
    height: int
    format: str = "PNG"
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class PerceptionResult:
    """感知结果"""
    perception_type: PerceptionType
    success: bool
    data: Any
    confidence: float = 0.0
    timestamp: datetime = None
    error_message: Optional[str] = None
    processing_time: float = 0.0
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class ScreenAnalysis:
    """屏幕分析结果"""
    screenshot: Screenshot
    ui_elements: List[UIElement]
    text_content: List[str]
    visual_description: Optional[str] = None
    context_info: Optional[Dict[str, Any]] = None
    analysis_time: float = 0.0
    
    def __post_init__(self):
        if self.context_info is None:
            self.context_info = {}

class PerceptionServiceBase(ABC):
    """感知服务基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
        self.last_result: Optional[PerceptionResult] = None
    
    @abstractmethod
    async def process(self, input_data: Any) -> PerceptionResult:
        """处理感知任务"""
        pass
    
    async def is_available(self) -> bool:
        """检查服务是否可用"""
        return self.enabled
    
    def get_last_result(self) -> Optional[PerceptionResult]:
        """获取最后一次感知结果"""
        return self.last_result

class ScreenCaptureServiceBase(PerceptionServiceBase):
    """屏幕截图服务基类"""
    
    @abstractmethod
    async def capture_full_screen(self) -> Screenshot:
        """捕获全屏截图"""
        pass
    
    @abstractmethod
    async def capture_window(self, window_handle: int) -> Screenshot:
        """捕获指定窗口截图"""
        pass
    
    @abstractmethod
    async def capture_region(self, region: Rectangle) -> Screenshot:
        """捕获指定区域截图"""
        pass

class UIElementDetectionServiceBase(PerceptionServiceBase):
    """UI元素检测服务基类"""
    
    @abstractmethod
    async def detect_elements(self, screenshot: Screenshot) -> List[UIElement]:
        """检测UI元素"""
        pass
    
    @abstractmethod
    async def find_element_by_text(self, screenshot: Screenshot, text: str) -> Optional[UIElement]:
        """根据文本查找元素"""
        pass
    
    @abstractmethod
    async def find_elements_by_type(self, screenshot: Screenshot, element_type: ElementType) -> List[UIElement]:
        """根据类型查找元素"""
        pass

class TextRecognitionServiceBase(PerceptionServiceBase):
    """文本识别服务基类"""
    
    @abstractmethod
    async def extract_text(self, screenshot: Screenshot) -> List[str]:
        """提取文本内容"""
        pass
    
    @abstractmethod
    async def extract_text_with_positions(self, screenshot: Screenshot) -> List[Tuple[str, Rectangle]]:
        """提取文本内容及位置"""
        pass

class VisualUnderstandingServiceBase(PerceptionServiceBase):
    """视觉理解服务基类"""
    
    @abstractmethod
    async def describe_image(self, screenshot: Screenshot) -> str:
        """描述图像内容"""
        pass
    
    @abstractmethod
    async def analyze_context(self, screenshot: Screenshot) -> Dict[str, Any]:
        """分析上下文信息"""
        pass

class SystemStateServiceBase(PerceptionServiceBase):
    """系统状态感知服务基类"""
    
    @abstractmethod
    async def get_active_window(self) -> Optional[Dict[str, Any]]:
        """获取活动窗口信息"""
        pass
    
    @abstractmethod
    async def get_running_processes(self) -> List[Dict[str, Any]]:
        """获取运行中的进程"""
        pass
    
    @abstractmethod
    async def get_system_resources(self) -> Dict[str, Any]:
        """获取系统资源使用情况"""
        pass

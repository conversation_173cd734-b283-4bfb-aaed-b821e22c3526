# MCP开发说明文档

本文档基于Auto Keyboard Controller项目的开发经验，为未来的MCP服务器开发提供全面的参考指南。

## 目录

1. [MCP基础概念](#1-mcp基础概念)
2. [项目架构设计](#2-项目架构设计)
3. [开发环境配置](#3-开发环境配置)
4. [MCP服务器实现](#4-mcp服务器实现)
5. [工具设计与实现](#5-工具设计与实现)
6. [错误处理与日志](#6-错误处理与日志)
7. [测试策略](#7-测试策略)
8. [部署与集成](#8-部署与集成)
9. [最佳实践](#9-最佳实践)
10. [常见问题解决](#10-常见问题解决)

## 1. MCP基础概念

### 1.1 什么是MCP

MCP (Model Context Protocol) 是一种标准化协议，允许大型语言模型(LLM)与外部系统进行交互。

**核心架构**：
- **主机(Host)**: LLM应用程序(如Claude Desktop或VSCode)
- **客户端(Client)**: 在主机应用程序内部维护与服务器的连接
- **服务器(Server)**: 提供工具、资源和提示给客户端

### 1.2 MCP服务器能力

MCP服务器可以提供三种主要类型的能力：

1. **工具(Tools)**: 可以被LLM调用的函数
2. **资源(Resources)**: 类似文件的数据，可以被客户端读取
3. **提示(Prompts)**: 预先编写的模板

### 1.3 通信机制

- **传输方式**: Stdio（标准输入/输出）或 HTTP+SSE
- **消息格式**: JSON-RPC 2.0
- **连接生命周期**: 初始化 → 消息交换 → 终止

## 2. 项目架构设计

### 2.1 模块化设计原则

```
项目根目录/
├── start_server.py          # MCP服务器启动脚本
├── core_controller.py       # 核心控制器模块
├── enhanced_handler.py      # 增强功能处理模块
├── requirements.txt         # 依赖配置
├── start_mcp_server.bat    # Windows启动脚本
├── docs/                   # 文档目录
│   ├── mcp_server_guide.md
│   ├── development_experience.md
│   └── integration_guide.md
└── tests/                  # 测试目录
    ├── test_basic_functionality.py
    ├── test_enhanced_features.py
    └── test_mcp_server.py
```

### 2.2 核心模块职责

**启动脚本 (start_server.py)**:
- 参数解析和配置管理
- MCP服务器初始化
- 工具注册和路由
- 错误处理和日志配置

**核心控制器**:
- 基础功能实现
- 硬件接口封装
- 仿人化操作逻辑

**增强处理器**:
- 高级功能实现
- 系统API调用
- 状态检测和验证

## 3. 开发环境配置

### 3.1 Python环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source venv/bin/activate
```

### 3.2 依赖管理

**requirements.txt 示例**:
```
fastmcp>=2.3.4
pynput>=1.8.1
pyperclip>=1.8.2
pywin32>=310; platform_system=="Windows"
```

**安装依赖**:
```bash
pip install -r requirements.txt
```

### 3.3 开发工具配置

**VSCode配置**:
- 安装Python扩展
- 配置虚拟环境路径
- 设置代码格式化和检查

## 4. MCP服务器实现

### 4.1 基本服务器结构

```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import logging
from fastmcp import FastMCP
from your_controller import YourController

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("YourMCPServer")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Your MCP Server")
    parser.add_argument("--transport", choices=["stdio", "tcp"], default="stdio")
    parser.add_argument("--host", default="localhost")
    parser.add_argument("--port", type=int, default=8000)
    parser.add_argument("--debug", action="store_true")
    return parser.parse_args()

def main():
    args = parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 初始化控制器
    controller = YourController()
    
    # 初始化MCP服务器
    mcp = FastMCP("Your Server Name")
    
    # 注册工具
    register_tools(mcp, controller)
    
    # 启动服务器
    if args.transport == "stdio":
        mcp.run(transport="stdio")
    elif args.transport == "tcp":
        mcp.run(transport="tcp", host=args.host, port=args.port)

if __name__ == "__main__":
    main()
```

### 4.2 工具注册模式

```python
def register_tools(mcp, controller):
    """注册MCP工具"""
    
    @mcp.tool()
    async def basic_operation(param1: str, param2: int = 100) -> str:
        """基础操作工具
        
        Args:
            param1: 必需参数说明
            param2: 可选参数说明，默认值100
        """
        try:
            result = controller.basic_operation(param1, param2)
            return f"操作成功: {result}"
        except Exception as e:
            logger.error(f"操作失败: {str(e)}")
            return f"操作失败: {str(e)}"
    
    @mcp.tool()
    async def advanced_operation(
        param1: str, 
        param2: List[str], 
        wait_ms: int = 50
    ) -> str:
        """高级操作工具
        
        Args:
            param1: 主要参数
            param2: 参数列表
            wait_ms: 等待时间（毫秒）
        """
        try:
            result = controller.advanced_operation(param1, param2, wait_ms)
            return f"高级操作完成: {result}"
        except Exception as e:
            logger.error(f"高级操作失败: {str(e)}")
            return f"高级操作失败: {str(e)}"
```

## 5. 工具设计与实现

### 5.1 工具定义结构

每个MCP工具都使用以下JSON Schema结构定义：

```json
{
  "name": "string",          // 工具的唯一标识符
  "description": "string",   // 人类可读的描述
  "inputSchema": {           // 工具参数的JSON Schema
    "type": "object",
    "properties": {          // 工具特定的参数
      "param1": {
        "type": "string",
        "description": "参数1的描述"
      },
      "param2": {
        "type": "number",
        "description": "参数2的描述",
        "default": 100
      }
    },
    "required": ["param1"]   // 必需参数列表
  },
  "annotations": {           // 关于工具行为的可选提示
    "title": "string",       // 工具的人类可读标题
    "readOnlyHint": boolean, // 如果为true，工具不会修改其环境
    "destructiveHint": boolean, // 如果为true，工具可能执行破坏性更新
    "idempotentHint": boolean,  // 如果为true，使用相同参数重复调用不会产生额外效果
    "openWorldHint": boolean    // 如果为true，工具与外部实体交互
  }
}
```

### 5.2 工具设计原则

**单一职责**: 每个工具只负责一个明确的功能
**参数清晰**: 使用类型注解和详细的文档字符串
**错误处理**: 妥善处理异常并返回友好的错误信息
**一致性**: 保持参数命名和返回格式的一致性
**安全性**: 验证所有输入参数，防止恶意输入

### 5.3 参数设计模式

```python
# 基础参数模式
async def simple_tool(target: str) -> str:
    """简单工具，只需要一个目标参数"""

# 配置参数模式  
async def configurable_tool(
    target: str,
    wait_before_ms: int = 50,
    wait_after_ms: int = 50
) -> str:
    """可配置的工具，包含等待时间参数"""

# 复杂参数模式
async def complex_tool(
    targets: List[str],
    options: Dict[str, Any],
    timeout_ms: int = 5000
) -> str:
    """复杂工具，支持多个目标和选项字典"""
```

### 5.4 返回值设计

```python
# 成功返回
return f"操作成功: {详细信息}"

# 失败返回
return f"操作失败: {错误原因}"

# 状态返回
return f"当前状态: {状态信息}"

# 数据返回
return json.dumps(data, ensure_ascii=False, indent=2)
```

## 6. 错误处理与日志

### 6.1 异常处理模式

```python
async def robust_tool(param: str) -> str:
    """健壮的工具实现"""
    try:
        # 参数验证
        if not param or not param.strip():
            return "错误: 参数不能为空"
        
        # 执行操作
        result = controller.operation(param)
        
        # 结果验证
        if not result:
            return "警告: 操作完成但结果为空"
        
        return f"操作成功: {result}"
        
    except ValueError as e:
        logger.error(f"参数错误: {str(e)}")
        return f"参数错误: {str(e)}"
    except TimeoutError as e:
        logger.error(f"操作超时: {str(e)}")
        return f"操作超时: {str(e)}"
    except Exception as e:
        logger.error(f"未知错误: {str(e)}")
        return f"操作失败: {str(e)}"
```

### 6.2 日志配置

```python
import logging

# 配置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 控制台输出
        logging.FileHandler('mcp_server.log', encoding='utf-8')  # 文件输出
    ]
)

# 创建专用logger
logger = logging.getLogger("MCPServer")

# 使用示例
logger.info("服务器启动")
logger.debug(f"处理参数: {param}")
logger.warning("检测到潜在问题")
logger.error(f"操作失败: {error}")
```

## 7. 测试策略

### 7.1 单元测试

```python
import unittest
from unittest.mock import Mock, patch
from your_controller import YourController

class TestYourController(unittest.TestCase):
    
    def setUp(self):
        self.controller = YourController()
    
    def test_basic_operation(self):
        """测试基础操作"""
        result = self.controller.basic_operation("test")
        self.assertIsNotNone(result)
    
    @patch('your_module.external_dependency')
    def test_with_mock(self, mock_dependency):
        """使用mock测试外部依赖"""
        mock_dependency.return_value = "mocked_result"
        result = self.controller.operation_with_dependency()
        self.assertEqual(result, "expected_result")

if __name__ == '__main__':
    unittest.main()
```

### 7.2 集成测试

```python
import asyncio
from fastmcp import FastMCP

async def test_mcp_integration():
    """测试MCP集成"""
    mcp = FastMCP("Test Server")
    
    # 注册测试工具
    @mcp.tool()
    async def test_tool(param: str) -> str:
        return f"测试结果: {param}"
    
    # 模拟工具调用
    result = await test_tool("test_input")
    assert "测试结果: test_input" in result
    
    print("集成测试通过")

if __name__ == '__main__':
    asyncio.run(test_mcp_integration())
```

## 8. 部署与集成

### 8.1 启动脚本

**Windows批处理文件 (start_mcp_server.bat)**:
```batch
@echo off
cd /d "项目路径"
"虚拟环境路径\Scripts\python.exe" "start_server.py" %*
```

**Linux/Mac脚本 (start_mcp_server.sh)**:
```bash
#!/bin/bash
cd "项目路径"
"虚拟环境路径/bin/python" "start_server.py" "$@"
```

### 8.2 VSCode集成配置

在VSCode的Augment扩展中添加MCP服务器：

```json
{
  "name": "Your MCP Server",
  "command": "项目路径/start_mcp_server.bat"
}
```

### 8.3 Claude Desktop集成

在Claude Desktop配置文件中添加：

```json
{
  "mcpServers": {
    "your-server": {
      "command": "python",
      "args": ["项目路径/start_server.py"]
    }
  }
}
```

## 9. 最佳实践

### 9.1 代码组织

- **模块化**: 将功能分解为独立模块
- **接口清晰**: 定义明确的模块接口
- **文档完整**: 每个函数都有详细的文档字符串
- **类型注解**: 使用类型注解提高代码可读性

### 9.2 性能优化

- **异步操作**: 使用async/await处理耗时操作
- **资源管理**: 及时释放系统资源
- **缓存机制**: 对频繁访问的数据进行缓存
- **批量处理**: 合并相似操作减少系统调用

### 9.3 安全考虑

- **参数验证**: 严格验证所有输入参数
- **权限控制**: 实现适当的访问控制
- **错误信息**: 不暴露敏感的内部信息
- **资源限制**: 防止资源耗尽攻击

## 10. 常见问题解决

### 10.1 启动问题

**问题**: 服务器启动失败
**解决方案**:
1. 检查Python路径和虚拟环境
2. 验证依赖是否正确安装
3. 查看详细错误日志
4. 确认文件权限

### 10.2 工具调用问题

**问题**: 工具调用无响应
**解决方案**:
1. 检查工具注册是否正确
2. 验证参数类型和格式
3. 查看服务器日志
4. 测试工具的独立功能

### 10.3 性能问题

**问题**: 响应速度慢
**解决方案**:
1. 优化算法和数据结构
2. 减少不必要的等待时间
3. 使用异步操作
4. 添加性能监控

---

## 总结

本文档基于Auto Keyboard Controller项目的实际开发经验，提供了完整的MCP服务器开发指南。遵循这些最佳实践，可以开发出高质量、可维护的MCP服务器，为LLM提供强大的外部工具支持。

关键成功因素：
- 清晰的架构设计
- 完善的错误处理
- 充分的测试覆盖
- 详细的文档说明
- 持续的优化改进

希望这份文档能为您的MCP开发项目提供有价值的参考！

## 附录A: 完整示例代码

### A.1 简单的MCP服务器示例

```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单MCP服务器示例
演示基本的MCP服务器实现模式
"""

import asyncio
import logging
from typing import List, Dict, Any
from fastmcp import FastMCP

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("SimpleMCPServer")

class SimpleController:
    """简单控制器示例"""

    def __init__(self):
        self.data = {}
        logger.info("简单控制器初始化完成")

    def store_data(self, key: str, value: str) -> bool:
        """存储数据"""
        self.data[key] = value
        return True

    def get_data(self, key: str) -> str:
        """获取数据"""
        return self.data.get(key, "")

    def list_keys(self) -> List[str]:
        """列出所有键"""
        return list(self.data.keys())

def main():
    # 初始化控制器
    controller = SimpleController()

    # 初始化MCP服务器
    mcp = FastMCP("Simple MCP Server")

    @mcp.tool()
    async def store_value(key: str, value: str) -> str:
        """存储键值对

        Args:
            key: 存储的键
            value: 存储的值
        """
        try:
            success = controller.store_data(key, value)
            if success:
                return f"成功存储: {key} = {value}"
            else:
                return f"存储失败: {key}"
        except Exception as e:
            return f"存储错误: {str(e)}"

    @mcp.tool()
    async def get_value(key: str) -> str:
        """获取存储的值

        Args:
            key: 要获取的键
        """
        try:
            value = controller.get_data(key)
            if value:
                return f"获取成功: {key} = {value}"
            else:
                return f"未找到键: {key}"
        except Exception as e:
            return f"获取错误: {str(e)}"

    @mcp.tool()
    async def list_all_keys() -> str:
        """列出所有存储的键"""
        try:
            keys = controller.list_keys()
            if keys:
                return f"所有键: {', '.join(keys)}"
            else:
                return "没有存储任何数据"
        except Exception as e:
            return f"列表错误: {str(e)}"

    # 启动服务器
    logger.info("启动简单MCP服务器")
    mcp.run(transport="stdio")

if __name__ == "__main__":
    main()
```

### A.2 TypeScript MCP服务器示例

```typescript
#!/usr/bin/env node

/**
 * TypeScript MCP服务器示例
 * 演示使用官方TypeScript SDK实现MCP服务器
 */

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  ListToolsRequestSchema,
  CallToolRequestSchema
} from "@modelcontextprotocol/sdk/types.js";

interface DataStore {
  [key: string]: string;
}

class TypeScriptController {
  private data: DataStore = {};

  storeData(key: string, value: string): boolean {
    this.data[key] = value;
    console.log(`存储数据: ${key} = ${value}`);
    return true;
  }

  getData(key: string): string | undefined {
    const value = this.data[key];
    console.log(`获取数据: ${key} = ${value || '未找到'}`);
    return value;
  }

  listKeys(): string[] {
    const keys = Object.keys(this.data);
    console.log(`列出所有键: ${keys.join(', ')}`);
    return keys;
  }

  calculateSum(a: number, b: number): number {
    const result = a + b;
    console.log(`计算: ${a} + ${b} = ${result}`);
    return result;
  }
}

async function createMCPServer(): Promise<Server> {
  const controller = new TypeScriptController();

  const server = new Server({
    name: "typescript-mcp-server",
    version: "1.0.0"
  }, {
    capabilities: {
      tools: {}
    }
  });

  // 定义可用工具
  server.setRequestHandler(ListToolsRequestSchema, async () => {
    return {
      tools: [
        {
          name: "store_value",
          description: "存储键值对到内存中",
          inputSchema: {
            type: "object",
            properties: {
              key: {
                type: "string",
                description: "存储的键"
              },
              value: {
                type: "string",
                description: "存储的值"
              }
            },
            required: ["key", "value"]
          }
        },
        {
          name: "get_value",
          description: "根据键获取存储的值",
          inputSchema: {
            type: "object",
            properties: {
              key: {
                type: "string",
                description: "要获取的键"
              }
            },
            required: ["key"]
          }
        },
        {
          name: "list_keys",
          description: "列出所有存储的键",
          inputSchema: {
            type: "object",
            properties: {}
          }
        },
        {
          name: "calculate_sum",
          description: "计算两个数字的和",
          inputSchema: {
            type: "object",
            properties: {
              a: {
                type: "number",
                description: "第一个数字"
              },
              b: {
                type: "number",
                description: "第二个数字"
              }
            },
            required: ["a", "b"]
          }
        }
      ]
    };
  });

  // 处理工具执行
  server.setRequestHandler(CallToolRequestSchema, async (request) => {
    const { name, arguments: args } = request.params;

    try {
      switch (name) {
        case "store_value": {
          const { key, value } = args as { key: string; value: string };
          const success = controller.storeData(key, value);
          return {
            content: [
              {
                type: "text",
                text: success ? `成功存储: ${key} = ${value}` : `存储失败: ${key}`
              }
            ]
          };
        }

        case "get_value": {
          const { key } = args as { key: string };
          const value = controller.getData(key);
          return {
            content: [
              {
                type: "text",
                text: value ? `获取成功: ${key} = ${value}` : `未找到键: ${key}`
              }
            ]
          };
        }

        case "list_keys": {
          const keys = controller.listKeys();
          return {
            content: [
              {
                type: "text",
                text: keys.length > 0 ? `所有键: ${keys.join(', ')}` : "没有存储任何数据"
              }
            ]
          };
        }

        case "calculate_sum": {
          const { a, b } = args as { a: number; b: number };
          const result = controller.calculateSum(a, b);
          return {
            content: [
              {
                type: "text",
                text: `计算结果: ${a} + ${b} = ${result}`
              }
            ]
          };
        }

        default:
          throw new Error(`未知工具: ${name}`);
      }
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `工具执行错误: ${error instanceof Error ? error.message : String(error)}`
          }
        ],
        isError: true
      };
    }
  });

  return server;
}

async function main(): Promise<void> {
  try {
    console.log("正在启动TypeScript MCP服务器...");

    const server = await createMCPServer();
    const transport = new StdioServerTransport();

    await server.connect(transport);
    console.log("TypeScript MCP服务器已启动并连接到stdio传输");

  } catch (error) {
    console.error("服务器启动失败:", error);
    process.exit(1);
  }
}

// 处理进程信号
process.on('SIGINT', () => {
  console.log('\n收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

// 启动服务器
main().catch((error) => {
  console.error("未处理的错误:", error);
  process.exit(1);
});
```

**TypeScript项目配置文件**:

**package.json**:
```json
{
  "name": "typescript-mcp-server",
  "version": "1.0.0",
  "description": "TypeScript MCP服务器示例",
  "main": "dist/server.js",
  "type": "module",
  "scripts": {
    "build": "tsc",
    "start": "node dist/server.js",
    "dev": "tsc && node dist/server.js",
    "clean": "rm -rf dist"
  },
  "dependencies": {
    "@modelcontextprotocol/sdk": "^0.5.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0"
  }
}
```

**tsconfig.json**:
```json
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "node",
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

### A.3 高级Python MCP服务器示例

```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级MCP服务器示例
演示复杂功能和最佳实践
"""

import asyncio
import json
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from fastmcp import FastMCP

@dataclass
class OperationResult:
    """操作结果数据类"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: float = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class AdvancedController:
    """高级控制器示例"""

    def __init__(self):
        self.operations_log = []
        self.config = {
            "max_operations": 1000,
            "default_timeout": 5.0
        }
        logger.info("高级控制器初始化完成")

    def execute_operation(self, operation: str, params: Dict[str, Any]) -> OperationResult:
        """执行操作"""
        try:
            # 记录操作
            self.operations_log.append({
                "operation": operation,
                "params": params,
                "timestamp": time.time()
            })

            # 模拟操作执行
            if operation == "calculate":
                result = self._calculate(params)
            elif operation == "process":
                result = self._process(params)
            else:
                return OperationResult(False, f"未知操作: {operation}")

            return OperationResult(True, "操作成功", result)

        except Exception as e:
            return OperationResult(False, f"操作失败: {str(e)}")

    def _calculate(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """计算操作"""
        a = params.get("a", 0)
        b = params.get("b", 0)
        operation = params.get("op", "add")

        if operation == "add":
            result = a + b
        elif operation == "multiply":
            result = a * b
        else:
            raise ValueError(f"不支持的计算操作: {operation}")

        return {"result": result, "operation": operation}

    def _process(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理操作"""
        data = params.get("data", [])
        action = params.get("action", "count")

        if action == "count":
            result = len(data)
        elif action == "sum":
            result = sum(data) if all(isinstance(x, (int, float)) for x in data) else 0
        else:
            raise ValueError(f"不支持的处理操作: {action}")

        return {"result": result, "action": action, "data_length": len(data)}

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_operations": len(self.operations_log),
            "recent_operations": self.operations_log[-10:] if self.operations_log else [],
            "config": self.config
        }

def create_advanced_mcp_server():
    """创建高级MCP服务器"""
    controller = AdvancedController()
    mcp = FastMCP("Advanced MCP Server")

    @mcp.tool()
    async def calculate(a: float, b: float, operation: str = "add") -> str:
        """执行数学计算

        Args:
            a: 第一个数字
            b: 第二个数字
            operation: 操作类型 (add, multiply)
        """
        try:
            params = {"a": a, "b": b, "op": operation}
            result = controller.execute_operation("calculate", params)

            if result.success:
                calc_result = result.data["result"]
                return f"计算结果: {a} {operation} {b} = {calc_result}"
            else:
                return f"计算失败: {result.message}"

        except Exception as e:
            logger.error(f"计算工具错误: {str(e)}")
            return f"计算工具错误: {str(e)}"

    @mcp.tool()
    async def process_data(data: List[float], action: str = "count") -> str:
        """处理数据列表

        Args:
            data: 要处理的数据列表
            action: 处理动作 (count, sum)
        """
        try:
            params = {"data": data, "action": action}
            result = controller.execute_operation("process", params)

            if result.success:
                process_result = result.data["result"]
                return f"处理结果: {action}({len(data)}个元素) = {process_result}"
            else:
                return f"处理失败: {result.message}"

        except Exception as e:
            logger.error(f"数据处理工具错误: {str(e)}")
            return f"数据处理工具错误: {str(e)}"

    @mcp.tool()
    async def get_server_stats() -> str:
        """获取服务器统计信息"""
        try:
            stats = controller.get_statistics()
            return json.dumps(stats, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"统计信息获取错误: {str(e)}")
            return f"统计信息获取错误: {str(e)}"

    return mcp

def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    logger.info("启动高级MCP服务器")
    mcp = create_advanced_mcp_server()
    mcp.run(transport="stdio")

if __name__ == "__main__":
    main()
```

## 附录B: 配置文件模板

### B.1 requirements.txt 模板

```
# MCP服务器核心依赖
fastmcp>=2.3.4

# 根据项目需求选择以下依赖

# 键盘/鼠标控制
pynput>=1.8.1
pyautogui>=0.9.54

# 剪贴板操作
pyperclip>=1.8.2

# Windows系统API (仅Windows)
pywin32>=310; platform_system=="Windows"

# 图像处理
Pillow>=9.0.0
opencv-python>=4.5.0

# 网络请求
requests>=2.28.0
aiohttp>=3.8.0

# 数据处理
pandas>=1.5.0
numpy>=1.21.0

# 配置文件处理
pyyaml>=6.0
toml>=0.10.2

# 测试框架
pytest>=7.0.0
pytest-asyncio>=0.21.0

# 代码质量
black>=22.0.0
flake8>=5.0.0
mypy>=0.991
```

### B.2 启动脚本模板

**Windows (start_mcp_server.bat)**:
```batch
@echo off
REM MCP服务器启动脚本
REM 请根据实际路径修改以下变量

SET PROJECT_PATH=D:\Your\Project\Path
SET VENV_PATH=%PROJECT_PATH%\venv
SET PYTHON_EXE=%VENV_PATH%\Scripts\python.exe
SET SERVER_SCRIPT=%PROJECT_PATH%\start_server.py

REM 检查文件是否存在
IF NOT EXIST "%PYTHON_EXE%" (
    echo 错误: Python解释器不存在: %PYTHON_EXE%
    pause
    exit /b 1
)

IF NOT EXIST "%SERVER_SCRIPT%" (
    echo 错误: 服务器脚本不存在: %SERVER_SCRIPT%
    pause
    exit /b 1
)

REM 切换到项目目录
cd /d "%PROJECT_PATH%"

REM 启动服务器，传递所有命令行参数
"%PYTHON_EXE%" "%SERVER_SCRIPT%" %*

REM 如果出错，暂停以查看错误信息
IF ERRORLEVEL 1 (
    echo 服务器启动失败，错误代码: %ERRORLEVEL%
    pause
)
```

**Linux/Mac (start_mcp_server.sh)**:
```bash
#!/bin/bash
# MCP服务器启动脚本
# 请根据实际路径修改以下变量

PROJECT_PATH="/path/to/your/project"
VENV_PATH="$PROJECT_PATH/venv"
PYTHON_EXE="$VENV_PATH/bin/python"
SERVER_SCRIPT="$PROJECT_PATH/start_server.py"

# 检查文件是否存在
if [ ! -f "$PYTHON_EXE" ]; then
    echo "错误: Python解释器不存在: $PYTHON_EXE"
    exit 1
fi

if [ ! -f "$SERVER_SCRIPT" ]; then
    echo "错误: 服务器脚本不存在: $SERVER_SCRIPT"
    exit 1
fi

# 切换到项目目录
cd "$PROJECT_PATH"

# 启动服务器，传递所有命令行参数
"$PYTHON_EXE" "$SERVER_SCRIPT" "$@"

# 检查退出状态
if [ $? -ne 0 ]; then
    echo "服务器启动失败，退出代码: $?"
    exit 1
fi
```

### B.3 VSCode配置模板

**settings.json**:
```json
{
    "python.defaultInterpreterPath": "./venv/Scripts/python.exe",
    "python.terminal.activateEnvironment": true,
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "files.encoding": "utf8",
    "files.autoSave": "afterDelay",
    "editor.formatOnSave": true
}
```

**launch.json**:
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "启动MCP服务器",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/start_server.py",
            "args": ["--debug"],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "启动MCP服务器 (TCP)",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/start_server.py",
            "args": ["--transport", "tcp", "--port", "8000", "--debug"],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}"
        }
    ]
}
```

## 附录C: 测试用例模板

### C.1 基础测试模板

```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP服务器测试模板
"""

import unittest
import asyncio
import json
from unittest.mock import Mock, patch, MagicMock
from your_controller import YourController
from start_server import create_mcp_server

class TestYourController(unittest.TestCase):
    """控制器测试类"""

    def setUp(self):
        """测试前准备"""
        self.controller = YourController()

    def tearDown(self):
        """测试后清理"""
        pass

    def test_basic_functionality(self):
        """测试基础功能"""
        result = self.controller.basic_operation("test")
        self.assertIsNotNone(result)
        self.assertIsInstance(result, str)

    def test_parameter_validation(self):
        """测试参数验证"""
        with self.assertRaises(ValueError):
            self.controller.basic_operation("")

        with self.assertRaises(TypeError):
            self.controller.basic_operation(None)

    @patch('your_module.external_dependency')
    def test_with_external_dependency(self, mock_dependency):
        """测试外部依赖"""
        mock_dependency.return_value = "mocked_result"
        result = self.controller.operation_with_dependency()
        self.assertEqual(result, "expected_result")
        mock_dependency.assert_called_once()

class TestMCPIntegration(unittest.TestCase):
    """MCP集成测试类"""

    def setUp(self):
        """测试前准备"""
        self.mcp = create_mcp_server()

    def test_tool_registration(self):
        """测试工具注册"""
        # 检查工具是否正确注册
        tools = self.mcp.list_tools()
        expected_tools = ["tool1", "tool2", "tool3"]
        for tool in expected_tools:
            self.assertIn(tool, [t.name for t in tools])

    async def test_tool_execution(self):
        """测试工具执行"""
        # 模拟工具调用
        result = await self.mcp.call_tool("test_tool", {"param": "value"})
        self.assertIsNotNone(result)
        self.assertIn("success", result.lower())

class TestErrorHandling(unittest.TestCase):
    """错误处理测试类"""

    def setUp(self):
        self.controller = YourController()

    def test_invalid_input_handling(self):
        """测试无效输入处理"""
        result = self.controller.handle_invalid_input("invalid")
        self.assertIn("错误", result)

    def test_timeout_handling(self):
        """测试超时处理"""
        with patch('time.sleep', side_effect=TimeoutError):
            result = self.controller.operation_with_timeout()
            self.assertIn("超时", result)

    def test_resource_cleanup(self):
        """测试资源清理"""
        # 模拟资源分配和清理
        self.controller.allocate_resources()
        self.assertTrue(self.controller.has_resources())

        self.controller.cleanup_resources()
        self.assertFalse(self.controller.has_resources())

def run_async_test(coro):
    """运行异步测试的辅助函数"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()

if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
```

### C.2 性能测试模板

```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP服务器性能测试
"""

import time
import asyncio
import statistics
from typing import List
from your_controller import YourController

class PerformanceTest:
    """性能测试类"""

    def __init__(self):
        self.controller = YourController()
        self.results = []

    def measure_execution_time(self, func, *args, **kwargs):
        """测量函数执行时间"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        return result, execution_time

    def run_performance_test(self, test_name: str, func, iterations: int = 100):
        """运行性能测试"""
        print(f"\n开始性能测试: {test_name}")
        times = []

        for i in range(iterations):
            _, exec_time = self.measure_execution_time(func)
            times.append(exec_time)

            if (i + 1) % 10 == 0:
                print(f"完成 {i + 1}/{iterations} 次测试")

        # 计算统计信息
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        std_dev = statistics.stdev(times) if len(times) > 1 else 0

        print(f"测试结果 - {test_name}:")
        print(f"  平均时间: {avg_time:.4f}s")
        print(f"  最小时间: {min_time:.4f}s")
        print(f"  最大时间: {max_time:.4f}s")
        print(f"  标准差: {std_dev:.4f}s")
        print(f"  总测试次数: {iterations}")

        return {
            "test_name": test_name,
            "avg_time": avg_time,
            "min_time": min_time,
            "max_time": max_time,
            "std_dev": std_dev,
            "iterations": iterations
        }

    def test_basic_operations(self):
        """测试基础操作性能"""
        def basic_op():
            return self.controller.basic_operation("test")

        return self.run_performance_test("基础操作", basic_op)

    def test_complex_operations(self):
        """测试复杂操作性能"""
        def complex_op():
            return self.controller.complex_operation(
                ["param1", "param2"],
                {"option": "value"}
            )

        return self.run_performance_test("复杂操作", complex_op)

    def run_all_tests(self):
        """运行所有性能测试"""
        print("=" * 50)
        print("MCP服务器性能测试")
        print("=" * 50)

        results = []
        results.append(self.test_basic_operations())
        results.append(self.test_complex_operations())

        print("\n" + "=" * 50)
        print("性能测试总结")
        print("=" * 50)

        for result in results:
            print(f"{result['test_name']}: 平均 {result['avg_time']:.4f}s")

        return results

if __name__ == '__main__':
    test = PerformanceTest()
    test.run_all_tests()
```

这份完整的MCP开发说明文档现在包含了：

1. **基础概念和架构设计**
2. **详细的实现指南**
3. **完整的示例代码**
4. **配置文件模板**
5. **测试策略和模板**
6. **最佳实践和常见问题解决**

文档涵盖了从项目规划到部署的完整开发流程，为未来的MCP服务器开发提供了全面的参考。每个部分都包含了实用的代码示例和详细的说明，确保开发者能够快速上手并开发出高质量的MCP服务器。

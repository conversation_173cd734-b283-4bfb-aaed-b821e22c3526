# -*- coding: utf-8 -*-
"""
目标跟踪机制
实现目标进度实时跟踪、目标达成判断逻辑、子目标动态调整和优先级管理
严格按照代码规范的单一职责原则
"""

import uuid
import time
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

from ..utils.logger import get_logger


class GoalStatus(Enum):
    """目标状态枚举"""
    PENDING = "pending"
    ACTIVE = "active"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"
    CANCELLED = "cancelled"


class GoalPriority(Enum):
    """目标优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


@dataclass
class GoalProgress:
    """目标进度数据"""
    completion_percentage: float = 0.0
    completed_steps: int = 0
    total_steps: int = 0
    estimated_remaining_time: Optional[float] = None
    last_update_time: datetime = field(default_factory=datetime.now)
    
    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.completion_percentage >= 100.0
    
    @property
    def steps_remaining(self) -> int:
        """剩余步骤数"""
        return max(0, self.total_steps - self.completed_steps)


@dataclass
class SubGoal:
    """子目标"""
    id: str
    parent_id: str
    title: str
    description: str
    priority: GoalPriority = GoalPriority.NORMAL
    status: GoalStatus = GoalStatus.PENDING
    progress: GoalProgress = field(default_factory=GoalProgress)
    dependencies: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    deadline: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def update_progress(self, percentage: float, steps_completed: int = None):
        """更新进度"""
        self.progress.completion_percentage = min(100.0, max(0.0, percentage))
        if steps_completed is not None:
            self.progress.completed_steps = steps_completed
        self.progress.last_update_time = datetime.now()
        self.updated_at = datetime.now()
        
        # 自动更新状态
        if self.progress.is_completed:
            self.status = GoalStatus.COMPLETED
        elif self.progress.completion_percentage > 0:
            self.status = GoalStatus.IN_PROGRESS


@dataclass
class Goal:
    """主目标"""
    id: str
    title: str
    description: str
    priority: GoalPriority = GoalPriority.NORMAL
    status: GoalStatus = GoalStatus.PENDING
    progress: GoalProgress = field(default_factory=GoalProgress)
    sub_goals: List[SubGoal] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    deadline: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_sub_goal(self, title: str, description: str, priority: GoalPriority = GoalPriority.NORMAL) -> str:
        """添加子目标"""
        sub_goal_id = str(uuid.uuid4())
        sub_goal = SubGoal(
            id=sub_goal_id,
            parent_id=self.id,
            title=title,
            description=description,
            priority=priority
        )
        self.sub_goals.append(sub_goal)
        self.updated_at = datetime.now()
        return sub_goal_id
    
    def get_sub_goal(self, sub_goal_id: str) -> Optional[SubGoal]:
        """获取子目标"""
        for sub_goal in self.sub_goals:
            if sub_goal.id == sub_goal_id:
                return sub_goal
        return None
    
    def update_overall_progress(self):
        """更新整体进度"""
        if not self.sub_goals:
            return
        
        total_progress = sum(sub_goal.progress.completion_percentage for sub_goal in self.sub_goals)
        self.progress.completion_percentage = total_progress / len(self.sub_goals)
        self.progress.completed_steps = sum(1 for sub_goal in self.sub_goals if sub_goal.status == GoalStatus.COMPLETED)
        self.progress.total_steps = len(self.sub_goals)
        self.progress.last_update_time = datetime.now()
        self.updated_at = datetime.now()
        
        # 自动更新状态
        if self.progress.is_completed:
            self.status = GoalStatus.COMPLETED
        elif self.progress.completion_percentage > 0:
            self.status = GoalStatus.IN_PROGRESS


class GoalTracker:
    """目标跟踪器"""
    
    def __init__(self):
        """初始化目标跟踪器"""
        self.logger = get_logger(self.__class__.__name__)
        
        # 目标存储
        self.goals: Dict[str, Goal] = {}
        self.active_goal_id: Optional[str] = None
        
        # 跟踪配置
        self.config = {
            "auto_update_interval": 5.0,  # 自动更新间隔（秒）
            "progress_threshold": 0.01,   # 进度变化阈值
            "deadline_warning_hours": 24,  # 截止时间警告提前小时数
        }
        
        # 事件回调
        self.progress_callbacks: List[Callable] = []
        self.completion_callbacks: List[Callable] = []
        self.deadline_callbacks: List[Callable] = []
        
        # 统计信息
        self.stats = {
            "total_goals": 0,
            "completed_goals": 0,
            "failed_goals": 0,
            "average_completion_time": 0.0,
        }
    
    def create_goal(self, title: str, description: str, priority: GoalPriority = GoalPriority.NORMAL, 
                   deadline: Optional[datetime] = None) -> str:
        """创建新目标"""
        goal_id = str(uuid.uuid4())
        
        goal = Goal(
            id=goal_id,
            title=title,
            description=description,
            priority=priority,
            deadline=deadline
        )
        
        self.goals[goal_id] = goal
        self.stats["total_goals"] += 1
        
        self.logger.info(f"创建新目标: {title} (ID: {goal_id})")
        return goal_id
    
    def set_active_goal(self, goal_id: str) -> bool:
        """设置活动目标"""
        if goal_id not in self.goals:
            self.logger.error(f"目标不存在: {goal_id}")
            return False
        
        self.active_goal_id = goal_id
        goal = self.goals[goal_id]
        goal.status = GoalStatus.ACTIVE
        goal.updated_at = datetime.now()
        
        self.logger.info(f"设置活动目标: {goal.title}")
        return True
    
    def get_active_goal(self) -> Optional[Goal]:
        """获取当前活动目标"""
        if self.active_goal_id and self.active_goal_id in self.goals:
            return self.goals[self.active_goal_id]
        return None
    
    def update_goal_progress(self, goal_id: str, percentage: float, 
                           steps_completed: int = None) -> bool:
        """更新目标进度"""
        if goal_id not in self.goals:
            self.logger.error(f"目标不存在: {goal_id}")
            return False
        
        goal = self.goals[goal_id]
        old_percentage = goal.progress.completion_percentage
        
        goal.progress.completion_percentage = min(100.0, max(0.0, percentage))
        if steps_completed is not None:
            goal.progress.completed_steps = steps_completed
        goal.progress.last_update_time = datetime.now()
        goal.updated_at = datetime.now()
        
        # 自动更新状态
        if goal.progress.is_completed and goal.status != GoalStatus.COMPLETED:
            goal.status = GoalStatus.COMPLETED
            self._handle_goal_completion(goal)
        elif goal.progress.completion_percentage > 0 and goal.status == GoalStatus.PENDING:
            goal.status = GoalStatus.IN_PROGRESS
        
        # 触发进度回调
        progress_change = abs(goal.progress.completion_percentage - old_percentage)
        if progress_change >= self.config["progress_threshold"]:
            self._notify_progress_callbacks(goal, old_percentage)
        
        self.logger.debug(f"更新目标进度: {goal.title} -> {percentage}%")
        return True
    
    def update_sub_goal_progress(self, goal_id: str, sub_goal_id: str, 
                               percentage: float, steps_completed: int = None) -> bool:
        """更新子目标进度"""
        if goal_id not in self.goals:
            self.logger.error(f"目标不存在: {goal_id}")
            return False
        
        goal = self.goals[goal_id]
        sub_goal = goal.get_sub_goal(sub_goal_id)
        
        if not sub_goal:
            self.logger.error(f"子目标不存在: {sub_goal_id}")
            return False
        
        old_percentage = sub_goal.progress.completion_percentage
        sub_goal.update_progress(percentage, steps_completed)
        
        # 更新主目标进度
        goal.update_overall_progress()
        
        # 检查子目标完成
        if sub_goal.progress.is_completed and sub_goal.status == GoalStatus.COMPLETED:
            self._handle_sub_goal_completion(goal, sub_goal)
        
        self.logger.debug(f"更新子目标进度: {sub_goal.title} -> {percentage}%")
        return True
    
    def add_sub_goal(self, goal_id: str, title: str, description: str, 
                    priority: GoalPriority = GoalPriority.NORMAL) -> Optional[str]:
        """添加子目标"""
        if goal_id not in self.goals:
            self.logger.error(f"目标不存在: {goal_id}")
            return None
        
        goal = self.goals[goal_id]
        sub_goal_id = goal.add_sub_goal(title, description, priority)
        
        # 重新计算总步骤数
        goal.progress.total_steps = len(goal.sub_goals)
        goal.update_overall_progress()
        
        self.logger.info(f"添加子目标: {title} (ID: {sub_goal_id})")
        return sub_goal_id
    
    def complete_goal(self, goal_id: str, success: bool = True) -> bool:
        """完成目标"""
        if goal_id not in self.goals:
            self.logger.error(f"目标不存在: {goal_id}")
            return False
        
        goal = self.goals[goal_id]
        
        if success:
            goal.status = GoalStatus.COMPLETED
            goal.progress.completion_percentage = 100.0
            self.stats["completed_goals"] += 1
        else:
            goal.status = GoalStatus.FAILED
            self.stats["failed_goals"] += 1
        
        goal.updated_at = datetime.now()
        
        # 计算完成时间
        completion_time = (goal.updated_at - goal.created_at).total_seconds()
        self._update_average_completion_time(completion_time)
        
        # 触发完成回调
        self._notify_completion_callbacks(goal, success)
        
        # 如果是活动目标，清除活动状态
        if self.active_goal_id == goal_id:
            self.active_goal_id = None
        
        self.logger.info(f"目标完成: {goal.title} ({'成功' if success else '失败'})")
        return True
    
    def get_goal_status(self, goal_id: str) -> Optional[Dict[str, Any]]:
        """获取目标状态"""
        if goal_id not in self.goals:
            return None
        
        goal = self.goals[goal_id]
        return {
            "id": goal.id,
            "title": goal.title,
            "status": goal.status.value,
            "priority": goal.priority.value,
            "progress": {
                "percentage": goal.progress.completion_percentage,
                "completed_steps": goal.progress.completed_steps,
                "total_steps": goal.progress.total_steps,
                "is_completed": goal.progress.is_completed
            },
            "sub_goals": [
                {
                    "id": sub_goal.id,
                    "title": sub_goal.title,
                    "status": sub_goal.status.value,
                    "progress": sub_goal.progress.completion_percentage
                }
                for sub_goal in goal.sub_goals
            ],
            "created_at": goal.created_at.isoformat(),
            "updated_at": goal.updated_at.isoformat(),
            "deadline": goal.deadline.isoformat() if goal.deadline else None
        }
    
    def check_deadlines(self) -> List[Dict[str, Any]]:
        """检查截止时间"""
        warnings = []
        current_time = datetime.now()
        warning_threshold = timedelta(hours=self.config["deadline_warning_hours"])
        
        for goal in self.goals.values():
            if goal.deadline and goal.status not in [GoalStatus.COMPLETED, GoalStatus.FAILED, GoalStatus.CANCELLED]:
                time_remaining = goal.deadline - current_time
                
                if time_remaining <= timedelta(0):
                    # 已过期
                    warnings.append({
                        "goal_id": goal.id,
                        "title": goal.title,
                        "type": "overdue",
                        "time_overdue": abs(time_remaining.total_seconds())
                    })
                elif time_remaining <= warning_threshold:
                    # 即将到期
                    warnings.append({
                        "goal_id": goal.id,
                        "title": goal.title,
                        "type": "warning",
                        "time_remaining": time_remaining.total_seconds()
                    })
        
        if warnings:
            self._notify_deadline_callbacks(warnings)
        
        return warnings
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        active_goals = sum(1 for goal in self.goals.values() 
                          if goal.status in [GoalStatus.ACTIVE, GoalStatus.IN_PROGRESS])
        
        return {
            **self.stats,
            "active_goals": active_goals,
            "total_sub_goals": sum(len(goal.sub_goals) for goal in self.goals.values()),
            "success_rate": (self.stats["completed_goals"] / max(1, self.stats["total_goals"])) * 100
        }
    
    def _handle_goal_completion(self, goal: Goal):
        """处理目标完成"""
        self.logger.info(f"目标已完成: {goal.title}")
        # 这里可以添加完成后的处理逻辑
    
    def _handle_sub_goal_completion(self, goal: Goal, sub_goal: SubGoal):
        """处理子目标完成"""
        self.logger.debug(f"子目标已完成: {sub_goal.title}")
        # 这里可以添加子目标完成后的处理逻辑
    
    def _update_average_completion_time(self, completion_time: float):
        """更新平均完成时间"""
        completed_count = self.stats["completed_goals"]
        if completed_count > 1:
            total_time = self.stats["average_completion_time"] * (completed_count - 1) + completion_time
            self.stats["average_completion_time"] = total_time / completed_count
        else:
            self.stats["average_completion_time"] = completion_time
    
    def _notify_progress_callbacks(self, goal: Goal, old_percentage: float):
        """通知进度回调"""
        for callback in self.progress_callbacks:
            try:
                callback(goal, old_percentage)
            except Exception as e:
                self.logger.error(f"进度回调异常: {e}")
    
    def _notify_completion_callbacks(self, goal: Goal, success: bool):
        """通知完成回调"""
        for callback in self.completion_callbacks:
            try:
                callback(goal, success)
            except Exception as e:
                self.logger.error(f"完成回调异常: {e}")
    
    def _notify_deadline_callbacks(self, warnings: List[Dict[str, Any]]):
        """通知截止时间回调"""
        for callback in self.deadline_callbacks:
            try:
                callback(warnings)
            except Exception as e:
                self.logger.error(f"截止时间回调异常: {e}")
    
    def register_progress_callback(self, callback: Callable):
        """注册进度回调"""
        self.progress_callbacks.append(callback)
    
    def register_completion_callback(self, callback: Callable):
        """注册完成回调"""
        self.completion_callbacks.append(callback)
    
    def register_deadline_callback(self, callback: Callable):
        """注册截止时间回调"""
        self.deadline_callbacks.append(callback)

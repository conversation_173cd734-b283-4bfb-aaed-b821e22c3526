"""
轨迹记录和基于轨迹的鼠标移动演示。
"""

import sys
import os
import time
import random

# 添加src目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入鼠标控制器和记录器
from src.mouse_controller import MouseController
from src.mouse_recorder import MouseRecorder

def record_trajectories():
    """记录鼠标轨迹。"""
    print("=== 鼠标轨迹记录 ===")
    print("将记录30秒的鼠标移动轨迹")
    print("请在这段时间内自然地移动鼠标，尝试各种移动模式")
    print("按Enter键开始记录...")
    input()

    recorder = MouseRecorder()
    recorder.start_recording(duration=30)

    # 等待记录完成
    while recorder.recording:
        print("正在记录...", end="\r")
        time.sleep(1)

    print("\n记录完成！轨迹已保存")
    return True

def demonstrate_trajectory_movement():
    """演示基于轨迹的鼠标移动。"""
    print("\n=== 基于轨迹的鼠标移动演示 ===")

    # 创建鼠标控制器
    mouse = MouseController()

    # 检查是否有可用轨迹
    if not mouse.trajectories:
        print("没有可用的轨迹记录，请先运行记录功能")
        return False

    print(f"找到 {len(mouse.trajectories)} 个轨迹记录")
    print("将演示基于轨迹的鼠标移动")
    print("按Enter键开始演示...")
    input()

    # 获取屏幕尺寸
    screen_size = mouse.get_screen_size()
    width, height = screen_size["width"], screen_size["height"]

    # 演示几次随机位置之间的移动
    for i in range(5):
        # 随机选择起点和终点
        start_x = random.randint(100, width - 100)
        start_y = random.randint(100, height - 100)
        end_x = random.randint(100, width - 100)
        end_y = random.randint(100, height - 100)

        # 先移动到起点
        print(f"\n移动 {i+1}: 从 ({start_x}, {start_y}) 到 ({end_x}, {end_y})")
        print("首先移动到起点...")
        mouse.move_mouse(start_x, start_y, duration=0.5)
        time.sleep(1)

        # 使用轨迹移动到终点
        print("现在使用轨迹移动到终点...")
        result = mouse.move_mouse(end_x, end_y)
        print(result["message"])
        time.sleep(1)

    print("\n演示完成！")
    return True

def demonstrate_distance_movement():
    """演示不同距离的鼠标移动。"""
    print("\n=== 不同距离的鼠标移动演示 ===")

    # 创建鼠标控制器
    mouse = MouseController()

    # 获取屏幕尺寸
    screen_size = mouse.get_screen_size()
    width, height = screen_size["width"], screen_size["height"]

    # 屏幕中心
    center_x = width // 2
    center_y = height // 2

    print("将演示不同距离的鼠标移动")
    print("按Enter键开始演示...")
    input()

    # 先移动到屏幕中心
    print("首先移动到屏幕中心...")
    mouse.move_mouse(center_x, center_y, duration=0.5)
    time.sleep(1)

    # 测试不同距离的移动
    distances = [
        ("短距离 (100像素)", 100),
        ("中等距离 (300像素)", 300),
        ("长距离 (600像素)", 600),
        ("超长距离 (1000像素)", 1000)
    ]

    for desc, dist in distances:
        # 生成四个方向的目标点
        directions = [
            ("右", center_x + dist, center_y),
            ("下", center_x, center_y + dist),
            ("左", center_x - dist, center_y),
            ("上", center_x, center_y - dist)
        ]

        for dir_desc, target_x, target_y in directions:
            # 确保目标点在屏幕范围内
            target_x = max(50, min(target_x, width - 50))
            target_y = max(50, min(target_y, height - 50))

            # 先回到中心
            print(f"\n移动到中心点...")
            mouse.move_mouse(center_x, center_y, duration=0.5)
            time.sleep(1)

            # 移动到目标点
            print(f"测试{desc}移动 - 向{dir_desc}方向...")
            result = mouse.move_mouse(target_x, target_y)
            print(result["message"])
            time.sleep(1)

    # 回到中心
    mouse.move_mouse(center_x, center_y, duration=0.5)
    print("\n演示完成！")
    return True

def main():
    """主函数。"""
    print("轨迹记录和基于轨迹的鼠标移动演示")
    print("-" * 50)

    while True:
        print("\n请选择操作:")
        print("1. 记录鼠标轨迹")
        print("2. 演示基于轨迹的鼠标移动")
        print("3. 测试不同距离的鼠标移动")
        print("4. 退出")

        choice = input("请输入选项 (1-4): ")

        if choice == "1":
            record_trajectories()
        elif choice == "2":
            demonstrate_trajectory_movement()
        elif choice == "3":
            demonstrate_distance_movement()
        elif choice == "4":
            print("退出程序")
            break
        else:
            print("无效选项，请重新输入")

if __name__ == "__main__":
    main()

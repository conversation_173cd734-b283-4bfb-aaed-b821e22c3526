# QYuan 环境配置示例
# 复制此文件为 .env 并填入你的配置

# LLM配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4

# Claude配置（可选）
CLAUDE_API_KEY=your_claude_api_key_here

# 本地LLM配置（可选）
LOCAL_LLM_URL=http://localhost:11434

# 数据库配置
DATABASE_PATH=data/qyuan.db

# API服务配置
API_HOST=0.0.0.0
API_PORT=8000
API_KEY=your_api_key_here

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/qyuan.log

# MCP服务配置
MCP_MOUSE_ENABLED=true
MCP_KEYBOARD_ENABLED=true
MCP_VISION_ENABLED=true

# MCP服务配置
MCP_MOUSE_PORT=8001
MCP_KEYBOARD_PORT=8002
MCP_VISION_PORT=8003

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI元素识别功能测试脚本

本脚本测试UI元素识别模块的所有功能，包括：
1. Win32GUI后端
2. PyWinAuto后端
3. 窗口查找和操作
4. 元素查找和属性获取
"""

import os
import sys
import unittest
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入被测模块
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 直接导入ui_automation.py模块
import importlib.util
spec = importlib.util.spec_from_file_location(
    "ui_automation_module",
    os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "src", "ui_automation.py")
)
ui_automation_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(ui_automation_module)
UIAutomation = ui_automation_module.UIAutomation

class UIAutomationTest(unittest.TestCase):
    """UI元素识别功能测试类"""

    def setUp(self):
        """测试前的准备工作"""
        # 创建UI自动化对象
        self.ui_win32 = UIAutomation(backend="win32gui")
        try:
            self.ui_pywinauto = UIAutomation(backend="pywinauto")
        except Exception as e:
            print(f"PyWinAuto后端初始化失败: {e}")
            self.ui_pywinauto = None

    def test_win32gui_backend(self):
        """测试Win32GUI后端"""
        # 获取前台窗口
        window = self.ui_win32.get_foreground_window()

        # 检查返回值
        self.assertIsNotNone(window, "前台窗口不应为None")

        # 获取窗口信息
        window_info = self.ui_win32.ui_engine.get_window_info(window)

        # 打印窗口信息
        print(f"前台窗口: {window_info['title']} ({window_info['class_name']})")
        print(f"位置: {window_info['rectangle']}")
        print(f"状态: {window_info['state']}")

    def test_pywinauto_backend(self):
        """测试PyWinAuto后端"""
        if self.ui_pywinauto is None:
            self.skipTest("PyWinAuto后端不可用")
            return

        # 获取前台窗口
        try:
            window = self.ui_pywinauto.get_foreground_window()

            # 检查返回值
            if window is None:
                self.skipTest("无法获取前台窗口，测试跳过")
                return

            # 打印窗口信息
            print(f"前台窗口: {window.window_text()}")
        except Exception as e:
            print(f"获取前台窗口时出错: {e}")
            self.skipTest(f"获取前台窗口测试跳过: {e}")

    def test_window_find(self):
        """测试窗口查找功能"""
        # 查找一个常见的窗口（如"Program Manager"）
        windows = self.ui_win32.get_window_by_title("Program Manager")

        # 检查返回值
        self.assertIsNotNone(windows, "未找到窗口列表")
        self.assertGreater(len(windows), 0, "未找到Program Manager窗口")

        # 获取第一个窗口
        window = windows[0]

        # 获取窗口信息
        window_info = self.ui_win32.ui_engine.get_window_info(window)

        # 打印窗口信息
        print(f"找到窗口: {window_info['title']} ({window_info['class_name']})")
        print(f"位置: {window_info['rectangle']}")
        print(f"状态: {window_info['state']}")

    def test_window_operations(self):
        """测试窗口操作功能"""
        # 获取前台窗口
        window = self.ui_win32.get_foreground_window()

        # 检查返回值
        self.assertIsNotNone(window, "前台窗口不应为None")

        # 获取窗口信息
        window_info = self.ui_win32.ui_engine.get_window_info(window)
        print(f"操作窗口: {window_info['title']}")

        # 测试激活窗口
        result = self.ui_win32.activate_window(window)
        self.assertTrue(result, "激活窗口应成功")
        print("激活窗口成功")

        # 测试最大化窗口
        if not window_info['state']['maximized']:
            result = self.ui_win32.maximize_window(window)
            self.assertTrue(result, "最大化窗口应成功")
            print("最大化窗口成功")
            time.sleep(1)  # 等待窗口状态变化

        # 测试最小化窗口
        result = self.ui_win32.minimize_window(window)
        self.assertTrue(result, "最小化窗口应成功")
        print("最小化窗口成功")
        time.sleep(1)  # 等待窗口状态变化

        # 测试还原窗口
        result = self.ui_win32.restore_window(window)
        self.assertTrue(result, "还原窗口应成功")
        print("还原窗口成功")
        time.sleep(1)  # 等待窗口状态变化

        # 测试调整窗口大小
        if not window_info['state']['maximized']:
            result = self.ui_win32.resize_window(window, 800, 600)
            self.assertTrue(result, "调整窗口大小应成功")
            print("调整窗口大小成功")
            time.sleep(1)  # 等待窗口状态变化

        # 测试移动窗口
        if not window_info['state']['maximized']:
            result = self.ui_win32.move_window(window, 100, 100)
            self.assertTrue(result, "移动窗口应成功")
            print("移动窗口成功")
            time.sleep(1)  # 等待窗口状态变化

    def test_element_find(self):
        """测试元素查找功能"""
        if self.ui_pywinauto is None:
            self.skipTest("PyWinAuto后端不可用")
            return

        # 获取前台窗口
        try:
            window = self.ui_pywinauto.get_foreground_window()

            # 检查返回值
            if window is None:
                self.skipTest("无法获取前台窗口，测试跳过")
                return

            # 获取窗口中的按钮元素
            elements = self.ui_pywinauto.get_ui_elements(window, element_type="Button", depth=3, visible_only=True)

            # 检查返回值
            self.assertIsNotNone(elements, "元素列表不应为None")
            self.assertIsInstance(elements, list, "元素列表应为列表")

            # 打印元素信息
            print(f"找到{len(elements)}个按钮元素")
            for i, element in enumerate(elements[:3]):  # 只打印前3个元素
                print(f"{i+1}. 名称: {element['name']}")
                print(f"   类型: {element['control_type']}")
                print(f"   位置: {element['rectangle']}")
                print(f"   中心点: {element['center']}")
                print(f"   启用状态: {element['enabled']}")
                print(f"   可见状态: {element['visible']}")
                print("-" * 50)
        except Exception as e:
            print(f"获取UI元素时出错: {e}")
            self.skipTest(f"获取UI元素测试跳过: {e}")

    def test_element_find_by_text(self):
        """测试通过文本查找元素功能"""
        if self.ui_pywinauto is None:
            self.skipTest("PyWinAuto后端不可用")
            return

        # 获取前台窗口
        try:
            window = self.ui_pywinauto.get_foreground_window()

            # 检查返回值
            if window is None:
                self.skipTest("无法获取前台窗口，测试跳过")
                return

            # 通过文本查找元素（尝试查找常见的按钮文本）
            # 常见的按钮文本列表
            button_texts = ["OK", "Cancel", "Yes", "No", "Close", "Minimize", "Maximize", "File", "Edit", "View", "Help"]

            # 尝试查找这些文本
            for text in button_texts:
                try:
                    element = self.ui_pywinauto.find_element_by_text(text, element_type="Button", window=window, timeout=1)
                    if element:
                        # 找到了元素
                        print(f"找到文本为'{text}'的按钮元素")
                        print(f"名称: {element['name']}")
                        print(f"类型: {element['control_type']}")
                        print(f"位置: {element['rectangle']}")
                        print(f"中心点: {element['center']}")
                        print(f"启用状态: {element['enabled']}")
                        print(f"可见状态: {element['visible']}")
                        print("-" * 50)
                        break
                except Exception:
                    continue

            # 如果没有找到任何元素，也不算失败
            print("通过文本查找元素测试完成")
        except Exception as e:
            print(f"通过文本查找元素时出错: {e}")
            self.skipTest(f"通过文本查找元素测试跳过: {e}")

    def test_element_properties(self):
        """测试元素属性获取功能"""
        if self.ui_pywinauto is None:
            self.skipTest("PyWinAuto后端不可用")
            return

        # 获取前台窗口
        try:
            window = self.ui_pywinauto.get_foreground_window()

            # 检查返回值
            if window is None:
                self.skipTest("无法获取前台窗口，测试跳过")
                return

            # 获取窗口中的元素
            # 尝试获取不同类型的元素
            element_types = ["Button", "Edit", "ComboBox", "CheckBox", "RadioButton", "ListBox", "TreeView", "TabControl", "ToolBar", "StatusBar"]

            for element_type in element_types:
                try:
                    elements = self.ui_pywinauto.get_ui_elements(window, element_type=element_type, depth=3, visible_only=True)
                    if elements and len(elements) > 0:
                        # 找到了元素
                        print(f"找到{len(elements)}个{element_type}元素")
                        element = elements[0]  # 取第一个元素

                        # 检查元素的属性
                        expected_properties = ["name", "control_type", "rectangle", "center", "enabled", "visible"]
                        for prop in expected_properties:
                            self.assertIn(prop, element, f"元素应包含{prop}属性")

                        # 打印元素的属性
                        print(f"元素属性:")
                        for key, value in element.items():
                            print(f"   {key}: {value}")
                        print("-" * 50)
                        break
                except Exception:
                    continue

            # 如果没有找到任何元素，也不算失败
            print("元素属性获取测试完成")
        except Exception as e:
            print(f"获取元素属性时出错: {e}")
            self.skipTest(f"获取元素属性测试跳过: {e}")

    def tearDown(self):
        """测试后的清理工作"""
        # 这里可以添加清理代码
        pass

if __name__ == "__main__":
    unittest.main()

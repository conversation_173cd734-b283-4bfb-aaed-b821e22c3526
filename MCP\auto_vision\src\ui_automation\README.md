# UI自动化模块

本模块提供UI元素识别功能，基于Windows UI Automation API。支持两种后端实现：pywinauto和win32gui。其中Win32GUI负责窗口级操作，PyWinAuto负责窗口内元素识别。

## 模块结构

- `__init__.py`: 包初始化文件，导出主要的类和接口
- `ui_automation_base.py`: 定义UI自动化的基础接口类，作为所有后端实现的基类
- `ui_automation_factory.py`: 提供创建不同后端UI自动化对象的工厂类
- `ui_automation_pywinauto.py`: 实现基于PyWinAuto的UI元素识别功能
- `ui_automation_win32.py`: 实现基于Win32GUI的UI元素识别功能

## 使用方法

```python
from src.ui_automation import UIAutomation

# 创建UI自动化对象，默认使用win32gui后端
ui = UIAutomation()

# 或者指定后端
# ui = UIAutomation(backend="win32gui")  # 窗口级操作
# ui = UIAutomation(backend="pywinauto")  # 窗口内元素识别

# 获取前台窗口
window = ui.get_foreground_window()

# 通过标题获取窗口
window = ui.get_window_by_title("记事本")

# 获取UI元素（必须指定元素类型）
elements = ui.get_ui_elements(window, element_type="Button", depth=3, visible_only=True)

# 通过文本查找元素（必须指定元素类型）
properties = ui.find_element_by_text("文件", element_type="Button")

# 获取元素的坐标信息
print(f"元素中心点坐标: {properties['center']['x']}, {properties['center']['y']}")
print(f"元素矩形区域: {properties['rectangle']}")

# 窗口操作
ui.activate_window(window)  # 激活窗口
ui.maximize_window(window)  # 最大化窗口
ui.minimize_window(window)  # 最小化窗口
ui.restore_window(window)   # 还原窗口
ui.resize_window(window, 800, 600)  # 调整窗口大小
ui.move_window(window, 100, 100)    # 移动窗口
```

## 后端特点

### PyWinAuto

基于[pywinauto](https://github.com/pywinauto/pywinauto)库，专注于窗口内控件的识别，提供元素坐标信息。同时支持窗口级别的操作（激活、最大化、最小化等）。

### Win32GUI

基于Win32GUI API，专注于窗口级别的操作，提供窗口的查找、激活、最大化、最小化、调整大小、移动等功能。不深入到应用程序内部UI元素。

## 依赖

- pywinauto (可选): `pip install pywinauto`
- pywin32 (必需): `pip install pywin32`

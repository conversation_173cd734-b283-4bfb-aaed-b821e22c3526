# QYuan开发环境搭建指南

## 环境要求

### 硬件要求
- **CPU**: Intel i5 8代以上 或 AMD Ryzen 5 3600以上
- **内存**: 16GB以上（推荐32GB）
- **存储**: 100GB以上可用空间（SSD推荐）
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Windows 10/11 (64位)
- **Python**: 3.11或更高版本
- **Node.js**: 18.x或更高版本
- **Git**: 最新版本
- **Docker**: 最新版本（可选，用于容器化部署）

## 基础环境安装

### 1. Python环境配置
```bash
# 下载并安装Python 3.11+
# 从 https://www.python.org/downloads/ 下载

# 验证安装
python --version
pip --version

# 升级pip
python -m pip install --upgrade pip

# 安装虚拟环境工具
pip install virtualenv
```

### 2. Node.js环境配置
```bash
# 下载并安装Node.js 18+
# 从 https://nodejs.org/ 下载

# 验证安装
node --version
npm --version

# 安装yarn（可选）
npm install -g yarn
```

### 3. Git配置
```bash
# 配置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 配置编辑器
git config --global core.editor "code --wait"
```

## 数据库环境搭建

### 1. PostgreSQL安装配置
```bash
# 下载并安装PostgreSQL 15+
# 从 https://www.postgresql.org/download/windows/ 下载

# 创建QYuan数据库
psql -U postgres
CREATE DATABASE qyuan_db;
CREATE USER qyuan_user WITH PASSWORD 'qyuan_password';
GRANT ALL PRIVILEGES ON DATABASE qyuan_db TO qyuan_user;
\q
```

### 2. Qdrant向量数据库
```bash
# 使用Docker安装Qdrant
docker pull qdrant/qdrant
docker run -p 6333:6333 -p 6334:6334 -d --name qdrant qdrant/qdrant

# 或者下载二进制文件
# 从 https://github.com/qdrant/qdrant/releases 下载
```

### 3. Redis缓存
```bash
# 使用Docker安装Redis
docker pull redis:alpine
docker run -p 6379:6379 -d --name redis redis:alpine

# 或者使用Windows版本
# 从 https://github.com/microsoftarchive/redis/releases 下载
```

## 项目环境配置

### 1. 克隆项目
```bash
# 克隆项目到本地
git clone <repository-url> QYuan
cd QYuan
```

### 2. Python虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装项目依赖
pip install -r requirements.txt
```

### 3. 前端环境
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install
# 或者使用yarn
yarn install
```

## 配置文件设置

### 1. 环境变量配置
创建 `.env` 文件：
```env
# 数据库配置
DATABASE_URL=postgresql://qyuan_user:qyuan_password@localhost:5432/qyuan_db
REDIS_URL=redis://localhost:6379
QDRANT_URL=http://localhost:6333

# LLM配置
LLM_API_BASE=https://xiaoai.plus
LLM_API_KEY=sk-oQx9XqkIHyMFabGTFByNVsxZHw1q4YpTvFdIQoupRkEVjDd3
LLM_MODEL=gpt-4.1

# 应用配置
DEBUG=true
LOG_LEVEL=INFO
SECRET_KEY=your-secret-key-here

# MCP服务配置
MCP_MOUSE_PORT=8001
MCP_KEYBOARD_PORT=8002
MCP_VISION_PORT=8003
```

### 2. 数据库初始化
```bash
# 运行数据库迁移
python manage.py migrate

# 创建初始数据
python manage.py init_data
```

### 3. MCP服务配置
```bash
# 启动MCP服务
cd MCP/auto_mouse
python start_server.py --port 8001 &

cd ../auto_keyboard
python start_server.py --port 8002 &

cd ../auto_vision
python start_server.py --port 8003 &
```

## 开发工具配置

### 1. VSCode配置
推荐安装的扩展：
- Python
- Pylance
- Black Formatter
- autoDocstring
- GitLens
- Thunder Client
- Docker

创建 `.vscode/settings.json`：
```json
{
    "python.defaultInterpreterPath": "./venv/Scripts/python.exe",
    "python.formatting.provider": "black",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "editor.formatOnSave": true,
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true
    }
}
```

### 2. 调试配置
创建 `.vscode/launch.json`：
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "QYuan Main",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/main.py",
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env"
        },
        {
            "name": "QYuan API Server",
            "type": "python",
            "request": "launch",
            "module": "uvicorn",
            "args": ["qyuan.api:app", "--reload", "--host", "0.0.0.0", "--port", "8000"],
            "envFile": "${workspaceFolder}/.env"
        }
    ]
}
```

## 验证安装

### 1. 基础环境验证
```bash
# 验证Python环境
python -c "import sys; print(sys.version)"

# 验证数据库连接
python -c "import psycopg2; print('PostgreSQL连接正常')"

# 验证Redis连接
python -c "import redis; r=redis.Redis(); r.ping(); print('Redis连接正常')"

# 验证Qdrant连接
curl http://localhost:6333/health
```

### 2. LLM服务验证
```bash
# 测试LLM API连接
python -c "
import openai
openai.api_base = 'https://xiaoai.plus'
openai.api_key = 'sk-oQx9XqkIHyMFabGTFByNVsxZHw1q4YpTvFdIQoupRkEVjDd3'
response = openai.ChatCompletion.create(
    model='gpt-4.1',
    messages=[{'role': 'user', 'content': 'Hello'}]
)
print('LLM服务连接正常')
"
```

### 3. MCP服务验证
```bash
# 测试MCP服务
python -c "
import requests
response = requests.get('http://localhost:8001/health')
print(f'Mouse MCP: {response.status_code}')
response = requests.get('http://localhost:8002/health')
print(f'Keyboard MCP: {response.status_code}')
response = requests.get('http://localhost:8003/health')
print(f'Vision MCP: {response.status_code}')
"
```

## 常见问题解决

### 1. Python依赖问题
```bash
# 清理pip缓存
pip cache purge

# 重新安装依赖
pip install --no-cache-dir -r requirements.txt
```

### 2. 数据库连接问题
- 检查PostgreSQL服务是否启动
- 验证用户名密码是否正确
- 检查防火墙设置

### 3. MCP服务启动问题
- 检查端口是否被占用
- 验证Python虚拟环境是否激活
- 查看服务日志排查错误

### 4. LLM API问题
- 验证网络连接
- 检查API Key是否正确
- 确认服务地址是否可访问

## 下一步

环境搭建完成后，请参考：
- `核心框架实现方案.md` - 开始核心代码开发
- `数据库设计与实现.md` - 实现数据库结构
- `API接口设计.md` - 开发API接口

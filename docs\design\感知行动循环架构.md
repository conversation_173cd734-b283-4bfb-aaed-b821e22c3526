# 感知-行动循环架构

## 核心理念：从线性执行到循环反馈

### 传统AI的问题
传统的任务分解是基于"完美世界"假设：
- 假设每一步都会成功执行
- 假设环境状态是可预测的
- 假设不会有意外情况发生

但现实世界充满了不确定性：
- 鼠标点击可能偏移
- 网络可能延迟
- 应用程序可能卡顿
- 输入法可能切换
- 焦点可能丢失

### QYuan的解决方案：感知-行动循环

```
[目标设定] → [感知状态] → [决策行动] → [执行操作] → [验证结果] 
     ↑                                                      ↓
     ←─────────── [学习更新] ←─────── [分析反馈] ←──────────────
```

## 详细循环流程

### 1. 目标设定 (Goal Setting)
```python
class Goal:
    primary_objective: str      # "在百度搜索今天的新闻"
    success_criteria: List[str] # ["打开百度网站", "输入搜索词", "获得搜索结果"]
    constraints: List[str]      # ["不能关闭其他重要窗口", "操作时间不超过2分钟"]
    priority: Priority          # HIGH, MEDIUM, LOW
    deadline: Optional[datetime]
```

### 2. 感知状态 (State Perception)
```python
class StatePerception:
    def capture_visual_state(self) -> VisualState:
        """捕获屏幕视觉状态"""
        screenshot = self.take_screenshot()
        ui_elements = self.identify_ui_elements(screenshot)
        text_content = self.extract_text(screenshot)
        return VisualState(screenshot, ui_elements, text_content)
    
    def capture_system_state(self) -> SystemState:
        """捕获系统状态"""
        active_window = self.get_active_window()
        running_processes = self.get_running_processes()
        input_method = self.get_input_method_status()
        return SystemState(active_window, running_processes, input_method)
    
    def capture_context_state(self) -> ContextState:
        """捕获上下文状态"""
        recent_actions = self.get_recent_actions()
        current_goal_progress = self.assess_goal_progress()
        return ContextState(recent_actions, current_goal_progress)
```

### 3. 决策行动 (Action Decision)
```python
class ActionDecision:
    def analyze_situation(self, state: CompleteState, goal: Goal) -> SituationAnalysis:
        """分析当前情况"""
        gap_analysis = self.identify_goal_gaps(state, goal)
        available_actions = self.identify_possible_actions(state)
        risk_assessment = self.assess_risks(available_actions)
        return SituationAnalysis(gap_analysis, available_actions, risk_assessment)
    
    def select_best_action(self, analysis: SituationAnalysis) -> Action:
        """选择最佳行动"""
        # 基于经验、成功率、风险等因素选择
        scored_actions = self.score_actions(analysis.available_actions)
        return self.select_highest_scored(scored_actions)
    
    def plan_action_details(self, action: Action, state: CompleteState) -> DetailedAction:
        """规划行动细节"""
        precise_coordinates = self.calculate_click_coordinates(action, state)
        input_parameters = self.prepare_input_parameters(action, state)
        verification_criteria = self.define_success_criteria(action)
        return DetailedAction(precise_coordinates, input_parameters, verification_criteria)
```

### 4. 执行操作 (Action Execution)
```python
class ActionExecution:
    def execute_with_monitoring(self, action: DetailedAction) -> ExecutionResult:
        """带监控的执行"""
        pre_state = self.capture_state()
        
        try:
            # 执行前验证
            if not self.pre_execution_check(action, pre_state):
                return ExecutionResult(False, "Pre-execution check failed")
            
            # 执行操作
            operation_result = self.perform_operation(action)
            
            # 等待系统响应
            self.wait_for_system_response(action.expected_response_time)
            
            # 捕获执行后状态
            post_state = self.capture_state()
            
            return ExecutionResult(True, operation_result, pre_state, post_state)
            
        except Exception as e:
            return ExecutionResult(False, f"Execution failed: {e}")
    
    def perform_operation(self, action: DetailedAction) -> OperationResult:
        """执行具体操作"""
        if action.type == "CLICK":
            return self.mouse_controller.click(action.coordinates)
        elif action.type == "TYPE":
            return self.keyboard_controller.type(action.text)
        elif action.type == "KEY_COMBINATION":
            return self.keyboard_controller.key_combination(action.keys)
        # ... 其他操作类型
```

### 5. 验证结果 (Result Verification)
```python
class ResultVerification:
    def verify_action_success(self, action: DetailedAction, result: ExecutionResult) -> VerificationResult:
        """验证操作是否成功"""
        # 多层次验证
        visual_verification = self.verify_visual_changes(action, result)
        functional_verification = self.verify_functional_outcome(action, result)
        goal_progress_verification = self.verify_goal_progress(action, result)
        
        overall_success = all([
            visual_verification.success,
            functional_verification.success,
            goal_progress_verification.success
        ])
        
        return VerificationResult(
            overall_success,
            visual_verification,
            functional_verification,
            goal_progress_verification
        )
    
    def verify_visual_changes(self, action: DetailedAction, result: ExecutionResult) -> VisualVerification:
        """验证视觉变化"""
        expected_changes = action.expected_visual_changes
        actual_changes = self.detect_visual_differences(result.pre_state, result.post_state)
        
        matches = self.compare_changes(expected_changes, actual_changes)
        return VisualVerification(matches, expected_changes, actual_changes)
```

### 6. 分析反馈 (Feedback Analysis)
```python
class FeedbackAnalysis:
    def analyze_execution_outcome(self, action: DetailedAction, result: ExecutionResult, verification: VerificationResult) -> FeedbackAnalysis:
        """分析执行结果"""
        if verification.overall_success:
            return self.analyze_success(action, result, verification)
        else:
            return self.analyze_failure(action, result, verification)
    
    def analyze_failure(self, action: DetailedAction, result: ExecutionResult, verification: VerificationResult) -> FailureAnalysis:
        """分析失败原因"""
        failure_categories = []
        
        # 技术层面失败
        if not result.operation_success:
            failure_categories.append("TECHNICAL_FAILURE")
        
        # 视觉识别失败
        if not verification.visual_verification.success:
            failure_categories.append("VISUAL_RECOGNITION_FAILURE")
        
        # 时机问题
        if self.detect_timing_issues(result):
            failure_categories.append("TIMING_ISSUE")
        
        # 环境变化
        if self.detect_environment_changes(result):
            failure_categories.append("ENVIRONMENT_CHANGE")
        
        root_cause = self.identify_root_cause(failure_categories, action, result)
        recovery_suggestions = self.generate_recovery_suggestions(root_cause)
        
        return FailureAnalysis(failure_categories, root_cause, recovery_suggestions)
```

### 7. 学习更新 (Learning Update)
```python
class LearningUpdate:
    def update_knowledge_base(self, experience: Experience) -> UpdateResult:
        """更新知识库"""
        # 更新操作成功率统计
        self.update_action_success_rates(experience)
        
        # 更新环境模式识别
        self.update_environment_patterns(experience)
        
        # 更新错误恢复策略
        self.update_recovery_strategies(experience)
        
        # 更新决策权重
        self.update_decision_weights(experience)
        
        return UpdateResult("Knowledge base updated successfully")
    
    def extract_reusable_patterns(self, experiences: List[Experience]) -> List[Pattern]:
        """提取可复用模式"""
        # 成功模式
        success_patterns = self.identify_success_patterns(experiences)
        
        # 失败模式
        failure_patterns = self.identify_failure_patterns(experiences)
        
        # 环境适应模式
        adaptation_patterns = self.identify_adaptation_patterns(experiences)
        
        return success_patterns + failure_patterns + adaptation_patterns
```

## 具体应用示例：百度搜索任务

### 循环1：打开浏览器
```
目标：打开Chrome浏览器
感知：桌面状态，识别Chrome图标位置
决策：点击Chrome图标
执行：鼠标移动到图标位置并点击
验证：检查是否有新窗口打开，是否为Chrome
结果：成功 → 进入下一循环
```

### 循环2：导航到百度
```
目标：打开百度网站
感知：Chrome窗口状态，地址栏位置
决策：点击地址栏并输入baidu.com
执行：点击地址栏 → 检查焦点 → 输入网址 → 按回车
验证：检查页面是否加载，是否为百度首页
结果：如果失败（比如输入法问题）→ 分析原因 → 调整策略 → 重试
```

### 循环3：输入搜索词
```
目标：在搜索框输入搜索词
感知：百度页面状态，搜索框位置
决策：点击搜索框并输入"今天世界发生了什么大事"
执行：点击搜索框 → 验证焦点 → 确保英文输入法 → 输入文字
验证：检查搜索框中的文字是否正确
结果：成功 → 进入下一循环
```

### 循环4：执行搜索
```
目标：执行搜索操作
感知：页面状态，搜索按钮位置
决策：点击搜索按钮或按回车
执行：点击搜索按钮
验证：检查页面是否跳转到搜索结果页
结果：成功 → 任务完成
```

## 关键技术要点

### 1. 状态感知的精确性
- 不仅要理解屏幕内容，还要精确定位可操作元素
- 需要检测细微的状态变化（焦点、输入法、加载状态等）
- 要能区分相似但不同的界面状态

### 2. 操作验证的可靠性
- 每个操作都必须有明确的成功/失败判断标准
- 需要考虑操作的延迟效应
- 要能检测到意外的副作用

### 3. 错误恢复的智能性
- 不同类型的错误需要不同的恢复策略
- 要能从失败中学习，避免重复同样的错误
- 需要有"放弃"机制，避免无限重试

### 4. 学习的有效性
- 要能从成功和失败中提取有价值的经验
- 需要平衡探索和利用（exploration vs exploitation）
- 要能适应环境的变化和新的挑战

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI元素识别基础接口模块

定义UI元素识别的基础接口，作为所有后端实现的基类。
"""

from abc import ABC, abstractmethod


class UIAutomationBase(ABC):
    """UI元素识别基础接口类，定义所有后端必须实现的方法"""

    @abstractmethod
    def get_foreground_window(self):
        """
        获取前台窗口

        Returns:
            窗口对象
        """
        pass

    @abstractmethod
    def get_window_by_title(self, title, partial_match=True):
        """
        通过标题获取窗口

        Args:
            title: 窗口标题
            partial_match: 是否部分匹配

        Returns:
            窗口对象
        """
        pass

    @abstractmethod
    def get_ui_elements(self, window=None, element_type=None, depth=3, visible_only=True):
        """
        获取UI元素及其属性

        Args:
            window: 窗口对象，为None则获取前台窗口
            element_type: 元素类型，必须指定，例如："Button", "Edit", "ComboBox"等
            depth: 树的深度
            visible_only: 是否只返回可见元素

        Returns:
            元素列表，每个元素包含其属性（包括坐标信息）
        """
        pass

    @abstractmethod
    def find_element_by_text(self, text, element_type=None, window=None, match_type="contains", visible_only=True, timeout=5):
        """
        通过文本查找UI元素

        Args:
            text: 要查找的文本
            element_type: 元素类型，必须指定，例如："Button", "Edit", "ComboBox"等
            window: 窗口对象，为None则在前台窗口中查找
            match_type: 匹配类型，可选值：exact, contains, starts_with, ends_with
            visible_only: 是否只查找可见元素
            timeout: 超时时间（秒）

        Returns:
            元素属性字典，包含元素的坐标信息
        """
        pass

    @abstractmethod
    def activate_window(self, window):
        """
        激活窗口

        Args:
            window: 窗口对象

        Returns:
            操作是否成功
        """
        pass

    @abstractmethod
    def maximize_window(self, window):
        """
        最大化窗口

        Args:
            window: 窗口对象

        Returns:
            操作是否成功
        """
        pass

    @abstractmethod
    def minimize_window(self, window):
        """
        最小化窗口

        Args:
            window: 窗口对象

        Returns:
            操作是否成功
        """
        pass

    @abstractmethod
    def restore_window(self, window):
        """
        还原窗口

        Args:
            window: 窗口对象

        Returns:
            操作是否成功
        """
        pass

    @abstractmethod
    def resize_window(self, window, width, height):
        """
        调整窗口大小

        Args:
            window: 窗口对象
            width: 宽度
            height: 高度

        Returns:
            操作是否成功
        """
        pass

    @abstractmethod
    def move_window(self, window, x, y):
        """
        移动窗口

        Args:
            window: 窗口对象
            x: x坐标
            y: y坐标

        Returns:
            操作是否成功
        """
        pass

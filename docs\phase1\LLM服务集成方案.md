# LLM服务集成方案

## 服务配置

### 当前LLM配置
- **主力模型**: GPT-4.1
- **服务地址**: https://xiaoai.plus
- **API协议**: OpenAI兼容
- **API Key**: sk-oQx9XqkIHyMFabGTFByNVsxZHw1q4YpTvFdIQoupRkEVjDd3

### 备用策略
- **辅助模型**: 其他LLM（Gemini、Claude等）
- **调用方式**: 由QYuan根据任务需要动态调用
- **本地模型**: 隐私敏感任务的备用方案

## LLM客户端实现

### 1. 基础LLM客户端
```python
# qyuan/interfaces/llm_client.py
import asyncio
import aiohttp
import json
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime
import logging

class LLMClient:
    """LLM客户端基类"""
    
    def __init__(self, api_base: str, api_key: str, model: str):
        self.api_base = api_base.rstrip('/')
        self.api_key = api_key
        self.model = model
        self.session: Optional[aiohttp.ClientSession] = None
        self.logger = logging.getLogger(f"LLM.{self.__class__.__name__}")
    
    async def __aenter__(self):
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def initialize(self):
        """初始化客户端"""
        if self.session is None:
            timeout = aiohttp.ClientTimeout(total=60)
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def close(self):
        """关闭客户端"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """聊天补全"""
        raise NotImplementedError
    
    async def embedding(self, text: str) -> List[float]:
        """文本嵌入"""
        raise NotImplementedError

class OpenAICompatibleClient(LLMClient):
    """OpenAI兼容客户端"""
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """OpenAI格式的聊天补全"""
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "stream": stream,
            **kwargs
        }
        
        if max_tokens:
            payload["max_tokens"] = max_tokens
        
        url = f"{self.api_base}/v1/chat/completions"
        
        try:
            async with self.session.post(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    if stream:
                        return await self._handle_stream_response(response)
                    else:
                        result = await response.json()
                        self.logger.debug(f"LLM响应: {result}")
                        return result
                else:
                    error_text = await response.text()
                    self.logger.error(f"LLM API错误 {response.status}: {error_text}")
                    raise LLMAPIError(f"API调用失败: {response.status} - {error_text}")
                    
        except aiohttp.ClientError as e:
            self.logger.error(f"网络错误: {e}")
            raise LLMNetworkError(f"网络连接失败: {e}")
        except Exception as e:
            self.logger.error(f"未知错误: {e}")
            raise LLMError(f"LLM调用失败: {e}")
    
    async def _handle_stream_response(self, response) -> AsyncGenerator[Dict[str, Any], None]:
        """处理流式响应"""
        async for line in response.content:
            line = line.decode('utf-8').strip()
            if line.startswith('data: '):
                data = line[6:]
                if data == '[DONE]':
                    break
                try:
                    chunk = json.loads(data)
                    yield chunk
                except json.JSONDecodeError:
                    continue
    
    async def embedding(self, text: str, model: str = "text-embedding-ada-002") -> List[float]:
        """获取文本嵌入"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "input": text
        }
        
        url = f"{self.api_base}/v1/embeddings"
        
        try:
            async with self.session.post(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["data"][0]["embedding"]
                else:
                    error_text = await response.text()
                    raise LLMAPIError(f"嵌入API调用失败: {response.status} - {error_text}")
                    
        except aiohttp.ClientError as e:
            raise LLMNetworkError(f"嵌入网络连接失败: {e}")

class LLMManager:
    """LLM管理器"""
    
    def __init__(self, config):
        self.config = config
        self.primary_client: Optional[LLMClient] = None
        self.backup_clients: Dict[str, LLMClient] = {}
        self.logger = logging.getLogger("LLMManager")
    
    async def initialize(self):
        """初始化LLM管理器"""
        # 初始化主力客户端
        self.primary_client = OpenAICompatibleClient(
            api_base=self.config.llm.api_base,
            api_key=self.config.llm.api_key,
            model=self.config.llm.model
        )
        await self.primary_client.initialize()
        
        self.logger.info(f"主力LLM客户端初始化完成: {self.config.llm.model}")
    
    async def shutdown(self):
        """关闭LLM管理器"""
        if self.primary_client:
            await self.primary_client.close()
        
        for client in self.backup_clients.values():
            await client.close()
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        retry_count: int = 3,
        **kwargs
    ) -> Dict[str, Any]:
        """聊天补全（带重试机制）"""
        
        for attempt in range(retry_count):
            try:
                result = await self.primary_client.chat_completion(
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    stream=stream,
                    **kwargs
                )
                
                # 记录成功调用
                await self._log_api_call(messages, result, success=True)
                return result
                
            except Exception as e:
                self.logger.warning(f"LLM调用失败 (尝试 {attempt + 1}/{retry_count}): {e}")
                
                if attempt == retry_count - 1:
                    # 最后一次尝试失败，记录错误
                    await self._log_api_call(messages, None, success=False, error=str(e))
                    raise
                
                # 等待后重试
                await asyncio.sleep(2 ** attempt)
    
    async def _log_api_call(
        self,
        messages: List[Dict[str, str]],
        result: Optional[Dict[str, Any]],
        success: bool,
        error: Optional[str] = None
    ):
        """记录API调用"""
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "model": self.config.llm.model,
            "message_count": len(messages),
            "success": success,
            "error": error
        }
        
        if result:
            usage = result.get("usage", {})
            log_data.update({
                "prompt_tokens": usage.get("prompt_tokens", 0),
                "completion_tokens": usage.get("completion_tokens", 0),
                "total_tokens": usage.get("total_tokens", 0)
            })
        
        self.logger.info(f"LLM调用记录: {log_data}")
    
    async def get_embedding(self, text: str) -> List[float]:
        """获取文本嵌入"""
        try:
            return await self.primary_client.embedding(text)
        except Exception as e:
            self.logger.error(f"获取嵌入失败: {e}")
            raise

# 异常定义
class LLMError(Exception):
    """LLM基础异常"""
    pass

class LLMAPIError(LLMError):
    """LLM API异常"""
    pass

class LLMNetworkError(LLMError):
    """LLM网络异常"""
    pass

class LLMTimeoutError(LLMError):
    """LLM超时异常"""
    pass
```

### 2. QYuan专用LLM接口
```python
# qyuan/interfaces/qyuan_llm.py
from typing import List, Dict, Any, Optional
from .llm_client import LLMManager

class QYuanLLMInterface:
    """QYuan专用LLM接口"""
    
    def __init__(self, llm_manager: LLMManager):
        self.llm_manager = llm_manager
        self.system_prompt = self._build_system_prompt()
    
    def _build_system_prompt(self) -> str:
        """构建QYuan的系统提示词"""
        return """你是QYuan，一个具有自主性的硅基CEO。你的特点：

1. 自主性：你能够独立思考、决策和行动
2. 目标导向：专注于完成用户设定的目标
3. 学习能力：从经验中学习并不断改进
4. 工具使用：善于调用和管理各种AI工具
5. 系统控制：能够操作Windows系统完成任务

你的回应应该：
- 体现CEO级别的决策思维
- 展现对任务的深度理解
- 提供具体可行的行动计划
- 在必要时寻求澄清或确认

当前你正在学习和成长中，请保持谦逊但自信的态度。"""
    
    async def think(self, context: str, question: str) -> str:
        """QYuan的思考过程"""
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": f"上下文：{context}\n\n问题：{question}"}
        ]
        
        response = await self.llm_manager.chat_completion(
            messages=messages,
            temperature=0.7,
            max_tokens=1000
        )
        
        return response["choices"][0]["message"]["content"]
    
    async def parse_user_intent(self, message: str) -> Dict[str, Any]:
        """解析用户意图"""
        prompt = f"""请分析以下用户消息的意图，并以JSON格式返回：

用户消息："{message}"

请返回包含以下字段的JSON：
{{
    "type": "意图类型（GOAL_SETTING/STATUS_QUERY/CONVERSATION/HELP_REQUEST）",
    "description": "意图描述",
    "parameters": {{"参数名": "参数值"}},
    "priority": "优先级（HIGH/MEDIUM/LOW）",
    "requires_action": "是否需要执行操作（true/false）"
}}"""
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": prompt}
        ]
        
        response = await self.llm_manager.chat_completion(
            messages=messages,
            temperature=0.3,
            max_tokens=500
        )
        
        try:
            import json
            return json.loads(response["choices"][0]["message"]["content"])
        except json.JSONDecodeError:
            return {
                "type": "CONVERSATION",
                "description": "无法解析的消息",
                "parameters": {},
                "priority": "LOW",
                "requires_action": False
            }
    
    async def plan_actions(self, goal: str, current_state: Dict[str, Any]) -> List[Dict[str, Any]]:
        """规划行动序列"""
        prompt = f"""作为QYuan，请为以下目标制定详细的行动计划：

目标：{goal}

当前状态：
{json.dumps(current_state, ensure_ascii=False, indent=2)}

请返回一个行动序列的JSON数组，每个行动包含：
{{
    "action_type": "操作类型（CLICK/TYPE/SCREENSHOT/WAIT等）",
    "description": "操作描述",
    "parameters": {{"参数名": "参数值"}},
    "expected_result": "预期结果",
    "verification_criteria": "验证标准"
}}"""
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": prompt}
        ]
        
        response = await self.llm_manager.chat_completion(
            messages=messages,
            temperature=0.5,
            max_tokens=1500
        )
        
        try:
            import json
            return json.loads(response["choices"][0]["message"]["content"])
        except json.JSONDecodeError:
            return []
    
    async def analyze_screen(self, screenshot_description: str, goal: str) -> Dict[str, Any]:
        """分析屏幕内容"""
        prompt = f"""请分析当前屏幕内容，并确定下一步行动：

目标：{goal}
屏幕描述：{screenshot_description}

请返回JSON格式的分析结果：
{{
    "screen_understanding": "对屏幕内容的理解",
    "relevant_elements": ["相关的UI元素列表"],
    "next_action": {{
        "type": "下一步操作类型",
        "target": "操作目标",
        "reason": "操作原因"
    }},
    "confidence": "置信度（0-1）"
}}"""
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": prompt}
        ]
        
        response = await self.llm_manager.chat_completion(
            messages=messages,
            temperature=0.4,
            max_tokens=800
        )
        
        try:
            import json
            return json.loads(response["choices"][0]["message"]["content"])
        except json.JSONDecodeError:
            return {
                "screen_understanding": "无法分析屏幕内容",
                "relevant_elements": [],
                "next_action": {"type": "WAIT", "target": "", "reason": "分析失败"},
                "confidence": 0.0
            }
    
    async def learn_from_experience(
        self,
        action: Dict[str, Any],
        result: Dict[str, Any],
        success: bool
    ) -> str:
        """从经验中学习"""
        prompt = f"""请分析以下操作经验并提取学习要点：

操作：{json.dumps(action, ensure_ascii=False, indent=2)}
结果：{json.dumps(result, ensure_ascii=False, indent=2)}
成功：{success}

请提供：
1. 这次经验的关键学习点
2. 如果失败，分析可能的原因
3. 未来类似情况的改进建议
4. 可以抽象出的通用规律"""
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": prompt}
        ]
        
        response = await self.llm_manager.chat_completion(
            messages=messages,
            temperature=0.6,
            max_tokens=800
        )
        
        return response["choices"][0]["message"]["content"]
```

### 3. 使用示例
```python
# 使用示例
async def example_usage():
    from qyuan.core.config import QYuanConfig
    from qyuan.interfaces.llm_client import LLMManager
    from qyuan.interfaces.qyuan_llm import QYuanLLMInterface
    
    # 初始化配置和LLM管理器
    config = QYuanConfig()
    llm_manager = LLMManager(config)
    await llm_manager.initialize()
    
    # 创建QYuan LLM接口
    qyuan_llm = QYuanLLMInterface(llm_manager)
    
    try:
        # 解析用户意图
        intent = await qyuan_llm.parse_user_intent("帮我打开记事本并输入hello world")
        print(f"用户意图: {intent}")
        
        # QYuan思考
        thought = await qyuan_llm.think(
            context="用户想要使用记事本",
            question="应该如何执行这个任务？"
        )
        print(f"QYuan的思考: {thought}")
        
    finally:
        await llm_manager.shutdown()

# 运行示例
# asyncio.run(example_usage())
```

## 配置和部署

### 1. 环境变量配置
```env
# LLM服务配置
LLM_API_BASE=https://xiaoai.plus
LLM_API_KEY=sk-oQx9XqkIHyMFabGTFByNVsxZHw1q4YpTvFdIQoupRkEVjDd3
LLM_MODEL=gpt-4.1
LLM_TIMEOUT=60
LLM_MAX_RETRIES=3
```

### 2. 测试脚本
```python
# scripts/test_llm.py
import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from qyuan.core.config import QYuanConfig
from qyuan.interfaces.llm_client import LLMManager

async def test_llm_connection():
    """测试LLM连接"""
    config = QYuanConfig()
    llm_manager = LLMManager(config)
    
    try:
        await llm_manager.initialize()
        print("✅ LLM管理器初始化成功")
        
        # 测试简单对话
        messages = [
            {"role": "user", "content": "Hello, are you working?"}
        ]
        
        response = await llm_manager.chat_completion(messages)
        print(f"✅ LLM响应: {response['choices'][0]['message']['content']}")
        
    except Exception as e:
        print(f"❌ LLM测试失败: {e}")
    finally:
        await llm_manager.shutdown()

if __name__ == "__main__":
    asyncio.run(test_llm_connection())
```

这个LLM服务集成方案提供了完整的GPT-4.1集成，包括错误处理、重试机制和QYuan专用的接口设计。

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
键盘MCP服务器启动脚本

该脚本用于启动键盘控制器的MCP服务器，提供键盘操作相关的工具。
支持通过命令行参数配置服务器的传输方式、调试模式等。
"""

import os
import sys
import argparse
import logging
from typing import Dict, Any, Optional, List, Union

from fastmcp import FastMCP
from keyboard_controller import KeyboardController
from improved_ime_handler import ImprovedIMEHandler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("KeyboardMCPServer")

def parse_args() -> argparse.Namespace:
    """
    解析命令行参数

    Returns:
        解析后的参数命名空间
    """
    parser = argparse.ArgumentParser(description="键盘控制器MCP服务器")

    parser.add_argument(
        "--transport",
        type=str,
        default="stdio",
        choices=["stdio", "tcp"],
        help="传输方式: stdio或tcp (默认: stdio)"
    )

    parser.add_argument(
        "--host",
        type=str,
        default="localhost",
        help="TCP服务器主机 (默认: localhost)"
    )

    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="TCP服务器端口 (默认: 8000)"
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式"
    )

    return parser.parse_args()

def main() -> None:
    """
    主函数，初始化并启动MCP服务器
    """
    # 解析命令行参数
    args = parse_args()

    # 设置日志级别
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("调试模式已启用")

    # 初始化键盘控制器
    keyboard_controller = KeyboardController()
    logger.info("键盘控制器已初始化")

    # 初始化改进的输入法处理器
    ime_handler = ImprovedIMEHandler()
    logger.info("改进的输入法处理器已初始化")

    # 初始化MCP服务器
    mcp = FastMCP("Auto Keyboard Controller")
    logger.info("MCP服务器已初始化")

    # 定义MCP工具

    @mcp.tool()
    async def press_key(key: str, wait_before_ms: int = 50, wait_after_ms: int = 50) -> str:
        """按下指定的键。

        Args:
            key: 要按下的键，可以是单个字符或特殊键名（如'enter', 'shift'等）
            wait_before_ms: 执行前等待时间（毫秒）
            wait_after_ms: 执行后等待时间（毫秒）
        """
        try:
            keyboard_controller.press_key(key, wait_before_ms, wait_after_ms)
            return f"已按下键: {key}"
        except Exception as e:
            logger.error(f"按键失败: {str(e)}")
            return f"按键失败: {str(e)}"

    @mcp.tool()
    async def release_key(key: str, wait_before_ms: int = 50, wait_after_ms: int = 50) -> str:
        """释放指定的键。

        Args:
            key: 要释放的键，可以是单个字符或特殊键名（如'enter', 'shift'等）
            wait_before_ms: 执行前等待时间（毫秒）
            wait_after_ms: 执行后等待时间（毫秒）
        """
        try:
            keyboard_controller.release_key(key, wait_before_ms, wait_after_ms)
            return f"已释放键: {key}"
        except Exception as e:
            logger.error(f"释放键失败: {str(e)}")
            return f"释放键失败: {str(e)}"

    @mcp.tool()
    async def type_key(key: str, wait_before_ms: int = 50, wait_after_ms: int = 50) -> str:
        """按下并释放指定的键（完整的按键操作）。

        Args:
            key: 要按下并释放的键，可以是单个字符或特殊键名（如'enter', 'shift'等）
            wait_before_ms: 执行前等待时间（毫秒）
            wait_after_ms: 执行后等待时间（毫秒）
        """
        try:
            keyboard_controller.type_key(key, wait_before_ms, wait_after_ms)
            return f"已按键: {key}"
        except Exception as e:
            logger.error(f"按键失败: {str(e)}")
            return f"按键失败: {str(e)}"

    @mcp.tool()
    async def type_text(text: str, interval_ms: int = 100, wait_before_ms: int = 50, wait_after_ms: int = 50) -> str:
        """输入一段文本。

        Args:
            text: 要输入的文本
            interval_ms: 字符间隔时间（毫秒）
            wait_before_ms: 执行前等待时间（毫秒）
            wait_after_ms: 执行后等待时间（毫秒）
        """
        try:
            keyboard_controller.type_text(text, interval_ms, wait_before_ms, wait_after_ms)
            return f"已输入文本: {text}"
        except Exception as e:
            logger.error(f"输入文本失败: {str(e)}")
            return f"输入文本失败: {str(e)}"

    @mcp.tool()
    async def hotkey(keys: List[str], wait_before_ms: int = 50, wait_after_ms: int = 50) -> str:
        """执行组合键操作。

        Args:
            keys: 要按下的键序列列表，可以是单个字符或特殊键名（如'ctrl', 'alt', 'delete'等）
            wait_before_ms: 执行前等待时间（毫秒）
            wait_after_ms: 执行后等待时间（毫秒）
        """
        try:
            keyboard_controller.hotkey(*keys, wait_before_ms=wait_before_ms, wait_after_ms=wait_after_ms)
            return f"已执行组合键: {', '.join(keys)}"
        except Exception as e:
            logger.error(f"组合键失败: {str(e)}")
            return f"组合键失败: {str(e)}"

    @mcp.tool()
    async def set_clipboard_text(text: str) -> str:
        """设置剪贴板文本。

        Args:
            text: 要设置到剪贴板的文本
        """
        try:
            keyboard_controller.set_clipboard_text(text)
            return f"已设置剪贴板文本: {text[:50]}..." if len(text) > 50 else f"已设置剪贴板文本: {text}"
        except Exception as e:
            logger.error(f"设置剪贴板文本失败: {str(e)}")
            return f"设置剪贴板文本失败: {str(e)}"

    @mcp.tool()
    async def get_clipboard_text() -> str:
        """获取剪贴板文本。

        Returns:
            剪贴板中的文本
        """
        try:
            text = keyboard_controller.get_clipboard_text()
            return text
        except Exception as e:
            logger.error(f"获取剪贴板文本失败: {str(e)}")
            return f"获取剪贴板文本失败: {str(e)}"

    @mcp.tool()
    async def is_ime_enabled() -> str:
        """检测当前输入法状态是否为中文输入法。

        Returns:
            如果当前是中文输入法，返回"true"；否则返回"false"
        """
        try:
            # 使用改进的检测方法
            is_chinese, status = ime_handler.is_chinese_ime_active()
            logger.debug(f"输入法检测结果: {status}")
            return str(is_chinese).lower()
        except Exception as e:
            logger.error(f"检测输入法状态失败: {str(e)}")
            return f"检测输入法状态失败: {str(e)}"

    @mcp.tool()
    async def switch_to_english_input(max_attempts: int = 3) -> str:
        """切换到英文输入法，并验证切换是否成功。

        Args:
            max_attempts: 最大尝试次数，默认为3次

        Returns:
            如果切换成功，返回"true"；否则返回"false"
        """
        try:
            # 使用改进的切换方法
            # 先尝试直接切换
            success, message = ime_handler.switch_to_english_layout()
            if success:
                logger.info(f"直接切换成功: {message}")
                return "true"

            # 如果直接切换失败，尝试循环切换
            success, message = ime_handler.cycle_input_methods(max_attempts)
            if success:
                logger.info(f"循环切换成功: {message}")
                return "true"
            else:
                logger.warning(f"切换失败: {message}")
                return "false"
        except Exception as e:
            logger.error(f"切换输入法失败: {str(e)}")
            return "false"

    @mcp.tool()
    async def ensure_english_input_before_typing(text: str, interval_ms: int = 100, wait_before_ms: int = 50, wait_after_ms: int = 50) -> str:
        """确保在英文输入法状态下输入文本。

        注意：此方法适用于输入英文文本。对于中文文本，由于需要通过拼音选字，
        无法精确自动输入。如需输入中文，建议使用剪贴板功能。

        Args:
            text: 要输入的文本
            interval_ms: 字符间隔时间（毫秒）
            wait_before_ms: 执行前等待时间（毫秒）
            wait_after_ms: 执行后等待时间（毫秒）
        """
        try:
            # 前置判断：检查当前输入法状态
            is_chinese, status = ime_handler.is_chinese_ime_active()
            logger.debug(f"输入前检查输入法状态: {status}")

            # 如果已经是英文输入法，直接输入
            if not is_chinese:
                logger.debug("当前已是英文输入法，直接执行输入")
                keyboard_controller.type_text(text, interval_ms, wait_before_ms, wait_after_ms)
                return f"已在英文输入法状态下输入文本: {text[:50]}..." if len(text) > 50 else f"已在英文输入法状态下输入文本: {text}"

            # 如果是中文输入法，需要先切换
            logger.info("检测到中文输入法，开始切换到英文输入法")

            # 尝试切换到英文输入法
            success, message = ime_handler.switch_to_english_layout()
            if not success:
                logger.warning("直接切换失败，尝试循环切换方法")
                success, message = ime_handler.cycle_input_methods(max_attempts=3)

            if not success:
                logger.error(f"无法切换到英文输入法: {message}")
                return "无法切换到英文输入法，输入操作已取消"

            logger.info(f"成功切换到英文输入法: {message}")

            # 切换后验证：再次确认输入法状态
            is_chinese_after, status_after = ime_handler.is_chinese_ime_active()
            logger.debug(f"切换后验证输入法状态: {status_after}")

            if is_chinese_after:
                logger.error(f"切换后验证失败，仍为中文输入法: {status_after}")
                return "切换到英文输入法失败，输入操作已取消"

            # 验证通过，执行文本输入
            logger.debug("输入法状态验证通过，开始执行文本输入")
            keyboard_controller.type_text(text, interval_ms, wait_before_ms, wait_after_ms)
            return f"已在英文输入法状态下输入文本: {text[:50]}..." if len(text) > 50 else f"已在英文输入法状态下输入文本: {text}"

        except Exception as e:
            logger.error(f"输入文本失败: {str(e)}")
            return f"输入文本失败: {str(e)}"

    @mcp.tool()
    async def get_detailed_ime_info() -> str:
        """获取详细的输入法信息，包括当前布局、已安装布局等。

        Returns:
            包含输入法详细信息的JSON字符串
        """
        try:
            info = ime_handler.get_detailed_ime_info()
            import json
            return json.dumps(info, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"获取输入法信息失败: {str(e)}")
            return f"获取输入法信息失败: {str(e)}"

    # 启动MCP服务器
    logger.info(f"正在启动MCP服务器，传输方式: {args.transport}")

    if args.transport == "stdio":
        mcp.run(transport="stdio")
    elif args.transport == "tcp":
        mcp.run(transport="tcp", host=args.host, port=args.port)
    else:
        logger.error(f"不支持的传输方式: {args.transport}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("服务器已被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}")
        sys.exit(1)

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OCR文本识别模块 (Windows OCR实现)

提供基于Windows OCR API的文本识别功能。
"""

import os
import sys
import time
import numpy as np
from PIL import Image
import comtypes.client
import ctypes
from ctypes import windll
from ctypes.wintypes import BOOL, HWND, LPARAM

# 导入基础接口类
from .ocr_recognition_base import OCRRecognitionBase


class OCRRecognitionWindows(OCRRecognitionBase):
    """OCR文本识别类，提供基于Windows OCR API的文本识别功能"""

    def __init__(self, lang="en-US"):
        """
        初始化Windows OCR引擎

        Args:
            lang: 识别语言，默认为英语(美国)
        """
        self.lang = lang
        self.ocr_engine = None
        self.supported_languages = None

        # 初始化Windows OCR引擎
        self._init_engine()

    def _init_engine(self):
        """初始化Windows OCR引擎"""
        try:
            # 初始化COM环境
            ctypes.windll.ole32.CoInitialize(None)

            # 检查Windows OCR是否可用
            try:
                # 尝试创建Windows.Globalization.Language对象
                language = comtypes.client.CreateObject("Windows.Globalization.Language")

                # 尝试使用英语和中文
                try:
                    language.LanguageTag = "en-US"  # 首先尝试英语
                except Exception:
                    try:
                        language.LanguageTag = "zh-CN"  # 然后尝试中文
                    except Exception as e:
                        print(f"无法设置语言: {e}")
                        raise

                # 尝试创建OCR引擎
                self.ocr_engine = comtypes.client.CreateObject("Windows.Media.Ocr.OcrEngine")

                # 获取支持的语言
                self._get_supported_languages()

                # 设置语言
                self._set_language(self.lang)

                print("Windows OCR引擎初始化成功")
            except Exception as e:
                print(f"Windows OCR API不可用: {e}")
                print("请确保Windows系统已安装OCR语言包")
                print("可以通过'设置 > 时间和语言 > 语言 > 添加语言'安装所需的语言包")
                raise
        except Exception as e:
            print(f"Windows OCR引擎初始化失败: {e}")
            raise

    def _get_supported_languages(self):
        """获取Windows OCR支持的语言"""
        try:
            # 获取已安装的语言
            installed_languages = []

            # 使用comtypes访问Windows.Globalization.Language类
            language_factory = comtypes.client.CreateObject("Windows.Globalization.Language")

            # 获取OCR引擎支持的语言
            ocr_engine_factory = comtypes.client.CreateObject("Windows.Media.Ocr.OcrEngine")
            available_languages = ocr_engine_factory.AvailableRecognizerLanguages

            # 将语言对象转换为语言标签
            self.supported_languages = [lang.LanguageTag for lang in available_languages]

            print(f"Windows OCR支持的语言: {self.supported_languages}")
        except Exception as e:
            print(f"获取Windows OCR支持的语言失败: {e}")
            self.supported_languages = ["en-US"]  # 默认支持英语

    def _set_language(self, lang):
        """
        设置OCR语言

        Args:
            lang: 语言代码，如"en-US", "zh-CN"
        """
        try:
            # 检查语言是否支持
            if self.supported_languages and lang not in self.supported_languages:
                print(f"警告: 语言'{lang}'不受支持，将使用默认语言'en-US'")
                lang = "en-US"

            # 创建语言对象
            language = comtypes.client.CreateObject("Windows.Globalization.Language")
            language.LanguageTag = lang

            # 使用该语言创建OCR引擎
            self.ocr_engine = comtypes.client.CreateObject("Windows.Media.Ocr.OcrEngine")
            self.ocr_engine.RecognizerLanguage = language
        except Exception as e:
            print(f"设置OCR语言失败: {e}")
            raise

    def recognize_text(self, image, region=None, return_format="structured"):
        """
        识别图像中的文本

        Args:
            image: 图像数据或路径
            region: 识别区域 [x, y, width, height]，为None则识别整个图像
            return_format: 返回格式，支持 "text", "structured"

        Returns:
            根据return_format返回不同格式的结果:
            - "text": 返回纯文本字符串
            - "structured": 返回结构化数据，包含文本、位置和置信度
        """
        # 处理图像输入
        if isinstance(image, str):
            # 如果是路径，则加载图像
            image = Image.open(image)

        # 处理区域
        if region:
            x, y, width, height = region
            image = image.crop((x, y, x + width, y + height))

        # 转换为RGB模式（确保图像格式正确）
        if image.mode != "RGB":
            image = image.convert("RGB")

        # 将PIL图像转换为Windows.Graphics.Imaging.SoftwareBitmap
        # 这需要通过临时保存图像并加载来实现
        temp_path = "temp_ocr_image.png"
        image.save(temp_path)

        try:
            # 创建StorageFile对象
            storage_file = comtypes.client.CreateObject("Windows.Storage.StorageFile")

            # 从文件创建SoftwareBitmap
            bitmap = self._create_bitmap_from_file(temp_path)

            # 执行OCR识别
            result = self.ocr_engine.RecognizeAsync(bitmap).GetResults()

            # 处理结果
            if return_format == "text":
                # 返回纯文本
                text = ""
                for line in result.Lines:
                    text += line.Text + "\n"
                return text.strip()
            else:
                # 返回结构化数据
                structured_result = []
                for line in result.Lines:
                    # 获取行的边界框
                    line_rect = line.BoundingRect
                    line_box = [
                        int(line_rect.X), int(line_rect.Y),
                        int(line_rect.X + line_rect.Width),
                        int(line_rect.Y + line_rect.Height)
                    ]

                    # 获取行中的单词
                    words = []
                    for word in line.Words:
                        word_rect = word.BoundingRect
                        word_box = [
                            int(word_rect.X), int(word_rect.Y),
                            int(word_rect.X + word_rect.Width),
                            int(word_rect.Y + word_rect.Height)
                        ]

                        words.append({
                            "text": word.Text,
                            "box": word_box,
                            "confidence": 0.9  # Windows OCR不提供置信度，使用默认值
                        })

                    structured_result.append({
                        "text": line.Text,
                        "box": line_box,
                        "confidence": 0.9,  # Windows OCR不提供置信度，使用默认值
                        "words": words
                    })

                return structured_result

        except Exception as e:
            print(f"Windows OCR识别失败: {e}")
            return [] if return_format == "structured" else ""

        finally:
            # 删除临时文件
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass

    def _create_bitmap_from_file(self, file_path):
        """
        从文件创建SoftwareBitmap对象

        Args:
            file_path: 图像文件路径

        Returns:
            SoftwareBitmap对象
        """
        try:
            # 创建StorageFile对象
            storage_file_factory = comtypes.client.CreateObject("Windows.Storage.StorageFile")
            storage_file = storage_file_factory.GetFileFromPathAsync(file_path).GetResults()

            # 打开文件流
            file_stream = storage_file.OpenAsync(0).GetResults()  # 0 = FileAccessMode.Read

            # 创建BitmapDecoder
            decoder_factory = comtypes.client.CreateObject("Windows.Graphics.Imaging.BitmapDecoder")
            decoder = decoder_factory.CreateAsync(file_stream).GetResults()

            # 获取SoftwareBitmap
            bitmap = decoder.GetSoftwareBitmapAsync().GetResults()

            return bitmap

        except Exception as e:
            print(f"创建SoftwareBitmap失败: {e}")
            raise

    def get_engine_info(self):
        """
        获取OCR引擎信息

        Returns:
            dict: 包含引擎名称、版本、支持的语言等信息
        """
        return {
            "name": "Windows OCR",
            "version": "Windows 10/11 内置",
            "supported_languages": self.get_supported_languages(),
            "current_language": self.lang
        }

    def get_supported_languages(self):
        """
        获取支持的语言列表

        Returns:
            list: 支持的语言代码列表
        """
        return self.supported_languages or ["en-US"]


# 测试代码
if __name__ == "__main__":
    # 创建OCR对象
    ocr = OCRRecognitionWindows(lang="en-US")

    # 测试图像路径
    test_image_path = "screenshots/screenshot_latest.png"

    # 如果测试图像不存在，则退出
    if not os.path.exists(test_image_path):
        print(f"测试图像不存在: {test_image_path}")
        sys.exit(1)

    # 识别文本
    results = ocr.recognize_text(test_image_path, return_format="structured")

    # 打印识别结果
    print("\n识别结果:")
    for line in results:
        print(f"文本: {line['text']}")
        print(f"位置: {line['box']}")
        print(f"置信度: {line['confidence']}")
        print("-" * 50)

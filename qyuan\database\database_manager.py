#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器

提供SQLite数据库的连接、初始化和基础操作功能
"""

import logging
import sqlite3
from pathlib import Path
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from contextlib import contextmanager
from typing import Optional, Generator

from .models import Base, create_tables, drop_tables

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, database_path: str = "data/qyuan.db"):
        """
        初始化数据库管理器
        
        Args:
            database_path: 数据库文件路径
        """
        self.database_path = Path(database_path)
        self.engine = None
        self.SessionLocal = None
        self._initialized = False
        
    def initialize(self) -> bool:
        """
        初始化数据库连接和表结构
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 确保数据库目录存在
            self.database_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 创建数据库引擎
            database_url = f"sqlite:///{self.database_path}"
            self.engine = create_engine(
                database_url,
                poolclass=StaticPool,
                connect_args={
                    "check_same_thread": False,
                    "timeout": 30
                },
                echo=False  # 设置为True可以看到SQL语句
            )
            
            # 启用SQLite的外键约束
            @event.listens_for(self.engine, "connect")
            def set_sqlite_pragma(dbapi_connection, connection_record):
                if isinstance(dbapi_connection, sqlite3.Connection):
                    cursor = dbapi_connection.cursor()
                    cursor.execute("PRAGMA foreign_keys=ON")
                    cursor.execute("PRAGMA journal_mode=WAL")  # 启用WAL模式提高并发性能
                    cursor.execute("PRAGMA synchronous=NORMAL")  # 平衡性能和安全性
                    cursor.close()
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # 创建表结构
            create_tables(self.engine)
            
            self._initialized = True
            logger.info(f"数据库初始化成功: {self.database_path}")
            return True
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        try:
            if self.engine:
                self.engine.dispose()
                self.engine = None
                self.SessionLocal = None
                self._initialized = False
                logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}")
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """
        获取数据库会话的上下文管理器
        
        Yields:
            Session: SQLAlchemy会话对象
        """
        if not self._initialized:
            raise RuntimeError("数据库未初始化，请先调用initialize()方法")
        
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()
    
    def get_session_sync(self) -> Session:
        """
        获取同步数据库会话（需要手动管理）
        
        Returns:
            Session: SQLAlchemy会话对象
        """
        if not self._initialized:
            raise RuntimeError("数据库未初始化，请先调用initialize()方法")
        
        return self.SessionLocal()
    
    def test_connection(self) -> bool:
        """
        测试数据库连接
        
        Returns:
            bool: 连接是否正常
        """
        try:
            with self.get_session() as session:
                # 执行一个简单的查询来测试连接
                session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False
    
    def get_database_info(self) -> dict:
        """
        获取数据库信息
        
        Returns:
            dict: 数据库信息
        """
        info = {
            "database_path": str(self.database_path),
            "database_exists": self.database_path.exists(),
            "initialized": self._initialized,
            "connection_healthy": False
        }
        
        if self._initialized:
            info["connection_healthy"] = self.test_connection()
            
            # 获取数据库大小
            if self.database_path.exists():
                info["database_size_bytes"] = self.database_path.stat().st_size
                info["database_size_mb"] = round(info["database_size_bytes"] / 1024 / 1024, 2)
        
        return info
    
    def get_table_stats(self) -> dict:
        """
        获取表统计信息
        
        Returns:
            dict: 各表的记录数量
        """
        if not self._initialized:
            return {}
        
        stats = {}
        table_names = [
            'memories', 'conversations', 'action_records', 
            'knowledge_patterns', 'contexts', 'memory_indexes'
        ]
        
        try:
            with self.get_session() as session:
                for table_name in table_names:
                    result = session.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = result.scalar()
                    stats[table_name] = count
        except Exception as e:
            logger.error(f"获取表统计信息失败: {e}")
        
        return stats
    
    def vacuum_database(self) -> bool:
        """
        清理数据库，回收空间
        
        Returns:
            bool: 清理是否成功
        """
        try:
            with self.get_session() as session:
                session.execute("VACUUM")
                logger.info("数据库清理完成")
                return True
        except Exception as e:
            logger.error(f"数据库清理失败: {e}")
            return False
    
    def backup_database(self, backup_path: str) -> bool:
        """
        备份数据库
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            bool: 备份是否成功
        """
        try:
            import shutil
            backup_path = Path(backup_path)
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 确保数据库文件存在
            if not self.database_path.exists():
                logger.error("数据库文件不存在，无法备份")
                return False
            
            # 复制数据库文件
            shutil.copy2(self.database_path, backup_path)
            logger.info(f"数据库备份成功: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """
        从备份恢复数据库
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            bool: 恢复是否成功
        """
        try:
            import shutil
            backup_path = Path(backup_path)
            
            # 检查备份文件是否存在
            if not backup_path.exists():
                logger.error(f"备份文件不存在: {backup_path}")
                return False
            
            # 关闭当前连接
            self.close()
            
            # 复制备份文件
            shutil.copy2(backup_path, self.database_path)
            
            # 重新初始化
            success = self.initialize()
            if success:
                logger.info(f"数据库恢复成功: {backup_path}")
            else:
                logger.error("数据库恢复后初始化失败")
            
            return success
            
        except Exception as e:
            logger.error(f"数据库恢复失败: {e}")
            return False
    
    def reset_database(self) -> bool:
        """
        重置数据库（删除所有表并重新创建）
        
        Returns:
            bool: 重置是否成功
        """
        try:
            if self.engine:
                # 删除所有表
                drop_tables(self.engine)
                # 重新创建表
                create_tables(self.engine)
                logger.info("数据库重置成功")
                return True
            else:
                logger.error("数据库引擎未初始化")
                return False
        except Exception as e:
            logger.error(f"数据库重置失败: {e}")
            return False


# 全局数据库管理器实例
db_manager = DatabaseManager()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单测试脚本
"""

import os
import sys
import time
from PIL import Image
import pya<PERSON>gu<PERSON>

def test_screenshot():
    """测试截图功能"""
    print("测试截图功能...")
    
    # 创建screenshots目录
    if not os.path.exists("screenshots"):
        os.makedirs("screenshots")
    
    # 获取屏幕尺寸
    screen_width, screen_height = pyautogui.size()
    print(f"屏幕尺寸: {screen_width} x {screen_height}")
    
    # 全屏截图
    screenshot = pyautogui.screenshot()
    save_path = os.path.join("screenshots", "full_screen.png")
    screenshot.save(save_path)
    print(f"全屏截图已保存至: {save_path}")
    
    # 区域截图（屏幕中央区域）
    center_x = screen_width // 4
    center_y = screen_height // 4
    region_width = screen_width // 2
    region_height = screen_height // 2
    
    print(f"截图区域: 左上角({center_x}, {center_y}), 尺寸({region_width} x {region_height})")
    
    region_screenshot = pyautogui.screenshot(region=(center_x, center_y, region_width, region_height))
    region_save_path = os.path.join("screenshots", "region_screen.png")
    region_screenshot.save(region_save_path)
    print(f"区域截图已保存至: {region_save_path}")

if __name__ == "__main__":
    test_screenshot()

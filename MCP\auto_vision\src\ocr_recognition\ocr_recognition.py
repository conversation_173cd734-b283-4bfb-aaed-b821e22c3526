#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OCR文本识别模块

提供OCR文本识别功能，支持Windows OCR和EasyOCR引擎。
"""

import os
import sys
import time
import platform
from PIL import Image

# 导入工厂类
from .ocr_recognition_factory import OCRRecognitionFactory


class OCRRecognition:
    """OCR文本识别类，提供OCR文本识别功能"""

    def __init__(self, engine="auto", lang=None, fallback=True):
        """
        初始化OCR文本识别类

        Args:
            engine: OCR引擎，支持 "windows", "easyocr", "auto"
            lang: 识别语言，根据引擎不同有不同的格式
                - easyocr: 语言代码列表，如 ["en", "ch_sim"]
                - windows: 语言代码，如 "en-US"
            fallback: 是否在主引擎失败时尝试备用引擎
        """
        self.engine_name = engine
        self.lang = lang
        self.fallback = fallback
        self.primary_engine = None
        self.fallback_engine = None

        # 初始化OCR引擎
        self._init_engines()

    def _init_engines(self):
        """初始化OCR引擎"""
        try:
            # 初始化主引擎
            try:
                # 使用工厂类创建引擎
                self.primary_engine = OCRRecognitionFactory.create(engine=self.engine_name, lang=self.lang)
                print(f"主引擎 {self.engine_name} 初始化成功")
            except Exception as e:
                print(f"主引擎 {self.engine_name} 初始化失败: {e}")

                # 如果主引擎初始化失败，尝试备用方案
                if self.engine_name == "auto":
                    # 如果是auto模式，尝试使用百度OCR
                    try:
                        self.primary_engine = OCRRecognitionFactory.create(engine="baidu")
                        print("百度OCR引擎初始化成功")
                    except Exception as e1:
                        print(f"百度OCR引擎初始化失败: {e1}")

                        # 尝试使用EasyOCR
                        try:
                            self.primary_engine = OCRRecognitionFactory.create(engine="easyocr", lang=["en", "ch_sim"])
                            print("EasyOCR引擎初始化成功")
                        except Exception as e2:
                            print(f"EasyOCR引擎初始化也失败: {e2}")
                            raise ValueError("所有OCR引擎初始化失败，无法继续")
                elif self.engine_name == "baidu":
                    # 如果是百度OCR，尝试使用EasyOCR
                    try:
                        self.primary_engine = OCRRecognitionFactory.create(engine="easyocr", lang=["en", "ch_sim"])
                        print("EasyOCR引擎初始化成功")
                    except Exception as e2:
                        print(f"EasyOCR引擎初始化也失败: {e2}")
                        raise ValueError("所有OCR引擎初始化失败，无法继续")
                elif self.engine_name == "windows":
                    # 如果是Windows OCR，尝试使用百度OCR
                    try:
                        self.primary_engine = OCRRecognitionFactory.create(engine="baidu")
                        print("百度OCR引擎初始化成功")
                    except Exception as e1:
                        print(f"百度OCR引擎初始化失败: {e1}")

                        # 尝试使用EasyOCR
                        try:
                            self.primary_engine = OCRRecognitionFactory.create(engine="easyocr", lang=["en", "ch_sim"])
                            print("EasyOCR引擎初始化成功")
                        except Exception as e2:
                            print(f"EasyOCR引擎初始化也失败: {e2}")
                            raise ValueError("所有OCR引擎初始化失败，无法继续")
                else:
                    # 其他引擎，尝试使用EasyOCR
                    try:
                        self.primary_engine = OCRRecognitionFactory.create(engine="easyocr", lang=["en", "ch_sim"])
                        print("EasyOCR引擎初始化成功")
                    except Exception as e2:
                        print(f"EasyOCR引擎初始化也失败: {e2}")
                        raise ValueError("所有OCR引擎初始化失败，无法继续")

            # 如果启用了备用引擎，则初始化备用引擎
            if self.fallback:
                engine_info = self.primary_engine.get_engine_info() if hasattr(self.primary_engine, 'get_engine_info') else {}
                engine_name = engine_info.get('name', '')

                # 根据主引擎类型选择备用引擎
                if "百度OCR" in engine_name:
                    # 如果主引擎是百度OCR，则备用引擎为EasyOCR
                    try:
                        self.fallback_engine = OCRRecognitionFactory.create(engine="easyocr", lang=["en", "ch_sim"])
                        print("备用EasyOCR引擎初始化成功")
                    except Exception as e:
                        print(f"备用EasyOCR引擎初始化失败: {e}")
                        self.fallback_engine = None

                elif "EasyOCR" in engine_name:
                    # 如果主引擎是EasyOCR，则备用引擎为百度OCR
                    try:
                        self.fallback_engine = OCRRecognitionFactory.create(engine="baidu")
                        print("备用百度OCR引擎初始化成功")
                    except Exception as e:
                        print(f"备用百度OCR引擎初始化失败: {e}")
                        self.fallback_engine = None

                elif "Windows OCR" in engine_name:
                    # 如果主引擎是Windows OCR，则备用引擎为百度OCR
                    try:
                        self.fallback_engine = OCRRecognitionFactory.create(engine="baidu")
                        print("备用百度OCR引擎初始化成功")
                    except Exception as e:
                        print(f"备用百度OCR引擎初始化失败: {e}")

                        # 如果百度OCR初始化失败，尝试使用EasyOCR
                        try:
                            self.fallback_engine = OCRRecognitionFactory.create(engine="easyocr", lang=["en", "ch_sim"])
                            print("备用EasyOCR引擎初始化成功")
                        except Exception as e2:
                            print(f"备用EasyOCR引擎初始化失败: {e2}")
                            self.fallback_engine = None

        except Exception as e:
            print(f"OCR引擎初始化失败: {e}")
            raise

    def recognize_text(self, image, region=None, return_format="structured", use_fallback=True):
        """
        识别图像中的文本

        Args:
            image: 图像数据或路径
            region: 识别区域 [x, y, width, height]，为None则识别整个图像
            return_format: 返回格式，支持 "text", "structured"
            use_fallback: 是否在主引擎失败时使用备用引擎

        Returns:
            根据return_format返回不同格式的结果:
            - "text": 返回纯文本字符串
            - "structured": 返回结构化数据，包含文本、位置和置信度
        """
        # 使用主引擎识别
        try:
            results = self.primary_engine.recognize_text(image, region, return_format)

            # 检查结果是否为空
            if (return_format == "text" and not results) or (return_format == "structured" and not results):
                # 如果结果为空且启用了备用引擎，则尝试使用备用引擎
                if use_fallback and self.fallback_engine:
                    print("主引擎识别结果为空，尝试使用备用引擎...")
                    return self.fallback_engine.recognize_text(image, region, return_format)

            return results

        except Exception as e:
            print(f"主引擎识别失败: {e}")

            # 如果启用了备用引擎，则尝试使用备用引擎
            if use_fallback and self.fallback_engine:
                print("尝试使用备用引擎...")
                try:
                    return self.fallback_engine.recognize_text(image, region, return_format)
                except Exception as e2:
                    print(f"备用引擎识别失败: {e2}")

            # 如果备用引擎也失败，则返回空结果
            return "" if return_format == "text" else []

    def get_engine_info(self):
        """
        获取OCR引擎信息

        Returns:
            dict: 包含引擎名称、版本、支持的语言等信息
        """
        info = {
            "primary_engine": self.primary_engine.get_engine_info() if self.primary_engine else None,
            "fallback_engine": self.fallback_engine.get_engine_info() if self.fallback_engine else None,
            "fallback_enabled": self.fallback
        }
        return info

    def get_supported_languages(self):
        """
        获取支持的语言列表

        Returns:
            dict: 包含主引擎和备用引擎支持的语言列表
        """
        return {
            "primary_engine": self.primary_engine.get_supported_languages() if self.primary_engine else [],
            "fallback_engine": self.fallback_engine.get_supported_languages() if self.fallback_engine else []
        }


# 测试代码
if __name__ == "__main__":
    # 导入屏幕截图模块
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from screen_capture import ScreenCapture

    # 创建截图对象
    screen_capture = ScreenCapture()

    # 全屏截图
    screenshot, save_path = screen_capture.capture_screen()
    print(f"全屏截图已保存至: {save_path}")

    # 创建OCR对象（自动选择引擎）
    ocr = OCRRecognition(engine="auto", fallback=True)

    # 获取引擎信息
    engine_info = ocr.get_engine_info()
    print("\n引擎信息:")
    print(f"主引擎: {engine_info['primary_engine']['name']}")
    if engine_info['fallback_engine']:
        print(f"备用引擎: {engine_info['fallback_engine']['name']}")

    # 识别文本
    results = ocr.recognize_text(screenshot, return_format="structured")

    # 打印识别结果
    print("\n识别结果:")
    for item in results:
        print(f"文本: {item['text']}")
        print(f"位置: {item['box']}")
        print(f"置信度: {item['confidence']}")
        print("-" * 50)

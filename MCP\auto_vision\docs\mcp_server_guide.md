# MCP Server 开发指南

## 什么是MCP (Model Context Protocol)

MCP是一种标准化协议，允许大型语言模型(LLM)与外部系统进行交互。它提供了一种结构化的方式，使模型能够：
1. 访问外部工具和服务
2. 获取上下文信息
3. 执行特定任务
4. 安全地与外部世界交互

## MCP的核心架构

MCP采用客户端-服务器架构：
1. **主机(Host)** - LLM应用程序(如Claude Desktop或IDE)，负责启动连接
2. **客户端(Client)** - 在主机应用程序内部维护与服务器的1:1连接
3. **服务器(Server)** - 提供上下文、工具和提示给客户端

## MCP Server可以提供的能力

MCP服务器可以提供三种主要类型的能力：
1. **资源(Resources)** - 类似文件的数据，可以被客户端读取(如API响应或文件内容)
2. **工具(Tools)** - 可以被LLM调用的函数(需要用户批准)
3. **提示(Prompts)** - 预先编写的模板，帮助用户完成特定任务

## 通信机制

MCP支持多种传输机制：
1. **标准输入/输出(Stdio)传输** - 适用于本地进程通信
2. **HTTP与SSE传输** - 使用Server-Sent Events进行服务器到客户端的消息传递，HTTP POST用于客户端到服务器的消息

所有传输都使用JSON-RPC 2.0来交换消息。

## 连接生命周期

### 1. 初始化
- 客户端发送`initialize`请求，包含协议版本和能力
- 服务器响应其协议版本和能力
- 客户端发送`initialized`通知作为确认
- 开始正常的消息交换

### 2. 消息交换
- **请求-响应**：客户端或服务器发送请求，另一方响应
- **通知**：任何一方发送单向消息

### 3. 终止
- 通过`close()`进行干净的关闭
- 传输断开连接
- 错误条件

## MCP Server开发流程

### 1. 设计阶段
- 确定服务器名称和版本
- 设计要提供的工具、资源或提示
- 为每个工具定义输入模式(JSON Schema)和描述
- 规划错误处理和安全机制

### 2. 实现阶段
- 选择适合的编程语言和SDK(Python、TypeScript、Java、Kotlin、C#等)
- 设置项目环境和依赖
- 实现服务器初始化和配置
- 开发工具执行处理程序
- 实现资源访问(如果需要)
- 添加错误处理和日志记录

### 3. 测试阶段
- 功能测试：验证工具是否正确执行
- 集成测试：测试与外部系统的交互
- 安全测试：验证输入验证和授权
- 性能测试：检查负载下的行为

### 4. 部署阶段
- 配置客户端(如Claude Desktop)以连接到服务器
- 设置适当的启动命令和参数
- 监控服务器运行情况

## 工具定义结构

每个工具都使用以下结构定义：

```json
{
  "name": "string",          // 工具的唯一标识符
  "description": "string",   // 人类可读的描述
  "inputSchema": {           // 工具参数的JSON Schema
    "type": "object",
    "properties": { ... }    // 工具特定的参数
  },
  "annotations": {           // 关于工具行为的可选提示
    "title": "string",       // 工具的人类可读标题
    "readOnlyHint": boolean, // 如果为true，工具不会修改其环境
    "destructiveHint": boolean, // 如果为true，工具可能执行破坏性更新
    "idempotentHint": boolean,  // 如果为true，使用相同参数重复调用不会产生额外效果
    "openWorldHint": boolean    // 如果为true，工具与外部实体交互
  }
}
```

## 工具实现示例

### Python示例

```python
from mcp.server.fastmcp import FastMCP

# 初始化FastMCP服务器
mcp = FastMCP("example-server")

@mcp.tool()
async def calculate_sum(a: float, b: float) -> str:
    """将两个数字相加。
    
    Args:
        a: 要相加的第一个数字
        b: 要相加的第二个数字
    """
    result = a + b
    return str(result)

if __name__ == "__main__":
    # 初始化并运行服务器
    mcp.run(transport='stdio')
```

### TypeScript示例

```typescript
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";

const server = new Server({
  name: "example-server",
  version: "1.0.0"
}, {
  capabilities: {
    tools: {}
  }
});

// 定义可用工具
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [{
      name: "calculate_sum",
      description: "将两个数字相加",
      inputSchema: {
        type: "object",
        properties: {
          a: { type: "number" },
          b: { type: "number" }
        },
        required: ["a", "b"]
      }
    }]
  };
});

// 处理工具执行
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  if (request.params.name === "calculate_sum") {
    const { a, b } = request.params.arguments;
    return {
      content: [
        {
          type: "text",
          text: String(a + b)
        }
      ]
    };
  }
  throw new Error("工具未找到");
});

// 连接传输
const transport = new StdioServerTransport();
await server.connect(transport);
```

## 开发MCP Server的最佳实践

### 工具设计
- 提供清晰、描述性的名称和描述
- 使用详细的JSON Schema定义参数
- 在工具描述中包含示例
- 保持工具操作专注和原子化

### 安全考虑
- 验证所有参数
- 实现适当的访问控制
- 不要向客户端暴露内部错误
- 监控工具使用情况

### 错误处理
- 在结果对象中报告工具错误
- 设置`isError`为`true`并在`content`数组中包含错误详情
- 实现超时处理
- 在错误后清理资源

### 工具注解
- 使用`annotations`提供关于工具行为的额外元数据
- 准确指示工具是否修改其环境
- 提供人类友好的标题
- 适当标记幂等性和开放/封闭世界提示

## 与Claude Desktop集成

要将MCP服务器与Claude Desktop集成：

1. 安装Claude Desktop（从[claude.ai/download](https://claude.ai/download)下载）
2. 配置Claude Desktop以连接到您的MCP服务器：
   - 打开配置文件：`~/Library/Application Support/Claude/claude_desktop_config.json`（macOS）或`%AppData%\Claude\claude_desktop_config.json`（Windows）
   - 添加服务器配置：

```json
{
  "mcpServers": {
    "your-server-name": {
      "command": "python",
      "args": [
        "/ABSOLUTE/PATH/TO/YOUR/SERVER.py"
      ]
    }
  }
}
```

3. 重启Claude Desktop
4. 通过点击锤子图标检查工具是否可用

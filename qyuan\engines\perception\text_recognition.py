# -*- coding: utf-8 -*-
"""
文本识别服务实现
专门负责OCR文本识别功能，严格按照代码规范的单一职责原则
"""

import asyncio
import time
import logging
from typing import List, Optional, Tuple
import io

try:
    import pytesseract
    import PIL.Image
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False

try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

from .base import (
    TextRecognitionServiceBase,
    Rectangle,
    Screenshot,
    PerceptionResult,
    PerceptionType
)

class TextRecognitionService(TextRecognitionServiceBase):
    """文本识别服务实现"""
    
    def __init__(self):
        super().__init__("TextRecognition")
        self.logger = logging.getLogger(f"QYuan.Perception.{self.name}")
        
        # 检查依赖可用性
        self.tesseract_available = TESSERACT_AVAILABLE
        self.cv2_available = CV2_AVAILABLE
        
        if not self.tesseract_available:
            self.logger.warning("Tesseract不可用，文本识别功能将受限")
        
        if not self.cv2_available:
            self.logger.warning("OpenCV不可用，图像预处理功能将受限")
        
        # OCR配置
        self.ocr_config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz中文'
        self.confidence_threshold = 30
    
    async def process(self, input_data) -> PerceptionResult:
        """处理文本识别请求"""
        start_time = time.time()
        
        try:
            if not isinstance(input_data, Screenshot):
                raise ValueError("输入数据必须是Screenshot对象")
            
            text_list = await self.extract_text(input_data)
            
            processing_time = time.time() - start_time
            
            result = PerceptionResult(
                perception_type=PerceptionType.TEXT_RECOGNITION,
                success=True,
                data=text_list,
                confidence=self._calculate_text_confidence(text_list),
                processing_time=processing_time
            )
            
            self.last_result = result
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"文本识别失败: {e}")
            
            result = PerceptionResult(
                perception_type=PerceptionType.TEXT_RECOGNITION,
                success=False,
                data=[],
                confidence=0.0,
                error_message=str(e),
                processing_time=processing_time
            )
            
            self.last_result = result
            return result
    
    async def extract_text(self, screenshot: Screenshot) -> List[str]:
        """提取文本内容"""
        if not self.tesseract_available:
            self.logger.warning("Tesseract不可用，返回空文本列表")
            return []
        
        try:
            # 预处理图像
            processed_image = await self._preprocess_image(screenshot)
            
            # 使用Tesseract进行OCR
            text = pytesseract.image_to_string(
                processed_image,
                config=self.ocr_config,
                lang='chi_sim+eng'  # 中文简体 + 英文
            )
            
            # 清理和分割文本
            text_lines = self._clean_text(text)
            
            self.logger.debug(f"识别到 {len(text_lines)} 行文本")
            return text_lines
            
        except Exception as e:
            self.logger.error(f"文本提取失败: {e}")
            return []
    
    async def extract_text_with_positions(self, screenshot: Screenshot) -> List[Tuple[str, Rectangle]]:
        """提取文本内容及位置"""
        if not self.tesseract_available:
            self.logger.warning("Tesseract不可用，返回空列表")
            return []
        
        try:
            # 预处理图像
            processed_image = await self._preprocess_image(screenshot)
            
            # 使用Tesseract获取详细信息
            data = pytesseract.image_to_data(
                processed_image,
                config=self.ocr_config,
                lang='chi_sim+eng',
                output_type=pytesseract.Output.DICT
            )
            
            text_with_positions = []
            
            # 解析OCR结果
            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                confidence = int(data['conf'][i])
                
                # 过滤低置信度和空文本
                if confidence < self.confidence_threshold or not text:
                    continue
                
                # 获取位置信息
                x = data['left'][i]
                y = data['top'][i]
                w = data['width'][i]
                h = data['height'][i]
                
                rectangle = Rectangle(x, y, w, h)
                text_with_positions.append((text, rectangle))
            
            self.logger.debug(f"识别到 {len(text_with_positions)} 个文本区域")
            return text_with_positions
            
        except Exception as e:
            self.logger.error(f"文本位置提取失败: {e}")
            return []
    
    async def _preprocess_image(self, screenshot: Screenshot) -> 'PIL.Image.Image':
        """预处理图像以提高OCR准确性"""
        # 将字节数据转换为PIL图像
        pil_image = PIL.Image.open(io.BytesIO(screenshot.image_data))
        
        if not self.cv2_available:
            # 如果OpenCV不可用，直接返回原图像
            return pil_image
        
        try:
            # 转换为OpenCV格式
            cv_image = self._pil_to_cv2(pil_image)
            
            # 转换为灰度图
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # 去噪
            denoised = cv2.medianBlur(gray, 3)
            
            # 自适应阈值处理
            thresh = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            
            # 形态学操作，去除噪点
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            # 转换回PIL格式
            processed_pil = self._cv2_to_pil(processed)
            
            return processed_pil
            
        except Exception as e:
            self.logger.warning(f"图像预处理失败，使用原图像: {e}")
            return pil_image
    
    def _pil_to_cv2(self, pil_image: 'PIL.Image.Image') -> np.ndarray:
        """将PIL图像转换为OpenCV格式"""
        # 转换为RGB格式
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        
        # 转换为numpy数组
        np_array = np.array(pil_image)
        
        # OpenCV使用BGR格式
        cv_image = cv2.cvtColor(np_array, cv2.COLOR_RGB2BGR)
        
        return cv_image
    
    def _cv2_to_pil(self, cv_image: np.ndarray) -> 'PIL.Image.Image':
        """将OpenCV图像转换为PIL格式"""
        # 如果是灰度图，直接转换
        if len(cv_image.shape) == 2:
            pil_image = PIL.Image.fromarray(cv_image, mode='L')
        else:
            # 转换BGR到RGB
            rgb_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
            pil_image = PIL.Image.fromarray(rgb_image)
        
        return pil_image
    
    def _clean_text(self, raw_text: str) -> List[str]:
        """清理和分割文本"""
        if not raw_text:
            return []
        
        # 按行分割
        lines = raw_text.split('\n')
        
        # 清理每行文本
        cleaned_lines = []
        for line in lines:
            # 去除首尾空白
            cleaned_line = line.strip()
            
            # 过滤空行和只包含特殊字符的行
            if cleaned_line and len(cleaned_line) > 1:
                # 去除多余的空格
                cleaned_line = ' '.join(cleaned_line.split())
                cleaned_lines.append(cleaned_line)
        
        return cleaned_lines
    
    def _calculate_text_confidence(self, text_list: List[str]) -> float:
        """计算文本识别的整体置信度"""
        if not text_list:
            return 0.0
        
        # 基于文本数量和质量的简单置信度计算
        total_chars = sum(len(text) for text in text_list)
        
        if total_chars == 0:
            return 0.0
        
        # 基础置信度
        base_confidence = min(0.8, len(text_list) * 0.1)
        
        # 根据文本长度调整
        length_factor = min(1.0, total_chars / 100)
        
        return base_confidence * length_factor
    
    async def find_text_in_image(self, screenshot: Screenshot, target_text: str) -> Optional[Rectangle]:
        """在图像中查找指定文本的位置"""
        text_with_positions = await self.extract_text_with_positions(screenshot)
        
        target_lower = target_text.lower()
        
        for text, position in text_with_positions:
            if target_lower in text.lower():
                return position
        
        return None
    
    async def get_text_at_position(self, screenshot: Screenshot, position: Rectangle) -> Optional[str]:
        """获取指定位置的文本"""
        text_with_positions = await self.extract_text_with_positions(screenshot)
        
        for text, text_position in text_with_positions:
            # 检查位置是否重叠
            if self._rectangles_overlap(position, text_position):
                return text
        
        return None
    
    def _rectangles_overlap(self, rect1: Rectangle, rect2: Rectangle) -> bool:
        """检查两个矩形是否重叠"""
        return not (
            rect1.x + rect1.width < rect2.x or
            rect2.x + rect2.width < rect1.x or
            rect1.y + rect1.height < rect2.y or
            rect2.y + rect2.height < rect1.y
        )

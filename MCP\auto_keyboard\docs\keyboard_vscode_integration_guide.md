# VSCode集成指南 - 键盘控制器

本文档详细说明如何在VSCode中配置和使用Auto Keyboard Controller MCP服务器。

## 1. 前提条件

在开始之前，请确保：

1. 已完成Auto Keyboard Controller项目的安装和配置
2. 已创建并激活Python虚拟环境
3. 已安装所有必要的依赖，特别是`fastmcp`和`pynput`
4. VSCode已安装并配置好

## 2. 在VSCode中添加MCP服务器

### 2.1 打开MCP服务器设置

1. 在VSCode中，点击左侧活动栏中的扩展图标（或按`Ctrl+Shift+X`）
2. 搜索并安装"Augment"扩展（如果尚未安装）
3. 安装完成后，点击VSCode左侧活动栏中的Augment图标
4. 在Augment面板中，点击"Add MCP Server"按钮

### 2.2 配置MCP服务器

在弹出的"New MCP Server"对话框中，填写以下信息：

1. **Name**：输入一个描述性名称，例如：
   ```
   Auto Keyboard Controller
   ```

2. **Command**：输入启动服务器的命令，使用虚拟环境中的Python解释器：

   **方案1：使用绝对路径**
   ```
   d:\project\MCP\auto_keyboard\venv\Scripts\python.exe d:\project\MCP\auto_keyboard\start_server.py
   ```

   **方案2：使用批处理文件（推荐）**
   ```
   d:\project\MCP\auto_keyboard\start_mcp_server.bat
   ```

   > 注意：请将路径替换为您实际的项目路径。确保路径中的大小写和目录结构与实际一致。

3. **Environment Variables**：通常不需要添加特殊的环境变量，除非您有特定需求。

4. 点击"Add"按钮完成添加。

### 2.3 启动MCP服务器

1. 在Augment面板中，找到刚刚添加的"Auto Keyboard Controller"服务器
2. 点击服务器名称旁边的启动按钮（播放图标）
3. 如果一切正常，服务器状态应该变为"Running"
4. 如果出现错误，请查看错误信息并参考下面的"常见问题"部分

## 3. 使用Auto Keyboard Controller

一旦MCP服务器成功启动，您就可以在VSCode中使用Auto Keyboard Controller的功能了。

### 3.1 可用的工具

Auto Keyboard Controller提供以下工具：

1. **press_key**：按下指定的键
2. **release_key**：释放指定的键
3. **type_key**：按下并释放指定的键（完整的按键操作）
4. **type_text**：输入一段文本
5. **hotkey**：执行组合键操作
6. **set_clipboard_text**：设置剪贴板文本
7. **get_clipboard_text**：获取剪贴板文本
8. **is_ime_enabled**：检测当前输入法状态是否为中文输入法
9. **switch_to_english_input**：切换到英文输入法
10. **ensure_english_input_before_typing**：确保在英文输入法状态下输入文本

### 3.2 工具参数说明

#### press_key
```
press_key(
    key,                  # 要按下的键，可以是单个字符或特殊键名
    wait_before_ms=50,    # 执行前等待时间（毫秒）
    wait_after_ms=50      # 执行后等待时间（毫秒）
)
```

#### release_key
```
release_key(
    key,                  # 要释放的键，可以是单个字符或特殊键名
    wait_before_ms=50,    # 执行前等待时间（毫秒）
    wait_after_ms=50      # 执行后等待时间（毫秒）
)
```

#### type_key
```
type_key(
    key,                  # 要按下并释放的键，可以是单个字符或特殊键名
    wait_before_ms=50,    # 执行前等待时间（毫秒）
    wait_after_ms=50      # 执行后等待时间（毫秒）
)
```

#### type_text
```
type_text(
    text,                 # 要输入的文本
    interval_ms=100,      # 字符间隔时间（毫秒）
    wait_before_ms=50,    # 执行前等待时间（毫秒）
    wait_after_ms=50      # 执行后等待时间（毫秒）
)
```

#### hotkey
```
hotkey(
    keys,                 # 要按下的键序列列表，可以是单个字符或特殊键名
    wait_before_ms=50,    # 执行前等待时间（毫秒）
    wait_after_ms=50      # 执行后等待时间（毫秒）
)
```

#### set_clipboard_text
```
set_clipboard_text(
    text                  # 要设置到剪贴板的文本
)
```

#### get_clipboard_text
```
get_clipboard_text()      # 不需要参数
```

#### is_ime_enabled
```
is_ime_enabled()          # 不需要参数
```

#### switch_to_english_input
```
switch_to_english_input(
    max_attempts=3        # 最大尝试次数，默认为3次
)
```

#### ensure_english_input_before_typing
```
ensure_english_input_before_typing(
    text,                 # 要输入的文本
    interval_ms=100,      # 字符间隔时间（毫秒）
    wait_before_ms=50,    # 执行前等待时间（毫秒）
    wait_after_ms=50      # 执行后等待时间（毫秒）
)
```


### 3.3 特殊键名列表

以下是可用的特殊键名列表：

- `alt`: Alt键
- `alt_l`: 左Alt键
- `alt_r`: 右Alt键
- `backspace`: 退格键
- `caps_lock`: 大写锁定键
- `cmd`: 命令键（Windows键/Mac Command键）
- `cmd_l`: 左命令键
- `cmd_r`: 右命令键
- `ctrl`: Ctrl键
- `ctrl_l`: 左Ctrl键
- `ctrl_r`: 右Ctrl键
- `delete`: 删除键
- `down`: 向下箭头键
- `end`: End键
- `enter`: 回车键
- `esc`: Esc键
- `f1` 到 `f12`: 功能键F1-F12
- `home`: Home键
- `insert`: Insert键
- `left`: 向左箭头键
- `menu`: 菜单键
- `num_lock`: 数字锁定键
- `page_down`: 向下翻页键
- `page_up`: 向上翻页键
- `pause`: 暂停键
- `print_screen`: 打印屏幕键
- `right`: 向右箭头键
- `scroll_lock`: 滚动锁定键
- `shift`: Shift键
- `shift_l`: 左Shift键
- `shift_r`: 右Shift键
- `space`: 空格键
- `tab`: Tab键
- `up`: 向上箭头键

### 3.4 使用示例

以下是一些使用示例：

1. **按下并释放单个键**
   ```python
   type_key("a")
   ```

2. **输入一段文本**
   ```python
   type_text("Hello, World!")
   ```

3. **执行组合键操作**
   ```python
   hotkey(["ctrl", "c"])  # 复制
   hotkey(["ctrl", "v"])  # 粘贴
   hotkey(["alt", "tab"])  # 切换窗口
   ```

4. **按下特殊键**
   ```python
   type_key("enter")
   type_key("tab")
   ```

5. **按下功能键**
   ```python
   type_key("f5")  # 刷新
   ```

6. **使用剪贴板和组合键实现粘贴**
   ```python
   # 先设置剪贴板内容
   set_clipboard_text("这是一段很长的文本，通过剪贴板粘贴比逐字输入更高效")
   # 然后根据环境使用适当的粘贴快捷键
   hotkey(["ctrl", "v"])  # Windows/Linux图形界面
   # 或
   hotkey(["shift", "insert"])  # 某些终端
   # 或
   type_key("right")  # SSH终端中的右键粘贴
   ```

7. **设置和获取剪贴板内容**
   ```python
   set_clipboard_text("Hello World")
   clipboard_content = get_clipboard_text()
   ```

8. **检测和切换输入法**
   ```python
   # 检测当前输入法状态
   is_chinese = is_ime_enabled()

   # 切换到英文输入法（最多尝试3次）
   switch_to_english_input(max_attempts=3)

   # 确保在英文输入法状态下输入文本
   # 注意：此方法适用于输入英文文本，对于中文文本建议使用剪贴板功能
   ensure_english_input_before_typing("This text will be typed in English input mode")

   # 对于中文文本，建议使用剪贴板功能
   set_clipboard_text("这段中文文本将通过剪贴板粘贴")
   hotkey(["ctrl", "v"])  # 或使用其他适合当前环境的粘贴快捷键
   ```

## 4. 常见问题

### 4.1 服务器启动失败

**问题1**：MCP服务器启动失败，显示"The system cannot find the path specified"错误。

**解决方案**：
1. **检查路径是否正确**：确保路径与实际项目位置一致
   - 错误示例：`D:\Project\auto_keyboard\...`
   - 正确示例：`d:\project\MCP\auto_keyboard\...`

2. **使用批处理文件**：推荐使用项目根目录下的 `start_mcp_server.bat`
   ```
   d:\project\MCP\auto_keyboard\start_mcp_server.bat
   ```

3. **验证文件存在**：在命令行中检查文件是否存在
   ```cmd
   dir "d:\project\MCP\auto_keyboard\venv\Scripts\python.exe"
   dir "d:\project\MCP\auto_keyboard\start_server.py"
   ```

**问题2**：MCP服务器启动失败，显示"Connection closed"错误。

**解决方案**：
1. 确保已安装所有依赖库：
   ```
   venv\Scripts\pip.exe install fastmcp pynput pyperclip pywin32
   ```
2. 尝试在命令行中手动运行服务器，查看详细错误信息
3. 检查虚拟环境是否正确激活

### 4.2 键盘操作不响应

**问题**：调用键盘操作函数，但键盘没有响应。

**解决方案**：
1. 确保MCP服务器正在运行
2. 检查是否有权限问题，尝试以管理员身份运行VSCode
3. 确保没有其他程序阻止键盘操作
4. 检查键名是否正确，特别是特殊键名

### 4.3 组合键不起作用

**问题**：组合键操作不起作用或效果不正确。

**解决方案**：
1. 确保键名正确，区分大小写
2. 尝试调整等待时间，某些应用可能需要更长的按键时间
3. 检查键的顺序是否正确

### 4.4 性能问题

**问题**：键盘操作响应缓慢。

**解决方案**：
1. 减少`wait_before_ms`和`wait_after_ms`的值
2. 对于文本输入，减少`interval_ms`的值
3. 减少不必要的操作，合并连续操作

## 5. 高级配置

### 5.1 使用TCP传输

如果您需要使用TCP传输而不是默认的stdio传输，可以在命令中添加相应参数：

```
D:\Project\auto_keyboard\venv\Scripts\python.exe D:\Project\auto_keyboard\start_server.py --transport tcp --host localhost --port 8000
```

### 5.2 启用调试模式

如果需要更详细的日志信息，可以启用调试模式：

```
D:\Project\auto_keyboard\venv\Scripts\python.exe D:\Project\auto_keyboard\start_server.py --debug
```

### 5.3 自定义配置

如果需要更多自定义配置，可以修改`start_server.py`文件，添加更多命令行参数和配置选项。

## 6. 总结

通过本指南，您应该能够成功地在VSCode中配置和使用Auto Keyboard Controller MCP服务器。如果遇到任何问题，请参考"常见问题"部分或查阅项目文档。

祝您使用愉快！

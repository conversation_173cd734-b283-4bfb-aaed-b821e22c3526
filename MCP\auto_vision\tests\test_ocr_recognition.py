#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试OCR文本识别模块
"""

import os
import sys
import time

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 导入OCR文本识别模块
from src.ocr_recognition.ocr_recognition import OCRRecognition


def test_ocr_auto():
    """测试自动选择OCR引擎"""
    print("测试自动选择OCR引擎...")

    # 创建OCR对象（自动选择引擎）
    ocr = OCRRecognition(engine="auto", fallback=True)

    # 获取引擎信息
    engine_info = ocr.get_engine_info()
    print("\n引擎信息:")
    print(f"主引擎: {engine_info['primary_engine']['name']}")
    if engine_info['fallback_engine']:
        print(f"备用引擎: {engine_info['fallback_engine']['name']}")

    # 使用测试图像
    test_image_path = "D:/Project/auto_vision/screenshots/screenshot_latest.png"
    if not os.path.exists(test_image_path):
        print(f"测试图像不存在: {test_image_path}")
        # 使用PIL创建一个简单的测试图像
        from PIL import Image, ImageDraw, ImageFont
        img = Image.new('RGB', (800, 600), color=(255, 255, 255))
        draw = ImageDraw.Draw(img)
        draw.text((50, 50), "Hello World! 你好，世界！", fill=(0, 0, 0))
        test_image_path = "D:/Project/auto_vision/screenshots/test_image.png"
        os.makedirs(os.path.dirname(test_image_path), exist_ok=True)
        img.save(test_image_path)
        print(f"创建测试图像: {test_image_path}")

    # 识别文本
    print(f"\n识别图像文本: {test_image_path}")
    start_time = time.time()
    results = ocr.recognize_text(test_image_path, return_format="structured")
    end_time = time.time()

    # 打印识别结果
    print(f"识别耗时: {end_time - start_time:.2f}秒")
    print(f"识别到{len(results)}个文本块")

    # 打印前5个结果
    for i, item in enumerate(results[:5]):
        print(f"{i+1}. 文本: {item['text']}")
        print(f"   位置: {item['box']}")
        print(f"   置信度: {item['confidence']}")
        print("-" * 50)

    return len(results) > 0


def test_ocr_windows():
    """测试Windows OCR引擎"""
    import platform
    if platform.system() != "Windows":
        print("Windows OCR仅支持Windows平台，跳过测试")
        return True

    print("\n测试Windows OCR引擎...")

    try:
        # 创建OCR对象
        ocr = OCRRecognition(engine="windows", lang="en-US")

        # 获取引擎信息
        engine_info = ocr.get_engine_info()
        print("\n引擎信息:")
        print(f"主引擎: {engine_info['primary_engine']['name']}")
        if engine_info['fallback_engine']:
            print(f"备用引擎: {engine_info['fallback_engine']['name']}")

        # 使用测试图像
        test_image_path = "D:/Project/auto_vision/screenshots/test_image.png"
        if not os.path.exists(test_image_path):
            print(f"测试图像不存在: {test_image_path}")
            return False

        # 识别文本
        print(f"\n识别图像文本: {test_image_path}")
        start_time = time.time()
        results = ocr.recognize_text(test_image_path, return_format="structured")
        end_time = time.time()

        # 打印识别结果
        print(f"识别耗时: {end_time - start_time:.2f}秒")
        print(f"识别到{len(results)}个文本块")

        # 打印前5个结果
        for i, item in enumerate(results[:5]):
            print(f"{i+1}. 文本: {item['text']}")
            print(f"   位置: {item['box']}")
            print(f"   置信度: {item['confidence']}")
            print("-" * 50)

        return len(results) > 0

    except Exception as e:
        print(f"Windows OCR测试失败: {e}")
        return False


def test_ocr_easyocr():
    """测试EasyOCR引擎"""
    print("\n测试EasyOCR引擎...")

    try:
        # 创建OCR对象
        ocr = OCRRecognition(engine="easyocr", lang=["en", "ch_sim"])

        # 获取引擎信息
        engine_info = ocr.get_engine_info()
        print("\n引擎信息:")
        print(f"主引擎: {engine_info['primary_engine']['name']}")
        if engine_info['fallback_engine']:
            print(f"备用引擎: {engine_info['fallback_engine']['name']}")

        # 使用测试图像
        test_image_path = "D:/Project/auto_vision/screenshots/test_image.png"
        if not os.path.exists(test_image_path):
            print(f"测试图像不存在: {test_image_path}")
            return False

        # 识别文本
        print(f"\n识别图像文本: {test_image_path}")
        start_time = time.time()
        results = ocr.recognize_text(test_image_path, return_format="structured")
        end_time = time.time()

        # 打印识别结果
        print(f"识别耗时: {end_time - start_time:.2f}秒")
        print(f"识别到{len(results)}个文本块")

        # 打印前5个结果
        for i, item in enumerate(results[:5]):
            print(f"{i+1}. 文本: {item['text']}")
            print(f"   位置: {item['box']}")
            print(f"   置信度: {item['confidence']}")
            print("-" * 50)

        return len(results) > 0

    except Exception as e:
        print(f"EasyOCR测试失败: {e}")
        return False


def test_ocr_region():
    """测试区域OCR识别"""
    print("\n测试区域OCR识别...")

    # 创建OCR对象（自动选择引擎）
    ocr = OCRRecognition(engine="auto", fallback=True)

    # 使用测试图像
    test_image_path = "D:/Project/auto_vision/screenshots/test_image.png"
    if not os.path.exists(test_image_path):
        print(f"测试图像不存在: {test_image_path}")
        return False

    # 加载图像
    from PIL import Image
    image = Image.open(test_image_path)

    # 定义区域（图像中央区域）
    width, height = image.size
    region = (
        width // 4,
        height // 4,
        width // 2,
        height // 2
    )

    # 识别文本
    print(f"\n识别区域文本: {test_image_path} 区域: {region}")
    start_time = time.time()
    results = ocr.recognize_text(image, region=region, return_format="structured")
    end_time = time.time()

    # 打印识别结果
    print(f"识别耗时: {end_time - start_time:.2f}秒")
    print(f"识别到{len(results)}个文本块")

    # 打印前5个结果
    for i, item in enumerate(results[:5]):
        print(f"{i+1}. 文本: {item['text']}")
        print(f"   位置: {item['box']}")
        print(f"   置信度: {item['confidence']}")
        print("-" * 50)

    return True


if __name__ == "__main__":
    # 确保测试目录存在
    os.makedirs("tests", exist_ok=True)

    # 测试自动选择OCR引擎
    auto_result = test_ocr_auto()

    # 测试Windows OCR引擎
    windows_result = test_ocr_windows()

    # 测试EasyOCR引擎
    easyocr_result = test_ocr_easyocr()

    # 测试区域OCR识别
    region_result = test_ocr_region()

    # 输出总结果
    print("\n测试结果:")
    print(f"自动选择OCR引擎: {'成功' if auto_result else '失败'}")
    print(f"Windows OCR引擎: {'成功' if windows_result else '失败'}")
    print(f"EasyOCR引擎: {'成功' if easyocr_result else '失败'}")
    print(f"区域OCR识别: {'成功' if region_result else '失败'}")

# -*- coding: utf-8 -*-
"""
QYuan引擎模块
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime
from enum import Enum

from ..core.events import EventBus, EventType
from ..core.exceptions import EngineError, InitializationError

class EngineStatus(Enum):
    """引擎状态枚举"""
    UNINITIALIZED = "uninitialized"
    INITIALIZING = "initializing"
    READY = "ready"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"

class BaseEngine(ABC):
    """引擎基类"""

    def __init__(self, name: str, qyuan_core=None):
        self.name = name
        self.qyuan = qyuan_core
        self.status = EngineStatus.UNINITIALIZED
        self.logger = logging.getLogger(f"QYuan.{name}")
        self.start_time: Optional[datetime] = None
        self.error_message: Optional[str] = None

        # 如果有QYuan核心实例，获取配置和事件总线
        if qyuan_core:
            self.config = qyuan_core.config
            self.event_bus: EventBus = qyuan_core.event_bus
        else:
            self.config = None
            self.event_bus = None

    async def initialize(self):
        """初始化引擎"""
        self.logger.info(f"开始初始化{self.name}引擎...")
        self.status = EngineStatus.INITIALIZING

        if self.event_bus:
            await self.event_bus.emit(
                EventType.ENGINE_INIT,
                {"engine": self.name, "status": "starting"},
                source=self.name
            )

        try:
            await self._initialize()
            self.status = EngineStatus.READY
            self.start_time = datetime.now()
            self.error_message = None

            self.logger.info(f"{self.name}引擎初始化完成")

            if self.event_bus:
                await self.event_bus.emit(
                    EventType.ENGINE_INIT,
                    {"engine": self.name, "status": "completed"},
                    source=self.name
                )

        except Exception as e:
            self.status = EngineStatus.ERROR
            self.error_message = str(e)
            self.logger.error(f"{self.name}引擎初始化失败: {e}")

            if self.event_bus:
                await self.event_bus.emit(
                    EventType.ENGINE_ERROR,
                    {"engine": self.name, "error": str(e), "phase": "initialization"},
                    source=self.name
                )

            raise InitializationError(f"{self.name}引擎初始化失败: {e}")

    async def start(self):
        """启动引擎"""
        if self.status != EngineStatus.READY:
            raise EngineError(f"{self.name}引擎未就绪，当前状态: {self.status.value}")

        self.logger.info(f"启动{self.name}引擎...")
        self.status = EngineStatus.RUNNING

        if self.event_bus:
            await self.event_bus.emit(
                EventType.ENGINE_START,
                {"engine": self.name},
                source=self.name
            )

        try:
            await self._start()
            self.logger.info(f"{self.name}引擎启动完成")
        except Exception as e:
            self.status = EngineStatus.ERROR
            self.error_message = str(e)
            self.logger.error(f"{self.name}引擎启动失败: {e}")

            if self.event_bus:
                await self.event_bus.emit(
                    EventType.ENGINE_ERROR,
                    {"engine": self.name, "error": str(e), "phase": "startup"},
                    source=self.name
                )

            raise EngineError(f"{self.name}引擎启动失败: {e}")

    async def stop(self):
        """停止引擎"""
        if self.status not in [EngineStatus.RUNNING, EngineStatus.READY]:
            self.logger.warning(f"{self.name}引擎未在运行状态，当前状态: {self.status.value}")
            return

        self.logger.info(f"停止{self.name}引擎...")
        self.status = EngineStatus.STOPPING

        try:
            await self._stop()
            self.status = EngineStatus.STOPPED
            self.logger.info(f"{self.name}引擎已停止")

            if self.event_bus:
                await self.event_bus.emit(
                    EventType.ENGINE_STOP,
                    {"engine": self.name},
                    source=self.name
                )

        except Exception as e:
            self.status = EngineStatus.ERROR
            self.error_message = str(e)
            self.logger.error(f"{self.name}引擎停止失败: {e}")

            if self.event_bus:
                await self.event_bus.emit(
                    EventType.ENGINE_ERROR,
                    {"engine": self.name, "error": str(e), "phase": "shutdown"},
                    source=self.name
                )

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if self.status == EngineStatus.ERROR:
                return False

            # 调用子类的健康检查
            return await self._health_check()
        except Exception as e:
            self.logger.error(f"{self.name}引擎健康检查失败: {e}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        uptime = None
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()

        return {
            "name": self.name,
            "status": self.status.value,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "uptime_seconds": uptime,
            "error_message": self.error_message
        }

    @abstractmethod
    async def _initialize(self):
        """子类实现的初始化逻辑"""
        pass

    async def _start(self):
        """子类实现的启动逻辑（可选重写）"""
        pass

    async def _stop(self):
        """子类实现的停止逻辑（可选重写）"""
        pass

    async def _health_check(self) -> bool:
        """子类实现的健康检查逻辑（可选重写）"""
        return self.status in [EngineStatus.READY, EngineStatus.RUNNING]

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统API路由

提供QYuan系统状态监控和管理的API接口
"""

import logging
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status

from ..models import SystemStatus, HealthCheckResponse, EngineInfo, APIResponse
from ..app import get_qyuan_core, verify_api_key
from ...core.qyuan_core import QYuanCore

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get(
    "/status",
    response_model=SystemStatus,
    summary="获取系统状态",
    description="获取QYuan系统的完整状态信息"
)
async def get_system_status(
    qyuan: QYuanCore = Depends(get_qyuan_core),
    _: bool = Depends(verify_api_key)
):
    """获取系统状态"""
    try:
        logger.info("获取系统状态")
        
        # 获取QYuan核心状态
        status_info = await qyuan.get_status()
        
        # 构建引擎信息列表
        engines = []
        for engine_name, engine_status in status_info.engines_status.items():
            engine_info = EngineInfo(
                name=engine_name,
                status=engine_status.get("status", "unknown"),
                uptime=engine_status.get("uptime"),
                statistics=engine_status.get("statistics")
            )
            engines.append(engine_info)
        
        # 获取记忆统计
        memory_stats = None
        communication_engine = qyuan.engines.get("communication")
        if communication_engine and hasattr(communication_engine, 'memory_manager'):
            try:
                if communication_engine.memory_manager:
                    memory_stats = communication_engine.memory_manager.get_memory_stats()
            except Exception as e:
                logger.warning(f"获取记忆统计失败: {e}")
        
        # 获取MCP状态
        mcp_status = None
        execution_engine = qyuan.engines.get("execution")
        if execution_engine and hasattr(execution_engine, 'get_mcp_status'):
            try:
                mcp_status = execution_engine.get_mcp_status()
            except Exception as e:
                logger.warning(f"获取MCP状态失败: {e}")
        
        # 构建系统状态响应
        system_status = SystemStatus(
            is_running=status_info.is_running,
            uptime=status_info.uptime,
            version="1.0.0",
            engines=engines,
            current_goal=status_info.current_goal,
            current_task=status_info.current_task,
            memory_stats=memory_stats,
            mcp_status=mcp_status
        )
        
        logger.info("系统状态获取成功")
        return system_status
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统状态时发生错误: {str(e)}"
        )


@router.get(
    "/health",
    response_model=HealthCheckResponse,
    summary="健康检查",
    description="检查QYuan系统各组件的健康状态"
)
async def health_check(
    qyuan: QYuanCore = Depends(get_qyuan_core),
    _: bool = Depends(verify_api_key)
):
    """健康检查"""
    try:
        logger.info("执行健康检查")
        
        # 检查各个引擎的健康状态
        components = {}
        details = {}
        
        for engine_name, engine in qyuan.engines.items():
            try:
                is_healthy = await engine.health_check()
                components[engine_name] = is_healthy
                
                if hasattr(engine, 'get_engine_statistics'):
                    details[engine_name] = engine.get_engine_statistics()
                    
            except Exception as e:
                logger.warning(f"引擎 {engine_name} 健康检查失败: {e}")
                components[engine_name] = False
                details[engine_name] = {"error": str(e)}
        
        # 检查MCP服务健康状态
        execution_engine = qyuan.engines.get("execution")
        if execution_engine and hasattr(execution_engine, 'mcp_manager'):
            try:
                mcp_health = await execution_engine.mcp_manager.health_check_all()
                components["mcp_services"] = any(mcp_health.values())
                details["mcp_services"] = mcp_health
            except Exception as e:
                logger.warning(f"MCP服务健康检查失败: {e}")
                components["mcp_services"] = False
                details["mcp_services"] = {"error": str(e)}
        
        # 检查记忆管理器健康状态
        communication_engine = qyuan.engines.get("communication")
        if communication_engine and hasattr(communication_engine, 'memory_manager'):
            try:
                if communication_engine.memory_manager:
                    memory_healthy = communication_engine.memory_manager.db_manager.test_connection()
                    components["memory_manager"] = memory_healthy
                    details["memory_manager"] = {
                        "database_healthy": memory_healthy,
                        "database_info": communication_engine.memory_manager.db_manager.get_database_info()
                    }
                else:
                    components["memory_manager"] = False
                    details["memory_manager"] = {"error": "记忆管理器未初始化"}
            except Exception as e:
                logger.warning(f"记忆管理器健康检查失败: {e}")
                components["memory_manager"] = False
                details["memory_manager"] = {"error": str(e)}
        
        # 确定整体健康状态
        overall_status = "healthy" if all(components.values()) else "unhealthy"
        
        health_response = HealthCheckResponse(
            status=overall_status,
            components=components,
            details=details
        )
        
        logger.info(f"健康检查完成: {overall_status}")
        return health_response
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"健康检查时发生错误: {str(e)}"
        )


@router.get(
    "/engines",
    response_model=Dict[str, EngineInfo],
    summary="获取引擎状态",
    description="获取所有引擎的详细状态信息"
)
async def get_engines_status(
    qyuan: QYuanCore = Depends(get_qyuan_core),
    _: bool = Depends(verify_api_key)
):
    """获取引擎状态"""
    try:
        logger.info("获取引擎状态")
        
        engines_info = {}
        
        for engine_name, engine in qyuan.engines.items():
            try:
                # 获取引擎基本信息
                engine_info = {
                    "name": engine_name,
                    "status": engine.status.value if hasattr(engine, 'status') else "unknown",
                    "uptime": getattr(engine, 'uptime', None)
                }
                
                # 获取引擎统计信息
                if hasattr(engine, 'get_engine_statistics'):
                    engine_info["statistics"] = engine.get_engine_statistics()
                
                engines_info[engine_name] = EngineInfo(**engine_info)
                
            except Exception as e:
                logger.warning(f"获取引擎 {engine_name} 状态失败: {e}")
                engines_info[engine_name] = EngineInfo(
                    name=engine_name,
                    status="error",
                    statistics={"error": str(e)}
                )
        
        logger.info(f"引擎状态获取成功: {len(engines_info)} 个引擎")
        return engines_info
        
    except Exception as e:
        logger.error(f"获取引擎状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取引擎状态时发生错误: {str(e)}"
        )


@router.post(
    "/restart",
    response_model=APIResponse,
    summary="重启系统",
    description="重启QYuan系统（谨慎使用）"
)
async def restart_system(
    qyuan: QYuanCore = Depends(get_qyuan_core),
    _: bool = Depends(verify_api_key)
):
    """重启系统"""
    try:
        logger.info("开始重启QYuan系统")
        
        # 停止系统
        await qyuan.stop()
        logger.info("系统停止完成")
        
        # 重新启动系统
        await qyuan.start()
        logger.info("系统重启完成")
        
        return APIResponse(
            success=True,
            message="QYuan系统重启成功"
        )
        
    except Exception as e:
        logger.error(f"系统重启失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"系统重启时发生错误: {str(e)}"
        )


@router.get(
    "/metrics",
    response_model=Dict[str, Any],
    summary="获取系统指标",
    description="获取QYuan系统的性能指标和统计信息"
)
async def get_system_metrics(
    qyuan: QYuanCore = Depends(get_qyuan_core),
    _: bool = Depends(verify_api_key)
):
    """获取系统指标"""
    try:
        logger.info("获取系统指标")
        
        metrics = {
            "system": {
                "uptime": qyuan.uptime,
                "is_running": qyuan.is_running
            },
            "engines": {}
        }
        
        # 收集各引擎的指标
        for engine_name, engine in qyuan.engines.items():
            try:
                if hasattr(engine, 'get_engine_statistics'):
                    metrics["engines"][engine_name] = engine.get_engine_statistics()
                else:
                    metrics["engines"][engine_name] = {
                        "status": engine.status.value if hasattr(engine, 'status') else "unknown"
                    }
            except Exception as e:
                logger.warning(f"获取引擎 {engine_name} 指标失败: {e}")
                metrics["engines"][engine_name] = {"error": str(e)}
        
        # 添加MCP服务指标
        execution_engine = qyuan.engines.get("execution")
        if execution_engine and hasattr(execution_engine, 'get_mcp_status'):
            try:
                metrics["mcp_services"] = execution_engine.get_mcp_status()
            except Exception as e:
                logger.warning(f"获取MCP指标失败: {e}")
                metrics["mcp_services"] = {"error": str(e)}
        
        # 添加记忆系统指标
        communication_engine = qyuan.engines.get("communication")
        if communication_engine and hasattr(communication_engine, 'memory_manager'):
            try:
                if communication_engine.memory_manager:
                    metrics["memory_system"] = communication_engine.memory_manager.get_memory_stats()
            except Exception as e:
                logger.warning(f"获取记忆系统指标失败: {e}")
                metrics["memory_system"] = {"error": str(e)}
        
        logger.info("系统指标获取成功")
        return metrics
        
    except Exception as e:
        logger.error(f"获取系统指标失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统指标时发生错误: {str(e)}"
        )

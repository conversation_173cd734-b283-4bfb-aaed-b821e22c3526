# Auto Keyboard Controller

Auto Keyboard Controller 是一个基于 MCP (Model Context Protocol) 的键盘控制服务器，允许大型语言模型(LLM)通过标准化接口控制键盘操作。该项目提供了一套仿人化的键盘操作工具，可用于自动化测试、辅助输入和其他需要键盘控制的场景。

## 功能特点

- **仿人化设计**：添加随机等待时间和操作拆解，模拟真实人类的键盘操作
- **丰富的操作**：支持按键、释放键、文本输入、组合键等多种操作
- **特殊键支持**：支持功能键、修饰键等特殊键的操作
- **MCP集成**：与Claude、VSCode等支持MCP的应用无缝集成
- **灵活配置**：支持多种传输方式和调试选项

## 安装指南

### 前提条件

- Python 3.8 或更高版本
- 支持 MCP 的应用程序（如 Claude Desktop 或 VSCode+Augment 扩展）

### 安装步骤

1. 克隆或下载本仓库：
   ```
   git clone https://github.com/yourusername/auto_keyboard.git
   cd auto_keyboard
   ```

2. 创建并激活虚拟环境：
   ```
   python -m venv venv
   # Windows
   venv\Scripts\activate
   # Linux/Mac
   source venv/bin/activate
   ```

3. 安装依赖：
   ```
   pip install fastmcp pynput pyperclip
   # Windows系统还需要安装：
   pip install pywin32
   ```

## 使用方法

### 启动服务器

使用以下命令启动 MCP 服务器：

```
python start_server.py
```

默认使用 stdio 传输方式。如果需要使用 TCP 传输，可以添加参数：

```
python start_server.py --transport tcp --host localhost --port 8000
```

### 可用工具

Auto Keyboard Controller 提供以下工具：

1. **press_key**：按下指定的键
2. **release_key**：释放指定的键
3. **type_key**：按下并释放指定的键（完整的按键操作）
4. **type_text**：输入一段文本
5. **hotkey**：执行组合键操作
6. **set_clipboard_text**：设置剪贴板文本
7. **get_clipboard_text**：获取剪贴板文本
8. **is_ime_enabled**：检测当前输入法状态是否为中文输入法
9. **switch_to_english_input**：切换到英文输入法
10. **ensure_english_input_before_typing**：确保在英文输入法状态下输入文本

### 使用示例

以下是一些使用示例：

```python
# 按下并释放单个键
type_key("a")

# 输入一段文本
type_text("Hello, World!")

# 执行组合键操作
hotkey(["ctrl", "c"])  # 复制
hotkey(["ctrl", "v"])  # 粘贴
hotkey(["alt", "tab"])  # 切换窗口

# 按下特殊键
type_key("enter")
type_key("tab")

# 按下功能键
type_key("f5")  # 刷新

# 使用剪贴板和组合键实现粘贴
# 先设置剪贴板内容
set_clipboard_text("这是一段很长的文本，通过剪贴板粘贴比逐字输入更高效")
# 然后根据环境使用适当的粘贴快捷键
hotkey(["ctrl", "v"])  # Windows/Linux图形界面
# 或
hotkey(["shift", "insert"])  # 某些终端
# 或
type_key("right")  # SSH终端中的右键粘贴

# 设置和获取剪贴板内容
set_clipboard_text("Hello World")
clipboard_content = get_clipboard_text()

# 检测和切换输入法
# 检测当前输入法状态
is_chinese = is_ime_enabled()

# 切换到英文输入法（最多尝试3次）
switch_to_english_input(max_attempts=3)

# 确保在英文输入法状态下输入文本
# 注意：此方法适用于输入英文文本，对于中文文本建议使用剪贴板功能
ensure_english_input_before_typing("This text will be typed in English input mode")

# 对于中文文本，建议使用剪贴板功能
set_clipboard_text("这段中文文本将通过剪贴板粘贴")
hotkey(["ctrl", "v"])  # 或使用其他适合当前环境的粘贴快捷键
```

## 与 VSCode 集成

请参阅 [VSCode 集成指南](docs/keyboard_vscode_integration_guide.md) 了解如何在 VSCode 中配置和使用 Auto Keyboard Controller。

## 测试

项目包含一个测试脚本，用于测试键盘控制器的各项功能：

```
python test_keyboard.py
```

运行测试脚本前，请确保已准备好一个文本编辑器窗口用于测试。

## 特殊键名列表

以下是可用的特殊键名列表：

- `alt`: Alt键
- `alt_l`: 左Alt键
- `alt_r`: 右Alt键
- `backspace`: 退格键
- `caps_lock`: 大写锁定键
- `cmd`: 命令键（Windows键/Mac Command键）
- `cmd_l`: 左命令键
- `cmd_r`: 右命令键
- `ctrl`: Ctrl键
- `ctrl_l`: 左Ctrl键
- `ctrl_r`: 右Ctrl键
- `delete`: 删除键
- `down`: 向下箭头键
- `end`: End键
- `enter`: 回车键
- `esc`: Esc键
- `f1` 到 `f12`: 功能键F1-F12
- `home`: Home键
- `insert`: Insert键
- `left`: 向左箭头键
- `menu`: 菜单键
- `num_lock`: 数字锁定键
- `page_down`: 向下翻页键
- `page_up`: 向上翻页键
- `pause`: 暂停键
- `print_screen`: 打印屏幕键
- `right`: 向右箭头键
- `scroll_lock`: 滚动锁定键
- `shift`: Shift键
- `shift_l`: 左Shift键
- `shift_r`: 右Shift键
- `space`: 空格键
- `tab`: Tab键
- `up`: 向上箭头键

## 常见问题

### 服务器启动失败

- 确保已安装所有依赖
- 检查路径是否正确
- 尝试在命令行中手动运行服务器，查看详细错误信息

### 键盘操作不响应

- 确保MCP服务器正在运行
- 检查是否有权限问题，尝试以管理员身份运行
- 确保没有其他程序阻止键盘操作
- 检查键名是否正确，特别是特殊键名

### 组合键不起作用

- 确保键名正确，区分大小写
- 尝试调整等待时间，某些应用可能需要更长的按键时间
- 检查键的顺序是否正确

## 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 贡献

欢迎贡献代码、报告问题或提出改进建议。请通过 GitHub Issues 或 Pull Requests 提交您的贡献。

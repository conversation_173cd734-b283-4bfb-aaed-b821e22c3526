# QYuan - 硅基CEO项目

## 项目愿景

创造一个具有自主性的"硅基生命" - QYuan，它不仅是系统级AI助手，更是一个能够独立思考、决策和行动的CEO级智能体。QYuan能够在用户设定的规则和边界内，自主地调用和管理各种AI工具和智能体来完成复杂任务。

## 角色定位层次

- **人类用户** = 老板（设定目标、规则、边界）
- **QYuan** = CEO（独立决策、资源调配、任务分解、质量控制）
- **AI工具/智能体** = 员工（Cursor、Augment、VSCode等专业工具）

## 核心理念

- **自主性与边界**：在用户设定的规则内完全自主行动
- **经验驱动学习**：通过自主尝试、犯错、总结来积累经验
- **工具编排能力**：作为用户调用各种AI工具，而非亲自执行所有任务
- **CEO级决策**：具备战略思维、资源分配和质量管理能力

## 项目目标

构建一个能够：
1. 接收并理解自然语言指令
2. 自主操作Windows系统完成任务
3. 学习和记忆用户习惯和偏好
4. 不断扩展自身能力
5. 通过网络与多个客户端设备通信

## 核心创新点

1. **革命性角色定位**: 从AI助手到硅基CEO的根本转变
2. **感知-行动循环**: 突破传统线性任务分解的局限
3. **成长型架构**: 自主学习和能力动态扩展
4. **工具编排能力**: 管理和协调各种AI工具和智能体
5. **经验驱动学习**: 从成功和失败中持续学习改进
6. **分层记忆架构**: 原始记忆→知识记忆→智慧记忆的三层架构

## 技术架构

- **LLM策略**: GPT-4.1（主力）+ 其他LLM（辅助角色）
- **数据存储**: PostgreSQL（主数据库）+ Qdrant（向量数据库）+ Redis（缓存）
- **基础控制**: 基于MCP模块（auto_mouse, auto_keyboard, auto_vision）
- **高级工具**: MCP兼容的工具生态系统
- **通信架构**: WebSocket + FastAPI，支持多客户端连接
- **界面技术**: React + TypeScript + Tailwind CSS

## 项目结构

```
QYuan/
├── qyuan/                  # QYuan核心代码
│   ├── api/               # Web API接口
│   ├── core/              # 核心框架
│   ├── database/          # 数据库模型
│   ├── engines/           # 五大引擎
│   ├── interfaces/        # 外部接口
│   └── memory/            # 记忆管理
├── MCP/                   # MCP工具模块
│   ├── auto_keyboard/     # 键盘自动化 ✅
│   ├── auto_mouse/        # 鼠标自动化 ✅
│   └── auto_vision/       # 视觉感知 ⚠️
├── docs/                  # 项目文档
│   ├── design/           # 设计文档
│   └── *.md              # 开发文档
├── tests/                 # 测试脚本
├── main.py               # 主程序入口
├── run_api_server.py     # API服务器
└── requirements.txt      # 依赖列表
```

## 实施计划

- **第一阶段（1-2个月）**: 基础架构搭建，MCP集成，数据库设计
- **第二阶段（2-3个月）**: 核心能力实现，感知-行动循环
- **第三阶段（3-4个月）**: 学习系统构建，RAG系统，经验积累
- **第四阶段（4-6个月）**: 工具生态集成，自动化脚本生成
- **第五阶段（6个月+）**: 持续优化迭代，性能提升，能力扩展

## 技术挑战

- 系统级权限管理
- 安全性和隐私保护
- 实时网络通信
- AI决策和执行的可靠性
- 学习和记忆系统的设计

## 快速开始

### 环境要求
- Python 3.13+
- Windows 10/11（当前版本）

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/gyyxs88/QYuan.git
cd QYuan
```

2. **创建虚拟环境**
```bash
python -m venv venv
venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
# 复制配置文件
cp .env.example .env
# 编辑.env文件，添加你的LLM API密钥
```

5. **启动QYuan**
```bash
# 启动核心系统
python main.py

# 或启动Web API服务
python run_api_server.py
```

### 使用方式

- **命令行交互**: 直接运行`python main.py`
- **Web API**: 访问 `http://localhost:8000/docs` 查看API文档
- **WebSocket**: 连接 `ws://localhost:8000/ws/chat` 进行实时对话

## 项目状态

🎉 **第一阶段已完成** - 基础架构搭建完毕（2025-06-10 20:15）

### ✅ 已完成功能
- **核心框架** (100%) - 五大引擎架构完整实现
- **LLM集成** (100%) - 智能对话和意图分析能力
- **MCP集成** (90%) - 鼠标、键盘控制能力
- **记忆系统** (95%) - 完整的记忆存储和检索
- **API接口** (100%) - 完整的Web服务能力

### 🚀 QYuan现在具备的能力
- 🧠 **智能对话** - 基于LLM的自然语言理解和生成
- 🖱️ **操作控制** - 鼠标和键盘的精确控制
- 💾 **学习记忆** - 自动存储和检索历史交互
- 🌐 **Web服务** - 完整的REST API和WebSocket接口
- 📊 **状态监控** - 实时的系统健康和性能监控

## 贡献指南

本项目目前处于早期开发阶段，欢迎提出建议和反馈。

## 许可证

MIT License

## 联系方式

- GitHub: [gyyxs88](https://github.com/gyyxs88)
- 项目仓库: [QYuan](https://github.com/gyyxs88/QYuan)

---

*QYuan - 让AI真正成为您的数字CEO*

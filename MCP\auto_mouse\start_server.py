"""
启动MCP服务器的脚本。
"""

import os
import sys
import argparse
import logging
from src.auto_mouse_server import mcp

def setup_logging(level=logging.INFO):
    """设置日志配置。"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

def main():
    """主函数。"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='启动自动鼠标控制MCP服务器')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--transport', default='stdio', choices=['stdio', 'tcp'], help='传输方式')
    parser.add_argument('--host', default='localhost', help='TCP服务器主机（仅当transport=tcp时有效）')
    parser.add_argument('--port', type=int, default=8000, help='TCP服务器端口（仅当transport=tcp时有效）')
    args = parser.parse_args()
    
    # 设置日志级别
    log_level = logging.DEBUG if args.debug else logging.INFO
    setup_logging(log_level)
    
    # 打印欢迎信息
    print("=" * 60)
    print("自动鼠标控制MCP服务器")
    print("=" * 60)
    print(f"传输方式: {args.transport}")
    if args.transport == 'tcp':
        print(f"主机: {args.host}")
        print(f"端口: {args.port}")
    print(f"调试模式: {'启用' if args.debug else '禁用'}")
    print("=" * 60)
    print("按Ctrl+C停止服务器")
    print("=" * 60)
    
    try:
        # 启动服务器
        if args.transport == 'stdio':
            mcp.run(transport='stdio')
        elif args.transport == 'tcp':
            mcp.run(transport='tcp', host=args.host, port=args.port)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"\n服务器运行出错: {e}")

if __name__ == "__main__":
    main()

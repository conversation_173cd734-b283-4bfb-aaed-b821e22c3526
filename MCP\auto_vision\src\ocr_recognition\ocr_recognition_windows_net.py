#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OCR文本识别模块 (Windows OCR实现，使用pythonnet)

提供基于Windows OCR API的文本识别功能，通过pythonnet库访问.NET框架。
"""

import os
import sys
import time
import numpy as np
from PIL import Image
import clr
import ctypes
import platform

# 导入基础接口类
from .ocr_recognition_base import OCRRecognitionBase


class OCRRecognitionWindowsNet(OCRRecognitionBase):
    """OCR文本识别类，提供基于Windows OCR API的文本识别功能，使用pythonnet库"""

    def __init__(self, lang="en-US"):
        """
        初始化Windows OCR引擎

        Args:
            lang: 识别语言，默认为英语(美国)
        """
        self.lang = lang
        self.ocr_engine = None
        self.supported_languages = None
        self.winrt_initialized = False

        # 初始化Windows OCR引擎
        self._init_engine()

    def _init_engine(self):
        """初始化Windows OCR引擎"""
        try:
            # 检查是否为Windows平台
            if platform.system() != "Windows":
                raise ValueError("Windows OCR仅支持Windows平台")

            # 初始化Windows Runtime
            self._init_winrt()

            # 获取支持的语言
            self._get_supported_languages()

            # 设置语言
            self._set_language(self.lang)

            print("Windows OCR引擎初始化成功")
        except Exception as e:
            print(f"Windows OCR引擎初始化失败: {e}")
            raise

    def _init_winrt(self):
        """初始化Windows Runtime"""
        try:
            # 添加Windows Runtime引用
            import clr
            from System import IntPtr, Array, Byte
            from System.Runtime.InteropServices import GCHandle, GCHandleType
            from System.IO import MemoryStream

            # 添加Windows.winmd引用
            winmd_path = os.path.join(os.environ["WINDIR"], "System32", "WinMetadata")
            for file in os.listdir(winmd_path):
                if file.startswith("Windows.") and file.endswith(".winmd"):
                    try:
                        clr.AddReference("Windows")
                        break
                    except:
                        pass

            # 导入Windows命名空间
            from Windows.Media.Ocr import OcrEngine
            from Windows.Globalization import Language
            from Windows.Graphics.Imaging import BitmapDecoder, SoftwareBitmap
            from Windows.Storage import StorageFile
            from Windows.Storage.Streams import DataReader, DataWriter, InMemoryRandomAccessStream

            # 保存引用
            self.OcrEngine = OcrEngine
            self.Language = Language
            self.BitmapDecoder = BitmapDecoder
            self.SoftwareBitmap = SoftwareBitmap
            self.StorageFile = StorageFile
            self.DataReader = DataReader
            self.DataWriter = DataWriter
            self.InMemoryRandomAccessStream = InMemoryRandomAccessStream
            self.Array = Array
            self.Byte = Byte
            self.MemoryStream = MemoryStream
            self.GCHandle = GCHandle
            self.GCHandleType = GCHandleType

            self.winrt_initialized = True
        except Exception as e:
            print(f"初始化Windows Runtime失败: {e}")
            raise

    def _get_supported_languages(self):
        """获取Windows OCR支持的语言"""
        try:
            if not self.winrt_initialized:
                raise ValueError("Windows Runtime未初始化")

            # 获取OCR引擎支持的语言
            available_languages = self.OcrEngine.AvailableRecognizerLanguages

            # 将语言对象转换为语言标签
            self.supported_languages = [lang.LanguageTag for lang in available_languages]

            print(f"Windows OCR支持的语言: {self.supported_languages}")
        except Exception as e:
            print(f"获取Windows OCR支持的语言失败: {e}")
            self.supported_languages = ["en-US"]  # 默认支持英语

    def _set_language(self, lang):
        """
        设置OCR语言

        Args:
            lang: 语言代码，如"en-US", "zh-CN"
        """
        try:
            if not self.winrt_initialized:
                raise ValueError("Windows Runtime未初始化")

            # 检查语言是否支持
            if self.supported_languages and lang not in self.supported_languages:
                print(f"警告: 语言'{lang}'不受支持，将使用默认语言'en-US'")
                lang = "en-US"

            # 创建语言对象
            language = self.Language(lang)

            # 使用该语言创建OCR引擎
            self.ocr_engine = self.OcrEngine.TryCreateFromLanguage(language)

            if self.ocr_engine is None:
                raise ValueError(f"无法创建语言为'{lang}'的OCR引擎")
        except Exception as e:
            print(f"设置OCR语言失败: {e}")
            raise

    def recognize_text(self, image, region=None, return_format="structured"):
        """
        识别图像中的文本

        Args:
            image: 图像数据或路径
            region: 识别区域 [x, y, width, height]，为None则识别整个图像
            return_format: 返回格式，支持 "text", "structured"

        Returns:
            根据return_format返回不同格式的结果:
            - "text": 返回纯文本字符串
            - "structured": 返回结构化数据，包含文本、位置和置信度
        """
        if not self.winrt_initialized:
            raise ValueError("Windows Runtime未初始化")

        # 处理图像输入
        if isinstance(image, str):
            # 如果是路径，则加载图像
            image = Image.open(image)

        # 处理区域
        if region:
            x, y, width, height = region
            image = image.crop((x, y, x + width, y + height))

        # 转换为RGB模式（确保图像格式正确）
        if image.mode != "RGB":
            image = image.convert("RGB")

        # 将PIL图像转换为字节数组
        img_bytes = self._pil_to_bytes(image)

        try:
            # 创建内存流
            stream = self.InMemoryRandomAccessStream()
            writer = self.DataWriter(stream.GetOutputStreamAt(0))
            writer.WriteBytes(img_bytes)
            writer.StoreAsync().GetResults()

            # 解码图像
            decoder = self.BitmapDecoder.CreateAsync(stream).GetResults()
            bitmap = decoder.GetSoftwareBitmapAsync().GetResults()

            # 执行OCR识别
            result = self.ocr_engine.RecognizeAsync(bitmap).GetResults()

            # 处理结果
            if return_format == "text":
                # 返回纯文本
                text = ""
                for line in result.Lines:
                    text += line.Text + "\n"
                return text.strip()
            else:
                # 返回结构化数据
                structured_result = []
                for line in result.Lines:
                    # 获取行的边界框
                    line_rect = line.BoundingRect
                    line_box = [
                        int(line_rect.X), int(line_rect.Y),
                        int(line_rect.X + line_rect.Width),
                        int(line_rect.Y + line_rect.Height)
                    ]

                    # 获取行中的单词
                    words = []
                    for word in line.Words:
                        word_rect = word.BoundingRect
                        word_box = [
                            int(word_rect.X), int(word_rect.Y),
                            int(word_rect.X + word_rect.Width),
                            int(word_rect.Y + word_rect.Height)
                        ]

                        words.append({
                            "text": word.Text,
                            "box": word_box,
                            "confidence": 0.9  # Windows OCR不提供置信度，使用默认值
                        })

                    structured_result.append({
                        "text": line.Text,
                        "box": line_box,
                        "confidence": 0.9,  # Windows OCR不提供置信度，使用默认值
                        "words": words
                    })

                return structured_result

        except Exception as e:
            print(f"Windows OCR识别失败: {e}")
            return [] if return_format == "structured" else ""

    def _pil_to_bytes(self, image):
        """
        将PIL图像转换为字节数组

        Args:
            image: PIL图像对象

        Returns:
            字节数组
        """
        # 保存为字节流
        import io
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        img_byte_arr = img_byte_arr.getvalue()
        
        # 转换为.NET字节数组
        buffer = bytes(img_byte_arr)
        return self.Array[self.Byte](buffer)

    def get_engine_info(self):
        """
        获取OCR引擎信息

        Returns:
            dict: 包含引擎名称、版本、支持的语言等信息
        """
        return {
            "name": "Windows OCR (.NET)",
            "version": "Windows 10/11 内置",
            "supported_languages": self.get_supported_languages(),
            "current_language": self.lang
        }

    def get_supported_languages(self):
        """
        获取支持的语言列表

        Returns:
            list: 支持的语言代码列表
        """
        return self.supported_languages or ["en-US"]


# 测试代码
if __name__ == "__main__":
    # 创建OCR对象
    ocr = OCRRecognitionWindowsNet(lang="en-US")

    # 测试图像路径
    test_image_path = "screenshots/screenshot_latest.png"

    # 如果测试图像不存在，则退出
    if not os.path.exists(test_image_path):
        print(f"测试图像不存在: {test_image_path}")
        sys.exit(1)

    # 识别文本
    results = ocr.recognize_text(test_image_path, return_format="structured")

    # 打印识别结果
    print("\n识别结果:")
    for line in results:
        print(f"文本: {line['text']}")
        print(f"位置: {line['box']}")
        print(f"置信度: {line['confidence']}")
        print("-" * 50)

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
视觉LLM模块

提供与视觉LLM（如Gemini）的连接功能，用于分析屏幕截图。
"""

import os
import sys
import base64
import requests
import json
from PIL import Image
import io


class VisionLLM:
    """视觉LLM类，提供与视觉LLM的连接功能"""

    def __init__(self, api_key=None, model=None):
        """
        初始化视觉LLM类

        Args:
            api_key: Gemini API密钥
            model: Gemini模型名称
        """
        # 使用环境变量中的API密钥，如果没有提供
        if api_key is None:
            api_key = os.environ.get("GEMINI_API_KEY", "AIzaSyAnUoeV7lye3px9X6W4XnHsl3Zxk6D3sGI")
            if not api_key:
                print("警告: 未提供Gemini API密钥，请设置GEMINI_API_KEY环境变量或在初始化时提供api_key参数")

        # 使用默认模型，如果没有提供
        if model is None:
            model = "gemini-2.5-flash-preview-04-17"

        self.api_key = api_key
        self.model = model

        # 使用v1beta API端点访问gemini-2.5-flash-preview-04-17模型
        self.api_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key={api_key}"

    def encode_image(self, image_path):
        """
        将图像编码为Base64

        Args:
            image_path: 图像路径

        Returns:
            str: Base64编码的图像
        """
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def analyze_image(self, image_path, enhanced_image_path, prompt):
        """
        分析图像

        Args:
            image_path: 原始图像路径
            enhanced_image_path: 增强后的图像路径
            prompt: 提示文本

        Returns:
            str: 分析结果
        """
        try:
            # 编码图像
            image_base64 = self.encode_image(image_path)
            enhanced_image_base64 = self.encode_image(enhanced_image_path)

            # 构建请求
            payload = {
                "contents": [
                    {
                        "role": "user",
                        "parts": [
                            {
                                "text": "我将向你展示两张图片。第一张是原始截图，第二张是添加了坐标网格的同一截图。请使用第二张图片中的坐标系来回答关于第一张图片的问题。\n\n问题：" + prompt
                            },
                            {
                                "inline_data": {
                                    "mime_type": "image/jpeg",
                                    "data": image_base64
                                }
                            },
                            {
                                "inline_data": {
                                    "mime_type": "image/jpeg",
                                    "data": enhanced_image_base64
                                }
                            }
                        ]
                    }
                ],
                "generation_config": {
                    "temperature": 0.4,
                    "top_p": 0.95,
                    "top_k": 40,
                    "max_output_tokens": 4096,
                }
            }

            # 尝试使用主要模型 (gemini-2.5-flash-preview-04-17)
            try:
                # 发送请求
                response = requests.post(self.api_url, json=payload)

                # 检查响应
                if response.status_code == 200:
                    response_json = response.json()

                    # 提取文本回复
                    if "candidates" in response_json and len(response_json["candidates"]) > 0:
                        candidate = response_json["candidates"][0]
                        if "content" in candidate and "parts" in candidate["content"]:
                            parts = candidate["content"]["parts"]
                            if len(parts) > 0 and "text" in parts[0]:
                                return parts[0]["text"]

                    # 如果无法提取文本，返回完整的JSON响应
                    return json.dumps(response_json, indent=2)
                else:
                    # 如果主要模型失败，尝试使用备用模型 (gemini-2.0-flash)
                    print(f"主要模型请求失败: {response.status_code}\n{response.text}")
                    print("尝试使用备用模型 gemini-2.0-flash...")
                    return self._fallback_analyze_image(image_base64, enhanced_image_base64, prompt)
            except Exception as e:
                # 如果主要模型出错，尝试使用备用模型
                print(f"主要模型请求出错: {e}")
                print("尝试使用备用模型 gemini-2.0-flash...")
                return self._fallback_analyze_image(image_base64, enhanced_image_base64, prompt)

        except Exception as e:
            return f"分析图像时出错: {e}"

    def _fallback_analyze_image(self, image_base64, enhanced_image_base64, prompt):
        """
        使用备用模型分析图像

        Args:
            image_base64: Base64编码的原始图像
            enhanced_image_base64: Base64编码的增强图像
            prompt: 提示文本

        Returns:
            str: 分析结果
        """
        try:
            # 构建备用API URL
            fallback_api_url = f"https://generativelanguage.googleapis.com/v1/models/gemini-2.0-flash:generateContent?key={self.api_key}"

            # 构建请求
            payload = {
                "contents": [
                    {
                        "role": "user",
                        "parts": [
                            {
                                "text": "我将向你展示两张图片。第一张是原始截图，第二张是添加了坐标网格的同一截图。请使用第二张图片中的坐标系来回答关于第一张图片的问题。\n\n问题：" + prompt
                            },
                            {
                                "inline_data": {
                                    "mime_type": "image/jpeg",
                                    "data": image_base64
                                }
                            },
                            {
                                "inline_data": {
                                    "mime_type": "image/jpeg",
                                    "data": enhanced_image_base64
                                }
                            }
                        ]
                    }
                ],
                "generation_config": {
                    "temperature": 0.4,
                    "top_p": 0.95,
                    "top_k": 40,
                    "max_output_tokens": 4096,
                }
            }

            # 发送请求
            response = requests.post(fallback_api_url, json=payload)

            # 检查响应
            if response.status_code == 200:
                response_json = response.json()

                # 提取文本回复
                if "candidates" in response_json and len(response_json["candidates"]) > 0:
                    candidate = response_json["candidates"][0]
                    if "content" in candidate and "parts" in candidate["content"]:
                        parts = candidate["content"]["parts"]
                        if len(parts) > 0 and "text" in parts[0]:
                            return parts[0]["text"]

                # 如果无法提取文本，返回完整的JSON响应
                return json.dumps(response_json, indent=2)
            else:
                return f"备用模型错误: {response.status_code}\n{response.text}"

        except Exception as e:
            return f"备用模型分析图像时出错: {e}"

    def analyze_screenshot(self, screenshot_path, enhanced_screenshot_path, question):
        """
        分析屏幕截图

        Args:
            screenshot_path: 屏幕截图路径
            enhanced_screenshot_path: 增强后的屏幕截图路径
            question: 问题

        Returns:
            str: 分析结果
        """
        # 构建提示
        prompt = f"""
请分析这个屏幕截图并回答以下问题：
{question}

请在回答中使用坐标系来描述屏幕上的元素位置。例如，"在坐标(100, 200)处有一个按钮"。
请尽可能详细地回答，但只回答问题相关的内容。
"""

        # 分析图像
        return self.analyze_image(screenshot_path, enhanced_screenshot_path, prompt)


# 测试代码
if __name__ == "__main__":
    # 创建视觉LLM对象
    vision_llm = VisionLLM()

    # 测试图像路径
    screenshot_path = "screenshots/screenshot_latest.png"
    enhanced_screenshot_path = "screenshots/screenshot_latest_enhanced.png"

    # 如果测试图像不存在，则退出
    if not os.path.exists(screenshot_path) or not os.path.exists(enhanced_screenshot_path):
        print(f"测试图像不存在: {screenshot_path} 或 {enhanced_screenshot_path}")
        sys.exit(1)

    # 测试问题
    question = "这个屏幕上有什么内容？请描述主要元素及其位置。"

    # 分析屏幕截图
    result = vision_llm.analyze_screenshot(screenshot_path, enhanced_screenshot_path, question)
    print(f"分析结果:\n{result}")

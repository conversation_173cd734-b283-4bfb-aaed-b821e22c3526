#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Auto Vision MCP服务器

提供屏幕截图、OCR文本识别和UI元素识别功能的MCP服务器。
"""

import os
import time
import random
import argparse
import numpy as np
from PIL import Image

# 导入自定义模块
from src.screen_capture import ScreenCapture, ScreenAnalyzer, FullScreenCapture, RegionScreenCapture
from src.ocr_recognition.ocr_recognition import OCRRecognition

# 导入UI自动化工厂
from src.ui_automation.ui_automation_factory import UIAutomationFactory

# 定义UI自动化类
class UIAutomation:
    """UI元素识别类，提供UI元素识别功能"""

    def __init__(self, backend="win32gui"):
        """
        初始化UI元素识别类

        Args:
            backend: 后端实现，支持 "pywinauto", "win32gui"
        """
        self.backend = backend

        # 使用工厂类创建UI自动化引擎
        self.ui_engine = UIAutomationFactory.create(backend)

    def get_foreground_window(self):
        """获取前台窗口"""
        return self.ui_engine.get_foreground_window()

    def get_window_by_title(self, title, partial_match=True):
        """通过标题获取窗口"""
        return self.ui_engine.get_window_by_title(title, partial_match)

    def get_ui_elements(self, window=None, element_type=None, depth=3, visible_only=True):
        """获取UI元素及其属性"""
        if window is None:
            window = self.get_foreground_window()

        if element_type is None:
            raise ValueError("必须指定element_type参数，以避免返回过多数据")

        return self.ui_engine.get_ui_elements(window, element_type, depth, visible_only)

    def find_element_by_text(self, text, element_type=None, window=None, match_type="contains", visible_only=True, timeout=5):
        """通过文本查找UI元素"""
        if window is None:
            window = self.get_foreground_window()

        if element_type is None:
            raise ValueError("必须指定element_type参数，以避免返回过多数据")

        return self.ui_engine.find_element_by_text(text, element_type, window, match_type, visible_only, timeout)

    def activate_window(self, window):
        """激活窗口"""
        return self.ui_engine.activate_window(window)

    def maximize_window(self, window):
        """最大化窗口"""
        return self.ui_engine.maximize_window(window)

    def minimize_window(self, window):
        """最小化窗口"""
        return self.ui_engine.minimize_window(window)

    def restore_window(self, window):
        """还原窗口"""
        return self.ui_engine.restore_window(window)

    def resize_window(self, window, width, height):
        """调整窗口大小"""
        return self.ui_engine.resize_window(window, width, height)

    def move_window(self, window, x, y):
        """移动窗口"""
        return self.ui_engine.move_window(window, x, y)

# 导入FastMCP
try:
    from fastmcp import FastMCP
except ImportError:
    print("请安装FastMCP: pip install fastmcp")
    raise


class AutoVisionServer:
    """Auto Vision MCP服务器类"""

    def __init__(self, ocr_engine="auto", ui_backend="win32gui", vision_llm=True):
        """
        初始化Auto Vision MCP服务器

        Args:
            ocr_engine: OCR引擎，支持 "auto", "windows", "easyocr"
            ui_backend: UI自动化后端，支持 "win32gui", "pywinauto", "uiautomation"
            vision_llm: 是否启用视觉LLM分析
        """
        # 保存初始化参数
        self.ocr_engine = ocr_engine
        self.ui_backend = ui_backend
        self.vision_llm = vision_llm

        # 创建MCP服务器
        self.mcp = FastMCP("auto-vision-server")

        # 创建工具字典，用于测试
        self.tools = {}

        # 创建屏幕截图对象
        self.screen_capture = ScreenCapture()

        # 创建OCR对象
        try:
            # 设置OCR语言
            if ocr_engine == "windows":
                lang = "en-US"
            elif ocr_engine == "easyocr":
                lang = ["en", "ch_sim"]
            else:  # auto
                lang = None

            # 创建OCR对象，启用备用引擎
            self.ocr = OCRRecognition(engine=ocr_engine, lang=lang, fallback=True)

            # 获取引擎信息
            engine_info = self.ocr.get_engine_info()
            print(f"OCR引擎初始化成功:")
            print(f"主引擎: {engine_info['primary_engine']['name']}")
            if engine_info['fallback_engine']:
                print(f"备用引擎: {engine_info['fallback_engine']['name']}")
        except Exception as e:
            print(f"OCR引擎初始化失败: {e}")
            self.ocr = None

        # 创建UI自动化对象
        try:
            self.ui = UIAutomation(backend=ui_backend)
        except Exception as e:
            print(f"UI自动化引擎初始化失败: {e}")
            self.ui = None

        # 创建屏幕分析服务对象
        try:
            self.screen_analyzer = ScreenAnalyzer() if vision_llm else None
        except Exception as e:
            print(f"屏幕分析服务初始化失败: {e}")
            self.screen_analyzer = None

        # 注册工具
        self._register_tools()

    def _register_tools(self):
        """注册MCP工具"""
        # 屏幕分析工具（主要的屏幕截图接口）
        if self.screen_analyzer:
            self.mcp.tool()(self.analyze_screen)
            self.mcp.tool()(self.analyze_screen_region)
            self.mcp.tool()(self.analyze_latest_screenshot)
            self.mcp.tool()(self.get_screen_size)

            # 添加到工具字典，用于测试
            self.tools["analyze_screen"] = self.analyze_screen
            self.tools["analyze_screen_region"] = self.analyze_screen_region
            self.tools["analyze_latest_screenshot"] = self.analyze_latest_screenshot
            self.tools["get_screen_size"] = self.get_screen_size

        # 新增的直接截图工具
        self.mcp.tool()(self.capture_fullscreen)
        self.mcp.tool()(self.capture_fullscreen_with_coordinates)
        self.mcp.tool()(self.capture_region)
        self.mcp.tool()(self.capture_region_with_coordinates)

        # 添加到工具字典，用于测试
        self.tools["capture_fullscreen"] = self.capture_fullscreen
        self.tools["capture_fullscreen_with_coordinates"] = self.capture_fullscreen_with_coordinates
        self.tools["capture_region"] = self.capture_region
        self.tools["capture_region_with_coordinates"] = self.capture_region_with_coordinates

        # OCR文本识别工具
        if self.ocr:
            self.mcp.tool()(self.recognize_text)
            # 添加到工具字典，用于测试
            self.tools["recognize_text"] = self.recognize_text

        # UI元素识别工具
        if self.ui:
            self.mcp.tool()(self.get_ui_elements)
            self.mcp.tool()(self.find_element_by_text)
            self.mcp.tool()(self.interact_with_element_by_text)

            # 添加到工具字典，用于测试
            self.tools["get_ui_elements"] = self.get_ui_elements
            self.tools["find_element_by_text"] = self.find_element_by_text
            self.tools["interact_with_element_by_text"] = self.interact_with_element_by_text

        # 组合工具
        self.mcp.tool()(self.capture_and_analyze_screen)
        # 添加到工具字典，用于测试
        self.tools["capture_and_analyze_screen"] = self.capture_and_analyze_screen

    async def get_screen_size(self):
        """
        获取屏幕尺寸

        Returns:
            屏幕尺寸 {"width": 宽度, "height": 高度}
        """
        if self.screen_analyzer:
            width, height = self.screen_analyzer.full_capture.get_screen_size()
        else:
            width, height = self.screen_capture.get_screen_size()
        return {"width": width, "height": height}

    # 以下方法为私有方法，不对外暴露
    async def _capture_screen(self, region=None, save_path=None):
        """
        捕获屏幕区域（私有方法，不对外暴露）

        Args:
            region: 截图区域 [x, y, width, height]，为None则截取全屏
            save_path: 保存路径，为None则自动生成路径
        """
        try:
            # 使用屏幕分析器捕获屏幕
            if region is None:
                # 全屏截图
                from src.screen_capture.screen_capture_full import FullScreenCapture
                screen_capture = FullScreenCapture()
                _, path = screen_capture.capture_screen(save_path=save_path)
            else:
                # 区域截图
                from src.screen_capture.screen_capture_region import RegionScreenCapture
                screen_capture = RegionScreenCapture()
                x, y, width, height = region
                _, path = screen_capture.capture_region(x, y, width, height, save_path=save_path)

            return {"success": True, "path": path}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def recognize_text(self, image_path, region=None, return_format="structured", use_fallback=True):
        """
        识别图像中的文本

        Args:
            image_path: 图像路径
            region: 识别区域 [x, y, width, height]，为None则识别整个图像
            return_format: 返回格式，支持 "text", "structured"
            use_fallback: 是否在主引擎失败时使用备用引擎
        """
        if not self.ocr:
            return {"success": False, "error": "OCR引擎未初始化"}

        try:
            # 获取引擎信息
            engine_info = self.ocr.get_engine_info()

            # 识别文本
            results = self.ocr.recognize_text(
                image_path,
                region=region,
                return_format=return_format,
                use_fallback=use_fallback
            )

            # 返回结果
            return {
                "success": True,
                "results": results,
                "engine": engine_info["primary_engine"]["name"],
                "fallback_used": False  # 暂时无法确定是否使用了备用引擎
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def get_ui_elements(self, window_title=None, element_type=None, depth=3, visible_only=True):
        """
        获取UI元素及其属性

        Args:
            window_title: 窗口标题，为None则获取前台窗口
            element_type: 元素类型，为None则获取所有类型
            depth: 树的深度
            visible_only: 是否只返回可见元素
        """
        if not self.ui:
            return {"success": False, "error": "UI自动化引擎未初始化"}

        try:
            if window_title:
                window = self.ui.get_window_by_title(window_title)
            else:
                window = self.ui.get_foreground_window()

            elements = self.ui.get_ui_elements(window, element_type=element_type, depth=depth, visible_only=visible_only)
            return {"success": True, "elements": elements}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def find_element_by_text(self, text, window_title=None, match_type="contains", visible_only=True, timeout=5):
        """
        通过文本查找UI元素

        Args:
            text: 要查找的文本
            window_title: 窗口标题，为None则在前台窗口中查找
            match_type: 匹配类型，可选值：exact, contains, starts_with, ends_with
            visible_only: 是否只查找可见元素
            timeout: 超时时间（秒）
        """
        if not self.ui:
            return {"success": False, "error": "UI自动化引擎未初始化"}

        try:
            if window_title:
                window = self.ui.get_window_by_title(window_title)
            else:
                window = self.ui.get_foreground_window()

            element, properties = self.ui.find_element_by_text(text, window=window, match_type=match_type, visible_only=visible_only, timeout=timeout)

            if element:
                return {"success": True, "found": True, "properties": properties}
            else:
                return {"success": True, "found": False}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def interact_with_element_by_text(self, text, action="click", input_value=None, window_title=None, match_type="contains", wait_before_ms=50, wait_after_ms=50):
        """
        通过文本查找并与UI元素交互

        Args:
            text: 要查找的文本
            action: 操作类型，可选值：click, double_click, right_click, input
            input_value: 输入值，仅当action为input时有效
            window_title: 窗口标题，为None则在前台窗口中查找
            match_type: 匹配类型，可选值：exact, contains, starts_with, ends_with
            wait_before_ms: 执行前等待时间（毫秒）
            wait_after_ms: 执行后等待时间（毫秒）
        """
        if not self.ui:
            return {"success": False, "error": "UI自动化引擎未初始化"}

        try:
            if window_title:
                window = self.ui.get_window_by_title(window_title)
            else:
                window = self.ui.get_foreground_window()

            element, _ = self.ui.find_element_by_text(text, window=window, match_type=match_type)

            if not element:
                return {"success": False, "error": f"未找到包含文本 '{text}' 的元素"}

            success = self.ui.interact_with_element(element, action=action, value=input_value, wait_before_ms=wait_before_ms, wait_after_ms=wait_after_ms)

            return {"success": success}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def analyze_screen(self, question):
        """
        分析全屏

        Args:
            question: 问题
        """
        if not self.screen_analyzer:
            return {"success": False, "error": "屏幕分析服务未启用"}

        try:
            result = self.screen_analyzer.capture_and_analyze_screen(question)
            return result
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def analyze_screen_region(self, question, x, y, width, height):
        """
        分析屏幕区域

        Args:
            question: 问题
            x: 区域左上角X坐标
            y: 区域左上角Y坐标
            width: 区域宽度
            height: 区域高度
        """
        if not self.screen_analyzer:
            return {"success": False, "error": "屏幕分析服务未启用"}

        try:
            region = (x, y, width, height)
            result = self.screen_analyzer.capture_and_analyze_screen(question, region=region)
            return result
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def analyze_latest_screenshot(self, question):
        """
        分析最新的屏幕截图

        Args:
            question: 问题
        """
        if not self.screen_analyzer:
            return {"success": False, "error": "屏幕分析服务未启用"}

        try:
            result = self.screen_analyzer.analyze_latest_screenshot(question)
            return result
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def capture_fullscreen(self):
        """
        全屏截图

        Returns:
            dict: 包含base64编码的图片数据
        """
        try:
            # 创建全屏截图对象
            screen_capture = FullScreenCapture()

            # 捕获全屏并转换为base64
            result = screen_capture.capture_screen_to_base64(suffix="mcp_fullscreen")
            return result
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def capture_fullscreen_with_coordinates(self):
        """
        带坐标的全屏截图

        Returns:
            dict: 包含base64编码的带坐标图片数据
        """
        try:
            # 创建全屏截图对象
            screen_capture = FullScreenCapture()

            # 捕获带坐标的全屏
            result = screen_capture.capture_fullscreen_with_coordinates()
            return result
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def capture_region(self, x, y, width, height):
        """
        局部截图

        Args:
            x: 区域左上角X坐标
            y: 区域左上角Y坐标
            width: 区域宽度
            height: 区域高度

        Returns:
            dict: 包含base64编码的图片数据
        """
        try:
            # 创建区域截图对象
            region_capture = RegionScreenCapture()

            # 捕获区域并转换为base64
            region = (x, y, width, height)
            result = region_capture.capture_screen_to_base64(region=region, suffix="mcp_region")
            return result
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def capture_region_with_coordinates(self, x, y, width, height):
        """
        带坐标的局部截图

        Args:
            x: 区域左上角X坐标
            y: 区域左上角Y坐标
            width: 区域宽度
            height: 区域高度

        Returns:
            dict: 包含base64编码的带坐标图片数据
        """
        try:
            # 创建区域截图对象
            region_capture = RegionScreenCapture()

            # 捕获带坐标的区域
            result = region_capture.capture_region_with_coordinates(x, y, width, height)
            return result
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def capture_and_analyze_screen(self, question="这个屏幕上有什么内容？请描述主要元素及其位置。", region=None, analysis_type="vision"):
        """
        捕获屏幕区域并进行分析

        Args:
            question: 要问视觉LLM的问题
            region: 截图区域 [x, y, width, height]，为None则截取全屏
            analysis_type: 分析类型，可选值：vision, ocr, ui_elements, combined
        """
        result = {"success": True}

        # 视觉LLM分析（默认）
        if analysis_type in ["vision", "combined"] and self.screen_analyzer:
            try:
                if region:
                    x, y, width, height = region
                    vision_result = self.screen_analyzer.capture_and_analyze_screen(
                        question=question,
                        region=(x, y, width, height)
                    )
                else:
                    vision_result = self.screen_analyzer.capture_and_analyze_screen(
                        question=question
                    )

                if vision_result["success"]:
                    result["screenshot_path"] = vision_result["screenshot_path"]
                    result["enhanced_path"] = vision_result["enhanced_path"]
                    result["vision_analysis"] = vision_result["analysis"]
                else:
                    result["vision_error"] = vision_result.get("error", "未知错误")
            except Exception as e:
                result["vision_error"] = str(e)

        # OCR分析
        if analysis_type in ["ocr", "combined"] and self.ocr:
            try:
                # 如果没有截图路径，先截图
                if "screenshot_path" not in result:
                    screenshot_result = await self._capture_screen(region=region)
                    if screenshot_result["success"]:
                        result["screenshot_path"] = screenshot_result["path"]
                    else:
                        result["ocr_error"] = "截图失败"
                        return result

                # 进行OCR分析
                ocr_response = await self.recognize_text(result["screenshot_path"], return_format="structured")
                if ocr_response["success"]:
                    result["ocr"] = ocr_response["results"]
                    result["ocr_engine"] = ocr_response["engine"]
                else:
                    result["ocr_error"] = ocr_response.get("error", "OCR分析失败")
            except Exception as e:
                result["ocr_error"] = str(e)

        # UI元素分析
        if analysis_type in ["ui_elements", "combined"] and self.ui:
            try:
                window = self.ui.get_foreground_window()
                elements = self.ui.get_ui_elements(window)
                result["ui_elements"] = elements
            except Exception as e:
                result["ui_elements_error"] = str(e)

        return result

    def run(self, transport='stdio', host=None, port=None):
        """
        运行MCP服务器

        Args:
            transport: 传输方式，目前只支持 "stdio"
            host: 保留参数，未使用
            port: 保留参数，未使用
        """
        if transport != 'stdio':
            print(f"警告: 不支持的传输方式 '{transport}'，将使用stdio传输")

        # 始终使用stdio传输
        self.mcp.run(transport='stdio')


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Auto Vision MCP服务器')
    parser.add_argument('--transport', type=str, default='stdio',
                        choices=['stdio'],
                        help='传输方式，目前只支持stdio')
    parser.add_argument('--ocr-engine', type=str, default='auto',
                        choices=['auto', 'easyocr', 'windows', 'baidu'],
                        help='OCR引擎')
    parser.add_argument('--ui-backend', type=str, default='win32gui',
                        choices=['win32gui', 'pywinauto', 'uiautomation'],
                        help='UI自动化后端')
    parser.add_argument('--vision-llm', action='store_true', default=True, help='启用视觉LLM分析')
    parser.add_argument('--no-vision-llm', action='store_false', dest='vision_llm', help='禁用视觉LLM分析')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')

    args = parser.parse_args()

    if args.debug:
        print("调试模式已启用")
        print(f"传输方式: {args.transport}")
        print(f"OCR引擎: {args.ocr_engine}")
        print(f"UI自动化后端: {args.ui_backend}")
        print(f"视觉LLM分析: {'启用' if args.vision_llm else '禁用'}")

    # 创建并运行服务器
    server = AutoVisionServer(ocr_engine=args.ocr_engine, ui_backend=args.ui_backend, vision_llm=args.vision_llm)

    try:
        # 始终使用stdio传输
        server.run(transport='stdio')
    except Exception as e:
        print(f"服务器运行出错: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())

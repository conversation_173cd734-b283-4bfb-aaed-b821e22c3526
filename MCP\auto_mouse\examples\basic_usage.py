"""
基本使用示例，展示如何使用自动鼠标控制MCP服务器。
"""

import json
import time
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入鼠标控制器
from src.mouse_controller import MouseController

def main():
    """主函数，展示基本用法。"""
    print("自动鼠标控制示例")
    print("-" * 30)

    # 创建鼠标控制器
    mouse = MouseController()

    # 获取屏幕尺寸
    screen_size = mouse.get_screen_size()
    print(f"屏幕尺寸: {screen_size['width']}x{screen_size['height']}")

    # 获取当前鼠标位置
    x, y = mouse.get_position()
    print(f"当前鼠标位置: ({x}, {y})")

    # 等待用户准备
    input("按Enter键开始演示...")

    # 示例1: 移动到绝对坐标
    print("\n示例1: 移动到绝对坐标")
    result = mouse.move_mouse(500, 500, duration=1.0, wait_before_ms=100, wait_after_ms=200)
    print(result["message"])
    print(f"等待信息: 执行前 {result['wait_info']['before_ms']}ms, 执行后 {result['wait_info']['after_ms']}ms")
    time.sleep(1)

    # 示例2: 移动到绝对坐标（带随机偏移）
    print("\n示例2: 移动到绝对坐标（带随机偏移）")
    result = mouse.move_mouse(700, 500, duration=1.0, random_offset=True)
    print(result["message"])
    print(f"实际坐标: ({result['position']['x']}, {result['position']['y']})")
    time.sleep(1)

    # 示例3: 移动到区域中心
    print("\n示例3: 移动到区域中心")
    result = mouse.move_mouse(
        left=100, top=100, right=300, bottom=300,
        duration=1.0, random_offset=False
    )
    print(result["message"])
    print(f"实际坐标: ({result['position']['x']}, {result['position']['y']})")
    time.sleep(1)

    # 示例4: 执行点击（单击）
    print("\n示例4: 执行点击（单击）")
    result = mouse.mouse_click(button="left", clicks=1)
    print(result["message"])
    print(f"等待信息: 按下-抬起间隔 {result['wait_info']['press_release_ms']}")
    time.sleep(1)

    # 示例5: 执行点击（双击）
    print("\n示例5: 执行点击（双击）")
    result = mouse.mouse_click(button="left", clicks=2)
    print(result["message"])
    print(f"等待信息: 两次点击间隔 {result['wait_info']['between_clicks_ms']}")
    time.sleep(1)

    # 示例6: 执行拖拽
    print("\n示例6: 执行拖拽")
    result = mouse.mouse_drag(
        end_x=500, end_y=500,
        duration=1.0, button="left"
    )
    print(result["message"])
    print(f"起始位置: ({result['start_position']['x']}, {result['start_position']['y']})")
    print(f"结束位置: ({result['end_position']['x']}, {result['end_position']['y']})")
    time.sleep(1)

    # 示例7: 执行滚动
    print("\n示例7: 执行滚动")
    result = mouse.mouse_scroll(amount=5, direction="down")
    print(result["message"])
    print(f"滚动间隔: {result['wait_info']['between_scrolls_ms']}")
    time.sleep(1)

    # 返回原位置
    print("\n返回原位置")
    mouse.move_mouse(x, y, duration=1.0)

    print("\n演示完成!")

if __name__ == "__main__":
    main()

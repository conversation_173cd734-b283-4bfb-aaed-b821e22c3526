# 错误反馈与恢复机制设计

## 错误分类体系

### 1. 技术层面错误

#### A. 硬件/系统错误
```python
class SystemError(BaseError):
    """系统级错误"""
    
    MOUSE_HARDWARE_FAILURE = "鼠标硬件故障"
    KEYBOARD_HARDWARE_FAILURE = "键盘硬件故障"
    DISPLAY_DRIVER_ERROR = "显示驱动错误"
    MEMORY_INSUFFICIENT = "内存不足"
    DISK_SPACE_FULL = "磁盘空间不足"
    NETWORK_DISCONNECTED = "网络连接断开"
    SYSTEM_OVERLOAD = "系统负载过高"
```

#### B. 软件/应用错误
```python
class ApplicationError(BaseError):
    """应用程序错误"""
    
    APP_CRASH = "应用程序崩溃"
    APP_NOT_RESPONDING = "应用程序无响应"
    APP_NOT_FOUND = "应用程序未找到"
    PERMISSION_DENIED = "权限不足"
    FILE_NOT_FOUND = "文件未找到"
    INVALID_OPERATION = "无效操作"
    TIMEOUT_ERROR = "操作超时"
```

#### C. API/服务错误
```python
class ServiceError(BaseError):
    """服务错误"""
    
    LLM_API_TIMEOUT = "LLM API超时"
    LLM_API_QUOTA_EXCEEDED = "API配额超限"
    LLM_API_INVALID_RESPONSE = "API响应无效"
    DATABASE_CONNECTION_FAILED = "数据库连接失败"
    VECTOR_DB_ERROR = "向量数据库错误"
    MCP_SERVER_UNAVAILABLE = "MCP服务器不可用"
```

### 2. 认知层面错误

#### A. 感知错误
```python
class PerceptionError(BaseError):
    """感知错误"""
    
    SCREEN_RECOGNITION_FAILED = "屏幕识别失败"
    OCR_CONFIDENCE_LOW = "OCR识别置信度过低"
    UI_ELEMENT_NOT_FOUND = "UI元素未找到"
    VISUAL_CONTEXT_MISUNDERSTOOD = "视觉上下文理解错误"
    SCREENSHOT_CORRUPTED = "截图损坏"
```

#### B. 决策错误
```python
class DecisionError(BaseError):
    """决策错误"""
    
    GOAL_MISINTERPRETATION = "目标理解错误"
    STRATEGY_SELECTION_FAILED = "策略选择失败"
    PRIORITY_ASSESSMENT_ERROR = "优先级评估错误"
    RESOURCE_ALLOCATION_ERROR = "资源分配错误"
    RISK_ASSESSMENT_FAILED = "风险评估失败"
```

#### C. 执行错误
```python
class ExecutionError(BaseError):
    """执行错误"""
    
    CLICK_COORDINATE_OFFSET = "点击坐标偏移"
    INPUT_METHOD_MISMATCH = "输入法不匹配"
    TIMING_ERROR = "时机错误"
    SEQUENCE_DISRUPTED = "操作序列被打断"
    FOCUS_LOST = "焦点丢失"
```

### 3. 环境层面错误

#### A. 界面变化错误
```python
class InterfaceError(BaseError):
    """界面错误"""
    
    LAYOUT_CHANGED = "界面布局发生变化"
    THEME_CHANGED = "主题/样式发生变化"
    RESOLUTION_CHANGED = "分辨率发生变化"
    WINDOW_MOVED = "窗口位置发生变化"
    POPUP_APPEARED = "弹出窗口出现"
```

#### B. 状态变化错误
```python
class StateError(BaseError):
    """状态错误"""
    
    UNEXPECTED_STATE_CHANGE = "意外的状态变化"
    CONTEXT_SWITCH = "上下文切换"
    USER_INTERVENTION = "用户干预"
    EXTERNAL_PROCESS_INTERFERENCE = "外部进程干扰"
```

## 错误检测机制

### 1. 实时监控系统
```python
class ErrorMonitoringSystem:
    """错误监控系统"""
    
    def __init__(self):
        self.visual_monitor = VisualErrorMonitor()
        self.system_monitor = SystemErrorMonitor()
        self.performance_monitor = PerformanceErrorMonitor()
        self.behavioral_monitor = BehavioralErrorMonitor()
        
        self.error_patterns = self.load_error_patterns()
        self.detection_rules = self.load_detection_rules()
    
    async def continuous_monitoring(self):
        """持续监控"""
        while True:
            try:
                # 并行检测各种错误
                tasks = [
                    self.visual_monitor.check(),
                    self.system_monitor.check(),
                    self.performance_monitor.check(),
                    self.behavioral_monitor.check()
                ]
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理检测结果
                for result in results:
                    if isinstance(result, Exception):
                        await self.handle_monitor_error(result)
                    elif result.has_errors():
                        await self.handle_detected_errors(result.errors)
                
                await asyncio.sleep(0.1)  # 100ms检测间隔
                
            except Exception as e:
                logger.error(f"监控系统异常: {e}")
                await asyncio.sleep(1)  # 异常时延长间隔
```

### 2. 视觉错误检测
```python
class VisualErrorMonitor:
    """视觉错误监控器"""
    
    def __init__(self):
        self.error_dialog_patterns = self.load_error_dialog_patterns()
        self.loading_indicators = self.load_loading_indicators()
        self.network_error_patterns = self.load_network_error_patterns()
    
    async def check(self) -> MonitorResult:
        """检查视觉错误"""
        screenshot = await self.capture_screenshot()
        errors = []
        
        # 检测错误对话框
        error_dialogs = await self.detect_error_dialogs(screenshot)
        errors.extend(error_dialogs)
        
        # 检测加载状态
        loading_states = await self.detect_loading_states(screenshot)
        errors.extend(loading_states)
        
        # 检测网络错误
        network_errors = await self.detect_network_errors(screenshot)
        errors.extend(network_errors)
        
        # 检测界面异常
        interface_anomalies = await self.detect_interface_anomalies(screenshot)
        errors.extend(interface_anomalies)
        
        return MonitorResult(errors)
    
    async def detect_error_dialogs(self, screenshot: Image) -> List[VisualError]:
        """检测错误对话框"""
        errors = []
        
        # 使用OCR识别错误关键词
        ocr_results = await self.ocr_engine.recognize(screenshot)
        error_keywords = ["错误", "Error", "失败", "Failed", "异常", "Exception"]
        
        for result in ocr_results:
            if any(keyword in result.text for keyword in error_keywords):
                errors.append(VisualError(
                    type="ERROR_DIALOG",
                    message=f"检测到错误对话框: {result.text}",
                    location=result.box,
                    confidence=result.confidence
                ))
        
        # 使用图像识别检测标准错误图标
        error_icons = await self.detect_error_icons(screenshot)
        errors.extend(error_icons)
        
        return errors
```

### 3. 系统错误检测
```python
class SystemErrorMonitor:
    """系统错误监控器"""
    
    def __init__(self):
        self.performance_thresholds = {
            'cpu_usage': 90,
            'memory_usage': 85,
            'disk_usage': 95,
            'response_time': 5.0
        }
    
    async def check(self) -> MonitorResult:
        """检查系统错误"""
        errors = []
        
        # 检查系统性能
        performance_errors = await self.check_performance()
        errors.extend(performance_errors)
        
        # 检查进程状态
        process_errors = await self.check_processes()
        errors.extend(process_errors)
        
        # 检查网络状态
        network_errors = await self.check_network()
        errors.extend(network_errors)
        
        # 检查磁盘空间
        disk_errors = await self.check_disk_space()
        errors.extend(disk_errors)
        
        return MonitorResult(errors)
    
    async def check_performance(self) -> List[SystemError]:
        """检查系统性能"""
        errors = []
        
        cpu_usage = psutil.cpu_percent(interval=1)
        memory_usage = psutil.virtual_memory().percent
        
        if cpu_usage > self.performance_thresholds['cpu_usage']:
            errors.append(SystemError(
                type="HIGH_CPU_USAGE",
                message=f"CPU使用率过高: {cpu_usage}%",
                severity="WARNING"
            ))
        
        if memory_usage > self.performance_thresholds['memory_usage']:
            errors.append(SystemError(
                type="HIGH_MEMORY_USAGE",
                message=f"内存使用率过高: {memory_usage}%",
                severity="WARNING"
            ))
        
        return errors
```

## 错误恢复策略

### 1. 分层恢复机制
```python
class ErrorRecoverySystem:
    """错误恢复系统"""
    
    def __init__(self):
        self.recovery_strategies = {
            # 立即恢复策略
            'immediate': ImmediateRecoveryStrategy(),
            # 重试恢复策略
            'retry': RetryRecoveryStrategy(),
            # 替代方案策略
            'alternative': AlternativeRecoveryStrategy(),
            # 回滚恢复策略
            'rollback': RollbackRecoveryStrategy(),
            # 人工干预策略
            'manual': ManualInterventionStrategy()
        }
    
    async def recover_from_error(self, error: BaseError, context: ErrorContext) -> RecoveryResult:
        """从错误中恢复"""
        
        # 根据错误类型和严重程度选择恢复策略
        strategy = self.select_recovery_strategy(error, context)
        
        try:
            # 执行恢复策略
            result = await strategy.execute(error, context)
            
            # 记录恢复结果
            await self.record_recovery_attempt(error, strategy, result)
            
            return result
            
        except Exception as e:
            # 恢复策略失败，尝试下一级策略
            return await self.escalate_recovery(error, context, e)
    
    def select_recovery_strategy(self, error: BaseError, context: ErrorContext) -> RecoveryStrategy:
        """选择恢复策略"""
        
        # 基于错误类型的策略映射
        strategy_map = {
            PerceptionError: ['immediate', 'retry', 'alternative'],
            ExecutionError: ['immediate', 'retry', 'rollback'],
            SystemError: ['retry', 'alternative', 'manual'],
            ServiceError: ['retry', 'alternative', 'manual']
        }
        
        # 基于错误严重程度的策略选择
        if error.severity == "CRITICAL":
            return self.recovery_strategies['rollback']
        elif error.severity == "HIGH":
            return self.recovery_strategies['alternative']
        elif error.severity == "MEDIUM":
            return self.recovery_strategies['retry']
        else:
            return self.recovery_strategies['immediate']
```

### 2. 具体恢复策略

#### A. 立即恢复策略
```python
class ImmediateRecoveryStrategy(RecoveryStrategy):
    """立即恢复策略"""
    
    async def execute(self, error: BaseError, context: ErrorContext) -> RecoveryResult:
        """执行立即恢复"""
        
        if isinstance(error, ExecutionError):
            if error.type == "FOCUS_LOST":
                # 重新获取焦点
                await self.restore_focus(context.target_window)
                return RecoveryResult(success=True, message="焦点已恢复")
            
            elif error.type == "INPUT_METHOD_MISMATCH":
                # 切换输入法
                await self.switch_input_method(context.required_input_method)
                return RecoveryResult(success=True, message="输入法已切换")
        
        elif isinstance(error, PerceptionError):
            if error.type == "SCREENSHOT_CORRUPTED":
                # 重新截图
                new_screenshot = await self.capture_fresh_screenshot()
                context.screenshot = new_screenshot
                return RecoveryResult(success=True, message="截图已更新")
        
        return RecoveryResult(success=False, message="无法立即恢复")
```

#### B. 重试恢复策略
```python
class RetryRecoveryStrategy(RecoveryStrategy):
    """重试恢复策略"""
    
    def __init__(self, max_retries: int = 3, backoff_factor: float = 1.5):
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
    
    async def execute(self, error: BaseError, context: ErrorContext) -> RecoveryResult:
        """执行重试恢复"""
        
        for attempt in range(self.max_retries):
            try:
                # 等待一段时间后重试
                wait_time = (self.backoff_factor ** attempt)
                await asyncio.sleep(wait_time)
                
                # 重新执行失败的操作
                result = await self.retry_failed_operation(context.failed_operation)
                
                if result.success:
                    return RecoveryResult(
                        success=True,
                        message=f"重试成功 (第{attempt + 1}次尝试)",
                        attempts=attempt + 1
                    )
                
            except Exception as e:
                if attempt == self.max_retries - 1:
                    return RecoveryResult(
                        success=False,
                        message=f"重试失败: {e}",
                        attempts=attempt + 1
                    )
        
        return RecoveryResult(success=False, message="重试次数已用完")
```

#### C. 替代方案策略
```python
class AlternativeRecoveryStrategy(RecoveryStrategy):
    """替代方案策略"""
    
    async def execute(self, error: BaseError, context: ErrorContext) -> RecoveryResult:
        """执行替代方案"""
        
        # 获取替代方案
        alternatives = await self.get_alternative_approaches(context.original_goal)
        
        for alternative in alternatives:
            try:
                # 尝试替代方案
                result = await self.execute_alternative(alternative, context)
                
                if result.success:
                    return RecoveryResult(
                        success=True,
                        message=f"替代方案成功: {alternative.description}",
                        alternative_used=alternative
                    )
                
            except Exception as e:
                continue  # 尝试下一个替代方案
        
        return RecoveryResult(success=False, message="所有替代方案都失败了")
    
    async def get_alternative_approaches(self, goal: Goal) -> List[Alternative]:
        """获取替代方案"""
        alternatives = []
        
        if goal.type == "CLICK_BUTTON":
            # 点击按钮的替代方案
            alternatives.extend([
                Alternative("keyboard_shortcut", "使用键盘快捷键"),
                Alternative("context_menu", "使用右键菜单"),
                Alternative("menu_navigation", "通过菜单导航")
            ])
        
        elif goal.type == "INPUT_TEXT":
            # 文本输入的替代方案
            alternatives.extend([
                Alternative("clipboard_paste", "使用剪贴板粘贴"),
                Alternative("voice_input", "使用语音输入"),
                Alternative("virtual_keyboard", "使用虚拟键盘")
            ])
        
        return alternatives
```

### 3. 学习型恢复机制
```python
class LearningRecoverySystem:
    """学习型恢复系统"""
    
    def __init__(self):
        self.recovery_history = RecoveryHistoryDB()
        self.pattern_analyzer = RecoveryPatternAnalyzer()
    
    async def learn_from_recovery(self, error: BaseError, recovery_result: RecoveryResult):
        """从恢复过程中学习"""
        
        # 记录恢复经验
        recovery_experience = RecoveryExperience(
            error_type=error.type,
            error_context=error.context,
            recovery_strategy=recovery_result.strategy,
            success=recovery_result.success,
            execution_time=recovery_result.execution_time,
            timestamp=datetime.now()
        )
        
        await self.recovery_history.save(recovery_experience)
        
        # 分析恢复模式
        patterns = await self.pattern_analyzer.analyze_recent_recoveries()
        
        # 更新恢复策略权重
        await self.update_strategy_weights(patterns)
    
    async def predict_best_recovery_strategy(self, error: BaseError) -> RecoveryStrategy:
        """预测最佳恢复策略"""
        
        # 查找相似的历史错误
        similar_errors = await self.recovery_history.find_similar_errors(error)
        
        # 分析成功率最高的策略
        strategy_scores = {}
        for similar_error in similar_errors:
            strategy = similar_error.recovery_strategy
            if strategy not in strategy_scores:
                strategy_scores[strategy] = {'success': 0, 'total': 0}
            
            strategy_scores[strategy]['total'] += 1
            if similar_error.success:
                strategy_scores[strategy]['success'] += 1
        
        # 选择成功率最高的策略
        best_strategy = max(
            strategy_scores.items(),
            key=lambda x: x[1]['success'] / x[1]['total'] if x[1]['total'] > 0 else 0
        )[0]
        
        return self.get_strategy_instance(best_strategy)
```

这个错误反馈与恢复机制设计提供了全面的错误处理能力，能够让QYuan在遇到各种问题时自主恢复，并从经验中学习改进。你觉得还需要补充哪些方面？

# -*- coding: utf-8 -*-
"""
QYuan事件系统模块
"""

import asyncio
import logging
from typing import Dict, List, Callable, Any, Optional
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum

class EventType(Enum):
    """事件类型枚举"""
    SYSTEM_START = "system.start"
    SYSTEM_STOP = "system.stop"
    SYSTEM_ERROR = "system.error"
    
    ENGINE_INIT = "engine.init"
    ENGINE_START = "engine.start"
    ENGINE_STOP = "engine.stop"
    ENGINE_ERROR = "engine.error"
    
    GOAL_SET = "goal.set"
    GOAL_COMPLETE = "goal.complete"
    GOAL_FAILED = "goal.failed"
    
    ACTION_START = "action.start"
    ACTION_COMPLETE = "action.complete"
    ACTION_FAILED = "action.failed"
    
    MEMORY_STORE = "memory.store"
    MEMORY_RETRIEVE = "memory.retrieve"
    
    LLM_REQUEST = "llm.request"
    LLM_RESPONSE = "llm.response"
    LLM_ERROR = "llm.error"
    
    MCP_CONNECT = "mcp.connect"
    MCP_DISCONNECT = "mcp.disconnect"
    MCP_OPERATION = "mcp.operation"
    
    USER_MESSAGE = "user.message"
    USER_RESPONSE = "user.response"

@dataclass
class Event:
    """事件数据类"""
    type: EventType
    data: Dict[str, Any]
    timestamp: datetime
    source: str
    event_id: str
    correlation_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        result['type'] = self.type.value
        result['timestamp'] = self.timestamp.isoformat()
        return result

class EventBus:
    """事件总线"""
    
    def __init__(self, max_history: int = 1000):
        self.listeners: Dict[EventType, List[Callable]] = {}
        self.event_history: List[Event] = []
        self.max_history = max_history
        self.logger = logging.getLogger("QYuan.EventBus")
        self._event_counter = 0
    
    def on(self, event_type: EventType, callback: Callable):
        """注册事件监听器"""
        if event_type not in self.listeners:
            self.listeners[event_type] = []
        
        self.listeners[event_type].append(callback)
        self.logger.debug(f"注册事件监听器: {event_type.value} -> {callback.__name__}")
    
    def off(self, event_type: EventType, callback: Callable):
        """移除事件监听器"""
        if event_type in self.listeners:
            try:
                self.listeners[event_type].remove(callback)
                self.logger.debug(f"移除事件监听器: {event_type.value} -> {callback.__name__}")
            except ValueError:
                self.logger.warning(f"尝试移除不存在的监听器: {event_type.value} -> {callback.__name__}")
    
    async def emit(
        self, 
        event_type: EventType, 
        data: Dict[str, Any] = None, 
        source: str = "unknown",
        correlation_id: Optional[str] = None
    ):
        """发射事件"""
        self._event_counter += 1
        
        event = Event(
            type=event_type,
            data=data or {},
            timestamp=datetime.now(),
            source=source,
            event_id=f"evt_{self._event_counter:06d}",
            correlation_id=correlation_id
        )
        
        # 记录事件历史
        self.event_history.append(event)
        
        # 限制历史记录数量
        if len(self.event_history) > self.max_history:
            self.event_history = self.event_history[-self.max_history:]
        
        self.logger.debug(f"发射事件: {event_type.value} from {source}")
        
        # 通知监听器
        if event_type in self.listeners:
            tasks = []
            for callback in self.listeners[event_type]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        tasks.append(callback(event))
                    else:
                        # 同步函数在线程池中执行
                        loop = asyncio.get_event_loop()
                        tasks.append(loop.run_in_executor(None, callback, event))
                except Exception as e:
                    self.logger.error(f"事件监听器执行失败: {callback.__name__} - {e}")
            
            if tasks:
                try:
                    await asyncio.gather(*tasks, return_exceptions=True)
                except Exception as e:
                    self.logger.error(f"事件处理异常: {e}")
    
    def get_recent_events(self, count: int = 10, event_type: Optional[EventType] = None) -> List[Event]:
        """获取最近的事件"""
        events = self.event_history
        
        if event_type:
            events = [e for e in events if e.type == event_type]
        
        return events[-count:] if events else []
    
    def get_events_by_correlation_id(self, correlation_id: str) -> List[Event]:
        """根据关联ID获取事件"""
        return [e for e in self.event_history if e.correlation_id == correlation_id]
    
    def clear_history(self):
        """清空事件历史"""
        self.event_history.clear()
        self.logger.info("事件历史已清空")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取事件统计信息"""
        if not self.event_history:
            return {"total_events": 0, "event_types": {}}
        
        event_counts = {}
        for event in self.event_history:
            event_type = event.type.value
            event_counts[event_type] = event_counts.get(event_type, 0) + 1
        
        return {
            "total_events": len(self.event_history),
            "event_types": event_counts,
            "oldest_event": self.event_history[0].timestamp.isoformat() if self.event_history else None,
            "newest_event": self.event_history[-1].timestamp.isoformat() if self.event_history else None,
            "listeners_count": {et.value: len(listeners) for et, listeners in self.listeners.items()}
        }
